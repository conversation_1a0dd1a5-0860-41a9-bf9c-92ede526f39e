import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import {
  VnrGridNewComponent,
  VnrInputFactory,
  VnrRadioButtonBuilder,
  VnrRadioButtonComponent,
} from '@hrm-frontend-workspace/vnr-module';
import { TranslateModule } from '@ngx-translate/core';
import { EvaluationGridComponent } from '../../../../../../../shared';
import { dataCompetencyEvaluationSection } from '../../../data/test/competency-evaluation-section.data';
import { IFormSection } from '../../../models/form-builder.model';
import { EnumFormType, FormType } from '../../../models/enums/form-canvas.enum';

@Component({
  selector: 'app-competency-evaluation-section',
  templateUrl: './competency-evaluation-section.component.html',
  styleUrls: ['./competency-evaluation-section.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    EvaluationGridComponent,
    VnrRadioButtonComponent,
    FormsModule,
    ReactiveFormsModule,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CompetencyEvaluationSectionComponent implements OnInit, OnChanges {
  @Input() section: IFormSection | any;
  @Input() data: any;
  @Input() formType: FormType = EnumFormType.CREATE;

  @ViewChild('vnrGrid') vnrGrid: VnrGridNewComponent;
  @ViewChild('evaluationGrid') evaluationGrid: EvaluationGridComponent;

  @Output() formValueChange = new EventEmitter<any>();

  private inputFactory: VnrInputFactory = VnrInputFactory.init();
  protected builder: VnrRadioButtonBuilder = new VnrRadioButtonBuilder();
  protected vnrColumGridLocal: any[] = [];
  protected vnrDataGridLocal: any[] = []; //dataCompetencyEvaluationSection;
  protected selectedValue = 1;
  protected _edit = EnumFormType.EDIT;
  protected _preview = EnumFormType.PREVIEW;
  protected _create = EnumFormType.CREATE;
  ngOnChanges(changes: SimpleChanges): void {
    const { section, data } = changes;
    if (data && data.currentValue) {
      this.vnrDataGridLocal = data.currentValue;
    }
    if (section && section.currentValue) {
      this.vnrColumGridLocal = this.section?.properties?.gridConfig?.columns;
    }
  }
  ngOnInit() {
    this.initBuilder();
  }
  private initBuilder() {
    this.builder = this.inputFactory.builderRadioButton({
      label: '',
      options: {
        columnRadioButtonItem: 1,
      },
      dataSource: [
        { label: 'Lấy dữ liệu từ khung năng lực hiện tại', value: 1 },
        { label: 'Lấy dữ liệu từ khung năng lực vị trí kế tiếp', value: 2 },
        { label: 'Tự thiết lập tiêu chí đánh giá', value: 3 },
      ],
    });
  }
  onModelChange(event: any) {
    console.log(event);
  }
  onChange(event: any) {
    console.log(event);
  }
  onChangeAddRow(event: any) {
    const newData = this.evaluationGrid?.dataLocal;
    this.formValueChange.emit({
      sectionId: this.section.id,
      data: newData,
      type: 'addRow',
    });
  }

  onChangeAddRowGroup(event: any) {
    const newData = this.evaluationGrid?.dataLocal;
    this.formValueChange.emit({
      sectionId: this.section.id,
      data: newData,
      type: 'addRowGroup',
    });
  }
}
