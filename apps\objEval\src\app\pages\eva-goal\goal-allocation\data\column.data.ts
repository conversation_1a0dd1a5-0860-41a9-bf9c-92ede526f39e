export const gridAllocationCycleColumns = [
  {
    Name: 'GoalName',
    HeaderName: 'objEval.GoalPeriod.GoalName',
    Header<PERSON>ey: 'objEval.GoalPeriod.GoalName',
    DisplayName: 'objEval.GoalPeriod.GoalName',
    Width: 120,
    Sortable: true,
  },
  {
    Name: 'Weight',
    HeaderName: 'objEval.GoalPeriod.Weight',
    HeaderKey: 'objEval.GoalPeriod.Weight',
    DisplayName: 'objEval.GoalPeriod.Weight',
    Width: 80,
    Sortable: true,
  },
  {
    Name: 'AllocationMethod',
    HeaderName: 'objEval.GoalPeriod.AllocationMethod',
    HeaderKey: 'objEval.GoalPeriod.AllocationMethod',
    DisplayName: 'objEval.GoalPeriod.AllocationMethod',
    Width: 120,
    Sortable: true,
  },
  {
    Name: 'TotalTarget',
    HeaderName: 'objEval.GoalPeriod.TotalTarget',
    Header<PERSON>ey: 'objEval.GoalPeriod.TotalTarget',
    DisplayName: 'objEval.GoalPeriod.TotalTarget',
    Width: 100,
    Sortable: true,
  },
  {
    Name: 'DoneTarget',
    HeaderName: 'objEval.GoalPeriod.DoneTarget',
    HeaderKey: 'objEval.GoalPeriod.DoneTarget',
    DisplayName: 'objEval.GoalPeriod.DoneTarget',
    Width: 100,
    Sortable: true,
  },
  {
    Name: 'Allocated',
    HeaderName: 'objEval.GoalPeriod.Allocated',
    HeaderKey: 'objEval.GoalPeriod.Allocated',
    DisplayName: 'objEval.GoalPeriod.Allocated',
    Width: 80,
    Sortable: true,
  },
];

export const gridAllocationDepartmentColumns = [
  {
    Name: 'Department',
    HeaderName: 'objEval.GoalPeriod.Department',
    HeaderKey: 'objEval.GoalPeriod.Department',
    DisplayName: 'objEval.GoalPeriod.Department',
    Width: 150,
    Group: true,
    Hidden: true,
    Sortable: false,
  },
];

export const columnTotalTarget = {
  Name: 'TotalTarget',
  HeaderName: 'objEval.GoalPeriod.TotalTarget',
  HeaderKey: 'objEval.GoalPeriod.TotalTarget',
  DisplayName: 'objEval.GoalPeriod.TotalTarget',
  Width: 100,
  Sortable: true,
};

export const columnAllocated = {
  Name: 'Allocated',
  HeaderName: 'objEval.GoalPeriod.Allocated',
  HeaderKey: 'objEval.GoalPeriod.Allocated',
  DisplayName: 'objEval.GoalPeriod.Allocated',
  Width: 80,
  Sortable: true,
};

export const gridAllocationGoalColumns = [
  {
    Name: 'ParentID',
    HeaderName: 'objEval.GoalPeriod.GoalName',
    HeaderKey: 'objEval.GoalPeriod.GoalName',
    DisplayName: 'objEval.GoalPeriod.GoalName',
    Width: 150,
    Group: true,
    Hidden: true,
    Sortable: false,
  },
];

export const GroupDepartmentColumns = [
  {
    Name: 'Department',
    HeaderName: 'objEval.GoalPeriod.Department',
    HeaderKey: 'objEval.GoalPeriod.Department',
    DisplayName: 'objEval.GoalPeriod.Department',
    Width: 200,
    Sortable: false,
  },
  {
    Name: 'Weight',
    HeaderName: 'objEval.GoalPeriod.Weight',
    HeaderKey: 'objEval.GoalPeriod.Weight',
    DisplayName: 'objEval.GoalPeriod.Weight',
    Width: 80,
    Sortable: false,
  },
];

export const GroupGoalColumns = [
  {
    Name: 'GoalName',
    HeaderName: 'objEval.GoalPeriod.GoalName',
    HeaderKey: 'objEval.GoalPeriod.GoalName',
    DisplayName: 'objEval.GoalPeriod.GoalName',
    Width: 200,
    Sortable: true,
  },
  {
    Name: 'Weight',
    HeaderName: 'objEval.GoalPeriod.Weight',
    HeaderKey: 'objEval.GoalPeriod.Weight',
    DisplayName: 'objEval.GoalPeriod.Weight',
  },
];
