import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  TemplateRef,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';
import {
  VnrButtonFactory,
  VnrButtonNewBuilder,
  VnrButtonNewComponent,
  VnrListviewNewBuilder,
  VnrListviewNewComponent,
  VnrToolbarNewBuilder,
  VnrToolbarNewComponent,
  vnrUtilities,
} from '@hrm-frontend-workspace/vnr-module';
import { goalTemplateGroupColumns } from '../../data/column.data';

import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonService } from '@hrm-frontend-workspace/core';
import { ResponseStatus } from '@hrm-frontend-workspace/models';
import { VnrTagComponent } from '@hrm-frontend-workspace/ui';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzDrawerService } from 'ng-zorro-antd/drawer';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzModalService } from 'ng-zorro-antd/modal';
import { gridGoalGroupDefineDataSource } from '../../data/datasource.data';
import { GoalTemplateFacade } from '../../facade/goal-template.facade';
import { GoalTemplateFormDetailComponent } from '../goal-template-form-detail/goal-template-form-detail.component';
import { GoalTemplateFormGroupComponent } from '../goal-template-form-group/goal-template-form-group.component';
import { GoalTemplateGridListComponent } from './components/goal-grid-list/goal-template-grid-list.component';

@Component({
  selector: 'goal-template-grid',
  templateUrl: './goal-template-grid.component.html',
  styleUrls: ['./goal-template-grid.component.scss'],
  imports: [
    FormsModule,
    ReactiveFormsModule,
    CommonModule,
    VnrToolbarNewComponent,
    VnrListviewNewComponent,
    VnrTagComponent,
    TranslateModule,
    VnrButtonNewComponent,
    NzButtonModule,
    NzGridModule,
    GoalTemplateGridListComponent,
  ],
})
export class GoalTemplateGridComponent implements OnInit, OnChanges {
  @Output() onSelectGoalGroup = new EventEmitter<any>();
  @Output() onSelectGoal = new EventEmitter<any>();

  @ViewChild('vnrGrid', { static: true }) gridControl: VnrListviewNewComponent;
  @ViewChild('templateType', { static: true }) private _templateType: TemplateRef<any>;
  @ViewChild('tplContentDelete', { static: true }) private _tplContentDelete: TemplateRef<any>;
  @ViewChild('tplFooterDelete', { static: true })
  private _tplFooterDelete: TemplateRef<any>;
  @ViewChild('tplFooterDeleteGoal', { static: true })
  private _tplFooterDeleteGoal: TemplateRef<any>;
  @ViewChild('tplContentViewDetail', { static: true })
  private _tplContentViewDetail: TemplateRef<any>;
  @ViewChild('tplFooterViewDetail', { static: true })
  private _tplFooterViewDetail: TemplateRef<any>;
  private _dataFormSearch: any = {};
  private _btnFactory: VnrButtonFactory = VnrButtonFactory.init();
  protected selectedItem = null;
  private _selectedIDs = [];
  private _columnKey = 'Id';
  private _goalGroupID: string = null;
  protected builderbtnOpenColumnCheck: VnrButtonNewBuilder;
  protected builderbtnCancelOpenColumnCheck: VnrButtonNewBuilder;
  protected builderbtnExpandAll: VnrButtonNewBuilder;
  protected builderbtnCollapseAll: VnrButtonNewBuilder;
  protected builderbtnAddGoalItem: VnrButtonNewBuilder;
  protected isShowOpenColumnCheck: boolean = true;
  protected isShowSelectAll: boolean = true;
  protected isShowExpandAll: boolean = true;

  protected builderGrid: VnrListviewNewBuilder;
  protected builderToolbar: VnrToolbarNewBuilder;
  protected builderButtonCustom: VnrButtonNewBuilder;
  protected builderbtnDelete: VnrButtonNewBuilder;
  protected builderbtnEdit: VnrButtonNewBuilder;
  protected builderbtnDoNo: VnrButtonNewBuilder;
  protected builderbtnDoYes: VnrButtonNewBuilder;
  protected gridName = 'goal-template-grid';
  protected dataLocal = gridGoalGroupDefineDataSource;
  protected columns = goalTemplateGroupColumns;
  protected dataFormSearch = {};

  constructor(
    private _modalService: NzModalService,
    private _drawerService: NzDrawerService,
    private _commonService: CommonService,
    private _goalTemplateFacade: GoalTemplateFacade,
    private _vc: ViewContainerRef,
    private _translate: TranslateService,
  ) {}
  //#region Life cycle
  ngOnInit() {
    this.builderButtonComponent();
    this.builderGridComponent();
    this.builderToolbarComponent();
  }
  ngOnChanges(changes: SimpleChanges): void {}

  //#endregion
  //#region Builder
  private builderButtonComponent() {
    this.builderbtnDelete = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'common.delete',
      options: {
        style: 'outline-danger',
        icon: {
          fontIcon: 'fas fa-trash-alt',
        },
      },
    });
    this.builderbtnEdit = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'common.edit',
      options: {
        style: 'default',
        icon: {
          fontIcon: 'edit',
        },
      },
    });
    this.builderbtnDoYes = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'common.modal.buttonOk',
      options: {
        style: 'danger',
      },
    });
    this.builderbtnDoNo = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'common.modal.buttonNo',
      options: {
        style: 'default',
      },
    });

    this.builderbtnOpenColumnCheck = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'objEval.GoalTemplate.selectGoalGroup',
      options: {
        classNames: ['mr-1'],
        style: 'default',
        icon: {
          fontIcon: 'check-square',
        },
      },
    });
    this.builderbtnCancelOpenColumnCheck = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'objEval.GoalTemplate.cancelSelect',
      options: {
        classNames: ['mr-1'],
        style: 'default',
        icon: {
          fontIcon: 'stop',
        },
      },
    });
    this.builderbtnExpandAll = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'common.grid.buttonAction.ExpandAll',
      options: {
        classNames: ['mr-1'],
        style: 'default',
        icon: {
          fontIcon: 'arrows-alt',
        },
      },
    });
    this.builderbtnCollapseAll = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'common.grid.buttonAction.CollapseAll',
      options: {
        classNames: ['mr-1'],
        style: 'default',
        icon: {
          fontIcon: 'shrink',
        },
      },
    });
    this.builderbtnAddGoalItem = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'objEval.GoalTemplate.addGoalDetail',
      options: {
        classNames: ['mr-1'],
        style: 'outline-primary',
        icon: {
          fontIcon: 'plus',
        },
      },
    });
  }
  private builderToolbarComponent() {
    this.builderToolbar = new VnrToolbarNewBuilder({
      isShowConfig: false,
      gridName: this.gridName,
      isSupperAdmin: false,
      permission: '',
      screenName: 'goal-template-grid',
      storeName: '',
      options: {
        configButtonChangeColumn: {
          isShow: false,
        },
        configButtonExport: {
          isShowBtnExcelAll: false,
          isShowBtnWord: false,
          isShowBtnExcelByTemplate: false,
        },
        configButtonDelete: {
          isShow: false,
        },
        configQuickSearch: {
          isShow: false,
        },
        configFilterAdvanceQuick: {
          isShow: false,
        },
        isSetBackground: false,
        buttonMoreFlexDirection: 'inherit',
        isExpanded: false,
      },
    });
  }
  private builderGridComponent() {
    this.builderGrid = new VnrListviewNewBuilder({
      options: {
        configHeightGrid: {
          isAllowCalcRowHeight: true,
        },
        configSelectable: {
          columnKey: this._columnKey,
        },
        configShowHide: {
          isPageExpand: false,
          isShowViewDetail: true,
          isShowColumnCheck: false,
        },
      },
    });
  }
  //#endregion
  //#region Event for grid
  protected onGridSelectedID($event: any) {
    this.onSelectGoalGroup.emit($event);
  }
  protected onGridOpenDetail($event: any) {
    this.selectedItem = $event;
    this._selectedIDs = [$event[this._columnKey]];
    const modalRef = this._modalService.create({
      nzTitle: this._translate.instant('objEval.GoalTemplate.GoalGroup'),
      nzContent: this._tplContentViewDetail,
      nzFooter: this._tplFooterViewDetail,
      nzMask: true,
      nzWidth: '600px',
      nzMaskClosable: true,
      nzClosable: true,
    });
    modalRef.afterClose.subscribe((modal) => {
      if (modal?.type === 'Confirm') {
        this.reloadGridData();
      }
    });
  }
  protected onGridEdit($event: any) {
    this.selectedItem = $event;
    const modalRef = this._modalService.create({
      nzTitle: '',
      nzContent: GoalTemplateFormGroupComponent,
      nzViewContainerRef: this._vc,
      nzClosable: true,
      nzMaskClosable: true,
      nzMask: true,
      nzWidth: '600px',
      nzBodyStyle: { minheight: `512px`, paddingTop: '10px' },
      nzFooter: null,
      nzData: {
        paramEdit: $event,
      },
    });
    modalRef.afterClose.subscribe((modal) => {
      if (modal?.type === 'Confirm') {
        this.reloadGridData();
      }
    });
  }
  protected onGridDelete($event: any) {
    this._selectedIDs = [$event[this._columnKey]];
    this.onOpenConfirmDeleteGoalGroup();
  }
  protected onExpandAll($event) {
    this.gridControl.onExpandAll();
    this.isShowExpandAll = !this.isShowExpandAll;
  }
  protected onCollapseAll($event) {
    this.gridControl.onCollapseAll();
    this.isShowExpandAll = !this.isShowExpandAll;
  }
  protected onOpenColumnCheck($event) {
    this.gridControl.onOpenColumnCheck();
    this.isShowOpenColumnCheck = !this.isShowOpenColumnCheck;
  }
  protected onCancelOpenColumnCheck($event) {
    this.gridControl.onCancelOpenColumnCheck();
    this.gridControl.onCancelSelectAll();
    this.isShowOpenColumnCheck = !this.isShowOpenColumnCheck;
    this.onSelectGoalGroup.emit([]);
  }
  protected onLoadData($event) {
    this.isShowExpandAll = true;
  }
  //#endregion
  //#region Event Goal
  public onAddGoalDetailFromGroup($event: any, dataItem: any) {
    this._goalGroupID = dataItem?.Id;
    this.onAddGoalDetail($event);
  }
  public onAddNewGoal($event: any) {
    this._goalGroupID = null;
    this.onAddGoalDetail($event);
  }
  public onEditGoal($event: any, _paramEditID: string) {
    this._goalGroupID = null;
    this.onAddGoalDetail($event, _paramEditID);
  }
  public onDeleteGoal($event: any, selectedIDs: any) {
    this._selectedIDs = selectedIDs;
    this.onOpenConfirmDeleteGoal();
  }
  protected onAddGoalDetail($event: any, _paramEditID?: string) {
    $event?.stopPropagation();
    let title =
      _paramEditID != null
        ? this._translate.instant('Chỉnh sửa mẫu mục tiêu')
        : this._translate.instant('Thêm mới mẫu mục tiêu');
    let drawerRef = this._drawerService.create({
      nzTitle: title,
      nzContent: GoalTemplateFormDetailComponent,
      nzContentParams: {
        paramEditID: _paramEditID,
        goalGroupID: this._goalGroupID,
      },
      nzMaskClosable: false,
      nzWidth: '600px',
      nzWrapClassName: 'crud-modal',
      nzClosable: true,
      nzMask: true,
    });
    drawerRef.afterClose.subscribe((res) => {
      if (res && res['isReloadData'] == true) {
        this.gridControl.vnrReloadGrid();
      }
    });
  }
  protected onConfirmEditGoalGroup($modalRef: any) {
    $modalRef.close({
      type: 'Edit',
    });
    this.onGridEdit(this.selectedItem);
  }
  protected onOpenConfirmDeleteGoal() {
    const modalRef = this._modalService.create({
      nzIconType: '',
      nzTitle: '',
      nzContent: this._tplContentDelete,
      nzFooter: this._tplFooterDeleteGoal,
      nzMaskClosable: true,
      nzClosable: true,
    });
    modalRef.afterClose.subscribe((modal) => {
      if (modal?.type === 'Reload') {
        this.reloadGridData();
      }
    });
  }
  protected onConfirmDeleteGoal($event: any, $modalRef: any) {
    $event?.stopPropagation();
    if (!this._selectedIDs || this._selectedIDs.length <= 0) {
      this._commonService.message({ message: 'common.grid.noSelectedId_Delete', type: 'error' });
      return;
    }
    this._goalTemplateFacade.deleteGoal(this._selectedIDs).subscribe((res) => {
      if (res && res.Status === ResponseStatus.SUCCESS) {
        this._commonService.message({
          message: this._translate.instant('common.message.deletedRowOfData', {
            n: this._selectedIDs.length,
          }),
          type: 'success',
        });
        this.gridControl.onCancelSelectAll();
        $modalRef.close({
          type: 'Reload',
        });
      } else {
        this._commonService.message({
          message: res.Message || this._translate.instant('common.notification.descErr500'),
          type: 'error',
        });
      }
    });
  }

  //#endregion
  //#region Event Goal Group
  protected onOpenConfirmDeleteGoalGroup() {
    const modalRef = this._modalService.create({
      nzIconType: '',
      nzTitle: '',
      nzContent: this._tplContentDelete,
      nzFooter: this._tplFooterDelete,
      nzMaskClosable: true,
      nzClosable: true,
    });
    modalRef.afterClose.subscribe((modal) => {
      if (modal?.type === 'Reload') {
        this.reloadGridData();
      }
    });
  }
  public onDeleteGoalGroup($event: any, selectedIDs: any) {
    this._selectedIDs = selectedIDs;
    this.onOpenConfirmDeleteGoalGroup();
  }
  protected onConfirmDeleteGoalGroupFromDetail($modalRef: any) {
    $modalRef.close({
      type: 'ConfirmDelete',
    });
    this.onOpenConfirmDeleteGoalGroup();
  }
  protected onConfirmDeleteGoalGroup($event: any, $modalRef: any) {
    $event?.stopPropagation();
    if (!this._selectedIDs || this._selectedIDs.length <= 0) {
      this._commonService.message({ message: 'common.grid.noSelectedId_Delete', type: 'error' });
      return;
    }
    this._goalTemplateFacade.deleteGoalGroup(this._selectedIDs).subscribe((res) => {
      if (res && res.Status === ResponseStatus.SUCCESS) {
        this.gridControl.onCancelSelectAll();
        this._commonService.message({
          message: this._translate.instant('common.message.deletedRowOfData', {
            n: this._selectedIDs.length,
          }),
          type: 'success',
        });
        $modalRef.close({
          type: 'Reload',
        });
        this.gridControl.onCancelSelectAll();
      } else {
        this._commonService.message({
          message: res.Message || this._translate.instant('common.notification.descErr500'),
          type: 'error',
        });
      }
    });
  }
  //#endregion
  protected onSelectItemGoal($event: any) {
    this.onSelectGoal.emit($event);
  }
  public reloadGridData(): void {
    this.gridControl.vnrReadGrid();
  }
}
