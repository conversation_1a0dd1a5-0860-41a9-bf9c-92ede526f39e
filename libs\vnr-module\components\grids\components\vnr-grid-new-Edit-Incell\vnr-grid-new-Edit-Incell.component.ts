import {
  CommonModule,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Ng<PERSON><PERSON><PERSON><PERSON>,
  NgT<PERSON>plateOutlet,
} from '@angular/common';
import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Inject,
  Input,
  OnInit,
  Output,
  TemplateRef,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';
import {
  FormsModule,
  ReactiveFormsModule,
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { KENDO_BUTTON } from '@progress/kendo-angular-buttons';
import { KENDO_COMMON } from '@progress/kendo-angular-common';
import { GridComponent, KENDO_GRID } from '@progress/kendo-angular-grid';
import { KENDO_ICON } from '@progress/kendo-angular-icons';
import { KENDO_CHECKBOX, KENDO_INPUTS } from '@progress/kendo-angular-inputs';
import { Intl<PERSON>ervice, <PERSON><PERSON><PERSON><PERSON>_DATE, KENDO_INTL } from '@progress/kendo-angular-intl';
import { KENDO_PAGER } from '@progress/kendo-angular-pager';
import { cloneDeep } from 'lodash';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { of } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { VNRMODULE_TOKEN } from '../../../../base/api-config';
import { IVnrModule_Token } from '../../../../common/models/app-api-config.interface';
import {
  VnrEditorBuilder,
  VnrInputFactory,
  VnrInputNumberBuilder,
  VnrInputsModule,
  VnrTextAreaBuilder,
  VnrTextBoxBuilder,
} from '../../../../components/inputs';
import {
  VNR_DATE_MODE,
  VnrDatePickerBuilder,
  VnrPickerFactory,
  VnrPickersModule,
} from '../../../../components/pickers';
import { VnrSelectsModule } from '../../../../components/selects';
import { IVnrGridColumns } from '../../interfaces/grid-column.interface';
import { VnrColumnHiddenPipe } from '../../pipes/vnr-column-hidden.pipe';
import { VnrColumnWidthPipe } from '../../pipes/vnr-column-width.pipe';
import { VnrConvertFileNameToListPipe } from '../../pipes/vnr-convert-fileName-to-list.pipe';
import { VnrGridColumnsValidatorsPipe } from '../../pipes/vnr-grid-check-validators.pipe';
import { VnrGridInitBuilderControlsEditPipe } from '../../pipes/vnr-grid-edit-init-builder-controls.pipe';
import { VnrGridNewEditTypeControlsPipe } from '../../pipes/vnr-grid-new-edit-type-controls.pipe';
import { GridNewChangeColumnConfigGridService } from '../../services/grid-new-change-column-config-grid.service';
import { GridNewChangeColumnService } from '../../services/grid-new-change-column.service';
import { GridNewConfigService } from '../../services/grid-new-config.service';
import { VnrGridNewEditIncellService } from '../../services/grid-new-Edit-Incell.service';
import { VnrGridEditControls } from '../../types/vnr-grid-edit-controls.enum';
import { VnrGridNewChangeColumnComponent } from '../vnr-grid-new/change-new-column/change-new-column.component';
import { VnrGridNewChangeColumnOldComponent } from '../vnr-grid-new/change-old-column/change-old-column.component';
import { VnrGridNewComponent } from '../vnr-grid-new/vnr-grid-new.component';
import { VnrGridNewEditIncellBuilder } from './models/vnr-grid-new-edit-Incell-builder.model';

@Component({
  selector: 'vnr-grid-new-Edit-Incell',
  templateUrl: './vnr-grid-new-Edit-Incell.component.html',
  styleUrls: ['./vnr-grid-new-Edit-Incell.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    NgClass,
    NgSwitch,
    NgSwitchCase,
    NgIf,
    NgFor,
    NgTemplateOutlet,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    KENDO_GRID,
    KENDO_BUTTON,
    KENDO_ICON,
    KENDO_COMMON,
    KENDO_DATE,
    KENDO_INTL,
    KENDO_INPUTS,
    KENDO_CHECKBOX,
    KENDO_PAGER,
    NzButtonModule,
    NzIconModule,
    VnrConvertFileNameToListPipe,
    VnrColumnHiddenPipe,
    VnrColumnWidthPipe,
    VnrInputsModule,
    VnrPickersModule,
    VnrSelectsModule,
    VnrGridColumnsValidatorsPipe,
    VnrGridNewEditTypeControlsPipe,
    VnrGridInitBuilderControlsEditPipe,
    VnrGridNewChangeColumnComponent,
    VnrGridNewChangeColumnOldComponent,
  ],
})
export class VnrGridNewEditIncellComponent extends VnrGridNewComponent implements OnInit {
  @Input() override builder: VnrGridNewEditIncellBuilder = new VnrGridNewEditIncellBuilder();
  @Input() columnEditTemplates: { [key: string]: TemplateRef<any> };
  @Input() customControlTemplate: TemplateRef<any>;

  @Output() vnrSaveEvent = new EventEmitter<any>();
  @Output() vnrOutSideClick = new EventEmitter<any>();
  @Output() vnrCustomCellClick = new EventEmitter<any>();
  @Output() vnrChangeEditData = new EventEmitter<any>(); //emit data mới chỉnh sửa
  @Output() vnrOpenComboBox = new EventEmitter<any>(); //emit data mới chỉnh sửa của comboBox
  @Output() vnrChangeComboBoxDataItem = new EventEmitter<any>(); //emit data mới chỉnh sửa của comboBox
  @Output() vnrChangeComboBoxValue = new EventEmitter<any>(); //emit data mới chỉnh sửa của comboBox

  @ViewChild('gridEditInCell', { static: true })
  gridEditInCell: GridComponent;

  inputFactory: VnrInputFactory = VnrInputFactory.init();
  pickerFactory: VnrPickerFactory = VnrPickerFactory.init();
  builderInputNumber: VnrInputNumberBuilder;
  builderDatePicker: VnrDatePickerBuilder;
  builderTextBox: VnrTextBoxBuilder;
  builderEditor: VnrEditorBuilder = this.inputFactory.builderEditor({
    label: '',
    options: {
      hideToolbar: true,
    },
  });
  builderTextArea: VnrTextAreaBuilder = this.inputFactory.builderTextArea({
    label: '',
    rows: 1,
  });
  protected isEdited: boolean = true;
  protected isHover: boolean = true;
  vnrGridEditControls: any = VnrGridEditControls;

  constructor(
    @Inject(VNRMODULE_TOKEN) protected override _vnrModule_Token: IVnrModule_Token,
    protected override _gridService: GridNewConfigService,
    protected override modal: NzModalService,
    protected intl: IntlService,
    protected notificationService: NzNotificationService,
    protected override translate: TranslateService,
    protected override _cdr: ChangeDetectorRef,
    protected override _vc: ViewContainerRef,
    protected formBuilder: UntypedFormBuilder,
    protected _gridEditInlineService: VnrGridNewEditIncellService,
    protected override _gridNewChangeColumnConfigGridService?: GridNewChangeColumnConfigGridService,
    protected override _gridNewChangeColumnService?: GridNewChangeColumnService,
  ) {
    super(
      _vnrModule_Token,
      _gridService,
      modal,
      _cdr,
      translate,
      _vc,
      _gridNewChangeColumnConfigGridService,
      _gridNewChangeColumnService,
    );
  }

  //#region Lifecycle
  override async ngOnInit() {
    await super.ngOnInit();
  }
  protected override ensureApiAvailable(): void {
    if (!this.builder.options?.configEdit?.apiSaveChange) {
      this.builder.options.configEdit.apiSaveChange =
        this._vnrModule_Token.gridConfig_Token.apiSaveEditIncell?.url;
    }
    super.ensureApiAvailable();
  }
  //#endregion
  protected override async initColumns() {
    await super.initColumns();
    if (this.columnEditTemplates) {
      this.addEditorCustom(this.gridColumns);
    }
  }

  private addEditorCustom(listColumn: Array<IVnrGridColumns>) {
    listColumn.forEach((column: IVnrGridColumns) => {
      this.columnEditTemplates[column.Name] &&
        (column['editor'] = this.columnEditTemplates[column.Name]);
      if (column.MultiColumn && column.MultiColumn.length > 0) {
        this.addEditorCustom(column.MultiColumn);
      }
    });
  }
  //#region Events
  override showOrderNumber(dataItem: any, rowIndex: number) {
    dataItem.OrderNumber = rowIndex + 1;
    return rowIndex + 1;
  }

  hiddenColumn(col: any): boolean {
    return col?.IsShow === false || col?.Hidden === true;
  }

  getHeaderTrans(col: any) {
    if (col.LevelEva) {
      return this.translate.instant(col.HeaderKey) + ' ' + col.LevelEva;
    }
    return this.translate.instant(col.HeaderKey);
  }
  public cellClickHandler({
    sender,
    rowIndex,
    columnIndex,
    dataItem,
    isEdited,
    originalEvent,
    column,
  }: any): any {
    if (originalEvent.which === 3) return;
    if (!column.editor) return;

    if (
      this.builder.options.configEdit?.fieldDisabled &&
      dataItem[this.builder.options.configEdit?.fieldDisabled]
    ) {
      return;
    }
    this.isEdited = true;
    //Custom click to cell
    const _column = this.findDeepColumn(this.columns, column.field) || null;
    if (_column && _column.Format && _column.Format.toLowerCase().startsWith('html')) {
      this.vnrCustomCellClick.emit({ dataItem, column });
      return;
    }
    const formGroup = this.createFormGroup(dataItem);
    sender.editRow(rowIndex, formGroup);
    //Normal click
    if (!isEdited && !this.isReadOnly(column.field)) {
      let _columnIndex = columnIndex;
      if (this.builder.options.isVirtualColumn) {
        _columnIndex = column?._leafIndex;
      }
      sender.editCell(rowIndex, _columnIndex, this.createFormGroup(dataItem));
    }
  }

  private findDeepColumn(listColumn: any[], fieldName: string) {
    let result;
    //Find without group
    const columnsWithoutGroup = listColumn?.find((x) => x.Name === fieldName) || null;
    if (columnsWithoutGroup) {
      result = columnsWithoutGroup;
    } else {
      //Find all column in group
      const groupColumns =
        listColumn
          ?.filter((x) => x.Type === 'group' && x.MultiColumn && x.MultiColumn.length > 0)
          ?.flatMap((x) => x.MultiColumn) || [];

      if (groupColumns && groupColumns.length > 0) {
        result = groupColumns.find((x) => x.Name === fieldName);
      }
    }
    return result;
  }

  private isReadOnly(field: string): boolean {
    return this.builder.options?.configEdit?.readonlyColumns?.indexOf(field) > -1;
  }

  public cancelHandler({ sender, rowIndex }: any) {
    sender.closeRow(rowIndex);
  }

  public cellCloseHandler(args: any) {
    const { vnrGridEditIncellForm, formGroup, dataItem, column } = args;
    this.vnrOutSideClick.emit({ dataItem: dataItem, column: column });
    if (!formGroup.valid) {
      //Allow click edit other cell when open one cell
      if (this.builder.options?.configEdit?.isAllowEditOtherCell) {
        return;
      } else {
        //Validate and no handle more
        formGroup.ngSubmit.emit();
        args.preventDefault();
      }
      //validate
    } else if (
      !this.builder.options?.configEdit?.isInvalidData &&
      (formGroup.dirty || formGroup.value[column.field] !== dataItem[column.field])
    ) {
      //Assign and update data item
      //this._gridEditInlineService.assignValues(dataItem, formGroup.value);
      this._gridEditInlineService.update(dataItem);
      if (this.builder.options?.configEdit?.isAutoSave) {
        this.saveChanges(this.gridEditInCell);
      }
    }
  }
  public async saveHandler(evt: any) {
    const { sender, rowIndex, formGroup, isNew, dataItem } = evt;
    this._gridEditInlineService.update(dataItem);
    this.saveChanges(this.gridEditInCell);
  }
  protected createFormGroup(dataItem: any, isNew: boolean = false): UntypedFormGroup {
    Object.assign(dataItem, {
      IsEditing: isNew ? true : false,
      IsNew: isNew,
      IsDisabled: false,
    });
    /* Create an empty object to store the form controls */
    let form: any = {};
    let _valueFields: any[] = [];
    let _valueFieldsComboBox: any[] = [];
    let column: any[] = cloneDeep(this.columns);
    let arrFieldRequired = [];
    _valueFields = column?.flatMap((col) => {
      if (col?.Type === 'group' && col?.MultiColumn && col.MultiColumn.length > 0) {
        return col.MultiColumn?.map((multiCol) => {
          //Add field required
          if ((multiCol.Validators && multiCol.Validators.Required) || multiCol.Required)
            arrFieldRequired.push(col.Name);

          if (multiCol?.TypeControl === VnrGridEditControls.ComboBox) {
            const textFieldInit = multiCol?.Name?.endsWith('ID')
              ? multiCol?.Name?.slice(0, -2) + 'Name'
              : multiCol?.Name;
            _valueFieldsComboBox?.push(textFieldInit);
          }
          return multiCol.Name;
        });
      } else {
        //Add field required
        if ((col.Validators && col.Validators.Required) || col.Required)
          arrFieldRequired.push(col.Name);

        if (col?.TypeControl === VnrGridEditControls.ComboBox) {
          const textFieldInit = col?.Name?.endsWith('ID')
            ? col?.Name?.slice(0, -2) + 'Name'
            : col?.Name;
          _valueFieldsComboBox.push(textFieldInit);
        }
        return col.Name;
      }
    });

    _valueFields = _valueFields?.concat(['IsEditing', 'IsDisabled', 'IsNew']);
    _valueFields = _valueFields?.concat(_valueFieldsComboBox);

    /* Loop through the column names and create a FormControl for each one */
    _valueFields?.forEach((el) => {
      const textField = this.builder.options?.configEdit?.builderConfigByColumn?.[el]?.textField;
      const valueField = this.builder.options?.configEdit?.builderConfigByColumn?.[el]?.valueField;
      const isRequired = arrFieldRequired.includes(el) ? [Validators.required] : [];

      if (
        this.builder.options?.configEdit?.builderConfigByColumn &&
        this.builder.options?.configEdit?.builderConfigByColumn[el] &&
        this.builder.options?.configEdit?.builderConfigByColumn[el].serverSide &&
        textField &&
        valueField
      ) {
        form[textField] = [dataItem[textField]];
        form[valueField] = [dataItem[valueField], isRequired];

        let addedDataSource = {};
        addedDataSource[textField] = dataItem[textField];
        addedDataSource[valueField] = dataItem[valueField];

        if (dataItem[valueField]) {
          const currentDataSrc =
            this.builder.options?.configEdit?.builderConfigByColumn[el].dataSource || [];
          this.builder.options.configEdit.builderConfigByColumn[el].dataSource = [
            ...currentDataSrc,
            addedDataSource,
          ];
        }
        // Nếu col khác vs value & text => add form
        if (el !== valueField && el !== textField) {
          form[el] = [dataItem[el], isRequired];
          addedDataSource[textField] = dataItem[`${el}View`];
          addedDataSource[valueField] = dataItem[el];
          if (dataItem[el]) {
            const currentDataColSrc =
              this.builder.options?.configEdit?.builderConfigByColumn[el].dataSource || [];
            this.builder.options.configEdit.builderConfigByColumn[el].dataSource = [
              ...currentDataColSrc,
              addedDataSource,
            ];
          }
        }
      } else {
        form[el] = [dataItem[el], isRequired];
      }
    });
    /* Return the FormGroup created from the form controls */
    return this.formBuilder.group(form);
  }
  onRightClick() {
    return false;
  }

  initBuilderInputNumber(col: any) {
    this.builderInputNumber = this.inputFactory.builderInputNumber({
      label: '',
      options: {
        hasFeedBack: false,
        min:
          this.builder.options?.configEdit?.configValidate &&
          this.builder.options?.configEdit?.configValidate[col.Name]
            ? this.builder.options?.configEdit?.configValidate[col.Name].min
            : 0,
        max:
          this.builder.options?.configEdit?.configValidate &&
          this.builder.options?.configEdit?.configValidate[col.Name]
            ? this.builder.options?.configEdit?.configValidate[col.Name].max
            : 999999999,
        step: 1,
      },
    });

    if (col.Format && col.Format.toLowerCase().startsWith('number|')) {
      if (col.Format.split('|')[1] === '0') {
        this.builderInputNumber.options.format = (value: number) => `${Math.round(value)}`;
      }
    }

    return this.builderInputNumber;
  }

  getValidators(col: any): any {
    let validators: any = {};
    if (col && col.Required) {
      validators.required = true;
    }

    if (
      this.builder.options?.configEdit?.configValidate &&
      this.builder.options?.configEdit?.configValidate[col.Name]
    ) {
      if (this.builder.options?.configEdit?.configValidate[col.Name].maxLength) {
        validators.maxLength = this.builder.options?.configEdit?.configValidate[col.Name].maxLength;
      }

      if (this.builder.options?.configEdit?.configValidate[col.Name].minLength) {
        validators.minLength = this.builder.options?.configEdit?.configValidate[col.Name].minLength;
      }
    }

    return Object.keys(validators)?.length > 0 ? validators : null;
  }

  initBuilderDatePicker(col: any) {
    this.builderDatePicker = this.pickerFactory.builderDatePicker({
      label: '',
      options: {
        format: 'dd/MM/yyyy',
        mode: VNR_DATE_MODE.E_DATE,
        hasFeedBack: false,
      },
    });

    if (col.Format && col.Format.toLowerCase().startsWith('DateTime|')) {
      if (col.Format.split('|')[1] === 'MM/yyyy') {
        this.builderDatePicker.options.mode = VNR_DATE_MODE.E_MONTH;
      } else if (col.Format.split('|')[1] === 'yyyy') {
        this.builderDatePicker.options.mode = VNR_DATE_MODE.E_YEAR;
      }
      this.builderDatePicker.options.format = col.Format.split('|')[1];
    }

    return this.builderDatePicker;
  }

  checkRequiredEdit(column: any): boolean {
    if (this.builder.options.configEdit.columnsRequiredEdit.includes(column)) {
      return true;
    }
    return false;
  }

  public async saveChanges(grid: any) {
    this.isLoading = true;

    grid.closeCell();
    grid.cancelCell();

    let dataItem = this._gridEditInlineService.ListEditValue();
    if (this.builder.options?.configEdit?.apiSaveChange) {
      let result = await this._gridService
        .postEditGrid(this.builder.options?.configEdit?.apiSaveChange, dataItem)
        .pipe(
          catchError((err) => {
            this.notificationService.error(
              this.translate.instant('common.title.error'),
              this.builder.options?.configEdit?.isShowMessenger
                ? err
                : this.translate.instant('common.message.actionSuccess'),
              { nzPlacement: 'bottomRight' },
            );
            this.isLoading = false;
            return of(null);
          }),
        )
        .toPromise();
      if (result) {
        if (this.builder.options?.configEdit?.isShowMessenger)
          this.notificationService.success(
            this.translate.instant('common.title.success'),
            this.translate.instant('common.message.actionSuccess'),
          );

        this.resetEditPage();
      }
      this.vnrSaveEvent.emit(result);
    }
    this._gridEditInlineService.reset();
    this.vnrSaveEvent.emit(dataItem);
    this.isLoading = false;
    // this.editService.saveChanges()
  }
  public cancelChanges(grid: any, dataItem: any): void {
    const itemBeforeChange = this._gridEditInlineService.getItemBeforeChange(dataItem);
    if (!itemBeforeChange === false) {
      dataItem = itemBeforeChange;
    }
    this._gridEditInlineService.removeChange(dataItem);
    grid.cancelCell();
    // this.editService.reset();
    // this.resetEditPage();
    // this.editService.cancelChanges()
  }

  public addHandler({ sender }: any) {
    sender.addRow({});
  }

  onCustomCellClick(dataItem: any) {
    this.vnrCustomCellClick.emit(dataItem);
  }
  //#endregion

  /**
   *  Emit Event Click Format File
   * @param value => string
   */
  override onClickFileName(value: string) {
    this.vnrFormatFileClick.emit(value);
  }
  //#endregion

  /**
   *  Emit Event Click upload File
   * @param value => string
   */
  override onClickUploadFileToColumn(columnName: string, dataItem: any, column: any) {
    this.vnrUploadFileToColumnClick.emit({
      columnName: columnName,
      dataItem: dataItem,
      column: column,
    });
  }
  //#endregion
  protected onModelChange($event, rowIndex, columnName: string, dataItem: any) {
    this.vnrChangeEditData.emit({
      data: dataItem,
      rowIndex: rowIndex,
      columnName: columnName,
    });
  }
  onModelChangeComboBox($event, rowIndex, columnName: string, dataItem: any): any {
    // if (rowOnPage) {
    //   if ($event && dataItem && dataItem[columnName] !== $event) {
    //     rowOnPage.patchValue({ IsEditing: true })
    //   } else {
    //     rowOnPage.patchValue({ IsEditing: false })
    //   }
    // }
    this.vnrChangeComboBoxValue.emit($event);
  }
  onModelChangeMultiSelect($event, rowIndex, columnName: string, dataItem: any): any {
    // if (rowOnPage) {
    //   if ($event && dataItem && dataItem[columnName] !== $event) {
    //     rowOnPage.patchValue({ IsEditing: true })
    //   } else {
    //     rowOnPage.patchValue({ IsEditing: false })
    //   }
    // }
    this.vnrChangeComboBoxValue.emit($event);
  }

  onSelectDataItem($event: any, column: any, rowIndex, dataItem: any) {
    if (
      this.builder.options?.configEdit?.builderConfigByColumn &&
      this.builder.options?.configEdit?.builderConfigByColumn[column.field]
    ) {
      const textFieldInit = column.field.endsWith('ID')
        ? column.field.slice(0, -2) + 'Name'
        : column.field;
      dataItem[textFieldInit] =
        ($event &&
          $event[
            this.builder.options?.configEdit?.builderConfigByColumn[column?.field].textField
          ]) ||
        '';
    }
    this.vnrChangeComboBoxDataItem.emit($event);
  }

  onOpenComboBox($event, rowIndex) {
    this.vnrOpenComboBox.emit({ isOpen: $event, rowIndex: rowIndex });
  }
  public setDataInvalid() {
    if (this.builder.options?.configEdit) {
      this.builder.options.configEdit.isInvalidData = true;
    }
  }
  public setDataValid() {
    if (this.builder.options?.configEdit) {
      this.builder.options.configEdit.isInvalidData = false;
    }
  }
}
