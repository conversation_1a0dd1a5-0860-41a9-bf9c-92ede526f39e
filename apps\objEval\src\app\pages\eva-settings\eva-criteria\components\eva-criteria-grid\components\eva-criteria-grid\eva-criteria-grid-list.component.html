<vnr-grid-new
  class="grid-new"
  #vnrGrid
  [builder]="builderGrid"
  [gridName]="gridName"
  [dataLocal]="dataLocal"
  [columns]="columns"
  [dataFormSearch]="dataFormSearch"
  [columnTemplates]="{
    Scale: templateScale,
  }"
  (getSelectedID)="getSelectedID($event)"
  (vnrEdit)="onGridEdit($event)"
  (vnrDelete)="onGridDelete($event)"
  (vnrCellClick)="onGridCellClick($event)"
>
</vnr-grid-new>
<ng-template #templateScale let-dataItem>
  <ng-container [ngSwitch]="true">
    <ng-container *ngSwitchCase="!!dataItem?.ToScore">
      <div class="template-scale-score">{{ dataItem.FromScore }} - {{ dataItem.ToScore }}</div>
    </ng-container>

    <ng-container *ngSwitchCase="!!dataItem?.Rates?.length">
      <div class="template-scale-rate">
        <div *ngFor="let item of dataItem.Rates">
          <b>{{ item.Name }}: </b>
          <span>{{ item.FromScore ?? 0 }}</span> -
          <span>{{ item.ToScore ?? 0 }}</span>
        </div>
      </div>
    </ng-container>

    <ng-container *ngSwitchDefault> - </ng-container>
  </ng-container>
</ng-template>

<ng-template #tplContentDelete let-params>
  <div class="template-delete">
    <div class="template-delete__header">
      <img src="assets/icon/vnr-icon/icon-confirm-delete.png" alt="delete" />
    </div>
    <div class="template-delete__content">
      <div class="template-delete__content-title">
        <span>{{ 'common.modal.confirmDeleteData' | translate }}</span>
      </div>
      <div class="template-delete__content-description">
        {{ 'common.modal.deletedDataCannotBeRecovered' | translate }}
      </div>
    </div>
  </div>
</ng-template>
<ng-template #tplFooterDelete let-ref="modalRef">
  <div class="template-view-detail__action">
    <vnr-button-new [builder]="builderbtnDoNo" (vnrClick)="ref.destroy()"></vnr-button-new>
    <vnr-button-new
      [builder]="builderbtnDoYes"
      (vnrClick)="onConfirmDelete($event, ref)"
    ></vnr-button-new>
  </div>
</ng-template>
