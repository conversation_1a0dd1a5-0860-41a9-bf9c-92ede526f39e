<!-- <vnr-toolbar-new [builder]="builderToolbar"></vnr-toolbar-new> -->

<vnr-treelist-new
  class="grid-new"
  #vnrGrid
  [builder]="builderGrid"
  [gridName]="gridName"
  [dataLocal]="dataLocal"
  [columns]="columns"
  [defaultColumnTemplate]="tplCustomTemplateByColumn"
>
</vnr-treelist-new>

<ng-template #tplCustomTemplateByColumn let-dataItem let-columnItem="columnItem">
  <ng-container [ngSwitch]="columnItem['Name']">
    <ng-container *ngSwitchCase="'Assignee'">
      <div class="d-flex align-items-center">
        <app-vnr-letters-avatar
          [avatarName]="dataItem.Assignee"
          [circular]="true"
          [width]="32"
          class="mr-2"
        ></app-vnr-letters-avatar>
        <span>{{ dataItem.Assignee }}</span>
      </div>
    </ng-container>

    <!-- Target -->
    <ng-container *ngSwitchCase="'Target'">
      <span>{{ dataItem.TargetValue | targetFormat : dataItem.Unit }}</span>
    </ng-container>

    <!-- Hiển thị cho tất cả các tháng jan-dec -->
    <ng-container *ngSwitchCase="'jan'">
      <ng-container
        *ngTemplateOutlet="tplMonth; context: { month: dataItem['jan'] }"
      ></ng-container>
    </ng-container>
    <ng-container *ngSwitchCase="'feb'">
      <ng-container
        *ngTemplateOutlet="tplMonth; context: { month: dataItem['feb'] }"
      ></ng-container>
    </ng-container>
    <ng-container *ngSwitchCase="'mar'">
      <ng-container
        *ngTemplateOutlet="tplMonth; context: { month: dataItem['mar'] }"
      ></ng-container>
    </ng-container>
    <ng-container *ngSwitchCase="'apr'">
      <ng-container
        *ngTemplateOutlet="tplMonth; context: { month: dataItem['apr'] }"
      ></ng-container>
    </ng-container>
    <ng-container *ngSwitchCase="'may'">
      <ng-container
        *ngTemplateOutlet="tplMonth; context: { month: dataItem['may'] }"
      ></ng-container>
    </ng-container>
    <ng-container *ngSwitchCase="'jun'">
      <ng-container
        *ngTemplateOutlet="tplMonth; context: { month: dataItem['jun'] }"
      ></ng-container>
    </ng-container>
    <ng-container *ngSwitchCase="'jul'">
      <ng-container
        *ngTemplateOutlet="tplMonth; context: { month: dataItem['jul'] }"
      ></ng-container>
    </ng-container>
    <ng-container *ngSwitchCase="'aug'">
      <ng-container
        *ngTemplateOutlet="tplMonth; context: { month: dataItem['aug'] }"
      ></ng-container>
    </ng-container>
    <ng-container *ngSwitchCase="'sep'">
      <ng-container
        *ngTemplateOutlet="tplMonth; context: { month: dataItem['sep'] }"
      ></ng-container>
    </ng-container>
    <ng-container *ngSwitchCase="'oct'">
      <ng-container
        *ngTemplateOutlet="tplMonth; context: { month: dataItem['oct'] }"
      ></ng-container>
    </ng-container>
    <ng-container *ngSwitchCase="'nov'">
      <ng-container
        *ngTemplateOutlet="tplMonth; context: { month: dataItem['nov'] }"
      ></ng-container>
    </ng-container>
    <ng-container *ngSwitchCase="'dec'">
      <ng-container
        *ngTemplateOutlet="tplMonth; context: { month: dataItem['dec'] }"
      ></ng-container>
    </ng-container>

    <!-- Các cột khác -->
    <ng-container *ngSwitchDefault>
      <span>{{ dataItem[columnItem['Name']] || '' }}</span>
    </ng-container>
  </ng-container>
</ng-template>

<ng-template #tplMonth let-month="month">
  <div class="progress-cell" *ngIf="month && month.actual !== null && month.target !== null">
    <div class="progress-header">
      {{ month.actual | targetFormat : month.unit }}/{{ month.target | targetFormat : month.unit }}
      <span class="progress-percentage" *ngIf="month.progress !== null">{{ month.progress }}%</span>
    </div>
    <div
      *ngIf="month.progress !== null"
      class="progress-bar"
      [ngClass]="month.progress === 100 ? 'progress-complete' : 'progress-incomplete'"
      [ngStyle]="{ width: month.progress + '%' }"
    ></div>
  </div>
  <div class="progress-cell" *ngIf="!month || month.actual === null || month.target === null">
    <span></span>
  </div>
</ng-template>
