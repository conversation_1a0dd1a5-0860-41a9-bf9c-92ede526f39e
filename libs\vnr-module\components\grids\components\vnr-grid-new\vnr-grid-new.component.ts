import {
  CommonModule,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  NgT<PERSON>plateOutlet,
} from '@angular/common';
import {
  AfterViewInit,
  Component,
  EventEmitter,
  HostListener,
  Inject,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
  ViewContainerRef,
  TemplateRef,
  ChangeDetectorRef,
} from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { KENDO_BUTTON } from '@progress/kendo-angular-buttons';
import { KENDO_COMMON } from '@progress/kendo-angular-common';
import {
  DataStateChangeEvent,
  DetailExpandEvent,
  GridComponent,
  GridDataResult,
  GroupKey,
  GroupRowArgs,
  KENDO_GRID,
  PageChangeEvent,
  SelectAllCheckboxState,
  SelectableMode,
  SelectionEvent,
} from '@progress/kendo-angular-grid';
import { KENDO_ICON } from '@progress/kendo-angular-icons';
import { KENDO_CHECKBOX, KENDO_INPUTS } from '@progress/kendo-angular-inputs';
import { KENDO_DATE, KENDO_INTL } from '@progress/kendo-angular-intl';
import { KENDO_PAGER } from '@progress/kendo-angular-pager';
import { VnrGridGroupTitlePipe } from '../../pipes/vnr-grid-group-title.pipe';
import {
  AggregateDescriptor,
  AggregateResult,
  CompositeFilterDescriptor,
  GroupDescriptor,
  GroupResult,
  State,
  aggregateBy,
  process,
  toDataSourceRequest,
  translateDataSourceResultGroups,
} from '@progress/kendo-data-query';
import { cloneDeep, sumBy, union, uniqBy } from 'lodash';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzModalService } from 'ng-zorro-antd/modal';
import { Observable, Subject, fromEvent, of } from 'rxjs';
import { debounceTime, finalize, map, takeUntil } from 'rxjs/operators';
import { IVnRGridNewChangeColumnConfig } from '../../interfaces/grid-new-change-column-config.interface';
import {
  IApiOptionsChangeColumn,
  IApiOptionsCreateOrUpdateGridNewColumnConfig,
  IVnRGridNewConfigGetChangeColumn,
} from '../../interfaces/grid-new-change-column.interface';
import { IExportExcel, IExportWord } from '../../interfaces/export-excel.interface';
import { GridNewChangeColumnConfigGridService } from '../../services/grid-new-change-column-config-grid.service';
import { GridNewChangeColumnService } from '../../services/grid-new-change-column.service';
import { GridNewConfigService } from '../../services/grid-new-config.service';
import { VnrGridMenuType } from '../../types/vnr-grid.type';
import { VnrGridNewBuilder } from './models/vnr-grid-new-builder.model';
import { VnRGridNewChangeColumnBuilder } from './models/vnr-grid-new-change-column-builder.model';
import { VnRGridNewOptionChangeColumnPosition } from '../../models/grid-new-option-change-column.model';
import { VnrGridNewChangeColumnComponent } from './change-new-column/change-new-column.component';
import { VnrGridNewChangeColumnOldComponent } from './change-old-column/change-old-column.component';
import { VnrConvertFileNameToListPipe } from '../../pipes/vnr-convert-fileName-to-list.pipe';
import { VnRGridBase } from '../../../../base/grid-base';
import { IVnrGridColumns } from '../../interfaces/grid-column.interface';
import { VNRMODULE_TOKEN } from '../../../../base/api-config';
import { IVnrModule_Token } from '../../../../common/models/app-api-config.interface';
import { VnRGridNewDataSource } from '../../models/grid-new.model';

@Component({
  selector: 'vnr-grid-new',
  templateUrl: './vnr-grid-new.component.html',
  styleUrls: ['./vnr-grid-new.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    NgClass,
    NgSwitch,
    NgSwitchCase,
    NgIf,
    NgFor,
    NgTemplateOutlet,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    VnrGridGroupTitlePipe,
    KENDO_GRID,
    KENDO_BUTTON,
    KENDO_ICON,
    KENDO_COMMON,
    KENDO_DATE,
    KENDO_INTL,
    KENDO_INPUTS,
    KENDO_CHECKBOX,
    KENDO_PAGER,
    VnrGridNewChangeColumnOldComponent,
    VnrGridNewChangeColumnComponent,
    NzButtonModule,
    NzIconModule,
    VnrConvertFileNameToListPipe,
  ],
})
export class VnrGridNewComponent
  extends VnRGridBase<any>
  implements OnInit, OnChanges, AfterViewInit, OnDestroy
{
  @ViewChild('kendoGrid', { static: true }) protected kendoGrid: GridComponent;

  @Input() override builder: VnrGridNewBuilder = new VnrGridNewBuilder();

  @Input() gridName: string = 'grid-name';
  @Input() dataSource: VnRGridNewDataSource;
  @Input() dataLocal: any[] = [];
  @Input() dataFormSearch?: any;
  @Input() columns: any;
  @Input() pageSize: number = 50;
  @Input() aggregates?: AggregateDescriptor[];
  @Input() isOpenChangeColumn?: boolean = false;
  @Input() isChangeColumnNew?: boolean = false;
  @Input() isSupperAdmin: boolean = false;
  @Input() isAddColumnDev: boolean = false;

  @Input() columnTemplates: { [key: string]: TemplateRef<any> }; //[{ KPIName: this.tplHeader }, {...}]
  @Input() defaultColumnTemplate: TemplateRef<any>; // tplColumn
  @Input() rowDetailTemplate: TemplateRef<any>; // tplRow Children
  @Input() columnHeaderTemplate: TemplateRef<any>; // tplColumnHeader
  @Input() headerGroupTemplate: TemplateRef<any>; //Template for header group (nhóm dữ liệu)
  @Input() rowActionsTemplate: TemplateRef<any>; // tplButtonAction
  @Input() customTemplateGroupColumnFooter: any; // [{ KPIName: this.tplFooter }, {...}]
  @Input() customTemplateColumnFooter: any; // [{ KPIName: this.tplFooter }, {...}]
  @Input() customTemplateGroupColumnHeader: any; // [{ KPIName: this.tplHeader }, {...}]

  @Output() protected getSelectedID = new EventEmitter<string[]>();
  @Output() protected getSelectedDataItem = new EventEmitter<any[]>();
  @Output() protected getDataItem = new EventEmitter<any>();
  @Output() protected vnrEdit = new EventEmitter<any>(); //change event edit
  @Output() protected vnrDelete = new EventEmitter<any>(); //change event delete
  @Output() protected vnrViewDetails = new EventEmitter<any>();
  @Output() protected vnrFormatFileClick = new EventEmitter<string>();
  @Output() protected vnrUploadFileToColumnClick = new EventEmitter<any>(); //sự kiện output khi muốn chọn format là upload
  @Output() protected vnrMenuItemClick = new EventEmitter<any>();
  @Output() protected vnrPageChange = new EventEmitter<any>();
  @Output() protected vnrColumnResizeChange = new EventEmitter<any>();
  @Output() protected vnrDetailExpand = new EventEmitter<any>();
  @Output() protected vnrDoubleClick = new EventEmitter<any>();
  @Output() protected vnrSuccess = new EventEmitter<any>();
  @Output() protected vnrViewModeGrid = new EventEmitter<number>();
  @Output() protected vnrCellClick = new EventEmitter<any>(); //Cell click

  private _skip = 0;
  private _pageSize = 50;
  private _exportColumns: any[] = [];
  private _vnrGridBackData: any = [];
  private _isSelectedRows$: Subject<boolean> = new Subject<boolean>();
  private _defaultState: State = {};
  private _isLoadScroll = false;
  private _isSelectedRow = false;
  private _valueFieldsInit = '';
  private _signatureCallSave$: Subject<any> = new Subject<any>();
  private _gridHeight = 0;
  private _destroy$: Subject<any> = new Subject<any>();
  private _changeColumnConfig: IVnRGridNewChangeColumnConfig = {};
  private _totalRowWithGroup = 0;
  private _isInitialized = false;
  private _cellClickDataItem;

  public vnrAggregateResult: AggregateResult;
  public gridView: GridDataResult;

  protected _calcGridHeight$: Subject<any> = new Subject<any>();
  protected builderGridChangeColumn: VnRGridNewChangeColumnBuilder;
  protected selectedIds: any[] = [];
  protected selectedDataItem: any = null;
  protected hasLockedColumn = false;
  protected hasStickyColumn = false;
  protected isLastChild = false;
  protected isHovered = false;
  protected isLoading: boolean;
  protected buttonCount = 4;
  protected gridColumns: Array<IVnrGridColumns> = [];
  protected selectAllState: SelectAllCheckboxState = 'unchecked';
  protected gridSelectedKeys: any[] = [];
  public get getDataSource() {
    return cloneDeep(this._vnrGridBackData);
  }
  protected get getDataSourceGridEditing() {
    return this.gridView?.data || [];
  }
  protected dateFormat = 'dd/MM/yyyy';
  //#endregion
  constructor(
    @Inject(VNRMODULE_TOKEN) protected _vnrModule_Token: IVnrModule_Token,
    protected _gridService: GridNewConfigService,
    protected modal: NzModalService,
    protected _cdr: ChangeDetectorRef,
    protected translate?: TranslateService,
    protected _vc?: ViewContainerRef,
    protected _gridNewChangeColumnConfigGridService?: GridNewChangeColumnConfigGridService,
    protected _gridNewChangeColumnService?: GridNewChangeColumnService,
  ) {
    super();
    this.ensureBuilder();
    this.initDefaultState();
  }
  private ensureBuilder() {
    if (!this.builder) {
      this.builder = new VnrGridNewBuilder();
    }
  }
  private initDefaultState() {
    this._defaultState = Object.assign({}, this.builder.options.queryOption);
  }
  async ngOnChanges(changes: SimpleChanges) {
    const { builder, pageSize, dataSource, dataLocal, dataFormSearch, columns } = changes;
    if (builder && !builder.firstChange) {
      await this.ngOnInit();
    }
    if (pageSize && pageSize.currentValue) {
      this._pageSize = this.pageSize;
      this.builder.options.queryOption.take = this.pageSize;
      this._defaultState.take = this.pageSize;
    }
    if (dataFormSearch && !dataFormSearch.isFirstChange) {
      this.dataSource.dataFormSearch = dataFormSearch.currentValue;
      this.vnrReadGrid();
    }
    if (columns && !columns.firstChange && !this.columns) {
      this.initColumns();
    }
    if (dataLocal && !dataLocal.isFirstChange) {
      this.loadDataSource();
    }
  }
  //#region Lifecycle
  async ngOnInit() {
    this.subscribeCalcGridHeightEvent();
    this.ensureApiAvailable();
    this.builderGridChangeColumnComponent();
    await this.initColumns();
    this.autoBindData();
    this.adjustPagerMarginWhenShowRefresh();
    this.subscribeSignatureCallSaveEvent();
  }
  ngAfterViewInit(): void {
    this._isInitialized = true;
    setTimeout(() => this.subscribeGridScrollEvent());
  }
  ngOnDestroy(): void {
    this._destroy$.next(true);
    this._destroy$.complete();
  }
  //#endregion
  protected ensureApiAvailable(): void {
    if (!this.dataSource) {
      this.dataSource = {
        api: {
          url: this._vnrModule_Token.gridConfig_Token.apiGridGetDataSource?.url,
          method: this._vnrModule_Token.gridConfig_Token.apiGridGetDataSource?.method,
        },
        isAutobind: true,
        isLazyloadDatasource: false,
        isServerSupport: true,
      };
    }
    if (!this.dataSource?.api?.url) {
      this.dataSource.api.url = this._vnrModule_Token.gridConfig_Token.apiGridGetDataSource?.url;
    }
    if (!this.dataSource?.api?.method) {
      this.dataSource.api.method =
        this._vnrModule_Token.gridConfig_Token.apiGridGetDataSource?.method;
    }
    if (!this.builder.options?.configApiSupport?.apiGetColumn.url) {
      this.builder.options.configApiSupport.apiGetColumn.url =
        this._vnrModule_Token.gridConfig_Token.apiGetColumn?.url;
    }
    if (!this.builder.options?.configApiSupport?.apiDownload.url) {
      this.builder.options.configApiSupport.apiDownload.url =
        this._vnrModule_Token.gridConfig_Token.apiDownloadUrl?.url;
    }
    if (!this.builder.options?.configApiSupport?.apiExport.url) {
      this.builder.options.configApiSupport.apiExport.url =
        this._vnrModule_Token.gridConfig_Token.apiGridExport?.url;
    }
    if (!this.builder.options?.configApiSupport?.apiExportAll.url) {
      this.builder.options.configApiSupport.apiExportAll.url =
        this._vnrModule_Token.gridConfig_Token.apiGridExportAll?.url;
    }
    if (!this.builder.optionChangeColumn?.apiRestoreChangeColumn?.url) {
      this.builder.optionChangeColumn.apiRestoreChangeColumn.url =
        this._vnrModule_Token.gridConfig_Token.apiRestoreChangeColumn?.url;
    }
    if (!this.builder.optionChangeColumn?.apiSaveChangeColumn?.url) {
      this.builder.optionChangeColumn.apiSaveChangeColumn.url =
        this._vnrModule_Token.gridConfig_Token.apiSaveChangeColumn?.url;
    }
    if (!this.builder.optionChangeColumn?.apiSaveTranslate?.url) {
      this.builder.optionChangeColumn.apiSaveTranslate.url =
        this._vnrModule_Token.gridConfig_Token.apiSaveTranslate?.url;
    }
  }

  private autoBindData(): void {
    if (this.dataSource.isAutobind) {
      this.initData();
    } else {
      this.isLoading = false;
    }
  }

  private subscribeSignatureCallSaveEvent(): void {
    if (!this._isInitialized) {
      return;
    }
    this._signatureCallSave$
      .pipe(debounceTime(300), takeUntil(this._destroy$))
      .subscribe((res: any) => res && this.createOrUpdateConfigColumn());
    this._isInitialized = false;
  }

  private subscribeCalcGridHeightEvent(): void {
    this._calcGridHeight$.pipe(takeUntil(this._destroy$), debounceTime(300)).subscribe((res) => {
      if (res && this.builder.options.configHeightGrid?.isAllowCalcRowHeight) {
        setTimeout(() => {
          this.reCalcHeightGrid();
          this._cdr.detectChanges();
        }, 500);
      }
    });
  }

  private subscribeGridScrollEvent(): void {
    const containerElement = this._vc?.element?.nativeElement as HTMLElement;
    const gridContentElement = containerElement?.querySelector('.k-grid-content') as HTMLElement;
    if (gridContentElement.scrollWidth === gridContentElement.clientWidth) {
      this.isLastChild = true;
    } else {
      fromEvent(gridContentElement, 'scroll')
        .pipe(takeUntil(this._destroy$))
        .subscribe(() => {
          this.onWindowScroll(gridContentElement); //Xử lý add class cho cột action
        });
    }
  }

  private adjustPagerMarginWhenShowRefresh(): void {
    if (!this.builder?.options?.configShowHide?.isShowRefresh) {
      return;
    }
    setTimeout(() => {
      const gridElement = this._vc.element.nativeElement as HTMLElement;
      const pagerElement = gridElement?.querySelector(
        'kendo-pager-info.k-pager-info.k-label',
      ) as HTMLElement;
      if (pagerElement) {
        pagerElement.style.marginRight = '60px';
      }
    });
  }
  /***
   * Event double click
   */
  protected onGridDoubleClicked($event) {
    //#region check event db click
    $event.stopPropagation(); //stop event của dòng
    $event.preventDefault(); // dừng event đã gọi trước đó
    const disabledElements = $event?.target?.getElementsByClassName('k-disabled');
    if (
      $event.target.tagName == 'INPUT' ||
      $event.target.closest('.vnr-grid-action-column__group-btn') ||
      (disabledElements && disabledElements.length > 0)
    ) {
      return;
    }
    //#endregion

    //#region Kiểm tra có phải là dòng dữ liệu hay không
    const tr = $event.target.closest('tr') as HTMLElement;
    const row = tr?.classList?.contains('k-master-row');

    if (!row) return; //Không phải thì không emit
    //#endregion Kiểm tra có phải là dòng dữ liệu hay không

    //ưu tiên lấy theo chuẩn kendo
    if (!this.selectedDataItem && this._cellClickDataItem) {
      this.vnrDoubleClick.emit({ event: $event, record: this._cellClickDataItem });
      return;
    }
    if (!this.selectedDataItem) {
      const dataItem =
        this.getDataSource[$event.target.closest('tr')?.getAttribute('data-kendo-grid-item-index')];
      this.vnrDoubleClick.emit({ event: $event, record: dataItem });
    } else {
      this.vnrDoubleClick.emit({ event: $event, record: this._cellClickDataItem });
    }
  }

  /**
   * Re-Calculate Height Grid By Rows Element On Page
   * @param isDefault Default is TRUE => TRUE is Re-Calc With Elements and FALSE is Calc with Items on Page
   * @param newRowHeight Using when height row is changes (working with isDefault = FALSE)
   * @returns void
   */
  protected reCalcHeightGrid() {
    let _windowHeight = window.innerHeight;
    if (this._gridHeight == _windowHeight) {
      return;
    }
    const gridEle = this._vc?.element?.nativeElement.querySelector('.k-grid') as HTMLElement;
    if (!gridEle) {
      return;
    }
    const _gridHeaderHeight = this.getHeaderHeight(gridEle);
    const _footerWithoutGridHeight = this.getFooterWithoutGridHeight(gridEle);
    let gridWrapHeight = gridEle.querySelector('.k-grid-table')?.clientHeight;
    const _gridOffsetTop = this.getElementAbsoluteTop(gridEle);
    _windowHeight -= _gridOffsetTop + _footerWithoutGridHeight;
    if (this.builder.options?.configHeightGrid) {
      const _rowScrollHorizontal =
        this.builder.options.configHeightGrid.rowScrollHorizontal +
        this.builder.options.configHeightGrid.rowThreshold;
      _windowHeight -= _rowScrollHorizontal;
      gridWrapHeight += _rowScrollHorizontal;
    }
    let _gridNoRecords =
      gridEle.querySelectorAll('.k-grid-table tr.k-grid-norecords')?.length ||
      gridEle.querySelectorAll('.k-grid-table tr.k-table-row')?.length ||
      1;

    if (_windowHeight <= 0 || _windowHeight > gridWrapHeight) {
      let _gridRowHeight = gridWrapHeight / _gridNoRecords;
      if (this.builder.options.configHeightGrid.rowHeight) {
        _gridRowHeight = this.builder.options.configHeightGrid.rowHeight;
      }
      if (this.builder.options.configShowHide.isPageExpand) {
        if (this._pageSize > _gridNoRecords) {
          _gridNoRecords = this._pageSize;
        }
      } else if (this._pageSize >= _gridNoRecords) {
        _gridNoRecords = this._pageSize;
      }
      if (this.builder.options.configHeightGrid.isHeightByRow === true) {
        const _rowScrollHorizontal =
          this.builder.options.configHeightGrid.rowScrollHorizontal +
          this.builder.options.configHeightGrid.rowThreshold;
        _gridNoRecords = this.gridView?.total || 1;
        _windowHeight =
          _gridRowHeight * _gridNoRecords +
          _gridHeaderHeight +
          _footerWithoutGridHeight +
          _rowScrollHorizontal;
      } else {
        const _gridHeightByRow =
          _gridRowHeight * _gridNoRecords + _gridHeaderHeight + _footerWithoutGridHeight;
        if (_windowHeight > _gridHeightByRow) {
          _windowHeight = _gridHeightByRow;
        }
      }
    }

    this.builder.options.configHeightGrid.gridHeight = _windowHeight;
    this._gridHeight = _windowHeight;
    this._cdr.detectChanges();
  }
  private getElementAbsoluteTop(el: HTMLElement): number {
    let top = 0;
    while (el) {
      top += el.offsetTop;
      el = el.offsetParent as HTMLElement;
    }
    return top;
  }
  private getHeaderHeight(grid: HTMLElement) {
    let headerHeight = 0;
    const _groupPanel = grid.querySelector('kendo-grid-group-panel') as HTMLElement;
    const _gridHeader = grid.querySelector('.k-grid-header') as HTMLElement;

    const groupPanelHeight = _groupPanel?.offsetHeight || _groupPanel?.clientHeight || 0;
    const gridHeaderHeight = _gridHeader?.offsetHeight || _gridHeader?.clientHeight || 0;

    headerHeight += groupPanelHeight;
    headerHeight += gridHeaderHeight;
    return headerHeight;
  }
  private getFooterWithoutGridHeight(grid: HTMLElement) {
    let footerHeight = 0;
    const _gridLoadMore = grid.parentElement?.querySelector('.treelist-loadmore') as HTMLElement;
    const _gridPager = grid.parentElement?.querySelector('.k-grid-pager') as HTMLElement;
    const gridLoadMoreHeight = _gridLoadMore?.offsetHeight || _gridLoadMore?.clientHeight || 0;
    const gridPagerHeight = _gridPager?.offsetHeight || _gridPager?.clientHeight || 0;

    footerHeight += gridLoadMoreHeight;
    footerHeight += gridPagerHeight;
    return footerHeight;
  }

  /***
   * Lấy dữ liệu datasource từ view
   */
  protected getDataSourceFromView() {
    return this.gridView.data;
  }

  /**
   * Calculate Height Column If Column Has Group
   * @returns Header With Group Height
   */
  private calcHeightGroupHeader(): number {
    const groupColumn = this.gridColumns
      ? this.gridColumns?.find((x: any) => x.Type === 'group') || null
      : this.columns?.find((x) => x.Type === 'group') || null;
    let sum = 0;
    const isCompact = this.builder.options?.gridDensityClassName === 'compact';
    if (groupColumn) {
      if (!this.builder.options.configShowHide?.isPageExpand) {
        if (isCompact) {
          sum = 111;
        } else {
          sum = 121;
        }
      } else {
        if (isCompact) {
          sum = 100;
        } else {
          sum = 120;
        }
      }
    } else {
      if (isCompact) {
        sum = 32;
      } else {
        sum = 48;
      }
    }
    return sum;
  }

  //#endregion
  private builderGridChangeColumnComponent() {
    this.builderGridChangeColumn = new VnRGridNewChangeColumnBuilder().builderFromGrid(
      this.builder,
    );
    this.builderGridChangeColumn.gridName = this.gridName;
    this.builderGridChangeColumn.columns = this.columns;
    this.builderGridChangeColumn.isOpenChangeColumn = this.isOpenChangeColumn;
    this.builderGridChangeColumn.isSupperAdmin = this.isSupperAdmin;
    this.builderGridChangeColumn.options.isAddColumnDev = this.isAddColumnDev;
    this.builderGridChangeColumn.options.isChangeColumnNew = this.isChangeColumnNew;
  }
  //#region Methods
  /**
   * Init Columns
   */
  protected async initColumns() {
    let data: Array<IVnrGridColumns> = [];
    let group: Array<GroupDescriptor> = [];
    const gridName = this.gridName;
    const _configGetColumn: IVnRGridNewConfigGetChangeColumn = {
      apiGetColumn: this.builder.options.configApiSupport?.apiGetColumn?.url,
      method: this.builder.options.configApiSupport?.apiGetColumn?.method,
      isSupperAdmin: this.isSupperAdmin,
      gridName: gridName,
    };
    if (this.isChangeColumnNew) {
      this._changeColumnConfig = await this._gridNewChangeColumnConfigGridService
        .getChangeColumnConfigGrid$(_configGetColumn)
        .toPromise();
      data =
        this._changeColumnConfig &&
        this._changeColumnConfig.Columns &&
        this._changeColumnConfig.Columns.length > 0
          ? this._changeColumnConfig.Columns
          : this.columns;
      group =
        this._changeColumnConfig &&
        this._changeColumnConfig.GroupFields &&
        this._changeColumnConfig.GroupFields.length > 0
          ? this._changeColumnConfig.GroupFields
          : [];
      this.vnrViewModeGrid.emit(this._changeColumnConfig && this._changeColumnConfig.ViewMode);
    } else if (this.columns) {
      data = this.columns;
    } else if (this.builder.options.configApiSupport.apiGetColumn) {
      data = await this._gridService.loadColumnConfig(_configGetColumn).toPromise();
    }
    this.gridColumns = data;

    let sumWidth = 0;
    this.gridColumns.forEach((x, i) => {
      sumWidth += x.Width;
      if (this.gridColumns.length === i + 1) {
        if (this.builder.options.configShowHide?.isShowColumnCheck) {
          sumWidth += 50;
        }
        if (
          (this.builder.options.configShowHide?.isShowEdit ||
            this.builder.options.configShowHide?.isShowViewDetail) &&
          !this.builder.options.configShowHide?.isShowButtonMenu
        ) {
          sumWidth += 50;
        }
        if (this.builder.options.configIndexColumn?.isShow) {
          sumWidth += 76;
        }
        if (this.builder.options.configShowHide?.isShowButtonMenu) {
          sumWidth += 100;
        }
      }
    });

    this.hasLockedColumn = this.gridColumns.some((x) => x.Locked);
    this.hasStickyColumn = this.gridColumns.some((x) => x.Sticky);

    this._gridService.sendVnrGridMessage({
      gridName: this.gridName,
      listColumn: this.gridColumns,
    });
    //this.gridColumns[1].Locked = true
    /**
     * Page define
     */
    if (data.length > 0) {
      //add valueFields
      const lstColumnName = data
        ?.map((x) => {
          if (x.Type === 'group') {
            return x.MultiColumn?.map((y) => y.Name);
          } else {
            return x.Name;
          }
        })
        ?.flat();
      if (lstColumnName && lstColumnName.length > 0) {
        this._valueFieldsInit = lstColumnName?.join(',');
        Object.assign(this.builder.options.queryOption, { ValueFields: this._valueFieldsInit });
      }
      if (!data[0].RowOnPage) data[0].RowOnPage = this._pageSize;
      this.builder.options.queryOption.take = parseInt(data[0].RowOnPage);

      if (data[0].RowOnPage && this._pageSize !== data[0].RowOnPage)
        this._pageSize = parseInt(data[0].RowOnPage);

      this._exportColumns =
        cloneDeep(data)
          ?.flatMap((col) => {
            if (col.type === 'group') {
              return col.MultiColumn?.map((multiCol) => multiCol.Name);
            } else {
              return col.Name;
            }
          })
          ?.filter((x: any) => x.Name !== 'ID') || [];
    }
    if (this.isChangeColumnNew && group && group.length > 0) {
      this.builder.options.queryOption.group = group;
    } else {
      /**
       * Group define
       */
      this.builder.options.queryOption.group = data
        .filter((x) => x.Group === true)
        .sort(function (a, b) {
          return a.GroupIndex - b.GroupIndex;
        })
        .map((m) => {
          return {
            field: m.Name,
            aggregates: [{ field: m.Name, aggregate: 'count' }, ...this.aggregates],
          };
        });
    }
    /**
     * Sort define
     */
    if (this.gridColumns.length > 0 && this.gridColumns[0] && this.gridColumns[0].OrderColumn) {
      let sortConfig: any = this.gridColumns[0]['OrderColumn'];
      typeof sortConfig == 'string' && (sortConfig = JSON.parse(sortConfig));
      this.builder.options.queryOption.sort = [...[sortConfig]];
    }

    this._defaultState = cloneDeep(this.builder.options.queryOption);
    /**
     * Template define
     */
    if (this.columnTemplates) {
      this.gridColumns.forEach((column: IVnrGridColumns) => {
        //Check group column
        if (column.Type === 'group' && column.MultiColumn && column.MultiColumn.length > 0) {
          column.MultiColumn.forEach((childConfig: IVnrGridColumns) => {
            if (childConfig.LevelEva) {
              const listKeys = Object.keys(this.columnTemplates);
              const field: any =
                listKeys.find((x: string) => {
                  return childConfig.Name.startsWith(x);
                }) || childConfig.Name;
              childConfig['template'] = this.columnTemplates[field];
            } else {
              if (this.columnTemplates[childConfig.Name]) {
                childConfig['template'] = this.columnTemplates[childConfig.Name];
              }
            }
          });
        }

        //Check single column
        if (column.LevelEva) {
          const listKeys = Object.keys(this.columnTemplates);
          const field: any =
            listKeys.find((x: string) => {
              return column.Name.startsWith(x);
            }) || column.Name;
          column['template'] = this.columnTemplates[field];
        } else {
          if (this.columnTemplates[column.Name]) {
            column['template'] = this.columnTemplates[column.Name];
          }
        }
      });
    }
    /**
     * Edit Template
     */

    if (this.isChangeColumnNew) {
      Object.assign(this._changeColumnConfig, {
        GroupFields: this.builder.options.queryOption.group,
      });
      this._gridNewChangeColumnConfigGridService.setChangeColumnConfigGrid(
        this._changeColumnConfig,
        gridName,
      );
    }
  }

  /**
   * Delete editor custom
   * @param listColumn
   */
  private deleteEditorCustom(listColumn: Array<IVnrGridColumns>) {
    listColumn.forEach((config) => {
      delete config['editor'];
      delete config['template'];
      if (config.MultiColumn && config.MultiColumn.length > 0) {
        this.deleteEditorCustom(config.MultiColumn);
      }
    });
  }
  /**
   * Page Change
   * @param event
   */
  protected onPageChange(event: PageChangeEvent): void {
    this.vnrPageChange.emit(event);
    this._calcGridHeight$.next(true);
  }
  /**
   * Group Change
   */
  protected vnrGroupChange(groups: GroupDescriptor[]): void {
    if (groups && groups.length > 0) {
      groups = groups.map((m: any) => {
        const fieldName = m.Name ?? m.field;
        const groupColumn = this.gridColumns.find((col) => col.Name === fieldName);
        if (groupColumn) {
          groupColumn.Group = true;
        }
        return {
          field: fieldName,
          aggregates: [{ field: fieldName, aggregate: 'count' }, ...this.aggregates],
        };
      });
    }
    this._defaultState.group = groups;
    if (this.builder.options.configExpanded) {
      this.builder.options.configExpanded.groupKey = [];
    }
    if (!this.isSupperAdmin) return;
    this.isLoading = true;
    this._signatureCallSave$.next(groups);
    this._calcGridHeight$.next(true);
  }

  /**
   * Filter Change
   * @param filter
   */
  protected vnrFilterChange(filter: CompositeFilterDescriptor): void {
    this.builder.options.queryOption.filter = filter;
  }

  //Load DataSource
  private initData() {
    this.isLoading = true;
    if (!this.dataLocal === false) {
      this._vnrGridBackData = cloneDeep(this.dataLocal);
    }
    this.loadDataSource();
  }
  protected columnWidth(col: IVnrGridColumns) {
    if (col.hasOwnProperty('Width') && (!col.Width || col.Width === 0)) {
      delete col.Width;
    }
    return col.Width;
  }
  /**
   * Read datasource grid
   */
  public vnrReadGrid() {
    this.isLoading = true;
    this.builder.options.queryOption = this._defaultState;
    this.builder.options.queryOption.take =
      this.gridColumns[0] && this.gridColumns[0].RowOnPage
        ? parseInt(this.gridColumns[0].RowOnPage)
        : this._pageSize;
    this.gridSelectedKeys = [];
    this.selectedIds = [];
    this.selectedDataItem = null;
    this.selectAllState = 'unchecked';
    this.getSelectedID.emit(this.selectedIds);
    this.loadDataSource();
    this.checkSelectAllState();
    this.vnrKeyChange([]);
  }

  /**
   * Reload Grid
   */
  public vnrReloadGrid() {
    this.dataSource.isAutobind = true;
    this.isLoading = true;
    this.builder.options.queryOption = this._defaultState;
    this.builder.options.queryOption.take =
      this.gridColumns[0] && this.gridColumns[0].RowOnPage
        ? parseInt(this.gridColumns[0].RowOnPage)
        : this._pageSize;
    this.gridSelectedKeys = [];
    this.selectedIds = [];
    this.selectedDataItem = null;
    this.selectAllState = 'unchecked';
    this.getSelectedID.emit(this.selectedIds);
    this.ngOnInit();
    this.checkSelectAllState();
    this.vnrKeyChange([]);
  }

  /**
   * Event close and refresh grid
   * @param evt
   */
  public vnrcloseAndRefreshGird(evt: any) {
    this.setOpenChangeColumn(evt.isShowPopup);
    if (evt.isRefresh) {
      this.vnrReloadGrid();
    }
  }
  //#endregion
  //#region Events
  /**
   * Show Order Number
   * @param dataItem
   * @param rowIndex
   * @returns
   */
  protected showOrderNumber(dataItem: any, rowIndex: number) {
    dataItem.OrderNumber = rowIndex + 1;
    return rowIndex + 1;
  }

  /**
   * Select Row Change
   * @param evt
   */
  protected vnrSelectedRowChange(evt: SelectionEvent) {
    this.getDataItem.emit(evt.selectedRows?.map((x) => x.dataItem));
    //#region Check Have Selected Row
    if (this.selectedIds && this.selectedIds.length > 0) {
      if (!this._isSelectedRow) {
        this._isSelectedRow = true;
        this._isSelectedRows$.next(true);
      }
    } else if (this.selectedIds && this.selectedIds.length === 0 && this._isSelectedRow) {
      this.selectedDataItem = null;
      this._isSelectedRow = false;
      this._isSelectedRows$.next(false);
    }
    //#endregion
  }

  /**
   * Data State Change
   * @param state
   */
  protected vnrDataStateChange(state: DataStateChangeEvent): void {
    this.isLoading = true;
    this.builder.options.queryOption = state;
    if (this.builder.options.configShowHide?.isExpendButtonLoadMore) {
      this.builder.options.queryOption.skip = 0;
    }
    this.loadDataSource();
  }

  /**
   *DataSourceRequest server-side filtering and sorting question
   */
  private checkGridDataResultGroup(data: any[], state: State) {
    if (
      state.group &&
      state.group.length > 0 &&
      data.length > 0 &&
      data[0]['Items'] &&
      Array.isArray(data[0]['Items'])
    ) {
      this._totalRowWithGroup = sumBy(data, (item: any) => item.ItemCount) || 0;
      return translateDataSourceResultGroups(data);
    }
    return process(data, { group: state.group }).data;
  }

  /**
   * Execute Load DataSource
   */
  private loadDataSource(): void {
    if (!this.dataLocal) {
      const listDateFormat = this.getDateField();
      Object.assign(this.builder.options.queryOption, { ValueFields: this._valueFieldsInit });
      this._gridService
        .loadDataGrid(this.dataSource, this.builder.options.queryOption)
        .pipe(
          map((x: any) => {
            if (x.hasOwnProperty('Data')) delete x['Data'];
            if (x.hasOwnProperty('Total')) delete x['Total'];
            if (x.data && x.data.length > 0) {
              x.data.forEach((item: any) => {
                listDateFormat.forEach((field) => {
                  item[field] = this.partDateTime(item[field]);
                });
              });
            }
            return x;
          }),
        )
        .subscribe((rsp: any) => {
          this._vnrGridBackData = cloneDeep(rsp.data);
          this._calcGridHeight$.next(true);
          if (this._isLoadScroll) {
            const backData = [...this.gridView.data, ...rsp.data];
            this.gridView = {
              total: this.gridViewTotal(rsp),
              data: this.checkGridDataResultGroup(backData, this.builder.options.queryOption), //process(backData, { group: this.state.group }).data,
            };
          } else {
            this.vnrAggregateResult = aggregateBy(rsp.data, this.aggregates);
            this.gridView = {
              total: this.gridViewTotal(rsp),
              data: this.checkGridDataResultGroup(rsp.data, this.builder.options.queryOption), //process(rsp.data, { group: this.state.group }).data,
            };
          }

          this.vnrSuccess.emit({ status: true, data: this.gridView.data });
          this.isLoading = false;
          this._isLoadScroll = false;
          this.dataSource.isAutobind = false;
        });
    } else {
      let dataLocal = this._vnrGridBackData;
      this.dataLocal = dataLocal;
      const searchForm = this.dataFormSearch;
      if (searchForm) {
        Object.keys(searchForm).forEach((searchKey) => {
          const searchValue = this.dataFormSearch[searchKey];
          dataLocal = this.filterDataLocal(dataLocal, searchKey, searchValue);
        });
      }
      this.dataLocal = dataLocal;
      this._calcGridHeight$.next(true);

      this.vnrAggregateResult = aggregateBy(this.dataLocal, this.aggregates);
      this.gridView = process(this.dataLocal, this.builder.options.queryOption);

      this.vnrSuccess.emit({ status: true, data: this.gridView.data });
      this.isLoading = false;
    }
  }
  protected refreshAggregate() {
    this.vnrAggregateResult = aggregateBy(this.gridView.data, this.aggregates);
  }
  private filterDataLocal<T>(dataSource: T[], key: keyof T, query: string): T[] {
    if (!query || typeof query === 'object') {
      return dataSource;
    }
    return dataSource.filter(
      (item) =>
        !item[key] || String(item[key]).toLowerCase().includes(query?.toString()?.toLowerCase()),
    );
  }
  /**
   * Handle Grid View Total
   * @param rsp
   * @returns
   */
  private gridViewTotal(rsp: any) {
    let total = 0;
    if (!rsp) return total;

    if (rsp.total && rsp.total > 0) {
      total = rsp.total;
    } else if (rsp.totalPages && rsp.totalPages > 0) {
      total = rsp.totalPages;
    }
    return total;
  }

  /**
   * Part Date Time
   * @param d
   * @returns
   */
  private partDateTime(d: any): any {
    if (d && d instanceof Date && !isNaN(d.getTime())) {
      return d;
    } else if (d && /\/Date\((\d*)\)\//.exec(d)) {
      return new Date(+(+/\/Date\((\d*)\)\//.exec(d)![1]));
    } else if (d) {
      return new Date(d);
    }
  }

  /**
   * Get Date Field
   * @returns
   */
  private getDateField() {
    return union(
      this.gridColumns
        .filter((x) => {
          return x.Format?.toLowerCase()?.startsWith('datetime|');
        })
        .map((x) => x.Name),
      this.builder.options.configColumnSchema && this.builder.options.configColumnSchema?.fieldDate
        ? this.builder.options.configColumnSchema?.fieldDate
        : [],
    );
  }

  /**
   * Load Expand (Show More - update pagesize)
   */
  public loadExpandBtn() {
    if (this.builder.options.configShowHide?.isExpendButtonLoadMore) {
      this.loadExpandBtnV2();
    } else {
      this.builder.options.queryOption.take += this._pageSize;
      this.isLoading = true;
      this.loadDataSource();
    }
  }

  /**
   * Load Expand (Show More - Lazy Load DataSource)
   */
  public loadExpandBtnV2() {
    this._isLoadScroll = true;
    this.builder.options.queryOption.skip = this.gridView.data.length;
    // this.state.take = this._pageSize
    this.isLoading = true;
    this.loadDataSource();
  }
  /**
   * FetchData Export Excel
   * Old Event
   */
  public onExcelExport($event: any, param?: any): Observable<any> {
    $event.preventDefault();
    const state = cloneDeep(this.builder.options.queryOption && this.builder.options.queryOption);
    state ? (state.take = 20000 - 1) : {};

    const valueFields = this._getExportValueFields();
    const payload = this._buildExportPayload(state, valueFields, param, 'excel');
    const urlApi =
      this.selectedIds && this.selectedIds.length === 0
        ? this.builder.options.configApiSupport?.apiExportAll?.url
        : this.builder.options.configApiSupport?.apiExport?.url;
    return this._gridService.postExportExcel(urlApi, payload);
  }

  /**
   * New Event Export Excel
   * @param config Using `IExportExcel`
   * @returns Observable<any>
   */
  public onExportExcel(config: IExportExcel): Observable<any> {
    const state = cloneDeep(this.builder.options.queryOption && this.builder.options.queryOption);
    state ? (state.take = 20000 - 1) : {};
    if (this._exportColumns) {
      this._exportColumns = this._exportColumns.filter((x) => !x.Hidden && !x.IsHiddenConfig);
    }

    const defaultValueFields = this._exportColumns?.length > 0 ? this._exportColumns.join(',') : '';
    const valueFields = config?.valueFields || this._getExportValueFields() || defaultValueFields;
    const payload = this._buildExportPayload(state, valueFields, config.params, 'excel');
    const urlApi =
      this.selectedIds && this.selectedIds.length === 0
        ? config.urlExportAll
        : config.urlExportSelected;

    return this._gridService.postExportExcel(urlApi, payload);
  }

  /**
   *
   * @param config Using `IExportWord`
   * @returns Observable<any>
   */
  public onExportWord(config: IExportWord): Observable<any> {
    if (!config.urlExportAll && !config.urlExportSelected) return of(null);
    const state = cloneDeep(this.builder.options.queryOption && this.builder.options.queryOption);
    state ? (state.take = 20000 - 1) : {};

    if (this._exportColumns) {
      this._exportColumns = this._exportColumns.filter((x) => !x.Hidden && !x.IsHiddenConfig);
    }

    const defaultValueFields = this._exportColumns?.length > 0 ? this._exportColumns.join(',') : '';
    const valueFields = config?.valueFields || this._getExportValueFields() || defaultValueFields;
    const payload = this._buildExportPayload(state, valueFields, config.params, 'word');
    const urlApi =
      this.selectedIds && this.selectedIds.length === 0
        ? config.urlExportAll
        : config.urlExportSelected;
    return this._gridService.postExportWord(urlApi, payload);
  }
  private _getExportValueFields(): string {
    return (
      this.gridColumns
        ?.filter((x) => !x.Hidden && !x.IsHiddenConfig)
        ?.flatMap((col) =>
          col.Type === 'group' ? col.MultiColumn?.map((multiCol) => multiCol.Name) : col.Name,
        )
        ?.join(',') || ''
    );
  }
  private _buildExportPayload(
    state: any,
    valueFields: string,
    params: any,
    exportBy: 'excel' | 'word',
  ): any {
    const request = toDataSourceRequest(state);
    const data = {
      ...request,
      selectedIds: this.selectedIds ? this.selectedIds.join(',') : '',
      IsExport: true,
      IsPortal: true,
      exportBy,
      valueFields,
      ...(params || {}),
    };

    data['PageIndex'] = data['page'] || 1;
    data['PageSize'] = data['pageSize'] || state?.take;

    if (!this.dataFormSearch === false) {
      Object.assign(data, this.dataFormSearch);
    }

    if (params && !params.params === false) {
      Object.assign(data, params.params);
    }

    if (!this.dataSource.storeName === false) {
      const formData = new FormData();
      for (const key in data) {
        formData.append(key, data[key]);
      }
      return formData;
    }

    return data;
  }

  /**
   * Add More DataSource
   * @param dataItems
   */
  protected addDatasource(dataItems: any[]): void {
    this.gridView.data = uniqBy(
      [...(this.gridView.data || []), ...(dataItems || [])],
      this.builder.options.configSelectable?.columnKey,
    );
  }

  /**
   * Reset Edit Page
   */
  protected resetEditPage() {
    if (!this.dataLocal) {
      const total = this.gridView?.total;
      this.gridView = {
        total: total,
        data: this.checkGridDataResultGroup(
          cloneDeep(this._vnrGridBackData),
          this.builder.options.queryOption,
        ), //process(cloneDeep(this._vnrGridBackData), {
        //group: this.state.group,
        //}).data,
      };
      this.isLoading = false;
    } else {
      this.gridView = process(cloneDeep(this.dataLocal), this.builder.options.queryOption);
      // console.log('this.gridView', this.gridView);
      this.isLoading = false;
    }
  }

  /**
   * Load Grid Edit
   */
  protected loadGirdEdit() {
    this.loadDataSource();
  }
  /**
   * Emit Event Editt
   */
  protected onEdit($event: any) {
    this.vnrEdit.emit($event);
  }

  /**
   * Emit Event Editt
   */
  protected onDelete($event: any) {
    this.vnrDelete.emit($event);
  }

  /**
   * Emit Event View Details
   */
  protected onViewDetails($event: any) {
    this.vnrViewDetails.emit($event);
  }

  private onSetWidthColumn(
    _lstColumn: IVnrGridColumns[] = [],
    keyName = '',
    keyValue = '',
    keyChild = '',
    $event: any,
  ) {
    if (_lstColumn && _lstColumn.length > 0) {
      if (!keyName || !keyValue || !keyChild || !$event) return;

      const searchColumn = _lstColumn.filter((x) => x[keyName] == keyValue);
      if (searchColumn.length > 0)
        searchColumn.forEach((item) => {
          item.width = $event[0].newWidth;
        });
      else {
        _lstColumn
          .filter((x) => x[keyChild])
          .forEach((itemChild) => {
            this.onSetWidthColumn(itemChild[keyChild], keyName, keyValue, keyChild, $event);
          });
      }
    }
  }

  /**
   * Change Resize
   */
  protected onColumnResize($event: any) {
    if ($event && $event[0] && $event[0].column && $event[0].column.field) {
      const fieldName = $event[0].column.field;
      if (this.gridColumns && this.gridColumns.length > 0) {
        this.onSetWidthColumn(this.gridColumns, 'Name', fieldName, 'MultiColumn', $event);
        // let column = this.gridColumns.find((x) => x.Name === fieldName)
        // column.Width = $event[0].newWidth
      } else if (this.columns && this.columns.length > 0) {
        this.onSetWidthColumn(this.columns, 'Name', fieldName, 'MultiColumn', $event);

        // let column = this.vnrDataColumn.find((x) => x.Name === fieldName)
        // column.Width = $event[0].newWidth
      }
    }
    if (this.builder.options.configHeightGrid?.isAllowCalcRowHeight) {
      this._calcGridHeight$.next(true);
    }
    this.vnrColumnResizeChange.emit($event);
    //#region Save Config Column using Change Column
    this.saveChangeResizeColumn($event);
    //#endregion
  }

  /**
   * Save Button
   */
  private saveChangeResizeColumn(columnResized: any) {
    if (!this.builder.optionChangeColumn?.data) {
      return;
    }
    //#region Update Width Column Resized
    if (columnResized && columnResized.length > 0) {
      columnResized.forEach((columnResize: any) => {
        const _column = this.gridColumns.find((item) => {
          if (item.Type === 'group') {
            this.findGroupColumn(item, columnResize);
          }
          return item.Name === columnResize.column.field;
        });

        if (_column) _column.Width = columnResize.newWidth;
      });
    }
    //#endregion

    //#region  Save Config
    const dataSave: IApiOptionsChangeColumn = {
      apiSave: this.builder.optionChangeColumn?.apiSaveChangeColumn?.url,
      method: this.builder.optionChangeColumn?.apiSaveChangeColumn?.method,
      data: this.builder.optionChangeColumn?.data,
    };
    //set data option
    const listShowColum = cloneDeep(this.gridColumns);
    this.deleteEditorCustom(listShowColum);
    // listShowColum.forEach((element) => {
    //   delete element.template;
    //   delete element.editor;
    // });
    const { userInfoID } = dataSave.data;

    dataSave.data = {
      userInfoID: userInfoID || '',
      gridControlName: this.gridName,
      columnMode: JSON.stringify(listShowColum),
      keyChangeCol: true,
    };
    if (this.isChangeColumnNew && this.isSupperAdmin === true) {
      this._signatureCallSave$.next(this.gridColumns);
    }
    if (!this.isChangeColumnNew) {
      this._gridNewChangeColumnService.vnrSaveChangeColumn(dataSave).subscribe((resp: any) => {
        // console.log(resp);
      });
    }

    //#endregion
  }

  /**
   *  Emit Event Click Format File
   * @param value => string
   */
  protected onClickFileName(value: string) {
    this.vnrFormatFileClick.emit(value);
  }
  //#endregion

  /**
   *  Emit Event Click upload File
   * @param value => string
   */
  protected onClickUploadFileToColumn(columnName: string, dataItem: any, column: any) {
    this.vnrUploadFileToColumnClick.emit({
      columnName: columnName,
      dataItem: dataItem,
      column: column,
    });
  }
  //#endregion

  /**
   * Find Group Column
   */
  protected findGroupColumn(columnItem: IVnrGridColumns, columnResize: any) {
    const _columns = columnItem.MultiColumn || [];
    const _column = _columns.find((item: any) => {
      if (item.Type && item.Type === 'group') {
        this.findGroupColumn(item, columnResize);
      }
      return item.Name === columnResize.column.field;
    });

    if (_column) _column.Width = columnResize.newWidth;

    return;
  }

  /**
   * Emit event detail expand
   * @param $event
   */
  protected onDetailExpand($event: DetailExpandEvent) {
    this.vnrDetailExpand.emit($event);
  }

  /**
   * Emit Event Click On MenuItem
   */
  protected onMenuItemClick(dataItem: any, type: VnrGridMenuType) {
    this.vnrMenuItemClick.emit({ dataItem: dataItem, type: type });
  }
  //#endregion

  protected onCellClick(e) {
    this._cellClickDataItem = e.dataItem;
    this.vnrCellClick.emit(e);
  }

  protected sortChange($event) {}

  protected vnrReloadGridChangeColumn($event) {
    this.isLoading = true;
    if ($event) {
      this.loadColumnConfig();
    }
  }
  //#region load cấu hình cột mới
  protected loadColumnConfig() {
    const gridName = this.gridName;
    const _configGetColumn: IVnRGridNewConfigGetChangeColumn = {
      apiGetColumn: this.builder.options.configApiSupport?.apiGetColumn?.url,
      method: this.builder.options.configApiSupport?.apiGetColumn?.method,
      isSupperAdmin: this.isSupperAdmin,
      gridName: gridName,
    };
    this._gridNewChangeColumnConfigGridService
      .getChangeColumnConfigGrid$(_configGetColumn)
      .pipe(
        takeUntil(this._destroy$),
        finalize(() => this._calcGridHeight$.next(true)),
      )
      .subscribe((columnConfig: any) => {
        if (columnConfig) {
          this._changeColumnConfig = columnConfig;
          if (!columnConfig.Columns === false) {
            this.gridColumns = columnConfig.Columns; //cột
          }
          this.hasLockedColumn = this.gridColumns.some((x) => x.Locked);
          this.hasStickyColumn = this.gridColumns.some((x) => x.Sticky);
          this.vnrViewModeGrid.emit(this._changeColumnConfig && this._changeColumnConfig.ViewMode);
        }
      });
    setTimeout(() => {
      this.isLoading = false;
    }, 1000);
  }
  //#endregion

  //#region call api lưu cấu hình mới
  protected createOrUpdateConfigColumn() {
    const _columnConvert = this._gridNewChangeColumnConfigGridService.convertDataToColumns(
      this.gridColumns,
      this.builder.options.queryOption.group,
    );
    const gridName = this.gridName;
    const optionSaveColumn = new IApiOptionsCreateOrUpdateGridNewColumnConfig({
      apiSave: this.builder.optionChangeColumn?.apiSaveChangeColumn?.url,
      method: this.builder.optionChangeColumn?.apiSaveChangeColumn?.method,
      gridName: gridName,
      data: {
        translateKey: 'string',
        modelName: this.builder.optionChangeColumn?.modelName || '',
        assemblyName: this.builder.optionChangeColumn?.assemblyName || '',
        viewMode: this._changeColumnConfig?.ViewMode,
        columns: _columnConvert,
        groupFields: this.builder.options.queryOption?.group,
      },
    });
    this._gridNewChangeColumnService
      .vnrCreateOrUpdateGridColumnConfig(optionSaveColumn)
      .subscribe((_response: any) => {
        if (_response && (_response.Status === 'SUCCESS' || _response.Data)) {
          Object.assign(this._changeColumnConfig, optionSaveColumn.data);
          this._gridNewChangeColumnConfigGridService.setChangeColumnConfigGrid(
            new IVnRGridNewChangeColumnConfig(this._changeColumnConfig),
            gridName,
          );
        }
      });
    setTimeout(() => {
      this.isLoading = false;
    }, 1000);
  }
  //#endregion

  //#region tính số total row
  protected funcCalcCountLoadMore(dataGrid: any) {
    let result = 0;
    if (dataGrid) {
      if (
        this.builder.options.queryOption?.group &&
        this.builder.options.queryOption.group.length > 0 &&
        dataGrid.length > 0 &&
        ((dataGrid[0]['Items'] && Array.isArray(dataGrid[0]['Items'])) ||
          (dataGrid[0]['items'] && Array.isArray(dataGrid[0]['items'])))
      ) {
        if (dataGrid[0]?.ItemCount) {
          result =
            sumBy(dataGrid, (item: any) => {
              return item?.ItemCount;
            }) || 0;
        } else {
          const data = this._gridService.extractItems(dataGrid);
          return data?.length || 0;
        }
      } else {
        result = dataGrid?.length;
      }
    }
    return result;
  }
  //#endregion

  // HostListener to handle document hover events

  @HostListener('document:mouseover', ['$event'])
  protected onMouseEnter(event: MouseEvent) {
    const isGridRow = (event.target as HTMLElement)?.classList?.contains('k-table-td');
    if (isGridRow) {
      this.isHovered = true;
    } else {
      this.isHovered = false;
    }
  }

  @HostListener('document:mouseleave', ['$event'])
  protected onMouseLeave(event: MouseEvent) {
    this.isHovered = false;
  }
  private onWindowScroll(gridContentEle: HTMLElement) {
    // Kiểm tra xem có cuộn đến cột cuối cùng hay không
    if (gridContentEle.scrollLeft + gridContentEle.clientWidth >= gridContentEle.scrollWidth) {
      // Thêm một class mới khi cuộn đến cột cuối cùng
      this.isLastChild = true;
    } else {
      // Xóa class nếu không cuộn đến cột cuối cùng
      this.isLastChild = false;
    }
  }

  protected vnrKeyChange(e: any) {
    this.selectedIds = e;
    if (this.isGroupStateValid(this.builder.options.queryOption, this.gridView)) {
      const temp = this._gridService.extractItems(this.gridView.data) || [];
      temp?.forEach((item) => {
        if (this.selectedIds?.includes(item[this.builder.options.configSelectable?.columnKey])) {
          item.checked = true;
        } else {
          item.checked = false;
        }
      });
    } else {
      this.gridView?.data?.forEach((item) => {
        if (this.selectedIds?.includes(item[this.builder.options.configSelectable?.columnKey])) {
          item.checked = true;
        } else {
          item.checked = false;
        }
      });
    }
    this.checkSelectAllState();
    const dataSource = this._vnrGridBackData;
    if (this.selectedIds && this.selectedIds.length > 1) {
      this.selectedDataItem = null;
      const dataFilterByID =
        dataSource?.filter((ele) =>
          e.includes(ele[this.builder.options.configSelectable?.columnKey]),
        ) || [];
      this.getSelectedID.emit(e);
      this.getSelectedDataItem.emit(dataFilterByID);
      return;
    }

    //Set Selected Data Item If Length Selected Row Only One
    this.selectedDataItem =
      dataSource?.find((x: any) =>
        e.includes(x[this.builder.options.configSelectable?.columnKey]),
      ) || {};
    this.getSelectedID.emit(e);
  }

  // Add any additional logic or methods as needed
  protected onSelectAllChange(checkedState: SelectAllCheckboxState) {
    if (checkedState === 'checked') {
      if (this.gridView?.data) {
        if (this.isGroupStateValid(this.builder.options.queryOption, this.gridView)) {
          const temp = this._gridService.extractItems(this.gridView.data) || [];
          temp?.forEach((item) => {
            item.checked = true;
          });
          this.gridSelectedKeys = temp?.map(
            (item) => item[this.builder.options.configSelectable?.columnKey],
          );
        } else {
          this.gridSelectedKeys = this.gridView?.data?.map(
            (item) => item[this.builder.options.configSelectable?.columnKey],
          );
        }
      }
      this.selectAllState = 'checked';
    } else {
      if (this.isGroupStateValid(this.builder.options.queryOption, this.gridView)) {
        const temp = this._gridService.extractItems(this.gridView.data) || [];
        temp?.forEach((item) => {
          item.checked = false;
        });
      }
      this.gridSelectedKeys = [];
      this.selectAllState = 'unchecked';
    }
  }
  /**
   * Method to handle check box row for kendo grid angular
   * @param dataItem - Data item of the row
   */
  protected checkDataItem(dataItem: any) {
    if (dataItem) {
      this.gridSelectedKeys = this.gridSelectedKeys?.includes(
        dataItem[this.builder.options.configSelectable?.columnKey],
      )
        ? this.gridSelectedKeys?.filter(
            (item) => item !== dataItem[this.builder.options.configSelectable?.columnKey],
          )
        : [...this.gridSelectedKeys, dataItem[this.builder.options.configSelectable?.columnKey]];
    }
    this.checkSelectAllState();
  }
  /** checkGroup will find the number of children and returns data accordingly, it will also change your JSON structure, console if you want to see what is changed there! */
  protected checkGroup(group: GroupResult, index: any): void {
    const leafItems = this._gridService.getGroupItems(group);
    const shouldCheck = leafItems?.some((item) => !item.checked);
    leafItems?.forEach((item) => (item.checked = shouldCheck));
    const temp = this._gridService.extractItems(this.gridView?.data) || [];
    const dataFilterByID = temp?.filter((ele) => ele?.checked);
    this.gridSelectedKeys =
      dataFilterByID?.map((item) => item[this.builder.options.configSelectable?.columnKey]) || [];
    this.getSelectedID.emit(this.gridSelectedKeys);
    this.getSelectedDataItem.emit(dataFilterByID);
    this.checkSelectAllState();
  }

  /** Manage checkbox l eaf items if all children are selected, uncheck if not all of the checkboxes under the parent are not selected. */
  protected isGroupChecked(group: GroupResult): boolean {
    const leafItems = this._gridService.getGroupItems(group);
    return leafItems?.every((item) => item.checked);
  }

  private checkSelectAllState() {
    const len = this.gridSelectedKeys?.length;
    if (len === 0) {
      this.selectAllState = 'unchecked';
    } else if (len > 0 && len < this.funcCalcCountLoadMore(this.gridView?.data)) {
      this.selectAllState = 'indeterminate';
    } else {
      this.selectAllState = 'checked';
    }
  }

  /**
   * Checks if the group state is valid.
   * The group state is considered valid if it meets the following conditions:
   * - The state has a group property
   * - The length of the group property is greater than 0
   * - The gridView data has a length greater than 0
   * - The first element of the gridView data has either 'Items' or 'items' property that is an array
   *
   * @param state - The state object containing the group property
   * @param gridView - The gridView object containing the data property
   * @returns A boolean indicating if the group state is valid
   */
  protected isGroupStateValid(state: any, gridView: any): boolean {
    // Check if the state has a group property, if it has a length greater than 0,
    // if the gridView data has a length greater than 0,
    // and if the first element of the gridView data has either 'Items' or 'items' property that is an array
    return (
      state?.group &&
      state.group.length > 0 &&
      gridView?.data.length > 0 &&
      ((gridView?.data[0]['Items'] && Array.isArray(gridView?.data[0]['Items'])) ||
        (gridView?.data[0]['items'] && Array.isArray(gridView?.data[0]['items'])))
    );
  }
  protected vnrScrollBottom() {
    if (
      this.builder.options?.isEnabledScrollBottom &&
      this.builder.options.configShowHide?.isPageExpand &&
      this.gridView &&
      this.gridView.data &&
      this.gridView.data.length > 0 &&
      this.funcCalcCountLoadMore(this.gridView?.data) < this.gridView?.total
    ) {
      this.builder.options.queryOption.take += this._pageSize;
      this.isLoading = true;
      this.loadDataSource();
    }
  }

  /**
   * Expand all group rows in the grid.
   * This method sets the initially expanded flag to true and clears the expanded group keys array.
   * This will cause all groups to be expanded on the first load.
   */
  protected expandAll(): void {
    // Set the initially expanded flag to true
    if (this.builder.options.configExpanded) {
      this.builder.options.configExpanded.isExpanded = true;
      this.builder.options.configExpanded.groupKey = [];
    }
  }

  /**
   * Collapse all group rows in the grid.
   * This method clears the expandedGroupKeys array to collapse all groups
   * and sets the vnrInitiallyExpanded flag to false.
   */
  protected collapseAll(): void {
    // Clear the expandedGroupKeys array to ensure all groups are collapsed
    if (this.builder.options.configExpanded) {
      this.builder.options.configExpanded.isExpanded = false;
      this.builder.options.configExpanded.groupKey = [];
    }
  }

  protected isGroupExpanded = (rowArgs: GroupRowArgs): boolean => {
    const matchKey = this.builder.options.configExpanded?.groupKey?.some(
      (groupKey) =>
        groupKey.field === rowArgs?.group?.field && groupKey?.value === rowArgs?.group?.value,
    );
    return (
      (this.builder.options.configExpanded?.isExpanded && !matchKey) ||
      (!this.builder.options.configExpanded?.isExpanded && matchKey)
    );
  };
  /**
   * Toggles the expanded state of a group row in the grid.
   * @param rowArgs The GroupRowArgs object containing information about the group row.
   * @returns void
   */
  protected toggleGroup(rowArgs: GroupRowArgs): void {
    const keyIndex = this.builder.options.configExpanded?.groupKey?.findIndex(
      (groupKey) =>
        groupKey.field === rowArgs.group.field && groupKey.value === rowArgs.group.value,
    );
    if (this.builder.options.configExpanded?.groupKey === null) {
      this.builder.options.configExpanded.groupKey = [];
    }
    // If the group is not found in the expandedGroupKeys array, add it
    if (keyIndex === -1) {
      this.builder.options.configExpanded?.groupKey.push({
        field: rowArgs.group.field,
        value: rowArgs.group.value,
      });
    } else {
      // If the group is found, remove it from the array
      this.builder.options.configExpanded?.groupKey?.splice(keyIndex, 1);
    }
  }

  protected trackByFn(index: number, item: any): any {
    return item?.name || index;
  }
  protected isColumnHasFormatSpecial(column: IVnrGridColumns | any) {
    if (
      !column ||
      !column.template === false ||
      !this.builder.options.configColumnSchema ||
      this.builder.options.isEnabledFormat === false
    ) {
      return false;
    }
    if (column.Format || !column.Name) {
      return true;
    } else if (this.builder.options.configColumnSchema.fieldBoolean.indexOf(column.Name) >= 0) {
      return true;
    } else if (this.builder.options.configColumnSchema.fieldDate.indexOf(column.Name) >= 0) {
      return true;
    } else if (this.builder.options.configColumnSchema.fieldNumber.indexOf(column.Name) >= 0) {
      return true;
    } else if (this.builder.options.configColumnSchema.fieldNumberMoney.indexOf(column.Name) >= 0) {
      return true;
    }
    return false;
  }

  public setOpenChangeColumn(isOpenChangeColumn: boolean): void {
    if (!this.builder.optionChangeColumn) {
      return;
    }
    this.isOpenChangeColumn = isOpenChangeColumn;
    this.builderGridChangeColumn.isOpenChangeColumn = isOpenChangeColumn;
  }
  public setChangeColumnVersion(isNewVersion: boolean): void {
    if (!this.builder.optionChangeColumn) {
      return;
    }
    this.isChangeColumnNew = isNewVersion;
    if (this.builderGridChangeColumn.options) {
      this.builderGridChangeColumn.options.isChangeColumnNew = isNewVersion;
    }
  }
  public setChangeColumnPosition(position: VnRGridNewOptionChangeColumnPosition): void {
    if (!position) {
      return;
    }
    if (this.builder?.optionChangeColumn) {
      this.builder.optionChangeColumn.position = {
        bottom: position.bottom ?? this.builder.optionChangeColumn?.position?.bottom,
        top: position.top ?? this.builder.optionChangeColumn?.position?.top,
        left: position.left ?? this.builder.optionChangeColumn?.position?.left,
        right: position.right ?? this.builder.optionChangeColumn?.position?.right,
        width: position.width ?? this.builder.optionChangeColumn?.position?.width,
        height: position.height ?? this.builder.optionChangeColumn?.position?.height,
      };
    }
    if (this.builderGridChangeColumn?.options) {
      this.builderGridChangeColumn.options.position = {
        bottom: position.bottom ?? this.builderGridChangeColumn.options.position?.bottom,
        top: position.top ?? this.builderGridChangeColumn.options.position?.top,
        left: position.left ?? this.builderGridChangeColumn.options.position?.left,
        right: position.right ?? this.builderGridChangeColumn.options.position?.right,
        width: position.width ?? this.builderGridChangeColumn.options.position?.width,
        height: position.height ?? this.builderGridChangeColumn.options.position?.height,
      };
    }
  }
  public setAutobind(isAutobind: boolean): void {
    this.dataSource.isAutobind = isAutobind;
  }
  public removeDataFilter(filterKeys: string[]): void {
    if (this.dataFormSearch) {
      filterKeys.forEach((field) => {
        if (field in this.dataFormSearch) {
          delete this.dataFormSearch[field];
        }
      });
    }
  }
  public setDataFilter(filter: any): void {
    if (!this.dataFormSearch) {
      this.dataFormSearch = {};
    }
    Object.assign(this.dataFormSearch, filter);
  }
  public setGridHeight(_height: number): void {
    this.builder.options.configHeightGrid.gridHeight = _height;
  }
  public getGridStoreName(): string {
    return this.dataSource.storeName;
  }
  public setColumns(_columns: any) {
    this.columns = _columns;
    this.initColumns();
  }
  public setDataSourceLocal(_dataSourceLocal: any) {
    this.dataLocal = _dataSourceLocal;
    this._vnrGridBackData = _dataSourceLocal;
    this.loadDataSource();
  }
  public setPageSize(_pageSize: number) {
    this._pageSize = _pageSize;
    this.pageSize = _pageSize;
  }
}
