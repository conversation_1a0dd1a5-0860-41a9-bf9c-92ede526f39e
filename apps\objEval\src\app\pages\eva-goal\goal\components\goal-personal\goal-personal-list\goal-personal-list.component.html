<obj-eval-tab-filter
  [data]="GoalStatusFilterData"
  (selectChange)="onSelectPersonalChange($event)"
  [tabDefault]="tabDefault"
  [tabCount]="tabCount"
></obj-eval-tab-filter>
<vnr-toolbar-new
  class="border-0 --custom-bg width-100p manage-personnel-records-grids__toolbar"
  [builder]="builderToolbar"
>
  <vnr-button
    [vnrText]="'objEval.GoalPeriod.AddGoal'"
    [vnrDisabled]="false"
    (vnrClick)="onOpenAddGoalModal()"
    [vnrType]="'primary'"
    [vnrIcon]="'plus'"
    rightToolbar
  >
  </vnr-button>
</vnr-toolbar-new>
<vnr-grid-new
  class="grid-new"
  #vnrGrid
  [builder]="builderGrid"
  [dataLocal]="dataLocal"
  [columns]="columns"
  [gridName]="gridName"
  [isSupperAdmin]="isSupperAdmin"
  [defaultColumnTemplate]="tplCustomTemplateByColumn"
  (getSelectedDataItem)="getSelectedDataItem($event)"
  (vnrDoubleClick)="onOpenDetail($event)"
>
</vnr-grid-new>
<ng-template #tplCustomTemplateByColumn let-dataItem let-column="columnItem" let-field="field">
  <ng-container [ngSwitch]="column['Name']">
    <span *ngSwitchCase="'GoalRegister'">
      <vnr-tag [vnrColor]="'purple'" [vnrTitle]="dataItem[column['Name']]"></vnr-tag>
    </span>
    <span *ngSwitchCase="'GoalConfirm'">
      <vnr-tag [vnrColor]="'green'" [vnrTitle]="dataItem[column['Name']]"></vnr-tag>
    </span>
    <span *ngSwitchCase="'GoalAllocation'">
      <vnr-tag [vnrColor]="'blue'" [vnrTitle]="dataItem[column['Name']]"></vnr-tag>
    </span>
    <span class="cursor-pointer" *ngSwitchCase="'ExecutionObject'">
      <div class="d-flex align-items-center">
        <app-vnr-letters-avatar
          [avatarName]="dataItem[column['Name']]"
          [circular]="true"
          [width]="32"
          [src]="dataItem && dataItem.ImagePathProcess"
          class="mr-2"
        ></app-vnr-letters-avatar>
        <span>{{ dataItem[column['Name']] }}</span>
      </div>
    </span>
    <span *ngSwitchCase="'Status'">
      <vnr-tag
        [vnrColor]="statusColorMap[dataItem['Status']] || 'default'"
        [vnrTitle]="statusTextMap[dataItem['Status']] | translate"
      ></vnr-tag>
    </span>
    <span *ngSwitchDefault>
      {{ dataItem[column['Name']] }}
    </span>
  </ng-container>
</ng-template>
