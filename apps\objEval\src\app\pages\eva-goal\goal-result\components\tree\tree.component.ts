import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { VnrButtonComponent } from '@hrm-frontend-workspace/ui';
import { VnrToolbarNewBuilder, VnrToolbarNewComponent } from '@hrm-frontend-workspace/vnr-module';
import * as go from 'gojs';
import {
  goalTypeColorMap,
  goalTreeDataSource,
  GoalNode,
  STATUS_PROGRESS_TAG,
  treeFilter,
} from '../../data/tree.data';
@Component({
  selector: 'app-tree',
  templateUrl: './tree.component.html',
  styleUrls: ['./tree.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [CommonModule, VnrButtonComponent, VnrToolbarNewComponent],
})
export class TreeComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('goalTreeDiagram', { static: true }) private diagramRef!: ElementRef;
  @Input() goalData: GoalNode[] = [];
  @Output() nodeClick = new EventEmitter<GoalNode>();

  private diagram: go.Diagram | null = null;
  builderToolbar: VnrToolbarNewBuilder;

  // Biến quản lý tỷ lệ zoom
  zoomPercentage = 100;
  private zoomFactor = 1.2; // Hệ số zoom mỗi lần nhấn nút
  private minZoom = 25; // Giới hạn thu nhỏ tối thiểu
  private maxZoom = 400; // Giới hạn phóng to tối đa

  constructor(private cdr: ChangeDetectorRef) {}

  ngOnInit(): void {
    // Sample data nếu không có dữ liệu đầu vào
    if (!this.goalData || this.goalData.length === 0) {
      this.goalData = goalTreeDataSource;
    }
    this.builderToolbarComponent();
  }

  ngAfterViewInit(): void {
    console.log(this.goalData);
    this.initDiagram();
    this.loadDiagramData();
  }

  ngOnDestroy(): void {
    if (this.diagram) {
      this.diagram.div = null;
    }
  }

  private builderToolbarComponent() {
    this.builderToolbar = new VnrToolbarNewBuilder({
      isSupperAdmin: true,
      screenName: 'GoalResult',
      gridName: 'GoalResult_Tree',
      storeName: 'eva_sp_get_GoalResult',
      isShowConfig: true,
      options: {
        configButtonChangeColumn: { isShow: true },
        configButtonExport: { isShowBtnExcelAll: true },
        configQuickSearch: {
          isShow: true,
        },
        configFilterAdvanceQuick: {
          isShow: true,
          components: treeFilter(),
          keyConfig: 'GoalResult_FilterAdvanceSeting',
          isShowBtnAdvance: false,
        },
      },
    });
  }

  /**
   * Phóng to biểu đồ
   */
  protected zoomIn(): void {
    if (this.diagram) {
      // Tính toán tỷ lệ zoom mới
      const newScale = this.diagram.scale * this.zoomFactor;

      // Kiểm tra giới hạn phóng to
      if (Math.round(newScale * 100) <= this.maxZoom) {
        this.diagram.scale = newScale;
        this.updateZoomPercentage();
      }
    }
  }

  /**
   * Thu nhỏ biểu đồ
   */
  protected zoomOut(): void {
    if (this.diagram) {
      // Tính toán tỷ lệ zoom mới
      const newScale = this.diagram.scale / this.zoomFactor;

      // Kiểm tra giới hạn thu nhỏ
      if (Math.round(newScale * 100) >= this.minZoom) {
        this.diagram.scale = newScale;
        this.updateZoomPercentage();
      }
    }
  }

  protected expandAll(): void {
    console.log('expandAll');
  }

  protected collapseAll(): void {
    console.log('collapseAll');
  }

  /**
   * Xử lý sự kiện khi người dùng click vào node
   * @param nodeData Dữ liệu của node được click
   */
  private handleNodeClick(nodeData: GoalNode): void {
    this.nodeClick.emit(nodeData);
  }

  /**
   * Cập nhật giá trị hiển thị tỷ lệ phần trăm
   */
  private updateZoomPercentage(): void {
    if (this.diagram) {
      this.zoomPercentage = Math.round(this.diagram.scale * 100);
      this.cdr.detectChanges(); // Cập nhật UI sau khi thay đổi giá trị
    }
  }

  /**
   * Khởi tạo diagram
   */
  private initDiagram(): void {
    const $ = go.GraphObject.make;

    // Khởi tạo Diagram với các cấu hình
    this.diagram = $(go.Diagram, this.diagramRef.nativeElement, {
      // Cấu hình layout dạng cây
      layout: $(go.TreeLayout, {
        angle: 90, // Hướng cây từ trái sang phải (90 độ)
        nodeSpacing: 40, // Khoảng cách giữa các node cùng cấp
        layerSpacing: 80, // Khoảng cách giữa các tầng (cấp) của cây
        arrangement: go.TreeLayout.ArrangementHorizontal, // Sắp xếp các node theo chiều ngang
      }),
      allowDrop: false, // Không cho phép kéo thả node
      'undoManager.isEnabled': true, // Bật tính năng undo/redo
      model: $(go.TreeModel), // Sử dụng mô hình dữ liệu dạng cây
      // Lắng nghe sự kiện thay đổi scale để cập nhật tỷ lệ phần trăm
      ViewportBoundsChanged: (e) => {
        this.updateZoomPercentage();
      },
    });

    // ================ NODE TEMPLATE ================
    // Định nghĩa cách hiển thị cho mỗi node trong cây
    this.diagram.nodeTemplate = $(
      go.Node, // Đối tượng Node cơ bản
      'Auto', // Panel tự động điều chỉnh kích thước theo nội dung
      {
        selectionObjectName: 'PANEL', // Đối tượng được chọn khi click vào node
        isTreeExpanded: true, // Mặc định mở rộng tất cả các node con
        isShadowed: true, // Bật hiệu ứng đổ bóng
        shadowBlur: 15, // Độ mờ của bóng đổ - tăng lên để bóng lan tỏa rộng hơn
        shadowColor: 'rgba(22, 22, 22, 0.1)', // Màu của bóng đổ (đen với độ trong suốt 10%)
        shadowOffset: new go.Point(0, 0), // Vị trí offset của bóng (0px) - đổ bóng đều 4 phía
        width: 320, // Chiều rộng cố định của node
        // Thêm sự kiện click cho node
        click: (e, obj) => {
          const node = obj.part as go.Node;
          if (node && node.data) {
            this.handleNodeClick(node.data as GoalNode);
          }
        },
        cursor: 'pointer', // Hiển thị con trỏ dạng pointer khi di chuột qua node
      },
      // ===== PHẦN NỀN CỦA NODE =====
      $(go.Shape, 'RoundedRectangle', {
        // Hình dạng của node là hình chữ nhật bo tròn
        fill: 'white', // Màu nền bên trong
        stretch: go.GraphObject.Fill,
        alignment: go.Spot.Center,
        stroke: null,
        strokeWidth: 0,
      }),
      // ===== NỘI DUNG BÊN TRONG NODE =====
      $(
        go.Panel, // Panel chứa nội dung
        'Vertical', // Sắp xếp các thành phần theo chiều dọc
        {
          name: 'PANEL', // Đặt tên cho panel
          alignmentFocus: go.Spot.Top,
          padding: 0,
          stretch: go.GraphObject.Fill,
          // Đảm bảo panel không vượt quá kích thước của shape
          margin: new go.Margin(0, 0, 0, 0),
        },

        // === HEADER - NỀN MÀU XANH NHẠT ===
        $(
          go.Panel, // Panel chứa phần header
          'Table', // Sử dụng Table để sắp xếp các phần tử
          {
            alignment: go.Spot.Top, // Canh lề trên
            alignmentFocus: go.Spot.Top, // Điểm neo canh lề
            stretch: go.GraphObject.Horizontal, // Kéo giãn theo chiều ngang
            background: '#F0F7FF', // Màu nền xanh nhạt
            padding: new go.Margin(12, 0), // Padding trên/dưới, loại bỏ padding trái/phải
            margin: new go.Margin(0, 0, 0, 0), // Không có margin để sát với góc bo tròn
            // Bo góc trên của header để phù hợp với shape chính
            defaultRowSeparatorStroke: null,
            defaultColumnSeparatorStroke: null,
          },
          // --- TÊN mục tiêu---
          $(go.TextBlock, new go.Binding('text', 'GoalName'), {
            // Binding đến thuộc tính department
            alignment: go.Spot.Left, // Canh trái
            font: 'bold 14px Roboto', // Font chữ in đậm
            stroke: '#303030', // Màu chữ gần đen
            column: 0, // Cột 0 trong Table
            maxSize: new go.Size(220, NaN), // Chiều rộng tối đa 220px, chiều cao tự động
            margin: new go.Margin(0, 0, 0, 12), // Thêm margin bên trái
          }),
          // --- TAG PHỤ BÊN PHẢI (TÀI CHÍNH) ---
          $(
            go.Panel,
            'Auto', // Panel tự động điều chỉnh kích thước
            { alignment: go.Spot.Right, column: 1, margin: new go.Margin(0, 12, 0, 0) }, // Canh phải
            // Binding để ẩn tag phụ cho node gốc
            new go.Binding('visible', 'parent', function (parent) {
              return parent !== ''; // Chỉ hiển thị khi parent không rỗng (không phải node gốc)
            }),
            $(
              go.Shape,
              'RoundedRectangle',
              {
                stroke: null, // Không có viền
                parameter1: 10, // Bo tròn hoàn toàn (10px)
              },
              // Binding màu sắc dựa trên category
              new go.Binding('fill', 'type', (type) => goalTypeColorMap[type]),
            ),
            $(go.TextBlock, new go.Binding('text', 'type'), {
              // Nội dung tag
              margin: new go.Margin(0, 6), // Margin 6px bên trái và phải
              font: '500 12px Roboto', // Font medium
              stroke: 'white', // Màu chữ trắng
            }),
          ),
        ),

        // === PHẦN NỘI DUNG CHÍNH ===
        $(
          go.Panel,
          'Vertical',
          {
            alignment: go.Spot.Top,
            alignmentFocus: go.Spot.Top,
            stretch: go.GraphObject.Horizontal, // Kéo giãn theo chiều ngang
            background: 'white', // Nền trắng
            padding: new go.Margin(4, 0), // Loại bỏ padding trái/phải, chỉ giữ lại padding trên/dưới
          },
          // Binding để ẩn/hiện phần nội dung dựa trên parent
          new go.Binding('visible', 'parent', function (parent) {
            return parent !== ''; // Chỉ hiển thị khi parent không rỗng
          }),
          // --- TIÊU ĐỀ MỤC TIÊU ---
          $(go.TextBlock, new go.Binding('text', 'Department'), {
            // Binding đến thuộc tính title
            alignment: go.Spot.Left, // Canh trái
            font: '500 14px Roboto', // Font medium
            margin: new go.Margin(4, 12, 4, 12), // Thêm margin trái/phải
            stretch: go.GraphObject.Horizontal, // Kéo giãn theo chiều ngang
          }),

          // --- PANEL CHỨA AVATAR VÀ TÊN NGƯỜI DÙNG ---
          $(
            go.Panel,
            'Horizontal', // Sắp xếp theo chiều ngang
            {
              alignment: go.Spot.Left,
              alignmentFocus: go.Spot.Left,
              margin: new go.Margin(0, 12, 4, 12), // Thêm margin trái/phải
            },
            $(go.Picture, {
              source: '/assets/images/avatars/avatar-default.jpg',
              width: 20,
              height: 20,
              margin: new go.Margin(4, 8, 0, 0), // Thêm margin bên phải cho avatar
            }),
            $(
              go.TextBlock,
              {
                font: '400 12px Roboto',
                alignment: go.Spot.Left,
                alignmentFocus: go.Spot.Center, // Căn giữa theo chiều dọc với avatar
              },
              new go.Binding('text', '', function (data) {
                // Kết hợp representative và jobTitle trong cùng một dòng
                const representative = data.Representative || '';
                const jobTitle = data.JobTitle || '';
                return representative + (jobTitle ? ' - ' + jobTitle : '');
              }),
            ),
          ),
          // --- MÔ TẢ MỤC TIÊU ---
          $(go.TextBlock, new go.Binding('text', 'description'), {
            // Binding đến description
            alignment: go.Spot.Left, // Canh trái
            font: '400 12px Roboto', // Font thường
            margin: new go.Margin(4, 12, 0, 12), // Thêm margin trái/phải
            stretch: go.GraphObject.Horizontal, // Kéo giãn theo chiều ngang
            wrap: go.TextBlock.WrapFit, // Cho phép text wrap nếu quá dài
          }),

          // === THANH TIẾN ĐỘ ===
          $(
            go.Panel,
            'Table', // Panel dạng bảng để sắp xếp chính xác
            {
              margin: new go.Margin(8, 12), // Thêm margin để tách biệt với phần trên dưới
              alignment: go.Spot.Left,
              stretch: go.GraphObject.Horizontal,
              defaultRowSeparatorStroke: null,
              defaultColumnSeparatorStroke: null,
            },
            // Panel chứa thanh tiến độ để kiểm soát kích thước
            $(
              go.Panel,
              'Auto',
              {
                row: 0,
                column: 0,
                width: 240, // Chiều rộng cố định cho thanh tiến độ
                height: 8, // Chiều cao cố định
                alignment: go.Spot.Left, // Đảm bảo căn trái
              },
              // --- NỀN THANH TIẾN ĐỘ ---
              $(go.Shape, 'RoundedRectangle', {
                fill: '#EBEBEB', // Màu nền xám nhạt
                stroke: null, // Không có viền
                parameter1: 4, // Bo tròn 4px
                width: 240, // Chiều rộng cố định
                height: 8, // Chiều cao cố định
              }),
              // --- THANH TIẾN ĐỘ CHÍNH ---
              $(
                go.Shape,
                'RoundedRectangle',
                {
                  height: 8, // Chiều cao 8px
                  fill: '#256AEB', // Màu xanh đậm
                  stroke: null, // Không có viền
                  parameter1: 4, // Bo tròn 4px
                  alignment: go.Spot.Left, // Căn trái
                },
                // Binding chiều rộng dựa trên % tiến độ
                new go.Binding('width', 'progress', function (p) {
                  // Tính toán % của chiều rộng cố định
                  return (p * 240) / 100; // Tính theo tỷ lệ phần trăm thực tế
                }),
              ),
            ),
            // --- PHẦN TRĂM HOÀN THÀNH ---
            $(
              go.TextBlock,
              {
                row: 0, // Cùng hàng với thanh tiến độ
                column: 1, // Đặt ở cột riêng biệt
                alignment: go.Spot.Left, // Căn trái
                font: '500 14px Roboto', // Font chữ đậm hơn
                stroke: '#303030', // Màu chữ gần đen
                margin: new go.Margin(0, 0, 0, 8), // Thêm margin bên trái
                alignmentFocus: go.Spot.Center, // Căn giữa theo chiều dọc
              },
              // Binding text hiển thị % tiến độ
              new go.Binding('text', 'progress', function (p) {
                return p + '%';
              }),
            ),
          ),
        ),

        // === FOOTER - PHẦN CHÂN NODE ===
        $(
          go.Panel,
          'Table', // Chuyển từ Horizontal sang Table để kiểm soát tốt hơn vị trí các phần tử
          {
            alignment: go.Spot.Bottom,
            alignmentFocus: go.Spot.Bottom,
            stretch: go.GraphObject.Horizontal,
            padding: new go.Margin(0, 12), // Thêm padding hai bên
            background: 'white',
            defaultRowSeparatorStroke: null,
            defaultColumnSeparatorStroke: null,
          },
          // Binding để ẩn/hiện phần footer dựa trên parent
          new go.Binding('visible', 'parent', function (parent) {
            return parent !== ''; // Chỉ hiển thị khi parent không rỗng
          }),
          // --- AVATAR ---
          $(
            go.Panel,
            'Auto',
            {
              alignment: go.Spot.Left,
              column: 1, // Chuyển sang cột thứ hai
              margin: new go.Margin(0, 0, 0, 0),
            },
            $(go.Shape, 'RoundedRectangle', {
              parameter1: 6,
              fill: '#F1F1F1',
              stroke: null,
            }),
            $(go.TextBlock, new go.Binding('text', 'dueDate'), {
              font: '400 12px Roboto',
              stroke: '#303030',
              margin: new go.Margin(0, 8), // Thêm padding trong text
            }),
          ),
          // --- TRẠNG THÁI ---
          $(
            go.Panel,
            'Auto',
            {
              margin: new go.Margin(0, 0, 0, 0),
              alignment: go.Spot.Right,
              column: 2, // Đặt ở cột thứ ba
            },
            $(
              go.Shape,
              'RoundedRectangle',
              {
                parameter1: 6,
                stroke: null,
              },
              // Binding màu nền dựa trên viewStatus
              new go.Binding('fill', 'viewStatus', function (status) {
                const statusConfig = STATUS_PROGRESS_TAG.find((item) => item.label === status);
                return statusConfig ? statusConfig.backgroundColor : '#DCECFE'; // Màu mặc định
              }),
            ),
            $(
              go.TextBlock,
              new go.Binding('text', '', function (data) {
                return data.viewStatus;
              }),
              {
                font: '500 12px Roboto',
                margin: new go.Margin(2, 8), // Thêm padding trong text
              },
              // Binding màu chữ dựa trên viewStatus
              new go.Binding('stroke', 'viewStatus', function (status) {
                const statusConfig = STATUS_PROGRESS_TAG.find((item) => item.label === status);
                return statusConfig ? statusConfig.textColor : '#1E45AF'; // Màu mặc định
              }),
            ),
          ),
        ),
      ),
    );

    // ================ LINK TEMPLATE ================
    // Định nghĩa cách hiển thị đường kết nối giữa các node
    this.diagram.linkTemplate = $(
      go.Link, // Đối tượng Link cơ bản
      {
        routing: go.Link.Orthogonal, // Kiểu định tuyến vuông góc (góc 90 độ)
        corner: 8, // Bo tròn ở góc với bán kính 8px
        relinkableFrom: false, // Không cho phép kéo đổi điểm đầu
        relinkableTo: false, // Không cho phép kéo đổi điểm cuối
        selectable: false, // Không cho phép chọn đường kết nối
        fromShortLength: 4, // Khoảng cách từ node đến điểm bắt đầu
        toShortLength: 4, // Khoảng cách từ node đến điểm kết thúc
      },
      $(go.Shape, {
        // Hình dạng của đường kết nối
        strokeWidth: 0.75, // Độ dày 0.75px - mỏng hơn
        stroke: '#616161', // Màu xám đậm
        strokeCap: 'round', // Làm tròn đầu mút của đường
        strokeJoin: 'round', // Điều chỉnh độ bo ở các góc nối
      }),
    );
  }

  private loadDiagramData(): void {
    if (this.diagram) {
      // Khởi tạo mô hình dữ liệu dạng cây
      const model = new go.TreeModel();
      // Gán dữ liệu từ Input
      model.nodeDataArray = this.goalData;
      // Gán mô hình vào diagram
      this.diagram.model = model;

      // Tự động điều chỉnh để hiển thị toàn bộ cây
      this.diagram.zoomToFit();
      // Căn giữa nội dung
      this.diagram.contentAlignment = go.Spot.Center;

      // Cập nhật tỷ lệ phần trăm ban đầu
      this.updateZoomPercentage();
    }
  }
}
