import { ApplicationConfig, provideZoneChangeDetection } from '@angular/core';
import { PreloadAllModules, provideRouter, withPreloading } from '@angular/router';
import { remoteRoutes } from './app.routes';
import { provideStore } from '@ngrx/store';
import { provideEffects } from '@ngrx/effects';
import { provideRouterStore } from '@ngrx/router-store';
import { EvaTemplateEffects } from './pages/eva-settings/eva-template/store/eva-template.effects';

export const appConfig: ApplicationConfig = {
  providers: [
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(remoteRoutes, withPreloading(PreloadAllModules)),
    provideStore(),
    provideEffects([EvaTemplateEffects]),
    provideRouterStore(),
  ],
};
