import { AbstractListviewNewBuilder } from '../../../models/abstract-listview-new-builder.model';
import { VnrListviewNewOptionChangeColumnBuilder } from './vnr-listview-new-option-change-column-builder.model';
import { VnrListviewNewOptionBuilder } from './vnr-listview-new-option-builder.model';

export class VnrListviewNewBuilder extends AbstractListviewNewBuilder<
  VnrListviewNewBuilder,
  VnrListviewNewOptionBuilder,
  VnrListviewNewOptionChangeColumnBuilder
> {
  constructor(builder?: VnrListviewNewBuilder) {
    super();
    if (!builder) {
      builder = {};
    }
    Object.assign(this, builder);
    this.options = new VnrListviewNewOptionBuilder(builder?.options);
    this.optionChangeColumn = new VnrListviewNewOptionChangeColumnBuilder(
      builder?.optionChangeColumn,
    );
  }

  builder?(builder?: VnrListviewNewBuilder) {
    Object.assign(this, builder);
    this.options = new VnrListviewNewOptionBuilder(builder?.options);
    this.optionChangeColumn = new VnrListviewNewOptionChangeColumnBuilder(
      builder?.optionChangeColumn,
    );
  }
}
