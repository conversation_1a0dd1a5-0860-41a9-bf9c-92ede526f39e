:host {
  display: block;
  background-color: #f0f2f5;
  height: 100%;

  .drawer-body {
    padding: 16px;
    height: calc(100% - 57px); // 57px is the height of drawer-footer
    overflow-y: auto;
  }

  .section-container {
    background: #fff;
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .section-title {
    font-size: 14px;
    font-weight: 700;
    margin-bottom: 16px;
    color: #262626;
  }

  .info-grid {
    display: grid;
    gap: 16px;
    grid-template-columns: auto 1fr;
    align-items: center;
    .info-label {
      font-size: 14px;
      font-weight: 500;
      color: #616161;
    }
  }

  .form-grid {
    display: grid;
    gap: 16px;
    grid-template-columns: 1fr;
  }

  .form-item {
    display: flex;
    flex-direction: column;
    gap: 4px;

    label {
      font-size: 14px;
      font-weight: 500;
      color: #616161;
    }

    span {
      font-size: 14px;
      color: #303030;
      font-weight: 500;
    }

    .required::after {
      content: ' *';
      color: red;
    }
  }

  .drawer-footer {
    position: sticky;
    bottom: 0;
    width: 100%;
    border-top: 1px solid #e9e9e9;
    padding: 12px 16px;
    background: #fff;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    z-index: 10;
  }
}
