import { Component, OnInit, ViewChild, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  VnrTreelistNewBuilder,
  VnrToolbarNewBuilder,
  VnrTreelistNewComponent,
  VnrToolbarNewComponent,
} from '@hrm-frontend-workspace/vnr-module';
import {
  VnrButtonComponent,
  VnrLettersAvatarComponent,
  VnrTagComponent,
} from '@hrm-frontend-workspace/ui';
import {
  overviewColumns,
  overviewDataSource,
  overviewFilter,
  STATUS_PROGRESS_TAG,
  STATUS_TAG,
} from '../../../../data/overview.data';
import { NzSegmentedModule } from 'ng-zorro-antd/segmented';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { TargetFormatPipe } from '../../../../../shared/pipe/target-format.pipe';

@Component({
  selector: 'app-grid',
  imports: [
    VnrTreelistNewComponent,
    VnrToolbarNewComponent,
    CommonModule,
    VnrTreelistNewComponent,
    VnrToolbarNewComponent,
    VnrButtonComponent,
    NzSegmentedModule,
    VnrLettersAvatarComponent,
    VnrTagComponent,
    TargetFormatPipe,
    NzTagModule,
  ],
  templateUrl: './grid.component.html',
  styleUrl: './grid.component.scss',
})
export class GridComponent implements OnInit, AfterViewInit {
  @ViewChild('vnrTreeList', { static: false }) vnrTreeList: VnrTreelistNewComponent;

  private _storeName = 'eva_sp_get_GoalResult';
  private _screenName = 'GoalResult';

  timeFilterOptions = ['Tháng', 'Quý', 'Nửa năm đầu', 'Nửa năm sau'];

  builderToolbar: VnrToolbarNewBuilder;
  builderTreeList: VnrTreelistNewBuilder;
  protected isSupperAdmin = true;
  protected gridName = 'Eva_GoalResult_Gird';
  protected columns = [];
  protected dataLocal = [];

  ngOnInit(): void {
    this.builderTreeListComponent();
    this.builderToolbarComponent();
  }

  ngAfterViewInit(): void {
    this.updateToolbarWithGridReference();
  }

  handleTimeFilterChange(e: string | number): void {
    if (e === 'Quý' || e === 'Tháng' || e === 'Nửa năm đầu' || e === 'Nửa năm sau') {
      const year = new Date().getFullYear();
      const idxAttachment = overviewColumns.findIndex((col) => col.Name === 'Attachment');
      const idxTotalTarget = overviewColumns.findIndex((col) => col.Name === 'TotalTarget');
      let dynamicColumns = [];
      if (e === 'Quý') {
        dynamicColumns = this.createQuarterColumns(year);
        this.dataLocal = this.dataLocal.map((item) => {
          const quarters = this.splitDataByQuarter(item);
          return {
            ...item,
            ...quarters,
          };
        });
      } else if (e === 'Tháng') {
        dynamicColumns = this.createColumns(year);
        this.dataLocal = this.dataLocal.map((item) => {
          const months = this.splitDataByMonth(item);
          return {
            ...item,
            ...months,
          };
        });
      } else if (e === 'Nửa năm đầu') {
        dynamicColumns = Array.from({ length: 6 }, (_, i) => ({
          Name: `Month${i + 1}`,
          HeaderName: `Tháng ${i + 1}/${year}`,
          HeaderKey: `Tháng ${i + 1}/${year}`,
          DisplayName: `Tháng ${i + 1}/${year}`,
          Class: 'grid__header--bordered',
          Width: 150,
        }));
        this.dataLocal = this.dataLocal.map((item) => {
          const months = this.splitDataByHalfYear(item, true);
          return {
            ...item,
            ...months,
          };
        });
      } else if (e === 'Nửa năm sau') {
        dynamicColumns = Array.from({ length: 6 }, (_, i) => ({
          Name: `Month${i + 7}`,
          HeaderName: `Tháng ${i + 7}/${year}`,
          HeaderKey: `Tháng ${i + 7}/${year}`,
          DisplayName: `Tháng ${i + 7}/${year}`,
          Class: 'grid__header--bordered',
          Width: 150,
        }));
        this.dataLocal = this.dataLocal.map((item) => {
          const months = this.splitDataByHalfYear(item, false);
          return {
            ...item,
            ...months,
          };
        });
      }
      this.columns = [
        ...overviewColumns.slice(0, idxAttachment + 1),
        ...dynamicColumns,
        ...overviewColumns.slice(idxTotalTarget),
      ];
    }
  }

  private updateToolbarWithGridReference(): void {
    if (this.builderToolbar && this.vnrTreeList) {
      this.builderToolbar.gridRef = this.vnrTreeList;
    }
  }

  private builderToolbarComponent() {
    this.builderToolbar = new VnrToolbarNewBuilder({
      gridName: this.gridName,
      isSupperAdmin: this.isSupperAdmin,
      storeName: this._storeName,
      screenName: this._screenName,
      isShowConfig: true,
      options: {
        configButtonChangeColumn: { isShow: true },
        configButtonExport: { isShowBtnExcelAll: true },
        configQuickSearch: {
          isShow: true,
        },
        configFilterAdvanceQuick: {
          isShow: true,
          components: overviewFilter(),
          keyConfig: 'GoalResult_FilterAdvanceSeting',
          isShowBtnAdvance: false,
        },
      },
    });
  }

  private builderTreeListComponent() {
    // Random dữ liệu 12 tháng cho mỗi item
    const year = new Date().getFullYear();
    this.dataLocal = overviewDataSource.map((item) => {
      const months = this.splitDataByMonth(item);
      return {
        ...item,
        ...months,
      };
    });

    // Tạo columns: chèn 12 tháng vào giữa Attachment và TotalTarget
    const idxAttachment = overviewColumns.findIndex((col) => col.Name === 'Attachment');
    const idxTotalTarget = overviewColumns.findIndex((col) => col.Name === 'TotalTarget');
    const monthColumns = this.createColumns(year);
    this.columns = [
      ...overviewColumns.slice(0, idxAttachment + 1),
      ...monthColumns,
      ...overviewColumns.slice(idxTotalTarget),
    ];

    this.builderTreeList = new VnrTreelistNewBuilder({
      options: {
        configHeightGrid: {
          isAllowCalcRowHeight: true,
        },
        displayField: 'GoalName',
        configSelectable: {
          columnKey: 'ID',
          groupKey: 'ParentID',
        },
        configCommandColumn: {
          isEnabledMenuAction: false,
        },
        configShowHide: {
          isShowViewDetail: false,
          isPageExpand: false,
          isShowDelete: false,
          isShowEdit: false,
        },
      },
    });
  }

  public getStatusProgressColor(dataItem: any) {
    // Dựa vào CompletionRate để tính toán: Nếu Mức độ hoàn thành lớn hơn 100% thì "Vượt chỉ tiêu", nhỏ hơn 100% thì "Không đạt", bằng 100% thì "Đạt chỉ tiêu"
    if (dataItem['ResultStatus'] === 'Đã huỷ') {
      return STATUS_PROGRESS_TAG[3];
    } else if (dataItem['ResultStatus'] === 'Tạm dừng') {
      return STATUS_PROGRESS_TAG[4];
    }

    const completionRate = (dataItem['TotalActual'] / dataItem['TotalTarget']) * 100;
    if (completionRate > 100) {
      return STATUS_PROGRESS_TAG[0];
    } else if (completionRate > 0 && completionRate < 100) {
      return STATUS_PROGRESS_TAG[2];
    } else if (completionRate >= 100) {
      return STATUS_PROGRESS_TAG[1];
    } else {
      return STATUS_PROGRESS_TAG[4];
    }
  }

  public getStatusTag(dataItem: any) {
    if (dataItem['Status'] === 'Chờ xác nhận') {
      return STATUS_TAG[0];
    } else if (dataItem['Status'] === 'Đã huỷ') {
      return STATUS_TAG[1];
    } else if (dataItem['Status'] === 'Tạm dừng') {
      return STATUS_TAG[2];
    }
  }

  public splitDataByMonth(dataItem: any) {
    const total = Number(dataItem['TotalActual']) || 0;
    const months = this.randomSplitNumber(total, 12);
    const result: any = {};
    for (let i = 0; i < 12; i++) {
      result[`Month${i + 1}`] = months[i];
    }
    return result;
  }

  public splitDataByQuarter(dataItem: any) {
    const total = Number(dataItem['TotalActual']) || 0;
    const [q1, q2, q3, q4] = this.randomSplitNumber(total, 4);
    return {
      Quarter1: q1,
      Quarter2: q2,
      Quarter3: q3,
      Quarter4: q4,
    };
  }

  /**
   * Chia một số thành 4 phần ngẫu nhiên, tổng đúng bằng total
   */
  private randomSplitNumber(total: number, parts = 4): number[] {
    if (parts <= 1) return [total];
    const splits: number[] = [];
    let remain = total;
    for (let i = 0; i < parts - 1; i++) {
      // random số từ 0 đến remain - (số phần còn lại - 1)
      const max = remain - (parts - i - 1);
      const value = Math.floor(Math.random() * (max + 1));
      splits.push(value);
      remain -= value;
    }
    splits.push(remain);
    // Đảo vị trí để ngẫu nhiên hơn
    for (let i = splits.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [splits[i], splits[j]] = [splits[j], splits[i]];
    }
    return splits;
  }

  /**
   * Tạo columns cho view Quý
   */
  private createQuarterColumns(year: number) {
    return [
      {
        Name: 'Quarter1',
        HeaderName: `Quý 1/${year}`,
        HeaderKey: `Quý 1/${year}`,
        DisplayName: `Quý 1/${year}`,
        Class: 'grid__header--bordered',
        Width: 150,
      },
      {
        Name: 'Quarter2',
        HeaderName: `Quý 2/${year}`,
        HeaderKey: `Quý 2/${year}`,
        DisplayName: `Quý 2/${year}`,
        Class: 'grid__header--bordered',
        Width: 150,
      },
      {
        Name: 'Quarter3',
        HeaderName: `Quý 3/${year}`,
        HeaderKey: `Quý 3/${year}`,
        DisplayName: `Quý 3/${year}`,
        Class: 'grid__header--bordered',
        Width: 150,
      },
      {
        Name: 'Quarter4',
        HeaderName: `Quý 4/${year}`,
        HeaderKey: `Quý 4/${year}`,
        DisplayName: `Quý 4/${year}`,
        Class: 'grid__header--bordered',
        Width: 150,
      },
    ];
  }

  private createColumns(year: number) {
    return Array.from({ length: 12 }, (_, index) => ({
      Name: `Month${index + 1}`,
      HeaderName: `Tháng ${index + 1}/${year}`,
      HeaderKey: `Tháng ${index + 1}/${year}`,
      DisplayName: `Tháng ${index + 1}/${year}`,
      Class: 'grid__header--bordered',
      Width: 150,
    }));
  }

  /**
   * Chia TotalActual thành 6 phần cho nửa đầu/nửa cuối năm
   */
  public splitDataByHalfYear(dataItem: any, isFirstHalf: boolean) {
    const total = Number(dataItem['TotalActual']) || 0;
    const months = this.randomSplitNumber(total, 6);
    const result: any = {};
    for (let i = 0; i < 6; i++) {
      const monthIdx = isFirstHalf ? i + 1 : i + 7;
      result[`Month${monthIdx}`] = months[i];
    }
    return result;
  }

  /**
   * Tính toán phần trăm hoàn thành:
   * - Nếu < 0 thì trả về 0
   * - Nếu > 100 thì trả về giá trị thực tế (có thể lớn hơn 100)
   * - Nếu trong khoảng 0-100 thì trả về phần trăm
   * @param dataItem Dữ liệu item
   * @returns Phần trăm hoàn thành
   */
  public calculateCompletionRate(dataItem: any): number {
    const totalActual = dataItem['TotalActual'] || 0;
    const totalTarget = dataItem['TotalTarget'] || 0;

    // Nếu không có mục tiêu hoặc mục tiêu = 0, trả về 0
    if (!totalTarget || totalTarget <= 0) {
      return 0;
    }

    const percentage = (totalActual / totalTarget) * 100;
    if (percentage < 0) {
      return 0;
    }
    if (percentage > 100) {
      return percentage;
    }
    return percentage;
  }
}
