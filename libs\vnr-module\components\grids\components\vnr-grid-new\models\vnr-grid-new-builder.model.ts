import { boldIcon } from '@progress/kendo-svg-icons';
import { AbstractGridBuilder } from '../../../models/abstract-grid-builder.model';
import { VnrGridNewOptionChangeColumnBuilder } from './vnr-grid-new-option-change-column-builder.model';
import { VnrGridNewOptionBuilder } from './vnr-grid-new-option-builder.model';

export class VnrGridNewBuilder extends AbstractGridBuilder<
  VnrGridNewBuilder,
  VnrGridNewOptionBuilder,
  VnrGridNewOptionChangeColumnBuilder
> {
  constructor(builder?: VnrGridNewBuilder) {
    super();
    if (!builder) {
      builder = {};
    }
    Object.assign(this, builder);
    this.options = new VnrGridNewOptionBuilder(builder?.options);
    this.optionChangeColumn = new VnrGridNewOptionChangeColumnBuilder(builder?.optionChangeColumn);
  }

  builder?(builder?: VnrGridNewBuilder) {
    Object.assign(this, builder);
    this.options = new VnrGridNewOptionBuilder(builder?.options);
    this.optionChangeColumn = new VnrGridNewOptionChangeColumnBuilder(builder?.optionChangeColumn);
  }
}
