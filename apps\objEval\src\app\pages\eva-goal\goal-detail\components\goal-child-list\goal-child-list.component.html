<div class="d-flex justify-content-between align-items-center mb-2">
  <div class="ant-drawer-title"><PERSON><PERSON><PERSON> ti<PERSON> con</div>
  <div class="d-flex align-items-center">
    <vnr-button
      class="mr-2"
      [vnrType]="'primary'"
      (click)="vnrGridChild.editAllHandler(vnrGridChild.grid)"
      [vnrText]="'common.edit'"
      *ngIf="!vnrGridChild?.isEdited"
    >
    </vnr-button>
    <vnr-button
      class="mr-2"
      [vnrType]="'default'"
      [vnrText]="'common.button.cancel'"
      (click)="vnrGridChild.cancelAllHandler(vnrGridChild.grid)"
      *ngIf="vnrGridChild?.isEdited"
    >
    </vnr-button>
    <vnr-button
      class="mr-2"
      [vnrType]="'success'"
      [vnrText]="'common.button.save'"
      (click)="vnrGridChild.saveAllHandler(vnrGridChild.grid)"
      *ngIf="vnrGridChild?.isEdited"
    >
    </vnr-button>
    <vnr-button
      [vnrText]="'objEval.GoalPeriod.Allocation'"
      [vnrType]="'primary'"
      (vnrClick)="onOpenAllocationGoalChildModal($event)"
    ></vnr-button>
  </div>
</div>
<vnr-grid-new-Edit-Inline
  #vnrGridChild
  [builder]="builderGrid"
  [gridName]="gridName"
  [dataLocal]="dataLocal"
  [columns]="columns"
  [isSupperAdmin]="isSupperAdmin"
  [defaultColumnTemplate]="tplCustomTemplateByColumn"
  [rowActionsTemplate]="tplCustomBtn"
>
</vnr-grid-new-Edit-Inline>
<ng-template #tplCustomBtn let-dataItem let-column="columnItem" let-field="field">
  <ng-container *ngIf="dataItem['Status'] === 'E_WAITING_APPROVE'">
    <vnr-button
      class="mr-1"
      [vnrType]="'success'"
      [vnrIcon]="'check'"
      [vnrSize]="'small'"
      (vnrClick)="onApprove(dataItem)"
    ></vnr-button>
    <vnr-button
      class="mr-1"
      [vnrType]="'warning'"
      [vnrIcon]="'edit'"
      [vnrSize]="'small'"
      (vnrClick)="onEdit(dataItem)"
    ></vnr-button>
    <vnr-button
      class="mr-1"
      [vnrType]="'danger'"
      [vnrIcon]="'close'"
      [vnrSize]="'small'"
      (vnrClick)="onReject(dataItem)"
    ></vnr-button>
  </ng-container>
</ng-template>
<ng-template #tplCustomTemplateByColumn let-dataItem let-column="columnItem" let-field="field">
  <ng-container [ngSwitch]="column['Name']">
    <span *ngSwitchCase="'Representative'">
      <div class="d-flex align-items-center">
        <app-vnr-letters-avatar
          [avatarName]="dataItem[column['Name']]"
          [circular]="true"
          [width]="32"
          [src]="dataItem && dataItem.ImagePathProcess"
          class="mr-2"
        ></app-vnr-letters-avatar>
        <span>{{ dataItem[column['Name']] }}</span>
      </div>
    </span>
    <span *ngSwitchCase="'Status'">
      <vnr-tag
        [vnrColor]="statusColorMap[dataItem['Status']] || 'default'"
        [vnrTitle]="statusTextMap[dataItem['Status']]"
      ></vnr-tag>
    </span>
    <span *ngSwitchDefault>
      {{ dataItem[column['Name']] }}
    </span>
  </ng-container>
</ng-template>
