<form [formGroup]="form">
  <vnr-radiobutton
    [builder]="departmentRadioBuilder"
    [(ngModel)]="allocationType"
    (ngModelChange)="onChangeAllocationType($event)"
    formControlName="allocationType"
  ></vnr-radiobutton>
  <vnr-multiselect
    *ngIf="allocationType === 'department'"
    [builder]="builderDepartment"
    formControlName="department"
  ></vnr-multiselect>
  <vnr-multiselect
    *ngIf="allocationType === 'employee'"
    [builder]="builderEmployee"
    formControlName="employee"
  ></vnr-multiselect>
</form>
<app-allocation-method
  [gridRef]="gridAllocationDepartment"
  (changeAllocationMethod)="onChangeAllocationMethod($event)"
  (changeAllocationCycle)="onChangeAllocationCycle($event)"
></app-allocation-method>
<div class="d-flex align-items-center gap-3 mt-4 mb-3">
  <div class="view-label mr-2">Xem theo:</div>
  <div class="horizontal-menu d-flex gap-2">
    <vnr-button
      class="menu-item mr-2"
      (vnrClick)="onChangeAllocationView('department-allocation')"
      [vnrType]="allocationView === 'department-allocation' ? 'primary' : 'default'"
      [vnrText]="'Phân rã theo Phòng ban'"
    >
    </vnr-button>
    <vnr-button
      class="menu-item mr-2"
      (vnrClick)="onChangeAllocationView('target-allocation')"
      [vnrType]="allocationView === 'target-allocation' ? 'primary' : 'default'"
      [vnrText]="'Phân rã theo Mục tiêu'"
    >
    </vnr-button>
    <vnr-button
      class="menu-item mr-2"
      (vnrClick)="onChangeAllocationView('department-group')"
      [vnrType]="allocationView === 'department-group' ? 'primary' : 'default'"
      [vnrText]="'Nhóm theo phòng ban'"
    >
    </vnr-button>
    <vnr-button
      class="menu-item mr-2"
      (vnrClick)="onChangeAllocationView('target-group')"
      [vnrType]="allocationView === 'target-group' ? 'primary' : 'default'"
      [vnrText]="'Nhóm theo Mục tiêu'"
    >
    </vnr-button>
  </div>
</div>
<vnr-grid-new-Edit-Inline
  *ngIf="showGrid"
  class="grid-new"
  #gridAllocationDepartment
  [builder]="builderGrid"
  [dataLocal]="dataLocal"
  [columns]="columns"
  [gridName]="gridName"
  [defaultColumnTemplate]="tplCustomTemplateByColumn"
></vnr-grid-new-Edit-Inline>

<ng-template #tplCustomTemplateByColumn let-dataItem let-column="columnItem" let-field="field">
  <ng-container [ngSwitch]="column['Name']">
    <span class="cursor-pointer" *ngSwitchCase="'AllocationMethod'">
      _
      <vnr-button
        *ngIf="allocationMethod === 'formula'"
        class="border-0"
        [vnrIcon]="'edit'"
        [vnrType]="'default'"
        [vnrSize]="'small'"
        (vnrClick)="onOpenFormulaConfig()"
      ></vnr-button>
    </span>
    <span *ngSwitchCase="'TotalTarget'">
      {{ dataItem['TotalTarget'] | targetFormat : dataItem['Unit'] }}
    </span>
    <span *ngSwitchCase="'DoneTarget'">
      {{ dataItem['DoneTarget'] | targetFormat : dataItem['Unit'] }}
    </span>
    <span *ngSwitchDefault>
      {{ dataItem[column['Name']] }}
    </span>
  </ng-container>
</ng-template>
