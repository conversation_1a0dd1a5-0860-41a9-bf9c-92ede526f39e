import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { VnrInputsModule } from '@hrm-frontend-workspace/vnr-module';
import { StoreModule } from '@ngrx/store';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { FormBuilderApi } from './eva-create-review-form/api/form-builder.api';
import { GenericSectionEditorComponent } from './eva-create-review-form/components/section-editors/generic-section-editor/generic-section-editor.component';
import { FormBuilderFacade } from './eva-create-review-form/facade/form-builder.facade';
import { FormBuilderState } from './eva-create-review-form/state/form-builder.state';
import { EvaTemplateApi } from './eva-evaluation-form-list/api/eva-template.api';
import { EvaTemplateFacade } from './eva-evaluation-form-list/facade/eva-template.facade';
import { EvaTemplateState } from './eva-evaluation-form-list/state/eva-template.state';
import { EvaTemplateRoutesModule } from './eva-template.routes';
import { EvaTemplateEffects } from './store/eva-template.effects';
import * as fromEvaTemplate from './store/eva-template.reducer';

// Order providers to ensure proper dependency injection
const PROVIDERS = [
  // States first
  FormBuilderState,
  EvaTemplateState,
  // Then facades
  FormBuilderFacade,
  EvaTemplateFacade,
];

const PRIVATE_PROVIDERS = [
  // APIs last
  FormBuilderApi,
  EvaTemplateApi,
  EvaTemplateEffects,
];

@NgModule({
  imports: [
    CommonModule,
    EvaTemplateRoutesModule,
    NzTabsModule,
    VnrInputsModule,
    GenericSectionEditorComponent,
    StoreModule.forFeature(fromEvaTemplate.evaTemplateFeatureKey, fromEvaTemplate.reducer),
    // EffectsModule.forFeature([EvaTemplateEffects]),
  ],
  providers: [...PROVIDERS, ...PRIVATE_PROVIDERS],
})
export class EvaTemplateModule {}
