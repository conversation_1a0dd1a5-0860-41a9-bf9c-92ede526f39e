import { ChangeDetectionStrategy, Component, OnDestroy, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TabFilterSchema, SharedImportsModule } from '@hrm-frontend-workspace/ui';
import { ProgressOverviewComponent } from '../components/progress-overview/progress-overview.component';
import { ProgressCheckinComponent } from '../components/progress-checkin/progress-checkin.component';
import { ProgressScheduleComponent } from '../components/progress-schedule/progress-schedule.component';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import {
  VNR_DATE_MODE,
  VnrComboBoxBuilder,
  VnrDatePickerBuilder,
} from '@hrm-frontend-workspace/vnr-module';
import { TranslateService } from '@ngx-translate/core';
import { Subject, takeUntil } from 'rxjs';
import { NzTabsModule, NzTabChangeEvent } from 'ng-zorro-antd/tabs';

@Component({
  selector: 'app-goal-progress',
  templateUrl: './goal-progress.component.html',
  styleUrls: ['./goal-progress.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [
    CommonModule,
    ProgressOverviewComponent,
    ProgressCheckinComponent,
    ProgressScheduleComponent,
    SharedImportsModule,
    ReactiveFormsModule,
    NzTabsModule,
  ],
})
export class GoalProgressComponent implements OnInit, OnDestroy {
  selectedTab = 0;

  tabs: (TabFilterSchema & { key: string })[] = [
    {
      id: 0,
      title: 'Tổng quan',
      key: 'overview',
      show: true,
    },
    {
      id: 1,
      title: 'Check-in',
      key: 'checkin',
      show: true,
    },
    {
      id: 2,
      title: 'Lịch định kỳ',
      key: 'schedule',
      show: true,
    },
  ];

  protected form: FormGroup;
  protected datePickerBuilder: VnrDatePickerBuilder = new VnrDatePickerBuilder();
  protected quarterBuilder: VnrComboBoxBuilder = new VnrComboBoxBuilder();

  private destroy$ = new Subject<void>();

  constructor(private fb: FormBuilder, private translate: TranslateService) {
    this.form = this.fb.group({
      time: [new Date()],
      quarter: ['month'],
    });
  }

  ngOnInit(): void {
    this.initBuilders();
    this.setupFormListeners();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onTabChange(event: NzTabChangeEvent): void {
    if (typeof event.index === 'number') {
      this.selectedTab = event.index;
    }
  }

  private initBuilders(): void {
    this.updateDatePickerFormat('month');

    this.quarterBuilder.builder({
      textField: 'label',
      valueField: 'value',
      dataSource: [
        { value: 'month', label: this.translate.instant('objEval.GoalPeriod.Month') },
        { value: 'quarter', label: this.translate.instant('objEval.GoalPeriod.Quarter') },
        { value: 'year', label: this.translate.instant('objEval.GoalPeriod.Year') },
      ],
    });

    this.datePickerBuilder.builder({
      options: {
        mode: VNR_DATE_MODE.E_MONTH,
      },
    });
  }

  private setupFormListeners(): void {
    this.form.valueChanges.pipe(takeUntil(this.destroy$)).subscribe((value) => {
      // Handle filter changes
      console.log(value);
    });

    this.form
      .get('quarter')
      .valueChanges.pipe(takeUntil(this.destroy$))
      .subscribe((value) => {
        this.updateDatePickerFormat(value);
      });
  }

  private updateDatePickerFormat(timeType: string): void {
    switch (timeType) {
      case 'month':
        this.datePickerBuilder.options.mode = VNR_DATE_MODE.E_MONTH;
        break;
      case 'quarter':
        this.datePickerBuilder.options.mode = VNR_DATE_MODE.E_QUARTER;
        break;
      case 'year':
        this.datePickerBuilder.options.mode = VNR_DATE_MODE.E_YEAR;
        break;
      default:
        this.datePickerBuilder.options.mode = VNR_DATE_MODE.E_MONTH;
    }
  }
}
