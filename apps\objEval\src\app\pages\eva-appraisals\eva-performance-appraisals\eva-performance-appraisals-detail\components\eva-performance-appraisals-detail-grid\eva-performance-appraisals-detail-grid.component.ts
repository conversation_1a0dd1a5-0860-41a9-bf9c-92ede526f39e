import { CommonModule, NgIf } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  Inject,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import {
  FormsModule,
  ReactiveFormsModule,
  UntypedFormBuilder,
  UntypedFormGroup,
} from '@angular/forms';
import { CommonService } from '@hrm-frontend-workspace/core';
import {
  VnrButtonModule,
  VnrLettersAvatarComponent,
  VnrTagComponent,
} from '@hrm-frontend-workspace/ui';
import {
  IVnrModule_Token,
  VnrButtonFactory,
  VnrButtonNewBuilder,
  VnrButtonNewComponent,
  VnrComboBoxBuilder,
  VnrComboBoxComponent,
  VnrFilterAdvanceQuickBuilder,
  VnrGridNewBuilder,
  VnrGridNewComponent,
  VNRMODULE_TOKEN,
  VnrSelectsModule,
  VnrToolbarNewBuilder,
  VnrToolbarNewComponent,
  VnrTreeViewBuilder,
  VnrTreeViewComponent,
  vnrUtilities,
} from '@hrm-frontend-workspace/vnr-module';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { ObjSharedModule } from 'apps/objEval/src/app/shared';
import { NzModalModule, NzModalService } from 'ng-zorro-antd/modal';
import { gridDefineColumns, gridDefineGroupColumns } from '../../data/column.data';
import { gridDefineDataFilterQuick } from '../../data/data-filter-advance';
import { performanceAppraisalsDetailDataSource } from '../../data/datasource.data';
import { performanceAppraisalDetailStatusFormat } from '../../data/eva-performance-appraisals-detail.data';
import { PerformanceAppraisalsDetailTabGridData } from '../../data/list-tabs-filter.data';
import { EvaPerformanceAppraisalsDetailFacade } from '../../facade/eva-performance-appraisals-detail.facade';
import { NzDrawerModule, NzDrawerService } from 'ng-zorro-antd/drawer';
import { EvaluationFormDetailsComponent } from '../evaluation-form-details/evaluation-form-details.component';
import {
  appraisalsKpiDepartmentDataSource,
  appraisalsKpiPositionDataSource,
} from '../../../../appraisals/appraisals-kpi/data/datasource-component.data';
import { GridViewModeComponent } from '../../../../../../shared/components/grid-view-mode/grid-view-mode.component';
import { cloneDeep } from 'lodash';
import { EvaluationFormAddEmployeeComponent } from '../evaluation-form-add-employee/evaluation-form-add-employee.component';
import { Router, ActivatedRoute } from '@angular/router';

@Component({
  selector: 'eva-performance-appraisals-detail-grid',
  templateUrl: './eva-performance-appraisals-detail-grid.component.html',
  styleUrls: ['./eva-performance-appraisals-detail-grid.component.scss'],
  imports: [
    FormsModule,
    ReactiveFormsModule,
    CommonModule,
    NgIf,
    VnrToolbarNewComponent,
    VnrGridNewComponent,
    VnrTagComponent,
    TranslateModule,
    VnrButtonNewComponent,
    NzModalModule,
    VnrLettersAvatarComponent,
    VnrSelectsModule,
    VnrTreeViewComponent,
    VnrComboBoxComponent,
    VnrButtonModule,
    ObjSharedModule,
    NzDrawerModule,
    GridViewModeComponent,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
})
export class EvaPerformanceAppraisalsDetailGridComponent implements OnInit, OnChanges {
  @Input() dataDetail: any;
  @ViewChild('vnrGrid', { static: true }) gridControl: VnrGridNewComponent;
  @ViewChild('templateStatus', { static: true }) private _templateStatus: TemplateRef<any>;

  private _dataFormSearch: any = {};
  protected statusFormat = performanceAppraisalDetailStatusFormat;
  protected gridName = 'eva-performance-appraisals-detail-grid';
  private _screenName = 'eva-performance-appraisals-detail-grid';
  protected selectedItem = [];
  protected formGroup: UntypedFormGroup = this._formBuider.group({
    department: [''],
    position: [''],
  });
  protected listColumnTemplates: {};
  protected builderGrid: VnrGridNewBuilder;
  protected builderToolbar: VnrToolbarNewBuilder;
  private _btnFactory: VnrButtonFactory = VnrButtonFactory.init();
  protected builderButtonViewDetail: VnrButtonNewBuilder;
  protected builderButtonCustom: VnrButtonNewBuilder;
  protected builderOrg: VnrTreeViewBuilder = new VnrTreeViewBuilder();
  protected builderPosition: VnrComboBoxBuilder = new VnrComboBoxBuilder();
  protected tabDefault = 0;
  protected tabCount: any;
  protected selectedDataItem: any = [];
  protected detailStatusFilterData: any[] = PerformanceAppraisalsDetailTabGridData;
  protected dataDetailInput: any;
  protected builderBtnAddEmployee: VnrButtonNewBuilder;
  protected builderFilterAdvanceQuick: VnrFilterAdvanceQuickBuilder;
  protected selectedViewMode = 'list';
  protected dataLocal = performanceAppraisalsDetailDataSource;
  protected columns = gridDefineColumns;
  constructor(
    private _modalService: NzModalService,
    private _formBuider: UntypedFormBuilder,
    private _commonService: CommonService,
    private _evaPerformanceAppraisalsDetailFacade: EvaPerformanceAppraisalsDetailFacade,
    private drawerService: NzDrawerService,
    private translate: TranslateService,
    @Inject(VNRMODULE_TOKEN) private _vnrModule_Token: IVnrModule_Token,
    private _router: Router,
    private _route: ActivatedRoute,
  ) {}
  ngOnChanges(changes: SimpleChanges): void {
    const { dataDetail } = changes;
    if (dataDetail) {
      this.dataDetailInput = this.dataDetail;
    }
  }

  ngOnInit() {
    this.builderButton();
    this.builderFilterAdvanceQuickComponent();
    this.builderGridComponent();
    this.builderToolbarComponent();
  }

  private builderFilterAdvanceQuickComponent() {
    this.builderFilterAdvanceQuick = new VnrFilterAdvanceQuickBuilder({
      screenName: this._screenName,
      gridName: this.gridName,
      storeName: '',
      components: gridDefineDataFilterQuick(),
      options: {
        displayFilterDefault: 'objEval.EvaPerformanceAppraisals.quickFilter.Status.Label',
        isSupperAdmin: true,
        isShowBtnAdvance: false,
        isUseConfig: false,
        keyConfig: 'EvaPerformanceAppraisals',
        fullFilterComponents: [],
      },
    });
  }
  private builderButton() {
    this.builderButtonCustom = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: 'custom',
    });
    this.builderOrg.builder({
      label: '',
      placeholder: 'objEval.Appraisals.selectAllOrg',
      valueField: 'OrderNumber',
      textField: 'Name',
      childKey: 'ListChild',
      options: {
        hasFeedBack: false,
        checkable: true,
        maxTagCount: 1,
        scrollX: true,
      },
      dataSource: appraisalsKpiDepartmentDataSource,
      //serverSide: {
      //  urlApi: `${this._vnrModule_Token.apiConfig_Token?.apiUrl}/api/Cat_GetData/GetOrgTreeView`,
      //  method: 'get',
      //},
    });
    this.builderPosition.builder({
      label: '',
      placeholder: 'objEval.Appraisals.selectAllPosition',
      textField: 'PositionNameAndCode',
      valueField: 'ID',
      //serverSide: {
      //  urlApi: `${this._vnrModule_Token.apiConfig_Token?.apiUrl}/api/Att_GetData/GetMultiPosition`,
      //  method: 'GET',
      //  data: { text: '', TextField: 'PositionName' },
      //},
      dataSource: appraisalsKpiPositionDataSource,
      options: {
        allowValueObject: true,
        hasFeedBack: false,
      },
    });
    this.builderButtonViewDetail = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'objEval.EvaPerformanceAppraisalsDetail.btnViewDetail',
      options: {
        style: 'primary',
        icon: {
          fontIcon: 'eye',
        },
      },
    });
    this.builderBtnAddEmployee = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'objEval.EvaPerformanceAppraisals.btnAddEmployee',
      options: {
        style: 'primary',
        icon: {
          fontIcon: 'plus',
        },
      },
    });
  }

  private builderToolbarComponent() {
    this.builderToolbar = new VnrToolbarNewBuilder({
      isShowConfig: true,
      gridName: this.gridName,
      isSupperAdmin: true,
      gridRef: this.gridControl,
      permission: '',
      screenName: this._screenName,
      storeName: '',
      options: {
        configButtonChangeColumn: {
          isShow: true,
          isConfigMenuDisplay: true,
          isChangeColumnNew: true,
          isDisabled: false,
          configButtonSetting: {
            isShow: true,
            isDisabled: true,
          },
        },
        configButtonExport: {
          isShowBtnExcelAll: true,
          isShowBtnWord: true,
          isShowBtnExcelByTemplate: true,
        },
        configQuickSearch: {
          textPlaceHolder: this.translate.instant('grid.toolbar.enterKeywordToSearch'),
          searchKey: 'EmployeeName',
        },
        configFilterAdvanceQuick: {
          isShow: false,
        },
        isSetBackground: false,
        buttonMoreFlexDirection: 'inherit',
        isExpanded: false,
      },
    });
  }

  private builderGridComponent() {
    let columnDefine = gridDefineColumns;
    if (this.selectedViewMode === 'list') {
      columnDefine = cloneDeep(gridDefineColumns);
    } else {
      columnDefine = cloneDeep(gridDefineGroupColumns);
    }
    this.builderGrid = new VnrGridNewBuilder({
      options: {
        configIndexColumn: {
          isShow: true,
        },
        configHeightGrid: {
          isAllowCalcRowHeight: true,
        },
        configSelectable: {
          columnKey: 'ID',
        },
        configShowHide: {
          isPageExpand: false,
          isShowColumnGroupCheck: false,
          isShowEdit: false,
          isShowDelete: false,
        },
        configCommandColumn: {
          isEnabledMenuAction: true,
        },
        configGroupable: {
          isEnabled: false,
        },
      },
    });
  }

  protected getSelectedID($event: any) {
    this.selectedItem = $event;
  }

  public setDataFilter(data: any): void {
    this._dataFormSearch = data;
    this.gridControl.setDataFilter(data);
  }

  public reloadGridData(): void {
    this.gridControl.vnrReadGrid();
  }

  public getSelectedIDs() {
    return this.selectedItem || [];
  }

  protected onModelChangePosition($event: any) {
    this.setDataFilter({ PositionId: $event });
    this.reloadGridData();
  }
  protected onModelChangeOrg($event: any) {
    this.setDataFilter({ OrderNumber: $event?.join() });
    this.reloadGridData();
  }
  protected onChangeFitler($event: any) {
    // this.reloadGridData();
  }
  protected onSelectStatusChange(_tabFilter: any) {
    this.gridControl.vnrReadGrid();
  }

  /**
   * Kiểm tra trạng thái check dựa vào status
   * @param status Status cần kiểm tra
   * @returns true nếu status khác E_SUTMIT_TEMP, false nếu status là E_SUTMIT_TEMP
   */
  getCheckStatus(status: string): boolean {
    return status !== 'E_SUTMIT_TEMP';
  }

  onSelectEmployee(dataItem: any) {
    const appraisalId = dataItem?.PerformanceID;
    const queryParams: any = {
      ProfileID: dataItem?.ProfileID1 || '1',
    };
    this._router.navigate(
      ['/objEval/eva-appraisals/performance-appraisals/form-appraisal', appraisalId],
      {
        relativeTo: this._route,
        queryParamsHandling: 'merge',
        queryParams: queryParams,
      },
    );

    // const drawerRef = this.drawerService.create<EvaluationFormDetailsComponent>({
    //   nzTitle: this.translate.instant('objEval.EvaPerformanceAppraisals.evaluationFormDetails'),
    //   nzContent: EvaluationFormDetailsComponent,
    //   nzClosable: true,
    //   nzMaskClosable: false,
    //   nzMask: true,
    //   nzData: {
    //     dataItem: dataItem,
    //   },
    //   nzWrapClassName: 'crud-modal',
    //   nzWidth: '80vw',
    // });
    // drawerRef.afterClose.subscribe((drawer) => {
    //   if (drawer && !drawer.status) {
    //     // this.vnrGrid.vnrReadGrid();
    //     // this.reloadCount.emit(true);
    //   }
    // });
  }
  protected onChangesFilterQuick(data: any) {
    if (!data || data.length === 0) {
      this.gridControl.removeDataFilter(['Status']);
    } else {
      this.gridControl.setDataFilter(data);
    }
    this.gridControl.vnrReadGrid();
  }
  protected onAddEmployee() {
    const drawerRef = this.drawerService.create<EvaluationFormAddEmployeeComponent>({
      nzTitle: this.translate.instant('objEval.EvaPerformanceAppraisals.addEmployee.title'),
      nzContent: EvaluationFormAddEmployeeComponent,
      nzClosable: true,
      nzMaskClosable: false,
      nzMask: true,
      nzData: {},
      nzWrapClassName: 'crud-modal',
      nzWidth: '70vw',
    });
    drawerRef.afterClose.subscribe((drawer) => {
      if (drawer && !drawer.status) {
        // this.vnrGrid.vnrReadGrid();
        // this.reloadCount.emit(true);
      }
    });
  }
  protected onAdditional($event: any) {
    this._commonService.message({ message: 'Tính năng đang phát triển', type: 'success' });
  }
  protected onViewModeChange(value: string) {
    if (this.selectedViewMode !== value) {
      this.selectedViewMode = value;
      this.builderGridComponent();
    }
  }
  protected onClickFileName(value: string) {
    this._commonService.message({ message: 'Tính năng đang phát triển', type: 'success' });
    console.log(value);
  }
  protected onAddEvaluationForm() {
    this._commonService.message({ message: 'Tính năng đang phát triển', type: 'success' });
  }
}
