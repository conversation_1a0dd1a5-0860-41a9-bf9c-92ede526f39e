import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { UntypedFormBuilder } from '@angular/forms';
import { gridDefineDataSource } from '../../data/datasource.data';
import { gridDefineColumns } from '../../data/column.data';
import { TranslateModule } from '@ngx-translate/core';
import { NgIf, NgSwitch, NgSwitchCase, NgSwitchDefault } from '@angular/common';
import { gridDefineDataFilterQuick } from '../../data/data-filter-advance';
import { NzDrawerModule, NzDrawerService } from 'ng-zorro-antd/drawer';
import { VnrModuleModule } from '@hrm-frontend-workspace/ui';
import {
  VnrFilterAdvanceFullComponent,
  VnrFilterAdvanceFullBuilder,
  VnrGridNewComponent,
  VnrGridNewBuilder,
  VnrToolbarNewBuilder,
  VnrToolbarNewComponent,
  VnrButtonNewBuilder,
  VnrButtonNewComponent,
} from '@hrm-frontend-workspace/vnr-module';
import { cloneDeep } from 'lodash';
import { vnrUtilities } from '@hrm-frontend-workspace/common';
import { CategoryFormComponent } from '../category-form/category-form.component';
import { NzModalService } from 'ng-zorro-antd/modal';

@Component({
  selector: 'category-list',
  templateUrl: './category-list.component.html',
  styleUrls: ['./category-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    VnrToolbarNewComponent,
    VnrModuleModule,
    NgSwitch,
    NgSwitchCase,
    NgIf,
    NgSwitchDefault,
    VnrButtonNewComponent,
    NzDrawerModule,
    TranslateModule,
  ],
})
export class CategoryListComponent implements OnInit {
  @ViewChild('vnrGrid', { static: true }) gridControl: VnrGridNewComponent;
  @ViewChild('vnrFilterAdvanceFull') filterAdvanceFullControl: VnrFilterAdvanceFullComponent;
  @Output() actionApprove = new EventEmitter<any>();
  @Output() actionReject = new EventEmitter<any>();

  protected builderGrid: VnrGridNewBuilder;
  protected builderToolbar: VnrToolbarNewBuilder;
  protected builderFilterAdvanceFull: VnrFilterAdvanceFullBuilder;
  protected builderButtonApprove: VnrButtonNewBuilder;
  protected builderButtonReject: VnrButtonNewBuilder;
  protected builderButtonAddNew: VnrButtonNewBuilder;

  private _permission = 'New_PortalV3_Personal_Hre_Dependant';
  private _screenName = 'grid-new-list';
  private _storeName = 'hrm_hr_sp_get_ProfileTest';
  protected gridName = 'PortalNew_GridNewExample';
  protected isSupperAdmin = true;
  protected dataSource = gridDefineDataSource;
  protected columns = gridDefineColumns;
  constructor(
    private fb: UntypedFormBuilder,
    private drawerService: NzDrawerService,
    private modalServices: NzModalService,
  ) {}
  ngOnInit() {
    this.builderGridComponent();
    this.builderToolbarComponent();
    this.builderButton();
  }

  private builderButton() {
    this.builderButtonApprove = new VnrButtonNewBuilder({
      text: 'Approve',
      action: 'create',
    });
    this.builderButtonReject = new VnrButtonNewBuilder({
      text: 'Reject',
      action: 'delete',
    });
    this.builderButtonAddNew = new VnrButtonNewBuilder({
      text: 'Add New',
      action: 'create',
    });
  }

  private builderToolbarComponent() {
    this.builderToolbar = new VnrToolbarNewBuilder({
      isShowConfig: true,
      gridName: this.gridName,
      isSupperAdmin: this.isSupperAdmin,
      gridRef: this.gridControl,
      permission: this._permission,
      screenName: this._screenName,
      storeName: this._storeName,
      options: {
        configButtonChangeColumn: {
          isShow: true,
          isConfigMenuDisplay: true,
          isChangeColumnNew: true,
          isDisabled: false,
          configButtonSetting: {
            isShow: true,
            isDisabled: true,
          },
        },
        configButtonExport: {
          isShowBtnExcelAll: true,
          isShowBtnWord: true,
          isShowBtnExcelByTemplate: true,
        },
        configButtonDelete: {
          isShow: true,
          isDisabled: false,
        },
        configQuickSearch: {
          textPlaceHolder: 'Tìm kiếm theo mã, tên...',
          searchKey: 'ProfileName',
          isShowBtn: true,
        },
        configFilterAdvanceQuick: {
          isShow: true,
          components: gridDefineDataFilterQuick(),
          keyConfig: 'PortalNew_GridNewExample_Advance',
        },
        isSetBackground: true,
        buttonMoreFlexDirection: 'inherit',
        isExpanded: false,
      },
    });
  }

  private builderGridComponent() {
    this.builderGrid = new VnrGridNewBuilder({
      options: {
        configSelectable: {
          columnKey: 'Id',
        },
        configShowHide: {
          isPageExpand: true,
        },
        configCommandColumn: {
          isEnabledMenuAction: true,
        },
        configGroupable: {
          isEnabled: true,
        },
      },
    });
  }
  getSelectedID($event) {
    console.log($event, 'getSelectedID');
  }
  getDataItem($event) {
    console.log($event, 'getDataItem');
  }
  onOpenDetail($event) {
    console.log($event, 'onOpenDetail');
  }
  onVnRViewModeGrid($event) {
    console.log($event, 'onVnRViewModeGrid');
  }
  onGridEdit($event) {
    this.drawerService.create({
      nzTitle: 'Edit',
      nzContent: CategoryFormComponent,
      nzWidth: 600,
      nzClosable: true,
      nzMaskClosable: false,
      nzMask: true,
      nzContentParams: {},
      nzWrapClassName: 'class-drawer',
    });
  }
  onGridDelete($event) {
    console.log($event, 'onGridDelete');
  }
  onGridViewDetail($event) {
    console.log($event, 'onGridViewDetail');
  }
  onGridCellClick($event) {
    console.log($event, 'onGridCellClick');
  }

  onActionApprove($event) {
    console.log($event, 'onActionApprove');
    this.actionApprove.emit($event);
  }
  onActionReject($event) {
    console.log($event, 'onActionReject');
    this.actionReject.emit($event);
  }
  onActionAddNew($event) {
    this.drawerService.create({
      nzTitle: 'Add New',
      nzContent: CategoryFormComponent,
      nzWidth: 600,
      nzClosable: true,
      nzMaskClosable: false,
      nzMask: true,
      nzContentParams: {},
      nzWrapClassName: 'class-drawer',
    });
  }
  toggleChangeColumn(isNewVersion: boolean) {
    this.gridControl?.setOpenChangeColumn(true);
    this.gridControl?.setChangeColumnVersion(isNewVersion);
  }

  protected onChangesFilter(dataSearch: any) {
    let searchValue: any = {};
    if (dataSearch) {
      const searchValueClone = cloneDeep(dataSearch);
      searchValue = vnrUtilities.convertArraysToStrings(searchValueClone);
    }
    this.gridControl.setDataFilter(searchValue);
    this.gridControl.vnrReadGrid();
  }
  protected onOpenComponentFilterFull(event: any) {
    this.filterAdvanceFullControl.onOpenFullFilter();
  }
}
