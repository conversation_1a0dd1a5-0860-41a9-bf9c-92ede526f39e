/**
 * <PERSON><PERSON><PERSON> hình mặc định cho goal evaluation section
 */
export const GOAL_EVALUATION_DEFAULT_CONFIG_TEST = [
  {
    GroupType: null,
    ReferenceName: null,
    ReferenceDataSource: null,
    TypeControl: null,
    MultiColumn: null,
    serverSide: null,
    Name: 'ID',
    HeaderName: 'ID',
    HeaderKey: 'ID',
    Width: 220,
    Sortable: false,
    Filter: false,
    Class: null,
    Hidden: true,
    Format: null,
    OrderColumn: null,
    RowOnPage: null,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: false,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    Locked: false,
    Sticky: false,
    Template: null,
    Type: null,
    IsMultiColumn: null,
    IsHiddenConfig: null,
    DisabledFormat: null,
    AllowClickColumn: null,
    ChildrenLink: null,
    ParentLink: null,
    Style: null,
    HeaderStyle: null,
    HeaderClass: null,
  },
  {
    GroupType: null,
    ReferenceName: null,
    ReferenceDataSource: null,
    TypeControl: null,
    MultiColumn: null,
    serverSide: null,
    Name: 'Criteria',
    HeaderName: 'Nhóm năng lực',
    HeaderKey: 'Nhóm năng lực',
    Width: 220,
    Sortable: false,
    Filter: false,
    Class: 'section-criteria',
    Hidden: true,
    Format: null,
    OrderColumn: null,
    RowOnPage: null,
    Group: true,
    Sum: false,
    isNumber: false,
    Disable: false,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    Locked: false,
    Sticky: false,
    Template: null,
    Type: null,
    IsMultiColumn: null,
    IsHiddenConfig: null,
    DisabledFormat: null,
    AllowClickColumn: null,
    ChildrenLink: null,
    ParentLink: null,
    Style: null,
    HeaderStyle: null,
    HeaderClass: null,
  },
  {
    GroupType: null,
    ReferenceName: null,
    ReferenceDataSource: null,
    TypeControl: null,
    MultiColumn: [
      {
        GroupType: null,
        ReferenceName: null,
        ReferenceDataSource: null,
        TypeControl: 'CustomControl|TextBox',
        MultiColumn: null,
        serverSide: null,
        Name: 'CriteriaName',
        HeaderName: 'Tên mục tiêu',
        HeaderKey: 'Tên mục tiêu',
        Width: 220,
        Sortable: false,
        Filter: false,
        Class: null,
        Hidden: false,
        Format: null,
        OrderColumn: null,
        RowOnPage: null,
        Group: false,
        Sum: false,
        isNumber: false,
        BackgroundFields: null,
        ColorFields: null,
        GroupIndex: null,
        Locked: false,
        Sticky: false,
        Template: null,
        Type: null,
        IsMultiColumn: null,
        IsHiddenConfig: null,
        DisabledFormat: null,
        AllowClickColumn: null,
        ChildrenLink: null,
        ParentLink: null,
        Style: null,
        HeaderStyle: null,
        HeaderClass: null,
        Disable: true,
      },
      {
        GroupType: null,
        ReferenceName: null,
        ReferenceDataSource: null,
        TypeControl: 'CustomControl|Number',
        MultiColumn: null,
        serverSide: {
          isEnum: null,
          isMulti: null,
          dataTable: 'Cat_OrgStructure',
          objectTable: null,
          childFieldOrigin: null,
          urlApi: 'http://172.21.30.65:2005/api/Cat_GetData/GetOrgTreeView',
          valueField: 'id',
          textField: 'FullName',
          compareField: null,
          data: {
            text: '',
            textField: null,
            Type: null,
          },
          dataSource: null,
        },
        Name: 'Weight',
        HeaderName: 'Trọng số',
        HeaderKey: 'Trọng số %',
        Width: 150,
        Sortable: false,
        Filter: false,
        Class: null,
        Hidden: false,
        Format: null,
        OrderColumn: null,
        RowOnPage: null,
        Group: false,
        Sum: false,
        isNumber: false,
        Disable: true,
        BackgroundFields: null,
        ColorFields: null,
        GroupIndex: null,
        Locked: false,
        Sticky: false,
        Template: null,
        Type: null,
        IsMultiColumn: null,
        IsHiddenConfig: null,
        DisabledFormat: null,
        AllowClickColumn: null,
        ChildrenLink: null,
        ParentLink: null,
        Style: null,
        HeaderStyle: null,
        HeaderClass: null,
        Calculate: 'sum',
      },
      {
        GroupType: 'Category',
        ReferenceName: 'KeyOrigin__bbd44046_08a5_4ca3_9842_05c428f67915',
        ReferenceDataSource: null,
        TypeControl: 'CustomControl|Org',
        MultiColumn: null,
        serverSide: {
          isEnum: null,
          isMulti: null,
          dataTable: 'Cat_OrgStructure',
          objectTable: null,
          childFieldOrigin: null,
          urlApi: 'http://172.21.30.65:2005/api/Cat_GetData/GetOrgTreeView',
          valueField: 'id',
          textField: 'FullName',
          compareField: null,
          data: {
            text: '',
            textField: null,
            Type: null,
          },
          dataSource: null,
        },
        Name: 'Key__bbd44046_08a5_4ca3_9842_05c428f67915',
        HeaderName: 'Chỉ tiêu',
        HeaderKey: 'Chỉ tiêu',
        Width: 150,
        Sortable: false,
        Filter: false,
        Class: null,
        Hidden: false,
        Format: 'Select',
        OrderColumn: null,
        RowOnPage: null,
        Group: false,
        Sum: false,
        isNumber: false,
        Disable: true,
        BackgroundFields: null,
        ColorFields: null,
        GroupIndex: null,
        Locked: false,
        Sticky: false,
        Template: null,
        Type: null,
        IsMultiColumn: null,
        IsHiddenConfig: null,
        DisabledFormat: null,
        AllowClickColumn: null,
        ChildrenLink: null,
        ParentLink: null,
        Style: null,
        HeaderStyle: null,
        HeaderClass: null,
      },
    ],
    serverSide: null,
    Name: 'InfoGoal',
    HeaderName: 'Thông tin mục tiêu',
    HeaderKey: 'Thông tin mục tiêu',
    Width: 220,
    Sortable: false,
    Filter: false,
    Class: null,
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: null,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: false,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    Locked: true,
    Sticky: false,
    Template: null,
    Type: 'group',
    IsMultiColumn: true,
    IsHiddenConfig: null,
    DisabledFormat: null,
    AllowClickColumn: null,
    ChildrenLink: null,
    ParentLink: null,
    Style: null,
    HeaderStyle: null,
    HeaderClass: null,
  },
  {
    GroupType: null,
    ReferenceName: null,
    ReferenceDataSource: null,
    TypeControl: null,
    MultiColumn: [
      {
        GroupType: 'Category',
        ReferenceName: 'KeyOrigin__bbd44046_08a5_4ca3_9842_05c428f67915',
        ReferenceDataSource: null,
        TypeControl: 'CustomControl|Org',
        MultiColumn: null,
        serverSide: {
          isEnum: null,
          isMulti: null,
          dataTable: 'Cat_OrgStructure',
          objectTable: null,
          childFieldOrigin: null,
          urlApi: 'http://172.21.30.65:2005/api/Cat_GetData/GetOrgTreeView',
          valueField: 'id',
          textField: 'FullName',
          compareField: null,
          data: {
            text: '',
            textField: null,
            Type: null,
          },
          dataSource: null,
        },
        Name: 'Key__bbd44046_08a5_4ca3_9842_05c428f67915',
        HeaderName: 'Đơn vị đo',
        HeaderKey: 'Đơn vị đo',
        Width: 220,
        Sortable: false,
        Filter: false,
        Class: null,
        Hidden: false,
        Format: 'Select',
        OrderColumn: null,
        RowOnPage: null,
        Group: false,
        Sum: false,
        isNumber: false,
        Disable: true,
        BackgroundFields: null,
        ColorFields: null,
        GroupIndex: null,
        Locked: false,
        Sticky: false,
        Template: null,
        Type: null,
        IsMultiColumn: null,
        IsHiddenConfig: null,
        DisabledFormat: null,
        AllowClickColumn: null,
        ChildrenLink: null,
        ParentLink: null,
        Style: null,
        HeaderStyle: null,
        HeaderClass: null,
      },
      {
        GroupType: null,
        ReferenceName: null,
        ReferenceDataSource: null,
        TypeControl: 'CustomControl|TextArea',
        MultiColumn: null,
        Name: 'Description',
        HeaderName: 'Mô tả',
        HeaderKey: 'Mô tả',
        Width: 220,
        Sortable: false,
        Filter: false,
        Class: null,
        Hidden: false,
        Format: null,
        OrderColumn: null,
        RowOnPage: null,
        Group: false,
        Sum: false,
        isNumber: false,
        Disable: false,
        BackgroundFields: null,
        ColorFields: null,
        GroupIndex: null,
        Locked: false,
        Sticky: false,
        Template: null,
        Type: null,
        IsMultiColumn: null,
        IsHiddenConfig: null,
        DisabledFormat: null,
        AllowClickColumn: null,
        ChildrenLink: null,
        ParentLink: null,
        Style: null,
        HeaderStyle: null,
        HeaderClass: null,
      },
      {
        GroupType: 'Category',
        ReferenceName: 'KeyOrigin__bbd44046_08a5_4ca3_9842_05c428f67915',
        ReferenceDataSource: null,
        TypeControl: 'CustomControl|Org',
        MultiColumn: null,
        serverSide: {
          isEnum: null,
          isMulti: null,
          dataTable: 'Cat_OrgStructure',
          objectTable: null,
          childFieldOrigin: null,
          urlApi: 'http://172.21.30.65:2005/api/Cat_GetData/GetOrgTreeView',
          valueField: 'id',
          textField: 'FullName',
          compareField: null,
          data: {
            text: '',
            textField: null,
            Type: null,
          },
          dataSource: null,
        },
        Name: 'Key__bbd44046_08a5_4ca3_9842_05c428f67915',
        HeaderName: 'Kết quả cần đạt',
        HeaderKey: 'Kết quả cần đạt',
        Width: 220,
        Sortable: false,
        Filter: false,
        Class: null,
        Hidden: false,
        Format: 'Select',
        OrderColumn: null,
        RowOnPage: null,
        Group: false,
        Sum: false,
        isNumber: false,
        Disable: false,
        BackgroundFields: null,
        ColorFields: null,
        GroupIndex: null,
        Locked: false,
        Sticky: false,
        Template: null,
        Type: null,
        IsMultiColumn: null,
        IsHiddenConfig: null,
        DisabledFormat: null,
        AllowClickColumn: null,
        ChildrenLink: null,
        ParentLink: null,
        Style: null,
        HeaderStyle: null,
        HeaderClass: null,
      },
    ],
    serverSide: null,
    Name: 'KeyObject__Rec_RecruitmentPlan',
    HeaderName: 'Chi tiết mục tiêu',
    HeaderKey: 'Chi tiết mục tiêu',
    Width: 220,
    Sortable: false,
    Filter: false,
    Class: null,
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: null,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: false,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    Locked: false,
    Sticky: false,
    Template: null,
    Type: 'group',
    IsMultiColumn: true,
    IsHiddenConfig: null,
    DisabledFormat: null,
    AllowClickColumn: null,
    ChildrenLink: null,
    ParentLink: null,
    Style: null,
    HeaderStyle: null,
    HeaderClass: null,
  },
  {
    GroupType: null,
    ReferenceName: null,
    ReferenceDataSource: null,
    TypeControl: null,
    MultiColumn: [
      {
        GroupType: 'Category',
        ReferenceName: 'KeyOrigin__bbd44046_08a5_4ca3_9842_05c428f67915',
        ReferenceDataSource: null,
        TypeControl: 'CustomControl|Org',
        MultiColumn: null,
        serverSide: {
          isEnum: null,
          isMulti: null,
          dataTable: 'Cat_OrgStructure',
          objectTable: null,
          childFieldOrigin: null,
          urlApi: 'http://172.21.30.65:2005/api/Cat_GetData/GetOrgTreeView',
          valueField: 'id',
          textField: 'FullName',
          compareField: null,
          data: {
            text: '',
            textField: null,
            Type: null,
          },
          dataSource: null,
        },
        Name: 'Key__bbd44046_08a5_4ca3_9842_05c428f67915',
        HeaderName: 'Chỉ số đầu',
        HeaderKey: 'Chỉ số đầu',
        Width: 220,
        Sortable: false,
        Filter: false,
        Class: null,
        Hidden: false,
        Format: 'Select',
        OrderColumn: null,
        RowOnPage: null,
        Group: false,
        Sum: false,
        isNumber: false,
        Disable: false,
        BackgroundFields: null,
        ColorFields: null,
        GroupIndex: null,
        Locked: false,
        Sticky: false,
        Template: null,
        Type: null,
        IsMultiColumn: null,
        IsHiddenConfig: null,
        DisabledFormat: null,
        AllowClickColumn: null,
        ChildrenLink: null,
        ParentLink: null,
        Style: null,
        HeaderStyle: null,
        HeaderClass: null,
      },
      {
        GroupType: 'Category',
        ReferenceName: 'KeyOrigin__bbd44046_08a5_4ca3_9842_05c428f67915',
        ReferenceDataSource: null,
        TypeControl: 'CustomControl|Org',
        MultiColumn: null,
        serverSide: {
          isEnum: null,
          isMulti: null,
          dataTable: 'Cat_OrgStructure',
          objectTable: null,
          childFieldOrigin: null,
          urlApi: 'http://172.21.30.65:2005/api/Cat_GetData/GetOrgTreeView',
          valueField: 'id',
          textField: 'FullName',
          compareField: null,
          data: {
            text: '',
            textField: null,
            Type: null,
          },
          dataSource: null,
        },
        Name: 'Key__bbd44046_08a5_4ca3_9842_05c428f67915',
        HeaderName: 'Hiệu chỉnh ',
        HeaderKey: 'Hiệu chỉnh ',
        Width: 220,
        Sortable: false,
        Filter: false,
        Class: null,
        Hidden: false,
        Format: 'Select',
        OrderColumn: null,
        RowOnPage: null,
        Group: false,
        Sum: false,
        isNumber: false,
        Disable: false,
        BackgroundFields: null,
        ColorFields: null,
        GroupIndex: null,
        Locked: false,
        Sticky: false,
        Template: null,
        Type: null,
        IsMultiColumn: null,
        IsHiddenConfig: null,
        DisabledFormat: null,
        AllowClickColumn: null,
        ChildrenLink: null,
        ParentLink: null,
        Style: null,
        HeaderStyle: null,
        HeaderClass: null,
      },
      {
        GroupType: 'Category',
        ReferenceName: 'KeyOrigin__bbd44046_08a5_4ca3_9842_05c428f67915',
        ReferenceDataSource: null,
        TypeControl: 'CustomControl|Org',
        MultiColumn: null,
        serverSide: {
          isEnum: null,
          isMulti: null,
          dataTable: 'Cat_OrgStructure',
          objectTable: null,
          childFieldOrigin: null,
          urlApi: 'http://172.21.30.65:2005/api/Cat_GetData/GetOrgTreeView',
          valueField: 'id',
          textField: 'FullName',
          compareField: null,
          data: {
            text: '',
            textField: null,
            Type: null,
          },
          dataSource: null,
        },
        Name: 'Key__bbd44046_08a5_4ca3_9842_05c428f67915',
        HeaderName: 'Hiệu chỉnh',
        HeaderKey: 'Hiệu chỉnh',
        Width: 220,
        Sortable: false,
        Filter: false,
        Class: null,
        Hidden: false,
        Format: 'Select',
        OrderColumn: null,
        RowOnPage: null,
        Group: false,
        Sum: false,
        isNumber: false,
        Disable: false,
        BackgroundFields: null,
        ColorFields: null,
        GroupIndex: null,
        Locked: false,
        Sticky: false,
        Template: null,
        Type: null,
        IsMultiColumn: null,
        IsHiddenConfig: null,
        DisabledFormat: null,
        AllowClickColumn: null,
        ChildrenLink: null,
        ParentLink: null,
        Style: null,
        HeaderStyle: null,
        HeaderClass: null,
      },
      {
        GroupType: 'Category',
        ReferenceName: 'KeyOrigin__bbd44046_08a5_4ca3_9842_05c428f67915',
        ReferenceDataSource: null,
        TypeControl: 'CustomControl|Org',
        MultiColumn: null,
        serverSide: {
          isEnum: null,
          isMulti: null,
          dataTable: 'Cat_OrgStructure',
          objectTable: null,
          childFieldOrigin: null,
          urlApi: 'http://172.21.30.65:2005/api/Cat_GetData/GetOrgTreeView',
          valueField: 'id',
          textField: 'FullName',
          compareField: null,
          data: {
            text: '',
            textField: null,
            Type: null,
          },
          dataSource: null,
        },
        Name: 'Key__bbd44046_08a5_4ca3_9842_05c428f67915',
        HeaderName: 'Mức độ hoàn thành',
        HeaderKey: 'Mức độ hoàn thành',
        Width: 220,
        Sortable: false,
        Filter: false,
        Class: null,
        Hidden: false,
        Format: 'Select',
        OrderColumn: null,
        RowOnPage: null,
        Group: false,
        Sum: false,
        isNumber: false,
        Disable: false,
        BackgroundFields: null,
        ColorFields: null,
        GroupIndex: null,
        Locked: false,
        Sticky: false,
        Template: null,
        Type: null,
        IsMultiColumn: null,
        IsHiddenConfig: null,
        DisabledFormat: null,
        AllowClickColumn: null,
        ChildrenLink: null,
        ParentLink: null,
        Style: null,
        HeaderStyle: null,
        HeaderClass: null,
      },
      {
        GroupType: 'Category',
        ReferenceName: 'KeyOrigin__bbd44046_08a5_4ca3_9842_05c428f67915',
        ReferenceDataSource: null,
        TypeControl: 'CustomControl|Org',
        MultiColumn: null,
        serverSide: {
          isEnum: null,
          isMulti: null,
          dataTable: 'Cat_OrgStructure',
          objectTable: null,
          childFieldOrigin: null,
          urlApi: 'http://172.21.30.65:2005/api/Cat_GetData/GetOrgTreeView',
          valueField: 'id',
          textField: 'FullName',
          compareField: null,
          data: {
            text: '',
            textField: null,
            Type: null,
          },
          dataSource: null,
        },
        Name: 'Key__bbd44046_08a5_4ca3_9842_05c428f67915',
        HeaderName: 'Quy đổi',
        HeaderKey: 'Quy đổi',
        Width: 220,
        Sortable: false,
        Filter: false,
        Class: null,
        Hidden: false,
        Format: 'Select',
        OrderColumn: null,
        RowOnPage: null,
        Group: false,
        Sum: false,
        isNumber: false,
        Disable: false,
        BackgroundFields: null,
        ColorFields: null,
        GroupIndex: null,
        Locked: false,
        Sticky: false,
        Template: null,
        Type: null,
        IsMultiColumn: null,
        IsHiddenConfig: null,
        DisabledFormat: null,
        AllowClickColumn: null,
        ChildrenLink: null,
        ParentLink: null,
        Style: null,
        HeaderStyle: null,
        HeaderClass: null,
      },
    ],
    serverSide: null,
    Name: 'KeyObject__Rec_RecruitmentPlan',
    HeaderName: 'Tính toán kết quả',
    HeaderKey: 'Tính toán kết quả',
    Width: 220,
    Sortable: false,
    Filter: false,
    Class: null,
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: null,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: false,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    Locked: false,
    Sticky: false,
    Template: null,
    Type: 'group',
    IsMultiColumn: true,
    IsHiddenConfig: null,
    DisabledFormat: null,
    AllowClickColumn: null,
    ChildrenLink: null,
    ParentLink: null,
    Style: null,
    HeaderStyle: null,
    HeaderClass: null,
  },
  {
    GroupType: null,
    ReferenceName: null,
    ReferenceDataSource: null,
    TypeControl: null,
    MultiColumn: [
      {
        GroupType: 'Category',
        ReferenceName: 'KeyOrigin__bbd44046_08a5_4ca3_9842_05c428f67915',
        ReferenceDataSource: null,
        TypeControl: 'CustomControl|Org',
        MultiColumn: null,
        serverSide: {
          isEnum: null,
          isMulti: null,
          dataTable: 'Cat_OrgStructure',
          objectTable: null,
          childFieldOrigin: null,
          urlApi: 'http://172.21.30.65:2005/api/Cat_GetData/GetOrgTreeView',
          valueField: 'id',
          textField: 'FullName',
          compareField: null,
          data: {
            text: '',
            textField: null,
            Type: null,
          },
          dataSource: null,
        },
        Name: 'Key__bbd44046_08a5_4ca3_9842_05c428f67915',
        HeaderName: 'Điểm',
        HeaderKey: 'Điểm',
        Width: 220,
        Sortable: false,
        Filter: false,
        Class: null,
        Hidden: false,
        Format: 'Select',
        OrderColumn: null,
        RowOnPage: null,
        Group: false,
        Sum: false,
        isNumber: false,
        Disable: false,
        BackgroundFields: null,
        ColorFields: null,
        GroupIndex: null,
        Locked: false,
        Sticky: false,
        Template: null,
        Type: null,
        IsMultiColumn: null,
        IsHiddenConfig: null,
        DisabledFormat: null,
        AllowClickColumn: null,
        ChildrenLink: null,
        ParentLink: null,
        Style: null,
        HeaderStyle: null,
        HeaderClass: null,
      },
      {
        GroupType: 'Category',
        ReferenceName: 'KeyOrigin__bbd44046_08a5_4ca3_9842_05c428f67915',
        ReferenceDataSource: null,
        TypeControl: 'CustomControl|Org',
        MultiColumn: null,
        serverSide: {
          isEnum: null,
          isMulti: null,
          dataTable: 'Cat_OrgStructure',
          objectTable: null,
          childFieldOrigin: null,
          urlApi: 'http://172.21.30.65:2005/api/Cat_GetData/GetOrgTreeView',
          valueField: 'id',
          textField: 'FullName',
          compareField: null,
          data: {
            text: '',
            textField: null,
            Type: null,
          },
          dataSource: null,
        },
        Name: 'Key__bbd44046_08a5_4ca3_9842_05c428f67915',
        HeaderName: 'Xếp loại',
        HeaderKey: 'Xếp loại',
        Width: 220,
        Sortable: false,
        Filter: false,
        Class: null,
        Hidden: false,
        Format: 'Select',
        OrderColumn: null,
        RowOnPage: null,
        Group: false,
        Sum: false,
        isNumber: false,
        Disable: false,
        BackgroundFields: null,
        ColorFields: null,
        GroupIndex: null,
        Locked: false,
        Sticky: false,
        Template: null,
        Type: null,
        IsMultiColumn: null,
        IsHiddenConfig: null,
        DisabledFormat: null,
        AllowClickColumn: null,
        ChildrenLink: null,
        ParentLink: null,
        Style: null,
        HeaderStyle: null,
        HeaderClass: null,
      },
      {
        GroupType: 'Category',
        ReferenceName: 'KeyOrigin__bbd44046_08a5_4ca3_9842_05c428f67915',
        ReferenceDataSource: null,
        TypeControl: 'CustomControl|Org',
        MultiColumn: null,
        serverSide: {
          isEnum: null,
          isMulti: null,
          dataTable: 'Cat_OrgStructure',
          objectTable: null,
          childFieldOrigin: null,
          urlApi: 'http://172.21.30.65:2005/api/Cat_GetData/GetOrgTreeView',
          valueField: 'id',
          textField: 'FullName',
          compareField: null,
          data: {
            text: '',
            textField: null,
            Type: null,
          },
          dataSource: null,
        },
        Name: 'Key__bbd44046_08a5_4ca3_9842_05c428f67915',
        HeaderName: 'Ghi chú/Nhận xét',
        HeaderKey: 'Ghi chú/Nhận xét',
        Width: 220,
        Sortable: false,
        Filter: false,
        Class: null,
        Hidden: false,
        Format: 'Select',
        OrderColumn: null,
        RowOnPage: null,
        Group: false,
        Sum: false,
        isNumber: false,
        Disable: false,
        BackgroundFields: null,
        ColorFields: null,
        GroupIndex: null,
        Locked: false,
        Sticky: false,
        Template: null,
        Type: null,
        IsMultiColumn: null,
        IsHiddenConfig: null,
        DisabledFormat: null,
        AllowClickColumn: null,
        ChildrenLink: null,
        ParentLink: null,
        Style: null,
        HeaderStyle: null,
        HeaderClass: null,
      },
    ],
    serverSide: null,
    Name: 'KeyObject__Rec_RecruitmentPlan',
    HeaderName: 'Các cấp đánh giá',
    HeaderKey: 'Các cấp đánh giá',
    Width: 220,
    Sortable: false,
    Filter: false,
    Class: null,
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: null,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: false,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    Locked: false,
    Sticky: false,
    Template: null,
    Type: 'group',
    IsMultiColumn: true,
    IsHiddenConfig: null,
    DisabledFormat: null,
    AllowClickColumn: null,
    ChildrenLink: null,
    ParentLink: null,
    Style: null,
    HeaderStyle: null,
    HeaderClass: null,
  },
];
