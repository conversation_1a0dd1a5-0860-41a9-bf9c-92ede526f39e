<form [formGroup]="formGroups" #vnrGridInlineForm="ngForm">
  <!-- #region Change Columns New -->
  <change-new-column
    *ngIf="isOpenChangeColumn && isChangeColumnNew"
    [builder]="builderGridChangeColumn"
    (vnrClosePopup)="vnrcloseAndRefreshGird($event)"
    (vnrReloadGridChangeColumn)="vnrReloadGridChangeColumn($event)"
  ></change-new-column>
  <!-- #endregion Change Columns -->
  <kendo-grid
    class="vnr-border-grid"
    #kendoGridInline
    [id]="'vnrGridInline'"
    [ngClass]="{
      'vnr-pageEP-custom': builder.options.configShowHide.isPageExpand,
      'vnr-grid__hide-vertical-scroll': builder.options.configHeightGrid.isAllowCalcRowHeight,
      'vnr-border-grid-edit': customTemplateGroupColumnHeader,
      'vnr-border-grid__usingActionBtn':
        columns &&
        columns.length > 0 &&
        builder.options.configCommandColumn.isEnabledMenuAction &&
        (builder.options.configShowHide.isShowEdit ||
          builder.options.configShowHide.isShowDelete ||
          builder.options.configEdit.isShowSave ||
          rowActionsTemplate)
    }"
    [reorderable]="true"
    [resizable]="true"
    [data]="gridView"
    [loading]="isLoading"
    [kendoGridSelectBy]="builder.options.configSelectable.columnKey"
    [sortable]="{
      allowUnsort: true,
      mode: false ? 'multiple' : 'single'
    }"
    [height]="builder.options.configHeightGrid.gridHeight"
    [sort]="builder.options.queryOption.sort"
    [group]="builder.options.queryOption.group"
    [pageSize]="builder.options.queryOption.take"
    [skip]="builder.options.queryOption.skip"
    [filter]="builder.options.queryOption.filter"
    [pageable]="{
      buttonCount: builder.options.configPageable.buttonCount,
      info: builder.options.configPageable.isShowInfo,
      type: builder.options.configPageable.type,
      pageSizes: builder.options.configPageable.isShowPageSize,
      previousNext: builder.options.configPageable.isPreviousNext,
      position: builder.options.configPageable.position,
    }"
    [selectedKeys]="gridSelectedKeys"
    [selectable]="{
      enabled: true,
      checkboxOnly: builder.options.configSelectable.isCheckboxOnly,
      cell: false
    }"
    [columnMenu]="true"
    filterable="menu"
    (edit)="editHandler($event)"
    (cancel)="cancelHandler($event)"
    (save)="saveHandler($event)"
    (filterChange)="vnrFilterChange($event)"
    (dataStateChange)="vnrDataStateChange($event)"
    (selectionChange)="vnrSelectedRowChange($event)"
    (columnResize)="onColumnResize($event)"
    (selectedKeysChange)="vnrKeyChange($event)"
    (dblclick)="onGridDoubleClicked($event)"
    (sortChange)="sortChange($event)"
    (pageChange)="onPageChange($event)"
    [rowClass]="builder.options.configEvent.rowClass"
    [resizable]="builder.options.isResizable"
    [groupable]="{
      emptyText: builder.options.configGroupable.emptyText | translate,
      enabled: builder.options.configGroupable.isEnabled,
      showFooter: builder.options.configGroupable.isShowFooter
    }"
    (groupChange)="vnrGroupChange($event)"
    (cellClick)="cellClickHandler($event)"
    (cellClose)="cellCloseHandler($event)"
    [virtualColumns]="builder.options.isVirtualColumn"
    [trackBy]="trackByIndex"
    kendoGridExpandGroupBy
    [groupsInitiallyExpanded]="builder.options.configExpanded.isExpanded"
    [(expandedGroupKeys)]="builder.options.configExpanded.groupKey"
  >
    <!-- [isGroupExpanded]="isGroupExpanded" (groupCollapse)="toggleGroup($event)"
    (groupExpand)="toggleGroup($event)" -->
    <!-- #region Checkbox -->
    <kendo-grid-checkbox-column
      [width]="50"
      [minResizableWidth]="50"
      [maxResizableWidth]="50"
      [autoSize]="true"
      class="text-center"
      *ngIf="
        columns &&
        columns.length > 0 &&
        builder.options.configShowHide.isShowColumnCheck &&
        !builder.options?.configEdit?.isEditAllRow &&
        !isEdited
      "
      [columnMenu]="false"
    >
      <ng-template kendoGridHeaderTemplate let-column let-columnIndex="rowIndex">
        <div class="text-center">
          <input
            type="checkbox"
            id="selectAllCheckboxId"
            kendoCheckBox
            class="k-checkbox"
            kendoGridSelectAllCheckbox
            [state]="selectAllState"
            (selectAllChange)="onSelectAllChange($event)"
          />
          <label class="k-checkbox-label" for="selectAllCheckboxId"></label>
        </div>
      </ng-template>
      <ng-template
        kendoGridCellTemplate
        let-dataItem
        let-rowIndex="rowIndex"
        let-field="field"
        let-value="value"
        *ngIf="builder.options.configShowHide.isShowColumnCheck"
      >
        <input
          type="checkbox"
          kendoCheckBox
          class="k-checkbox"
          [checked]="dataItem.checked"
          [(ngModel)]="dataItem.checked"
          class="customCheckBtn"
          id="id_{{ field }}_{{ value }}"
          [name]="dataItem?.ID"
          [ngModelOptions]="{ standalone: true }"
          (change)="checkDataItem(dataItem)"
          [kendoGridSelectionCheckbox]="rowIndex"
        />
      </ng-template>
    </kendo-grid-checkbox-column>
    <!-- #endregion -->

    <!-- #region Edit command -->
    <kendo-grid-command-column
      [width]="builder.options?.configEdit?.commandColumnWidth || 90"
      [columnMenu]="false"
      *ngIf="
        !builder.options?.configEdit?.isEditAllRow &&
        !builder.options.configCommandColumn.isEnabledMenuAction &&
        builder.options?.configEdit?.isShowColumnAction
      "
    >
      <ng-template
        kendoGridCellTemplate
        let-isNew="isNew"
        let-dataItem
        let-column
        let-columnIndex="rowIndex"
      >
        <button
          *ngIf="builder.options?.configShowHide?.isShowEdit"
          kendoGridEditCommand
          class="mr-1 btn-kendoGrid-customize btn-kendoGrid-customize__edit ant-btn ant-btn-primary"
        >
          <span nz-icon nzType="edit" nzTheme="outline"></span>
        </button>
        <button
          *ngIf="builder.options?.configShowHide?.isShowDelete"
          kendoGridRemoveCommand
          (click)="removeHandler(dataItem, grid)"
          class="mr-1 btn-kendoGrid-customize btn-kendoGrid-customize__delete ant-btn ant-btn-primary"
        >
          <i class="far fa-trash-alt"></i>
        </button>
        <button
          *ngIf="builder.options?.configEdit?.isShowSave"
          kendoGridSaveCommand
          class="mr-1 btn-kendoGrid-customize btn-kendoGrid-customize__primary ant-btn ant-btn-primary"
          [disabled]="vnrGridInlineForm.invalid || vnrGridInlineForm.pristine"
        >
          <i class="fas fa-save"></i>
        </button>
        <button
          kendoGridCancelCommand
          class="mr-1 btn-kendoGrid-customize btn-kendoGrid-customize__restore ant-btn ant-btn-primary"
        >
          <i class="fas fa-undo"></i>
        </button>
        <ng-template [ngTemplateOutlet]="tplCustomBtnAction"></ng-template>
      </ng-template>
    </kendo-grid-command-column>
    <kendo-grid-command-column
      [width]="builder.options?.configEdit?.commandColumnWidth"
      [columnMenu]="false"
      *ngIf="
        builder.options?.configEdit?.isEditAllRow &&
        !builder.options.configCommandColumn.isEnabledMenuAction &&
        builder.options?.configEdit?.isShowColumnAction
      "
      [hidden]="!isEdited"
    >
      <ng-template
        kendoGridCellTemplate
        let-isNew="isNew"
        let-dataItem
        let-column
        let-columnIndex="rowIndex"
      >
        <ng-container *ngIf="rowActionsTemplate; else tplBtnDeleteDefault">
          <ng-template [ngTemplateOutlet]="tplCustomBtnAction"></ng-template>
        </ng-container>
        <ng-template #tplBtnDeleteDefault>
          <button
            [ngClass]="{ 'd-block': isEdited }"
            (click)="removeHandler(dataItem, grid)"
            class="btn-kendoGrid-customize btn-kendoGrid-customize__delete ant-btn ant-btn-primary"
            kendoGridRemoveCommand
          >
            <i class="far fa-trash-alt"></i>
          </button>
        </ng-template>
      </ng-template>
    </kendo-grid-command-column>
    <ng-template
      #tplCustomBtnAction
      let-dataItem
      let-rowIndex="rowIndex"
      let-field="field"
      let-column
    >
      <ng-container
        *ngIf="rowActionsTemplate"
        [ngTemplateOutletContext]="{
          $implicit: dataItem,
          rowIndex: rowIndex,
          field: field,
          column: column,
          columnItem: column
        }"
        [ngTemplateOutlet]="rowActionsTemplate"
      ></ng-container>
    </ng-template>
    <!-- #endregion -->

    <!-- #region Order Number Column -->
    <kendo-grid-command-column
      [title]="'common.grid.orderNumber' | translate"
      [width]="76"
      [locked]="builder.options.configIndexColumn.isLocked"
      *ngIf="builder.options.configIndexColumn.isShow"
    >
      <ng-template
        kendoGridCellTemplate
        let-rowIndex="rowIndex"
        let-column="column"
        let-dataItem="dataItem"
        let-field="field"
      >
        {{ rowIndex + 1 }}
      </ng-template>
    </kendo-grid-command-column>
    <!-- #endregion  -->

    <!-- List Column -->
    <ng-container *ngFor="let col of columns; let last = last">
      <ng-container
        *ngIf="
          col.Type === 'group' && col.MultiColumn && col.MultiColumn.length > 0;
          else templateColumn
        "
      >
        <kendo-grid-column-group
          [title]="col.HasChangeDisplayName ? col.DisplayName : (col.HeaderKey | translate)"
          [width]="col | vnrColumnWidth"
          [minResizableWidth]="col?.MinResizableWidth || 50"
          [maxResizableWidth]="col?.MaxResizableWidth"
          [locked]="col.Locked"
          [headerClass]="col.Class"
        >
          <ng-template
            kendoGridHeaderTemplate
            let-columnIndex="columnIndex"
            let-column="column"
            let-dataItem
          >
            <ng-container
              *ngIf="columnHeaderTemplate; else tplDefaultHeaderTemplate"
              [ngTemplateOutlet]="columnHeaderTemplate"
              [ngTemplateOutletContext]="{
                $implicit: dataItem,
                column: column,
                columnIndex: columnIndex,
                col: col,
                fieldIndex: builder.options.configTitle.prefixTitleColumn
              }"
            ></ng-container>
            <ng-template #tplDefaultHeaderTemplate>
              <span
                [ngClass]="{
                  'vnr-grid-multiColumn':
                    col.Type === 'group' && col.MultiColumn && col.MultiColumn.length > 0
                }"
              >
                <ng-container
                  *ngIf="col[builder.options.configTitle.prefixTitleColumn]; else tplNormalHeader"
                >
                  <div
                    [ngClass]="{ 'text-center': builder.options.configTitle.alignment == 'Center' }"
                    [innerText]="
                      ((col.DisplayName ? col.DisplayName : col.HeaderKey) | translate) +
                      (col[builder.options.configTitle.prefixTitleColumn]
                        ? ' ' + col[builder.options.configTitle.prefixTitleColumn]
                        : '')
                    "
                  ></div>
                </ng-container>

                <ng-template #tplNormalHeader>
                  <div
                    [ngClass]="{ 'text-center': builder.options.configTitle.alignment == 'Center' }"
                    [innerText]="(col.DisplayName ? col.DisplayName : col.HeaderKey) | translate"
                  ></div>
                </ng-template>
              </span>
            </ng-template>
          </ng-template>

          <!-- colum templete -->
          <kendo-grid-column
            [field]="col.Name"
            title="{{ col.HasChangeDisplayName ? col.DisplayName : (col.HeaderKey | translate) }}"
            [filterable]="col.Filter"
            [width]="col | vnrColumnWidth"
            [minResizableWidth]="col?.MinResizableWidth || 50"
            [maxResizableWidth]="col?.MaxResizableWidth"
            [locked]="col.Locked === null || col.Locked === undefined ? false : col.Locked"
            [sortable]="col.Sortable"
            [hidden]="col.Hidden"
            [sticky]="col?.Sticky"
            [editable]="!col.Disable"
            [columnMenu]="!col.Hidden"
            [headerClass]="col.Class"
            class="{{
              (col.Format && col.Format.toLowerCase().startsWith('number|')) ||
              (col.Format && col.Format.toLowerCase().startsWith('datetime|')) ||
              (builder.options.configColumnSchema &&
                builder.options.configColumnSchema.fieldNumber &&
                builder.options.configColumnSchema.fieldNumber.indexOf(col.Name) >= 0) ||
              (builder.options.configColumnSchema &&
                builder.options.configColumnSchema.fieldNumberMoney &&
                builder.options.configColumnSchema.fieldNumberMoney.indexOf(col.Name) >= 0)
                ? col.Class + ' text-right'
                : col.Class
            }}"
            *ngFor="let col of col.MultiColumn"
          >
            <ng-template
              kendoGridHeaderTemplate
              let-column
              let-columnIndex="columnIndex"
              let-dataItem
            >
              <!-- *ngIf="!col.Disable" -->
              <!-- <span class="k-icon k-i-edit mr-1"></span> -->
              <span
                nz-icon
                nzType="edit"
                nzTheme="outline"
                class="mr-1"
                *ngIf="isEdited && builder.options?.configEdit?.isShowIconEdited"
              ></span>
              <span>
                <ng-container
                  *ngIf="columnHeaderTemplate; else tplDefaultHeaderEditTemplate"
                  [ngTemplateOutlet]="columnHeaderTemplate"
                  [ngTemplateOutletContext]="{
                    $implicit: dataItem,
                    column: column,
                    columnIndex: columnIndex,
                    col: col,
                    fieldIndex: builder.options.configTitle.prefixTitleColumn
                  }"
                ></ng-container>
                <ng-template #tplDefaultHeaderEditTemplate>
                  {{ col.HasChangeDisplayName ? col.DisplayName : (col.HeaderKey | translate) }}
                </ng-template>
              </span>
              <span
                class="col-required ml-1"
                *ngIf="
                  isEdited &&
                  (col
                    | vnrGridColumnsValidators
                      : 'Required'
                      : builder.options?.configEdit?.configValidate || checkRequiredEdit(col.Name))
                "
                >*
              </span>
            </ng-template>

            <!-- Edit incell -->
            <ng-template
              kendoGridEditTemplate
              let-dataItem="dataItem"
              let-rowIndex="rowIndex"
              let-field="field"
              let-column="column"
              let-formGroup="formGroup"
              let-isNew="isNew"
            >
              <ng-container
                [ngTemplateOutlet]="col.editor"
                *ngIf="column.editor"
                [ngTemplateOutletContext]="{
                  $implicit: dataItem,
                  rowIndex: rowIndex,
                  field: field,
                  column: column,
                  columnItem: col,
                  formGroup: formGroup,
                  isNew: isNew
                }"
              >
              </ng-container>
              <ng-container *ngIf="!col.editor">
                <ng-template
                  [ngTemplateOutlet]="tplControlsEdit"
                  [ngTemplateOutletContext]="{
                    $implicit: dataItem,
                    rowIndex: rowIndex,
                    field: field,
                    column: column,
                    columnItem: col,
                    formGroup: formGroup,
                    isNew: isNew
                  }"
                ></ng-template>
              </ng-container>
            </ng-template>

            <!-- Group -->
            <ng-template
              kendoGridGroupHeaderTemplate
              let-group="group"
              let-field="field"
              let-value="value"
              let-aggregates="aggregates"
              [ngClass]="{ aaaa: true }"
              let-index="index"
              let-dataItem
              let-column
              let-columnIndex="columnIndex"
              *ngIf="col.Group"
            >
              <span
                class="mr-1 ml-1"
                *ngIf="
                  builder.options.configShowHide.isShowColumnCheck &&
                  builder.options.configShowHide.isShowColumnGroupCheck
                "
              >
                <input
                  type="checkbox"
                  kendoCheckBox
                  class="k-checkbox"
                  (change)="checkGroup(group, index)"
                  [checked]="isGroupChecked(group)"
                  class="customCheckBtn"
                  id="id_{{ field }}_{{ value }}"
                />
              </span>
              <ng-container
                *ngIf="columnHeaderTemplate; else tplDefaultHeaderGroupTemplate"
                [ngTemplateOutlet]="columnHeaderTemplate"
                [ngTemplateOutletContext]="{
                  $implicit: dataItem,
                  column: column,
                  columnIndex: columnIndex,
                  col: col,
                  fieldIndex: builder.options.configTitle.prefixTitleColumn
                }"
              ></ng-container>
              <ng-template #tplDefaultHeaderGroupTemplate>
                <span
                  class="vnr-grid-grouping-title"
                  [ngClass]="{
                    'vnr-grid-grouping-title__checked':
                      builder.options.configShowHide.isShowColumnCheck &&
                      customTemplateGroupColumnHeader,
                    'vnr-grid-grouping-title__groupsHeader': customTemplateGroupColumnHeader
                  }"
                  title="{{ col | vnrGridGroupTitle : value : group }}"
                >
                  <strong class="mr-1" [class]="col?.Class + '__title-group'">
                    {{
                      (col.HasChangeDisplayName ? col.DisplayName : (col.HeaderKey | translate)) +
                        (col[builder.options.configTitle.prefixTitleColumn]
                          ? ' ' + col[builder.options.configTitle.prefixTitleColumn]
                          : '')
                    }}:
                  </strong>
                  <ng-container *ngIf="col && col.Format; else tplNormalHeaderFormat">
                    <span [ngSwitch]="col.Format.split('|')[0].toLowerCase()">
                      <span *ngSwitchCase="'datetime'">
                        <ng-container>
                          {{ value | date : col.Format.split('|')[1] }}
                          ({{ group.items.length | json }})
                        </ng-container>
                      </span>
                      <span *ngSwitchDefault> {{ value }} ({{ group.items.length | json }}) </span>
                    </span>
                  </ng-container>
                  <ng-template #tplNormalHeaderFormat>
                    {{ value }} ({{ group.items.length | json }})
                  </ng-template>
                </span>
              </ng-template>
            </ng-template>

            <!-- Format -->
            <ng-template
              kendoGridCellTemplate
              let-dataItem
              let-rowIndex="rowIndex"
              let-column="column"
              let-field="field"
              *ngIf="isColumnHasFormatSpecial(col)"
            >
              <ng-template *ngIf="col.Format; then gridFormat; else elseSchema"></ng-template>
              <!-- #region Template Column with Format -->
              <ng-template #gridFormat>
                <span *ngIf="col.Format?.toLowerCase()?.startsWith('datetime|')">
                  <ng-container
                    *ngIf="col.template || defaultColumnTemplate; else tplColumnDatetimeNoTemplate"
                    [ngTemplateOutlet]="col.template || defaultColumnTemplate"
                    [ngTemplateOutletContext]="{
                      $implicit: dataItem,
                      rowIndex: rowIndex,
                      field: field,
                      column: column,
                      columnItem: col
                    }"
                  >
                  </ng-container>
                  <ng-template #tplColumnDatetimeNoTemplate>
                    {{ dataItem[column.field] | date : col.Format.split('|')[1] }}
                  </ng-template>
                </span>
                <span *ngIf="col.Format?.toLowerCase()?.startsWith('number|')">
                  {{ dataItem[column.field] | kendoNumber : col.Format.split('|')[1] }}
                </span>
                <span *ngIf="col.Format?.toLowerCase()?.startsWith('bool')">
                  <label
                    nz-checkbox
                    [(ngModel)]="dataItem[column.field]"
                    nzDisabled
                    [name]="column.field + '__' + rowIndex"
                  >
                  </label>
                </span>

                <span *ngIf="col.Format?.toLowerCase()?.startsWith('link')">
                  <a [name]="column.field + '__' + rowIndex" [href]="dataItem[column.field]">
                    {{ dataItem[column.field] }}
                  </a>
                </span>

                <span *ngIf="col.Format?.toLowerCase()?.startsWith('file')">
                  <ng-container
                    *ngIf="col.template; else tplColumnFileNoTemplate"
                    [ngTemplateOutlet]="col.template"
                    [ngTemplateOutletContext]="{
                      $implicit: dataItem,
                      rowIndex: rowIndex,
                      field: field,
                      column: column,
                      columnItem: col
                    }"
                  >
                  </ng-container>
                  <ng-template #tplColumnFileNoTemplate>
                    <a
                      [name]="column.field + '__' + rowIndex"
                      (click)="onClickFileName(dataItem[column.field])"
                    >
                      {{ dataItem[column.field] }}
                    </a>
                  </ng-template>
                </span>

                <div
                  *ngIf="col.Format && col.Format.toLowerCase().startsWith('html')"
                  [innerHTML]="dataItem[column.field]"
                ></div>
              </ng-template>
              <!-- #endregion Template Column with Format-->

              <!-- #region Template Column without Format-->
              <ng-template #elseSchema>
                <span
                  *ngIf="
                    builder.options.configColumnSchema &&
                    builder.options.configColumnSchema.fieldBoolean &&
                    builder.options.configColumnSchema.fieldBoolean.indexOf(column.field) >= 0
                  "
                >
                  <label
                    nz-checkbox
                    [(ngModel)]="dataItem[column.field]"
                    nzDisabled
                    [name]="column.field + '__' + rowIndex"
                  >
                  </label>
                </span>

                <span
                  *ngIf="
                    builder.options.configColumnSchema &&
                    builder.options.configColumnSchema.fieldDate &&
                    builder.options.configColumnSchema.fieldDate.indexOf(column.field) >= 0
                  "
                >
                  {{ dataItem[column.field] | date : dateFormat }}
                </span>

                <span
                  *ngIf="
                    builder.options.configColumnSchema &&
                    builder.options.configColumnSchema.fieldNumber &&
                    builder.options.configColumnSchema.fieldNumber.indexOf(column.field) >= 0
                  "
                >
                  {{ dataItem[column.field] }}
                </span>

                <span
                  *ngIf="
                    builder.options.configColumnSchema &&
                    builder.options.configColumnSchema.fieldNumberMoney &&
                    builder.options.configColumnSchema.fieldNumberMoney.indexOf(column.field) >= 0
                  "
                >
                  {{ dataItem[column.field] }}
                </span>
              </ng-template>
              <!-- #endregion Template Column without Format-->
            </ng-template>

            <!-- #region Template outside -->
            <ng-template
              #tplColumnByColumnName
              *ngIf="col.template || defaultColumnTemplate"
              kendoGridCellTemplate
              let-dataItem
              let-rowIndex="rowIndex"
              let-field="field"
              let-column="column"
            >
              <ng-container *ngIf="isColumnHasFormatSpecial(col); else defaultDataGroup">
                <ng-container
                  *ngTemplateOutlet="
                    tplFormatTemplate;
                    context: {
                      $implicit: col,
                      dataItem: dataItem,
                      rowIndex: rowIndex
                    }
                  "
                ></ng-container>
              </ng-container>
              <ng-template #defaultDataGroup>
                <ng-container
                  [ngTemplateOutlet]="col.template || defaultColumnTemplate"
                  [ngTemplateOutletContext]="{
                    $implicit: dataItem,
                    rowIndex: rowIndex,
                    field: field,
                    column: column,
                    columnItem: col
                  }"
                >
                </ng-container>
              </ng-template>
            </ng-template>
            <!-- #endregion -->

            <!-- Fillter template menu-->
            <ng-template
              *ngIf="
                (col.Format && col.Format.toLowerCase().startsWith('datetime|')) ||
                (col.Format && col.Format.toLowerCase().startsWith('number|'))
              "
              kendoGridFilterMenuTemplate
              let-filter
              let-column="column"
              let-filterService="filterService"
            >
              <kendo-grid-date-filter-menu
                [column]="column"
                [filter]="filter"
                [filterService]="filterService"
                *ngIf="col.Format && col.Format.toLowerCase().startsWith('datetime|')"
                [format]="col.Format.split('|')[1]"
              >
              </kendo-grid-date-filter-menu>

              <kendo-grid-numeric-filter-menu
                [format]="col.Format.split('|')[1]"
                [column]="column"
                [filter]="filter"
                [filterService]="filterService"
                *ngIf="col.Format && col.Format.toLowerCase().startsWith('number|')"
              >
              </kendo-grid-numeric-filter-menu>
            </ng-template>

            <!--- Group Footer Template -->
            <ng-template
              kendoGridGroupFooterTemplate
              let-column
              let-columnIndex="columnIndex"
              let-aggregates
              let-field="field"
              let-group="group"
              let-rowIndex="rowIndex"
              let-dataItem
              *ngIf="customTemplateGroupColumnFooter && customTemplateGroupColumnFooter[col.Name]"
            >
              <ng-container
                [ngTemplateOutlet]="customTemplateGroupColumnFooter[col.Name]"
                [ngTemplateOutletContext]="{
                  $implicit: dataItem,
                  rowIndex: rowIndex,
                  columnIndex: columnIndex,
                  field: field,
                  column: column,
                  columnItem: col,
                  aggregates: aggregates,
                  aggregateItem: vnrAggregateResult,
                  group: group
                }"
              >
              </ng-container>
            </ng-template>

            <!-- Footer -->
            <ng-template
              kendoGridFooterTemplate
              let-column
              let-columnIndex="columnIndex"
              let-aggregates
              let-field="field"
              let-dataItem
              let-rowIndex="rowIndex"
              *ngIf="customTemplateColumnFooter && customTemplateColumnFooter[col.Name]"
            >
              <ng-container
                [ngTemplateOutlet]="customTemplateColumnFooter[col.Name]"
                [ngTemplateOutletContext]="{
                  $implicit: dataItem,
                  rowIndex: rowIndex,
                  columnIndex: columnIndex,
                  field: field,
                  column: column,
                  columnItem: col,
                  aggregates: aggregates,
                  aggregateItem: vnrAggregateResult
                }"
              >
              </ng-container>
            </ng-template>

            <!--- Group Header Template -->
            <ng-template
              kendoGridGroupHeaderColumnTemplate
              let-group="group"
              let-column
              let-columnIndex="columnIndex"
              let-aggregates
              let-field="field"
              let-dataItem
              let-rowIndex="rowIndex"
              *ngIf="customTemplateGroupColumnHeader && customTemplateGroupColumnHeader[col.Name]"
            >
              <ng-container
                [ngTemplateOutlet]="customTemplateGroupColumnHeader[col.Name]"
                [ngTemplateOutletContext]="{
                  $implicit: dataItem,
                  rowIndex: rowIndex,
                  columnIndex: columnIndex,
                  field: field,
                  column: column,
                  columnItem: col,
                  aggregates: aggregates,
                  aggregateItem: vnrAggregateResult,
                  group: group
                }"
              >
              </ng-container>
            </ng-template>
          </kendo-grid-column>
        </kendo-grid-column-group>
      </ng-container>
      <ng-template #templateColumn>
        <!-- colum templete -->
        <kendo-grid-column
          [field]="col.Name"
          [title]="col.HasChangeDisplayName ? col.DisplayName : (col.HeaderKey | translate)"
          [filterable]="col.Filter"
          [width]="col | vnrColumnWidth"
          [minResizableWidth]="col?.MinResizableWidth || 50"
          [maxResizableWidth]="col?.MaxResizableWidth"
          [locked]="col.Locked === null || col.Locked === undefined ? false : col.Locked"
          [sortable]="col.Sortable"
          [hidden]="col.Hidden"
          [sticky]="col?.Sticky"
          [editable]="!col.Disable"
          [columnMenu]="!col.Hidden"
          [headerClass]="col.Class"
          class="{{
            (col.Format && col.Format.toLowerCase().startsWith('number|')) ||
            (col.Format && col.Format.toLowerCase().startsWith('datetime|')) ||
            (builder.options.configColumnSchema &&
              builder.options.configColumnSchema.fieldNumber &&
              builder.options.configColumnSchema.fieldNumber.indexOf(col.Name) >= 0) ||
            (builder.options.configColumnSchema.fieldNumberMoney &&
              builder.options.configColumnSchema.fieldNumberMoney.indexOf(col.Name) >= 0)
              ? col.Class + ' text-right'
              : col.Class
          }}"
        >
          <ng-template
            kendoGridHeaderTemplate
            let-column
            let-columnIndex="columnIndex"
            let-dataItem
          >
            <!-- *ngIf="!col.Disable" -->
            <!-- <span class="k-icon k-i-edit mr-1"></span> -->
            <span
              nz-icon
              nzType="edit"
              nzTheme="outline"
              class="mr-1"
              *ngIf="isEdited && builder.options?.configEdit?.isShowIconEdited"
            ></span>
            <span>
              <ng-container
                *ngIf="columnHeaderTemplate; else tplDefaultHeaderEditTemplate"
                [ngTemplateOutlet]="columnHeaderTemplate"
                [ngTemplateOutletContext]="{
                  $implicit: dataItem,
                  column: column,
                  columnIndex: columnIndex,
                  col: col,
                  fieldIndex: builder.options.configTitle.prefixTitleColumn
                }"
              ></ng-container>
              <ng-template #tplDefaultHeaderEditTemplate>
                {{ col.HasChangeDisplayName ? col.DisplayName : (col.HeaderKey | translate) }}
              </ng-template>
            </span>
            <span
              class="col-required ml-1"
              *ngIf="
                isEdited &&
                (col
                  | vnrGridColumnsValidators
                    : 'Required'
                    : builder.options?.configEdit?.configValidate || checkRequiredEdit(col.Name))
              "
              >*
            </span>
          </ng-template>

          <!-- Edit incell -->
          <ng-template
            kendoGridEditTemplate
            let-dataItem="dataItem"
            let-rowIndex="rowIndex"
            let-field="field"
            let-column="column"
            let-formGroup="formGroup"
            let-isNew="isNew"
          >
            <ng-container
              [ngTemplateOutlet]="col.editor"
              *ngIf="column.editor"
              [ngTemplateOutletContext]="{
                $implicit: dataItem,
                rowIndex: rowIndex,
                field: field,
                column: column,
                columnItem: col,
                formGroup: formGroup,
                isNew: isNew
              }"
            >
            </ng-container>
            <ng-container *ngIf="!col.editor">
              <ng-template
                [ngTemplateOutlet]="tplControlsEdit"
                [ngTemplateOutletContext]="{
                  $implicit: dataItem,
                  rowIndex: rowIndex,
                  field: field,
                  column: column,
                  columnItem: col,
                  formGroup: formGroup,
                  isNew: isNew
                }"
              ></ng-template>
            </ng-container>
          </ng-template>

          <!-- Group -->
          <ng-template
            kendoGridGroupHeaderTemplate
            let-group="group"
            let-field="field"
            let-value="value"
            let-aggregates="aggregates"
            let-index="index"
            *ngIf="col.Group"
          >
            <div
              [ngClass]="{
                hasCustomGroupHeader: customTemplateGroupColumnHeader && !isEdited
              }"
            >
              <span
                class="mr-1 ml-1"
                *ngIf="
                  builder.options.configShowHide.isShowColumnCheck &&
                  builder.options.configShowHide.isShowColumnGroupCheck
                "
              >
                <input
                  type="checkbox"
                  kendoCheckBox
                  class="k-checkbox"
                  (change)="checkGroup(group, index)"
                  [checked]="isGroupChecked(group)"
                  class="customCheckBtn"
                  id="id_{{ field }}_{{ value }}"
                />
              </span>
              <span
                class="vnr-grid-grouping-title"
                [ngClass]="{
                  'vnr-grid-grouping-title__checked':
                    builder.options.configShowHide.isShowColumnCheck &&
                    customTemplateGroupColumnHeader,
                  'vnr-grid-grouping-title__groupsHeader': customTemplateGroupColumnHeader
                }"
                title="{{ col | vnrGridGroupTitle : value : group }}"
              >
                <strong [class]="col?.Class + '__title-group'">{{
                  col.HasChangeDisplayName ? col.DisplayName : (col.HeaderKey | translate)
                }}</strong
                >:
                <ng-container *ngIf="col && col.Format; else tplNormalHeaderFormat">
                  <span [ngSwitch]="col.Format.split('|')[0].toLowerCase()">
                    <span *ngSwitchCase="'datetime'">
                      <ng-container>
                        {{ value | date : col.Format.split('|')[1] }}
                        ({{ group.items.length | json }})
                      </ng-container>
                    </span>
                    <span *ngSwitchDefault> {{ value }} ({{ group.items.length | json }}) </span>
                  </span>
                </ng-container>
                <ng-template #tplNormalHeaderFormat>
                  {{ value }} ({{ group.items.length | json }})
                </ng-template>
              </span>
              <span
                *ngIf="
                  (field | isLastGroup : builder.options.queryOption.group) &&
                  builder.options?.configEdit?.isShowAddRowGroupData &&
                  isEdited
                "
              >
                <ng-container *ngIf="addGroupButtonTemplate; else tplCustomAddRowGroup">
                  <ng-container
                    *ngIf="addGroupButtonTemplate"
                    [ngTemplateOutletContext]="{
                      $implicit: group,
                      index: index
                    }"
                    [ngTemplateOutlet]="addGroupButtonTemplate"
                  ></ng-container>
                </ng-container>
                <ng-template #tplCustomAddRowGroup>
                  <button
                    (click)="addRowHandler(grid, index)"
                    class="btn-kendoGrid-customize btn-kendoGrid-customize__add btn-kendoGrid-customize__rounded btn-kendoGrid-customize__small ant-btn ant-btn-primary"
                  >
                    <span>
                      <i class="far fa-plus"></i>
                    </span>
                  </button>
                </ng-template>
              </span>
            </div>
          </ng-template>

          <!-- Format -->
          <ng-template
            kendoGridCellTemplate
            let-dataItem
            let-rowIndex="rowIndex"
            let-column="column"
            let-field="field"
            *ngIf="isColumnHasFormatSpecial(col)"
          >
            <ng-template *ngIf="col.Format; then gridFormat; else elseSchema"></ng-template>
            <ng-template #gridFormat>
              <span *ngIf="col.Format.toLowerCase().startsWith('datetime|')">
                <ng-container
                  *ngIf="col.template || defaultColumnTemplate; else tplColumnDatetimeNoTemplate"
                  [ngTemplateOutlet]="col.template || defaultColumnTemplate"
                  [ngTemplateOutletContext]="{
                    $implicit: dataItem,
                    rowIndex: rowIndex,
                    field: field,
                    column: column,
                    columnItem: col
                  }"
                >
                </ng-container>
                <ng-template #tplColumnDatetimeNoTemplate>
                  {{ dataItem[column.field] | date : col.Format.split('|')[1] }}
                </ng-template>
              </span>
              <span *ngIf="col.Format.toLowerCase().startsWith('number|')">
                {{ dataItem[column.field] | kendoNumber : col.Format.split('|')[1] }}
              </span>
              <span *ngIf="col.Format.toLowerCase()?.startsWith('bool')">
                <label
                  nz-checkbox
                  [(ngModel)]="dataItem[column.field]"
                  nzDisabled
                  [name]="column.field + '__' + rowIndex"
                >
                </label>
              </span>

              <span *ngIf="col.Format.toLowerCase()?.startsWith('link')">
                <a [name]="column.field + '__' + rowIndex" [href]="dataItem[column.field]">
                  {{ dataItem[column.field] }}
                </a>
              </span>

              <span *ngIf="col.Format.toLowerCase()?.startsWith('file')">
                <ng-container
                  *ngIf="col.template; else tplColumnFileNoTemplate"
                  [ngTemplateOutlet]="col.template"
                  [ngTemplateOutletContext]="{
                    $implicit: dataItem,
                    rowIndex: rowIndex,
                    field: field,
                    column: column,
                    columnItem: col
                  }"
                >
                </ng-container>
                <ng-template #tplColumnFileNoTemplate>
                  <a
                    [name]="column.field + '__' + rowIndex"
                    (click)="onClickFileName(dataItem[column.field])"
                  >
                    {{ dataItem[column.field] }}
                  </a>
                </ng-template>
              </span>

              <div
                *ngIf="col.Format && col.Format.toLowerCase().startsWith('html')"
                [innerHTML]="dataItem[column.field]"
              ></div>
            </ng-template>
            <ng-template #elseSchema>
              <span
                *ngIf="
                  builder.options.configColumnSchema &&
                  builder.options.configColumnSchema.fieldBoolean &&
                  builder.options.configColumnSchema.fieldBoolean.indexOf(column.field) >= 0
                "
              >
                <label
                  nz-checkbox
                  [(ngModel)]="dataItem[column.field]"
                  nzDisabled
                  [name]="column.field + '__' + rowIndex"
                >
                </label>
              </span>

              <span
                *ngIf="
                  builder.options.configColumnSchema &&
                  builder.options.configColumnSchema.fieldDate &&
                  builder.options.configColumnSchema.fieldDate.indexOf(column.field) >= 0
                "
              >
                {{ dataItem[column.field] | date : dateFormat }}
              </span>

              <span
                *ngIf="
                  builder.options.configColumnSchema &&
                  builder.options.configColumnSchema.fieldNumber &&
                  builder.options.configColumnSchema.fieldNumber.indexOf(column.field) >= 0
                "
              >
                {{ dataItem[column.field] }}
              </span>

              <span
                *ngIf="
                  builder.options.configColumnSchema &&
                  builder.options.configColumnSchema.fieldNumberMoney &&
                  builder.options.configColumnSchema.fieldNumberMoney.indexOf(column.field) >= 0
                "
              >
                {{ dataItem[column.field] }}
              </span>
            </ng-template>
          </ng-template>

          <!-- #region Custom by Column Name -->
          <ng-template
            *ngIf="col.template || defaultColumnTemplate"
            kendoGridCellTemplate
            let-dataItem
            let-rowIndex="rowIndex"
            let-field="field"
            let-column="column"
          >
            <ng-container *ngIf="isColumnHasFormatSpecial(col); else defaultDataWithoutGroup">
              <ng-container
                *ngTemplateOutlet="
                  tplFormatTemplate;
                  context: {
                    $implicit: col,
                    dataItem: dataItem,
                    rowIndex: rowIndex
                  }
                "
              ></ng-container>
            </ng-container>

            <ng-template #defaultDataWithoutGroup>
              <ng-container
                [ngTemplateOutlet]="col.template || defaultColumnTemplate"
                [ngTemplateOutletContext]="{
                  $implicit: dataItem,
                  rowIndex: rowIndex,
                  field: field,
                  column: column,
                  columnItem: col
                }"
              >
              </ng-container>
            </ng-template>
          </ng-template>
          <!-- #endregion Custom by Column Name -->

          <!-- Fillter template menu-->
          <ng-template
            *ngIf="
              (col.Format && col.Format.toLowerCase().startsWith('datetime|')) ||
              (col.Format && col.Format.toLowerCase().startsWith('number|'))
            "
            kendoGridFilterMenuTemplate
            let-filter
            let-column="column"
            let-filterService="filterService"
          >
            <kendo-grid-date-filter-menu
              [column]="column"
              [filter]="filter"
              [filterService]="filterService"
              *ngIf="col.Format && col.Format.toLowerCase().startsWith('datetime|')"
              [format]="col.Format.split('|')[1]"
            >
            </kendo-grid-date-filter-menu>

            <kendo-grid-numeric-filter-menu
              [format]="col.Format.split('|')[1]"
              [column]="column"
              [filter]="filter"
              [filterService]="filterService"
              *ngIf="col.Format && col.Format.toLowerCase().startsWith('number|')"
            >
            </kendo-grid-numeric-filter-menu>
          </ng-template>

          <!--- Group Footer Template -->
          <ng-template
            kendoGridGroupFooterTemplate
            let-column
            let-columnIndex="columnIndex"
            let-aggregates
            let-field="field"
            let-group="group"
            let-dataItem
            let-rowIndex="rowIndex"
            *ngIf="customTemplateGroupColumnFooter && customTemplateGroupColumnFooter[col.Name]"
          >
            <ng-container
              [ngTemplateOutlet]="customTemplateGroupColumnFooter[col.Name]"
              [ngTemplateOutletContext]="{
                $implicit: dataItem,
                rowIndex: rowIndex,
                columnIndex: columnIndex,
                field: field,
                column: column,
                columnItem: col,
                aggregates: aggregates,
                aggregateItem: vnrAggregateResult,
                group: group
              }"
            >
            </ng-container>
          </ng-template>
          <!--- Group Header Template -->
          <ng-template
            kendoGridGroupHeaderColumnTemplate
            let-group="group"
            let-column
            let-columnIndex="columnIndex"
            let-aggregates
            let-field="field"
            let-dataItem
            let-rowIndex="rowIndex"
            *ngIf="customTemplateGroupColumnHeader && customTemplateGroupColumnHeader[col.Name]"
          >
            <ng-container
              [ngTemplateOutlet]="customTemplateGroupColumnHeader[col.Name]"
              [ngTemplateOutletContext]="{
                $implicit: dataItem,
                rowIndex: rowIndex,
                columnIndex: columnIndex,
                field: field,
                column: column,
                columnItem: col,
                aggregates: aggregates,
                aggregateItem: vnrAggregateResult,
                group: group
              }"
            >
            </ng-container>
          </ng-template>
          <!-- Footer -->
          <ng-template
            kendoGridFooterTemplate
            let-column
            let-columnIndex="columnIndex"
            let-aggregates
            let-field="field"
            let-dataItem
            let-rowIndex="rowIndex"
            *ngIf="customTemplateColumnFooter && customTemplateColumnFooter[col.Name]"
          >
            <ng-container
              [ngTemplateOutlet]="customTemplateColumnFooter[col.Name]"
              [ngTemplateOutletContext]="{
                $implicit: dataItem,
                rowIndex: rowIndex,
                columnIndex: columnIndex,
                field: field,
                column: column,
                columnItem: col,
                aggregates: aggregates,
                aggregateItem: vnrAggregateResult
              }"
            >
            </ng-container>
          </ng-template>
        </kendo-grid-column>
      </ng-template>
    </ng-container>

    <!-- #region Action Column -->
    <kendo-grid-command-column
      *ngIf="
        columns &&
        columns.length > 0 &&
        builder.options.configCommandColumn.isEnabledMenuAction &&
        (builder.options.configShowHide.isShowEdit ||
          builder.options.configShowHide.isShowDelete ||
          builder.options.configEdit.isShowSave ||
          rowActionsTemplate)
      "
      class="vnr-grid-action-column"
      [class]="isLastChild ? 'vnr-grid-action-column-last' : ''"
      [headerClass]="['vnr-grid-action-column-header']"
      [footerClass]="['vnr-grid-action-column-footer']"
      [style]="{ 'background-color': 'transparent' }"
      [sticky]="rowDetailTemplate ? false : true"
      title=""
      [width]="builder.options.configCommandColumn.width"
      [minResizableWidth]="builder.options.configCommandColumn.width || 100"
      [maxResizableWidth]="builder.options.configCommandColumn.width || 100"
      [columnMenu]="false"
    >
      <ng-template kendoGridCellTemplate let-dataItem let-isNew="isNew">
        <div
          class="vnr-grid-action-column__group-btn d-flex justify-content-end align-items-center"
          [ngClass]="{
            'vnr-grid-action-column__group-btn-hidden': !isHovered,
            'vnr-grid-action-column__group-btn-show': isHovered
          }"
        >
          <ng-template [ngTemplateOutlet]="tplCustomBtn"></ng-template>
          <div
            class="mr-1"
            *ngIf="
              !builder.options?.configEdit?.isEditAllRow &&
              builder.options?.configShowHide?.isShowEdit
            "
          >
            <button
              kendoGridEditCommand
              class="btn-kendoGrid-customize btn-kendoGrid-customize__edit ant-btn ant-btn-primary"
            >
              <span nz-icon nzType="edit" nzTheme="outline"></span>
            </button>
          </div>
          <div
            class="mr-1"
            *ngIf="
              !builder.options?.configEdit?.isEditAllRow &&
              builder.options?.configShowHide?.isShowDelete
            "
          >
            <button
              [ngClass]="{ 'd-block': isEdited }"
              kendoGridRemoveCommand
              (click)="removeHandler(dataItem, grid)"
              class="btn-kendoGrid-customize btn-kendoGrid-customize__delete ant-btn ant-btn-primary"
            >
              <i class="far fa-trash-alt"></i>
            </button>
          </div>
          <div
            class="mr-1"
            *ngIf="
              !builder.options?.configEdit?.isEditAllRow && builder.options?.configEdit?.isShowSave
            "
          >
            <button
              kendoGridSaveCommand
              class="btn-kendoGrid-customize btn-kendoGrid-customize__primary ant-btn ant-btn-primary"
              [disabled]="vnrGridInlineForm.invalid || vnrGridInlineForm.pristine"
            >
              <i class="fas fa-save"></i>
            </button>
          </div>
          <div class="mr-1" *ngIf="!builder.options?.configEdit?.isEditAllRow">
            <button
              kendoGridCancelCommand
              class="btn-kendoGrid-customize btn-kendoGrid-customize__restore ant-btn ant-btn-primary"
            >
              <i class="fas fa-undo"></i>
            </button>
          </div>
        </div>
        <ng-template #tplCustomBtn>
          <ng-container
            *ngIf="rowActionsTemplate"
            [ngTemplateOutletContext]="{ $implicit: dataItem }"
            [ngTemplateOutlet]="rowActionsTemplate"
          ></ng-container>
        </ng-template>
      </ng-template>
    </kendo-grid-command-column>
    <!-- #endregion -->

    <!-- #region Collapse Template -->
    <ng-template *ngIf="rowDetailTemplate" kendoGridDetailTemplate let-dataItem let-column="column">
      <ng-container
        [ngTemplateOutlet]="rowDetailTemplate"
        [ngTemplateOutletContext]="{
          $implicit: dataItem,
          column: column
        }"
      ></ng-container>
    </ng-template>
    <!-- #endregion Collapse Template -->

    <!-- #region Translate Messages -->
    <kendo-grid-messages
      noRecords="{{ 'common.grid.noData' | translate }}"
      pagerPage="{{ 'common.grid.pagerPage' | translate }}"
      pagerOf="{{ 'common.grid.pagerOf' | translate }}"
      pagerItems="{{ 'common.grid.pagerItems' | translate }}"
      pagerItemsPerPage="{{ 'common.grid.pagerItemsPerPage' | translate }}"
      sortAscending="{{ 'common.grid.sortAscending' | translate }}"
      sortDescending="{{ 'common.grid.sortDescending' | translate }}"
      columns="{{ 'common.grid.columns' | translate }}"
      columnMenu="{{ 'common.grid.columnMenu' | translate }}"
      [columnsReset]="'common.grid.columnsReset' | translate"
      [columnsApply]="'common.grid.columnsApply' | translate"
    >
    </kendo-grid-messages>
    <!--  #endregion -->

    <!-- #region Pager -->
    <ng-template
      kendoPagerTemplate
      let-totalPages="totalPages"
      let-currentPage="currentPage"
      *ngIf="builder.options.configShowHide.isPageExpand"
    >
      <div
        class="d-flex justify-content-center align-items-center"
        style="text-align: center; width: 100%; min-height: 41px"
        [ngClass]="{
          'vnrPageExpand-none': funcCalcCountLoadMore(gridView?.data) === gridView?.total
        }"
      >
        <button
          class="kendo-loadMore-customs"
          nz-button
          nzType="default"
          (click)="loadExpandBtn()"
          nzShape="round"
          *ngIf="
            gridView &&
            gridView.data &&
            gridView.data.length > 0 &&
            funcCalcCountLoadMore(gridView.data) < gridView.total
          "
          [disabled]="isLoading"
        >
          <span class="d-flex justify-content-center align-items-center" nz-icon>
            <span class="mr-1 d-flex" nz-icon *ngIf="!isLoading">
              <i class="fas fa-angle-down"></i>
            </span>
            <span class="mr-1" nz-icon [nzType]="'loading'" *ngIf="isLoading"></span>
            {{ 'loadMore' | translate }}
            <span *ngIf="gridView && gridView.data?.length > 0">
              ({{ funcCalcCountLoadMore(gridView.data) }}/{{ gridView.total }})
            </span>
          </span>
        </button>
      </div>
    </ng-template>
    <!-- #endregion -->
    <kendo-grid-command-column title="command" [width]="1">
      <ng-template kendoGridCellTemplate let-isNew="isNew">
        <button kendoGridEditCommand type="button" [primary]="true">Edit</button>
        <button kendoGridRemoveCommand type="button">Remove</button>
        <button kendoGridSaveCommand type="button">
          {{ isNew ? 'Add' : 'Update' }}
        </button>
        <button kendoGridCancelCommand type="button">
          {{ isNew ? 'Discard changes' : 'Cancel' }}
        </button>
      </ng-template>
    </kendo-grid-command-column>
  </kendo-grid>

  <!-- #region Change Column -->
  <change-column-old
    *ngIf="isOpenChangeColumn && !isChangeColumnNew"
    style="width: 100%"
    [builder]="builderGridChangeColumn"
    (vnrClosePopup)="vnrcloseAndRefreshGird($event)"
  ></change-column-old>
  <!-- #endregion -->
</form>
<ng-template
  #tplFormatTemplate
  let-column
  let-dataItem="dataItem"
  let-rowIndex="rowIndex"
  let-field="field"
>
  <div [ngSwitch]="column?.Format?.split('|')[0]?.toLowerCase()">
    <span *ngSwitchCase="'datetime'">
      <ng-container>
        {{ dataItem[column.Name] | date : column.Format.split('|')[1] }}
      </ng-container>
    </span>
    <span *ngSwitchCase="'number'">
      <ng-container *ngIf="column?.Format?.split('|')[1] === 'cp'; else tplColumnNoCustom">
        {{
          dataItem[column.Name] !== null
            ? (dataItem[column.Name] | kendoNumber : '#,##0.00 \\%')
            : '%'
        }}
      </ng-container>
      <ng-template #tplColumnNoCustom>
        {{ dataItem[column.Name] | kendoNumber : column.Format.split('|')[1] }}
      </ng-template>
    </span>
    <span *ngSwitchCase="'bool'">
      <label
        nz-checkbox
        [(ngModel)]="dataItem[column.Name]"
        nzDisabled
        [name]="column.Name + '__' + rowIndex"
      >
      </label>
    </span>
    <span *ngSwitchCase="'link'">
      <a [name]="column.Name + '__' + rowIndex" [href]="dataItem[column.Name]">
        {{ dataItem[column.Name] }}
      </a>
    </span>
    <span *ngSwitchCase="'file'">
      <ng-container
        *ngIf="column.template; else tplColumnFileNoTemplate1"
        [ngTemplateOutlet]="column.template"
        [ngTemplateOutletContext]="{
          $implicit: dataItem,
          rowIndex: rowIndex,
          field: column.Name,
          column: column,
          columnItem: column
        }"
      >
      </ng-container>
      <ng-template #tplColumnFileNoTemplate1>
        <span class="d-flex">
          <ng-container
            *ngFor="
              let file of dataItem[column.Name]
                | vnrConvertFileNameToList : builder.options.configApiSupport.apiDownload?.url : 25;
              let last = last
            "
          >
            <a
              class="vnr-grids-upload__box"
              (click)="onClickFileName(file.name); $event.preventDefault()"
              [title]="file && file?.showTooltip && file?.name ? file.name : file?.nameFormat"
              [name]="column.Name + '__' + rowIndex"
            >
              <div class="vnr-grids-upload__nameFile">
                {{ file && file.nameFormat }}
              </div>
            </a>
            <span class="mr-1" *ngIf="!last">,</span>
          </ng-container>
        </span>
      </ng-template>
    </span>
    <span *ngSwitchCase="'html'">
      <div [innerHTML]="dataItem[column.Name]"></div>
    </span>
    <span *ngSwitchCase="'money'">
      {{ dataItem[column.Name] | currency : 'VND' : '' : '1.0-3' : '' }}
    </span>
    <span *ngSwitchCase="'uploadfile'" class="--has-custom-template vnr-format-type-uploadfile">
      <ng-container *ngIf="dataItem[column.Name]; else tplUploadIcon">
        <div class="d-flex">
          <span
            (click)="onClickUploadFileToColumn(column.Name, dataItem, column)"
            class="mr-2"
            style="cursor: pointer"
          >
            <i nz-icon nzType="edit" nzTheme="twotone"></i>
          </span>
          <ng-container
            *ngFor="
              let file of dataItem[column.Name]
                | vnrConvertFileNameToList : builder.options.configApiSupport.apiDownload?.url : 25;
              let last = last
            "
          >
            <a
              class="vnr-grids-upload__box"
              (click)="onClickFileName(file.name); $event.preventDefault()"
              [title]="file && file?.showTooltip && file?.name ? file.name : file?.nameFormat"
              [name]="column.Name + '__' + rowIndex"
            >
              <div class="vnr-grids-upload__nameFile">
                {{ file && file.nameFormat }}
              </div>
            </a>
            <span class="mr-1" *ngIf="!last">,</span>
          </ng-container>
        </div>
      </ng-container>
      <ng-template #tplUploadIcon>
        <button
          nz-button
          [nzType]="'default'"
          [nzSize]="'small'"
          (click)="onClickUploadFileToColumn(column.Name, dataItem, column)"
          class="d-flex justify-content-center align-items-center m-auto"
        >
          <i nz-icon nzType="upload" nzTheme="outline"></i>
        </button>
      </ng-template>
    </span>
    <span *ngSwitchDefault>
      <ng-container
        *ngIf="column.template || defaultColumnTemplate"
        [ngTemplateOutlet]="column.template || defaultColumnTemplate"
        [ngTemplateOutletContext]="{
          $implicit: dataItem,
          rowIndex: rowIndex,
          field: field,
          column: column,
          columnItem: column
        }"
      >
      </ng-container>
      <ng-template #tplColumnNoFormatTemplate>
        {{ dataItem && dataItem[column['Name']] }}
      </ng-template>
    </span>
  </div>
</ng-template>

<!-- #region Controls Edit -->
<ng-template
  #tplControlsEdit
  let-dataItem
  let-rowIndex="rowIndex"
  let-field="field"
  let-column="column"
  let-columnItem="columnItem"
  let-formGroup="formGroup"
  let-isNew="isNew"
>
  <ng-container *ngIf="columnItem && !columnItem.Disable; else tplNotEditor">
    <ng-container *ngIf="dataItem?.IsDisabled && isReadOnly(columnItem?.Name); else tplEditor">
      <ng-container
        *ngIf="dataItem.template || defaultColumnTemplate; else tplColumnReadOnlyEditTemplate"
        [ngTemplateOutlet]="defaultColumnTemplate"
        [ngTemplateOutletContext]="{
          $implicit: dataItem,
          rowIndex: rowIndex,
          field: field,
          column: column,
          columnItem: columnItem
        }"
      >
      </ng-container>
      <ng-template #tplColumnReadOnlyEditTemplate>
        <ng-container
          *ngIf="
            (columnItem | vnrGridNewEditTypeControls : builder.options.configColumnSchema) ===
              vnrGridEditControls.ComboBox;
            else tplNotEditor
          "
        >
          {{
            dataItem &&
              dataItem[
                columnItem?.Name?.endsWith('ID')
                  ? columnItem?.Name?.slice(0, -2) + 'Name'
                  : columnItem?.Name
              ]
          }}
        </ng-container>
      </ng-template>
    </ng-container>
    <ng-template #tplEditor>
      <ng-container
        [ngSwitch]="columnItem | vnrGridNewEditTypeControls : builder.options.configColumnSchema"
      >
        <ng-container *ngSwitchCase="vnrGridEditControls.ComboBox">
          <vnr-combobox
            [attr.index]="dataItem?.ID"
            [formControl]="getFormGroup(rowIndex, column)"
            [attr.name]="columnItem.Name"
            [builder]="
              columnItem
                | vnrGridEditInitBuilderControls
                  : vnrGridEditControls.ComboBox
                  : dataItem
                  : builder.options?.configEdit?.builderConfigByColumn
            "
            [required]="
              columnItem
                | vnrGridColumnsValidators
                  : 'Required'
                  : builder.options?.configEdit?.configValidate
            "
            (ngModelChange)="onModelChangeComboBox($event, rowIndex, columnItem.Name, dataItem)"
            (selectDataItem)="onSelectDataItem($event, column, rowIndex)"
            (vnrOpen)="onOpenComboBox($event, rowIndex)"
          ></vnr-combobox>
        </ng-container>
        <ng-container *ngSwitchCase="vnrGridEditControls.DatePicker">
          <vnr-datepicker
            [attr.index]="dataItem?.ID"
            [formControl]="getFormGroup(rowIndex, column)"
            [attr.name]="columnItem.Name"
            [builder]="columnItem | vnrGridEditInitBuilderControls : vnrGridEditControls.DatePicker"
            [required]="
              columnItem
                | vnrGridColumnsValidators
                  : 'Required'
                  : builder.options?.configEdit?.configValidate
            "
            (ngModelChange)="onModelChange($event, rowIndex, columnItem.Name, dataItem)"
          ></vnr-datepicker>
        </ng-container>
        <ng-container *ngSwitchCase="vnrGridEditControls.InputNumber">
          <vnr-inputnumber
            [attr.index]="dataItem?.ID"
            [formControl]="getFormGroup(rowIndex, column)"
            [attr.name]="columnItem.Name"
            [builder]="
              columnItem | vnrGridEditInitBuilderControls : vnrGridEditControls.InputNumber
            "
            [required]="
              columnItem
                | vnrGridColumnsValidators
                  : 'Required'
                  : builder.options?.configEdit?.configValidate
            "
            (ngModelChange)="onModelChangeInputNumber($event, rowIndex, columnItem.Name, dataItem)"
          ></vnr-inputnumber>
        </ng-container>
        <ng-container *ngSwitchCase="vnrGridEditControls.InputMoney">
          <vnr-input-money
            [attr.index]="dataItem?.ID"
            [attr.name]="columnItem.Name"
            [formControl]="getFormGroup(rowIndex, column)"
            [builder]="columnItem | vnrGridEditInitBuilderControls : vnrGridEditControls.InputMoney"
            [required]="
              columnItem
                | vnrGridColumnsValidators
                  : 'Required'
                  : builder.options?.configEdit?.configValidate
            "
            (ngModelChange)="onModelChangeMoney($event, rowIndex, columnItem.Name, dataItem)"
          >
          </vnr-input-money>
        </ng-container>
        <ng-container *ngSwitchCase="vnrGridEditControls.CheckBox">
          <label
            nz-checkbox
            [formControl]="getFormGroup(rowIndex, column)"
            [attr.name]="columnItem.Name"
            (ngModelChange)="onModelChange($event, rowIndex, columnItem.Name, dataItem)"
          ></label>
        </ng-container>
        <ng-container *ngSwitchCase="vnrGridEditControls.TextBox">
          <vnr-input
            [attr.index]="dataItem?.ID"
            [formControl]="getFormGroup(rowIndex, column)"
            [attr.name]="columnItem.Name"
            [builder]="columnItem | vnrGridEditInitBuilderControls : vnrGridEditControls.TextBox"
            [required]="
              columnItem
                | vnrGridColumnsValidators
                  : 'Required'
                  : builder.options?.configEdit?.configValidate
            "
            [maxlength]="
              columnItem
                | vnrGridColumnsValidators
                  : 'MaxLength'
                  : builder.options?.configEdit?.configValidate
            "
            [minlength]="
              columnItem
                | vnrGridColumnsValidators
                  : 'MinLength'
                  : builder.options?.configEdit?.configValidate
            "
            (ngModelChange)="onModelChange($event, rowIndex, columnItem.Name, dataItem)"
          >
          </vnr-input>
        </ng-container>
        <ng-container *ngSwitchCase="vnrGridEditControls.CustomControl">
          <ng-container *ngIf="customControlTemplate; else templateInput">
            <ng-template
              [ngTemplateOutlet]="customControlTemplate"
              [ngTemplateOutletContext]="{
                $implicit: dataItem,
                rowIndex: rowIndex,
                columnItem: columnItem,
                formGroup: formGroup,
                isNew: isNew,
                required:
                  columnItem
                  | vnrGridColumnsValidators
                    : 'Required'
                    : builder.options?.configEdit?.configValidate,
                maxlength:
                  columnItem
                  | vnrGridColumnsValidators
                    : 'MaxLength'
                    : builder.options?.configEdit?.configValidate,
                minlength:
                  columnItem
                  | vnrGridColumnsValidators
                    : 'MinLength'
                    : builder.options?.configEdit?.configValidate,
                min:
                  columnItem
                  | vnrGridColumnsValidators : 'Min' : builder.options?.configEdit?.configValidate,
                max:
                  columnItem
                  | vnrGridColumnsValidators : 'Max' : builder.options?.configEdit?.configValidate,
                validateConfig: builder.options?.configEdit?.configValidate
              }"
            ></ng-template>
          </ng-container>
          <ng-template #templateInput>
            <vnr-input
              [formControl]="getFormGroup(rowIndex, column)"
              [attr.index]="dataItem?.ID"
              [attr.name]="columnItem.Name"
              [builder]="columnItem | vnrGridEditInitBuilderControls : vnrGridEditControls.TextBox"
              [required]="
                columnItem
                  | vnrGridColumnsValidators
                    : 'Required'
                    : builder.options?.configEdit?.configValidate
              "
              [maxlength]="
                columnItem
                  | vnrGridColumnsValidators
                    : 'MaxLength'
                    : builder.options?.configEdit?.configValidate
              "
              [minlength]="
                columnItem
                  | vnrGridColumnsValidators
                    : 'MinLength'
                    : builder.options?.configEdit?.configValidate
              "
              (ngModelChange)="onModelChange($event, rowIndex, columnItem.Name, dataItem)"
            >
            </vnr-input>
          </ng-template>
        </ng-container>
        <ng-container *ngSwitchCase="vnrGridEditControls.TextArea">
          <vnr-textarea
            [formControl]="getFormGroup(rowIndex, column)"
            [attr.index]="dataItem?.ID"
            [attr.name]="columnItem.Name"
            [builder]="columnItem | vnrGridEditInitBuilderControls : vnrGridEditControls.TextArea"
            [required]="
              columnItem
                | vnrGridColumnsValidators
                  : 'Required'
                  : builder.options?.configEdit?.configValidate
            "
            [maxlength]="
              columnItem
                | vnrGridColumnsValidators
                  : 'MaxLength'
                  : builder.options?.configEdit?.configValidate
            "
            [minlength]="
              columnItem
                | vnrGridColumnsValidators
                  : 'MinLength'
                  : builder.options?.configEdit?.configValidate
            "
            (ngModelChange)="onModelChange($event, rowIndex, columnItem.Name, dataItem)"
          >
          </vnr-textarea>
        </ng-container>
        <ng-container *ngSwitchCase="vnrGridEditControls.Switch">
          <vnr-switch
            [formControl]="getFormGroup(rowIndex, column)"
            [attr.index]="dataItem?.ID"
            [attr.name]="columnItem.Name"
            [builder]="columnItem | vnrGridEditInitBuilderControls : vnrGridEditControls.Switch"
            [required]="
              columnItem
                | vnrGridColumnsValidators
                  : 'Required'
                  : builder.options?.configEdit?.configValidate
            "
            (ngModelChange)="onModelChange($event, rowIndex, columnItem.Name, dataItem)"
          >
          </vnr-switch>
        </ng-container>
        <ng-container *ngSwitchDefault>
          <ng-container
            *ngTemplateOutlet="
              tplFormatTemplate;
              context: {
                $implicit: columnItem,
                dataItem: dataItem,
                rowIndex: rowIndex
              }
            "
          ></ng-container>
        </ng-container>
      </ng-container>
    </ng-template>
  </ng-container>
  <ng-template #tplNotEditor>
    <ng-container
      *ngTemplateOutlet="
        tplFormatTemplate;
        context: {
          $implicit: columnItem,
          dataItem: dataItem,
          rowIndex: rowIndex
        }
      "
    ></ng-container>
  </ng-template>
</ng-template>
<!-- #endregion -->
