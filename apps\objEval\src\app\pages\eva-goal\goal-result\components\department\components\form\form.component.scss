:host {
  display: block;
}

::ng-deep {
  .wrapper-vnrTag {
    .ant-tag {
      min-height: 24px;
      border-radius: 2px;
      font-weight: 500;
      font-size: 13px;
      border: 0;
      line-height: 24px;
    }
  }
}

.detail-goal-result {
  .text-title {
    font-weight: 500;
    color: #616161;
  }

  .text-content {
    color: #303030;
  }

  .text-result {
    font-weight: 700;
    font-size: 18px;
  }
}

.goal-result__filter {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}

/* --- Status Pill Styles (theo mẫu) --- */
.status-pill {
  border-radius: 8px;
  padding: 4px 8px;
  font-family: Roboto, Arial, sans-serif;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
  cursor: pointer;
  user-select: none;
  outline: none;
  background: #fff;
  border: 1px solid #e3e3e3;
  color: #303030;
}
.status-pill--doing {
  background: #f0f7ff;
  border-color: #256aeb;
  color: #1e45af;
}
.status-pill--done {
  background: #e8f5e9;
  border-color: #13681c;
  color: #13681c;
}
.status-pill--notdone {
  background: #fff0f0;
  border-color: #ab1316;
  color: #ab1316;
}
.status-pill--cancel {
  background: #fff7e6;
  border-color: #85550e;
  color: #85550e;
}
.status-pill--pause {
  background: #f4f4f4;
  border-color: #bdbdbd;
  color: #757575;
}
.status-pill--notstart {
  background: #f9f9f9;
  border-color: #e3e3e3;
  color: #303030;
}
.status-pill--active {
  box-shadow: 0 0 0 2px #256aeb22;
  font-weight: 700;
  filter: brightness(0.95);
}
.status-pill:focus {
  outline: 2px solid #256aeb44;
}
.status-pill:hover {
  filter: brightness(0.97);
}

.goal-result__filter-label {
  margin-right: 2px;
}

.goal-result__filter-count {
  font-size: 12px;
  color: #888;
  margin-left: 2px;
}

@media (max-width: 600px) {
  .goal-result__filter {
    gap: 4px;
  }
  .goal-result__filter-btn {
    padding: 4px 8px;
    font-size: 12px;
  }
}

.grid-container {
  display: grid;
  grid-template-columns: auto auto auto;
  grid-gap: 10px;
  border-top: 1px solid #e3e3e3;
  line-height: 28px;
  padding: 20px 0 0 0;

  .item2 {
    border-right: 1px solid #e3e3e3;
    border-left: 1px solid #e3e3e3;
    padding: 10px 0;
  }
}

.grid-container > div {
  text-align: center;
  padding: 10px 0;
  font-size: 16px;
}
