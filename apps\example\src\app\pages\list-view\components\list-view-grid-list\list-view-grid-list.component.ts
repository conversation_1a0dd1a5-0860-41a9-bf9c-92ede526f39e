import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  OnInit,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';
import { UntypedFormBuilder } from '@angular/forms';
import { vnrUtilities } from '@hrm-frontend-workspace/common';
import { VnrModuleModule } from '@hrm-frontend-workspace/ui';
import {
  VnrButtonFactory,
  VnrButtonNewBuilder,
  VnrButtonNewComponent,
  VnrListviewNewBuilder,
  VnrListviewNewComponent,
  VnrToolbarNewBuilder,
  VnrToolbarNewComponent,
} from '@hrm-frontend-workspace/vnr-module';
import { TranslateModule } from '@ngx-translate/core';
import { RowClassArgs } from '@progress/kendo-angular-treelist';
import { cloneDeep } from 'lodash';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { gridDefineColumns } from '../../data/grid-define-column.data';
import { gridDefineDataSource } from '../../data/grid-define-data-source.data';
import { GridNewChildListComponent } from './components/grid-new-child-list.component';

@Component({
  selector: 'list-view-grid-list',
  templateUrl: './list-view-grid-list.component.html',
  styleUrls: ['./list-view-grid-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NzTagModule,
    VnrToolbarNewComponent,
    VnrModuleModule,
    TranslateModule,
    VnrListviewNewComponent,
    GridNewChildListComponent,
    VnrButtonNewComponent,
  ],
})
export class ListviewGridListComponent implements OnInit, AfterViewInit {
  @ViewChild('vnrListview', { static: true }) listViewControl: VnrListviewNewComponent;
  private _permission: string = 'New_PortalV3_Personal_Hre_Dependant';
  private _screenName: string = 'grid-new-list';
  private _storeName: string = 'hrm_hr_sp_get_ProfileTest';
  protected gridName: string = 'PortalNew_GridNewExample';
  protected isSupperAdmin: boolean = true;
  protected dataLocal = gridDefineDataSource;
  protected columns = gridDefineColumns;

  protected isShowOpenColumnCheck: boolean = true;
  protected isShowSelectAll: boolean = true;
  protected isShowExpandAll: boolean = true;
  protected screenWidth: number;
  protected screenHeight: number;
  protected compRef: ViewContainerRef;
  protected gridHeight: number;
  protected builderListview: VnrListviewNewBuilder;
  protected builderToolbar: VnrToolbarNewBuilder;
  private _btnFactory: VnrButtonFactory = VnrButtonFactory.init();
  protected builderbtnOpenColumnCheck: VnrButtonNewBuilder;
  protected builderbtnCancelOpenColumnCheck: VnrButtonNewBuilder;
  protected builderbtnSelectAll: VnrButtonNewBuilder;
  protected builderbtnCancelSelectAll: VnrButtonNewBuilder;
  protected builderbtnExpandAll: VnrButtonNewBuilder;
  protected builderbtnCollapseAll: VnrButtonNewBuilder;
  protected builderbtnAddGoalItem: VnrButtonNewBuilder;
  protected builderbtnChangeColumn: VnrButtonNewBuilder;
  //#endregion
  constructor(private fb: UntypedFormBuilder) {}
  ngAfterViewInit() {}
  ngOnInit() {
    this.builderButtonComponent();
    this.builderGridComponent();
    this.builderToolbarComponent();
  }
  private builderButtonComponent() {
    this.builderbtnOpenColumnCheck = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'example.btnOpenColumnCheck',
      options: {
        classNames: ['mr-1'],
        style: 'default',
        icon: {
          fontIcon: 'check-square',
        },
      },
    });
    this.builderbtnCancelOpenColumnCheck = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'example.btnCancelOpenColumnCheck',
      options: {
        classNames: ['mr-1'],
        style: 'default',
        icon: {
          fontIcon: 'stop',
        },
      },
    });

    this.builderbtnSelectAll = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'example.btnSelectAll',
      options: {
        classNames: ['mr-1'],
        style: 'default',
        icon: {
          fontIcon: 'check-square',
        },
      },
    });
    this.builderbtnCancelSelectAll = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'example.btnCancelSelectAll',
      options: {
        classNames: ['mr-1'],
        style: 'default',
        icon: {
          fontIcon: 'stop',
        },
      },
    });
    this.builderbtnExpandAll = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'example.btnExpandAll',
      options: {
        classNames: ['mr-1'],
        style: 'default',
        icon: {
          fontIcon: 'arrows-alt',
        },
      },
    });
    this.builderbtnCollapseAll = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'example.btnCollapseAll',
      options: {
        classNames: ['mr-1'],
        style: 'default',
        icon: {
          fontIcon: 'shrink',
        },
      },
    });
    this.builderbtnAddGoalItem = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'example.btnAddNew',
      options: {
        classNames: ['mr-1'],
        style: 'outline-primary',
        icon: {
          fontIcon: 'plus',
        },
      },
    });
    this.builderbtnChangeColumn = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'example.btnChangeColumn',
      options: {
        classNames: ['mr-1'],
        style: 'outline-primary',
        icon: {
          fontIcon: 'sync',
        },
      },
    });
  }
  private builderGridComponent() {
    this.builderListview = new VnrListviewNewBuilder({
      options: {
        queryOption: {
          take: 5,
        },
        configHeightGrid: {
          isAllowCalcRowHeight: true,
        },
        displayField: 'GoalName',
        configSelectable: {
          columnKey: 'Id',
        },
        configShowHide: {
          isPageExpand: false,
          isShowViewDetail: true,
          isShowColumnCheck: false,
        },
      },
    });
  }
  private builderToolbarComponent() {
    this.builderToolbar = new VnrToolbarNewBuilder({
      isShowConfig: true,
      gridName: this.gridName,
      isSupperAdmin: this.isSupperAdmin,
      //gridRef: this.listViewControl,
      permission: this._permission,
      screenName: this._screenName,
      storeName: this._storeName,
      options: {
        configButtonChangeColumn: {
          isShow: true,
          isConfigMenuDisplay: true,
          isChangeColumnNew: true,
          isDisabled: false,
          configButtonSetting: {
            isShow: true,
            isDisabled: true,
          },
        },
        configButtonExport: {
          isShowBtnExcelAll: true,
          isShowBtnWord: true,
          isShowBtnExcelByTemplate: true,
        },
        configButtonDelete: {
          isShow: true,
          isDisabled: false,
        },
        configQuickSearch: {
          textPlaceHolder: 'Tìm kiếm theo mã, tên...',
          searchKey: 'ProfileName',
          isShowBtn: true,
        },

        isSetBackground: true,
        buttonMoreFlexDirection: 'inherit',
        isExpanded: false,
      },
    });
  }
  getSelectedID($event) {
    console.log($event, 'getSelectedID');
  }
  getDataItem($event) {
    console.log($event, 'getDataItem');
  }
  onOpenDetail($event) {
    console.log($event, 'onOpenDetail');
  }
  onVnRViewModeGrid($event) {
    console.log($event, 'onVnRViewModeGrid');
  }
  onGridEdit($event) {
    console.log($event, 'onGridEdit');
  }
  onGridDelete($event) {
    console.log($event, 'onGridDelete');
  }
  onGridViewDetail($event) {
    console.log($event, 'onGridViewDetail');
  }
  onGridCellClick($event) {
    console.log($event, 'onGridCellClick');
  }
  selected: string = 'rightTop';
  toggleChangeColumn(isNewVersion: boolean) {
    this.listViewControl?.setOpenChangeColumn(true);
    this.listViewControl?.setChangeColumnVersion(isNewVersion);
  }
  reload() {
    this.isSupperAdmin = !this.isSupperAdmin;
    setTimeout(() => {
      this.listViewControl?.vnrReadGrid();
    });
  }
  reload1() {
    this.isSupperAdmin = !this.isSupperAdmin;
    setTimeout(() => {
      this.listViewControl?.vnrReloadGrid();
    });
  }
  protected onChangesFilter(dataSearch: any) {
    //let typeBusiness = this.builder.options?.configFilterAdvance?.typeOfBusiness;
    //this.valuePath[typeBusiness] = dataSearch;
    let searchValue: any = {};
    if (dataSearch) {
      let searchValueClone = cloneDeep(dataSearch);
      searchValue = vnrUtilities.convertArraysToStrings(searchValueClone);
    }
    this.listViewControl.setDataFilter(searchValue);
    this.listViewControl.vnrReadGrid();
  }
  protected rowClass = (context: RowClassArgs) => {
    return context.dataItem.Details ? 'row-details' : '';
  };
  protected readonlyEventHandler(event: any) {
    return event.column.field === 'FullName' && event.viewItem?.hasChildren;
  }
  protected onSelectAll($event) {
    this.listViewControl.onSelectAll();
    this.isShowSelectAll = !this.isShowSelectAll;
  }
  protected onCancelSelectAll($event) {
    this.listViewControl.onCancelSelectAll();
    this.isShowSelectAll = !this.isShowSelectAll;
  }
  protected onExpandAll($event) {
    this.listViewControl.onExpandAll();
    this.isShowExpandAll = !this.isShowExpandAll;
  }
  protected onCollapseAll($event) {
    this.listViewControl.onCollapseAll();
    this.isShowExpandAll = !this.isShowExpandAll;
  }
  protected onOpenColumnCheck($event) {
    this.listViewControl.onOpenColumnCheck();
    this.isShowOpenColumnCheck = !this.isShowOpenColumnCheck;
  }
  protected onCancelOpenColumnCheck($event) {
    this.listViewControl.onCancelOpenColumnCheck();
    this.isShowOpenColumnCheck = !this.isShowOpenColumnCheck;
  }
  protected onAddGoalItem($event, dataItem: any) {
    $event.stopPropagation();
    console.log(dataItem, 'onAddGoalItem');
  }
  protected onLoadData($event) {
    this.isShowExpandAll = true;
    console.log($event, 'onLoadData');
  }
}
