import { Route } from '@angular/router';
import { AuthGuard } from '@hrm-frontend-workspace/core';
import {
  InitLayoutResolver,
  LayoutAuthComponent,
  LayoutMainPortalComponent,
  LayoutModuleComponent,
  LayoutPublicComponent,
} from '@hrm-frontend-workspace/layout';
import { ModuleNameEnum } from '@hrm-frontend-workspace/models';
import { loadRemoteModule } from '@nx/angular/mf';
import { Error404PageComponent } from './pages/auth/404/404.component';

export const appRoutes: Route[] = [
  {
    path: '',
    redirectTo: 'dashboard/portal',
    pathMatch: 'full',
    resolve: [InitLayoutResolver],
  },
  {
    path: '',
    component: LayoutModuleComponent,
    children: [
      {
        path: 'example',
        canActivate: [AuthGuard],
        resolve: [InitLayoutResolver],
        data: { moduleName: ModuleNameEnum.EXAMPLE_MODULE },
        loadChildren: () => loadRemoteModule('example', './Routes').then((m) => m!.remoteRoutes),
      },
      {
        path: 'objEval',
        canActivate: [AuthGuard],
        resolve: [InitLayoutResolver],
        data: { moduleName: ModuleNameEnum.OBJECTIVE_EVALUATION_MODULE },
        loadChildren: () => loadRemoteModule('objEval', './Routes').then((m) => m!.remoteRoutes),
      },
      {
        path: 'human-resources',
        canActivate: [AuthGuard],
        resolve: [InitLayoutResolver],
        data: { moduleName: ModuleNameEnum.HUMAN_RESOURCES_MODULE },
        loadChildren: () =>
          loadRemoteModule('human-resources', './Routes').then((m) => m!.remoteRoutes),
      },
    ],
  },
  {
    path: '',
    component: LayoutMainPortalComponent,
    resolve: [InitLayoutResolver],
    data: { moduleName: ModuleNameEnum.PORTAL },
    children: [
      {
        path: 'dashboard',
        canActivate: [AuthGuard],
        loadChildren: () => loadRemoteModule('dashboard', './Routes').then((m) => m!.remoteRoutes),
      },
    ],
  },
  {
    path: 'auth',
    component: LayoutAuthComponent,
    children: [
      {
        path: '',
        loadChildren: () => import('./pages/auth/auth.module').then((m) => m.AuthModule),
      },
    ],
  },
  {
    path: 'public',
    component: LayoutPublicComponent,
    children: [
      {
        path: '',
        loadChildren: () => import('./pages/public/public.module').then((m) => m.PublicModule),
      },
    ],
  },
  {
    path: '404',
    component: Error404PageComponent,
  },
  {
    path: '**',
    redirectTo: '404',
  },
];
