import { ChangeDetectionStrategy, Component, Input, OnInit } from '@angular/core';
import { VnrGridNewEditInlineBuilder } from '@hrm-frontend-workspace/vnr-module';
import { gridGoalDetailDefineColumns } from '../../data/goal-list-column.data';
import { ObjSharedModule } from '../../../../../../app/shared';
import { statusColorMap, statusTextMap } from '../../../shared/enums/status.enum';
import { NzModalService } from 'ng-zorro-antd/modal';
import { TranslateService } from '@ngx-translate/core';
import { FormAllocationGoalDetailComponent } from '../../../shared/components/form-allocation-goal-detail/form-allocation-goal-detail.component';
import { quarterColumns } from '../../../shared/data/column.data';

@Component({
  selector: 'app-goal-detail-list',
  imports: [ObjSharedModule],
  templateUrl: './goal-detail-list.component.html',
  styleUrl: './goal-detail-list.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GoalDetailListComponent implements OnInit {
  @Input() dataItem: any;
  protected builderGrid: VnrGridNewEditInlineBuilder;

  protected statusColorMap = statusColorMap;
  protected statusTextMap = statusTextMap;

  protected gridName = 'ObjEval_ListGoalDetail';
  protected isSupperAdmin = true;
  protected columns: any[];
  protected dataLocal = [];

  ngOnInit() {
    (this.dataLocal = [
      {
        ...this.dataItem,
        Quarter1: 40,
        Quarter2: 40,
        Quarter3: 40,
        Quarter4: 40,
        Allocated: 100,
        Registered: 80,
        Attachment: 'tài liệu.pdf',
      },
    ]),
      this.builderGridComponent();
  }

  constructor(private modalService: NzModalService, private translate: TranslateService) {
    this.columns = [...gridGoalDetailDefineColumns];
    this.columns.splice(6, 0, ...quarterColumns);
  }

  private builderGridComponent() {
    console.log(this.columns);
    this.builderGrid = new VnrGridNewEditInlineBuilder({
      options: {
        isResizable: true,
        configHeightGrid: {
          gridHeight: 150,
        },
        configSelectable: {
          columnKey: 'Id',
        },
        configCommandColumn: {
          isEnabledMenuAction: true,
        },
        configShowHide: {
          isShowDelete: false,
          isShowEdit: false,
          isShowColumnCheck: false,
        },
      },
    });
  }

  protected onOpenAllocationGoalDetailModal(data: any) {
    this.modalService.create({
      nzTitle: this.translate.instant('objEval.GoalPeriod.AllocationDetail'),
      nzContent: FormAllocationGoalDetailComponent,
      nzData: data,
      nzWidth: 700,
      nzMaskClosable: false,
      nzFooter: null,
    });
  }
}
