import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { finalize } from 'rxjs/operators';
import { TranslateModule } from '@ngx-translate/core';
import { ObjSharedModule } from '../../../../../../shared/obj-shared.module';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { TargetFormatPipe } from '../../../../shared/pipe/target-format.pipe';
import {
  VnrTreelistNewBuilder,
  VnrRadioButtonBuilder,
  VnrComboBoxBuilder,
  VnrTextBoxBuilder,
  VnrTreeViewBuilder,
  VnrTreelistEditComponent,
  VnrCheckBoxLabelBuilder,
} from '@hrm-frontend-workspace/vnr-module';
import { gridDepartmentDefineColumnsAi } from '../../../data/column.data';
import { ChangeDetectorRef } from '@angular/core';
import { appraisalsKpiDepartmentDataSource } from '../../../../../eva-appraisals/appraisals/appraisals-kpi/data/datasource-component.data';
import { VnrAiService } from '@hrm-frontend-workspace/common';
import { NzDrawerRef } from 'ng-zorro-antd/drawer';
import { CommonService } from '@hrm-frontend-workspace/core';
@Component({
  selector: 'form-ai-create-goal',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule,
    ObjSharedModule,
    NzSelectModule,
    TargetFormatPipe,
    VnrTreelistEditComponent,
  ],
  templateUrl: './form-ai-create-goal.component.html',
  styleUrl: './form-ai-create-goal.component.scss',
})
export class FormAiCreateGoalComponent implements OnInit {
  @ViewChild('vnrTreeList', { static: false }) vnrTreeList: VnrTreelistEditComponent;
  protected builderTreelist: VnrTreelistNewBuilder;
  protected industryBuilder: VnrTextBoxBuilder;
  protected departmentBuilder: VnrComboBoxBuilder;
  protected representativeBuilder: VnrComboBoxBuilder;
  protected additionalPromptBuilder: VnrTextBoxBuilder;
  protected missionVisionBuilder: VnrTextBoxBuilder;
  protected goalTypeBuilder: VnrRadioButtonBuilder;
  protected builderOrg: VnrTreeViewBuilder;
  protected goalTemplateBuilder: VnrCheckBoxLabelBuilder;
  protected askAIBuilder: VnrTextBoxBuilder;
  protected departmentRadioBuilder: VnrRadioButtonBuilder;
  protected isVisiblePopup = false;
  protected aiForm: FormGroup;
  protected done = false;
  protected isLoading = false;
  protected allocationType = 'department';
  private departments = [
    { id: 1, name: 'Phòng Kế toán' },
    { id: 2, name: 'Phòng Kinh doanh' },
    { id: 3, name: 'Phòng Nhân sự' },
    { id: 4, name: 'Phòng IT' },
    { id: 5, name: 'Phòng Marketing' },
  ];

  private representatives = [
    { id: 1, name: 'Hoàng Vũ Anh Quân' },
    { id: 2, name: 'Lê Hồng Hải' },
    { id: 3, name: 'Nguyễn Xuân Hoài' },
    { id: 4, name: 'Nguyễn Thị Thúy' },
  ];
  protected gridName = 'abc';
  protected dataLocal = [];
  protected columns = gridDepartmentDefineColumnsAi;

  constructor(
    private fb: FormBuilder,
    private vnrAiService: VnrAiService,
    private message: NzMessageService,
    private cdr: ChangeDetectorRef,
    private commonService: CommonService,
    private drawerRef: NzDrawerRef,
  ) {}

  ngOnInit() {
    this.initForm();
    this.builderGridComponent();
    this.initBuilder();
  }

  protected onChangeAllocationType(event: string) {
    this.allocationType = event;
  }

  private initBuilder() {
    this.departmentRadioBuilder = new VnrRadioButtonBuilder({
      label: 'Đối tượng thực hiện',
      dataSource: [
        { value: 'department', label: 'Phòng ban' },
        { value: 'employee', label: 'Cá nhân' },
      ],
      textField: 'label',
      valueField: 'value',
    });
    this.industryBuilder = new VnrTextBoxBuilder({
      placeholder: 'Nhập ngành nghề của công ty',
      label: 'Ngành nghề',
    });
    this.departmentBuilder = new VnrComboBoxBuilder({
      placeholder: 'Chọn phòng ban',
      label: 'Phòng ban',
      valueField: 'name',
      textField: 'name',
      dataSource: this.departments,
    });
    this.representativeBuilder = new VnrComboBoxBuilder({
      placeholder: 'Chọn người đại diện',
      label: 'Người đại diện',
      valueField: 'name',
      textField: 'name',
      dataSource: this.representatives,
    });
    this.additionalPromptBuilder = new VnrTextBoxBuilder({
      placeholder: 'Nhập yêu cầu bổ sung để AI hiểu rõ hơn về mục tiêu của bạn',
      label: 'Yêu cầu bổ sung',
    });
    this.missionVisionBuilder = new VnrTextBoxBuilder({
      placeholder: 'Sứ mệnh và tầm nhìn của công ty',
      label: 'Sứ mệnh / tầm nhìn',
    });
    this.goalTypeBuilder = new VnrRadioButtonBuilder({
      placeholder: 'Chọn phương pháp',
      label: 'Phương pháp',
      valueField: 'value',
      textField: 'label',
      dataSource: [
        {
          value: 'KPI',
          label: 'KPI',
        },
        {
          value: 'OKR',
          label: 'OKR',
        },
        {
          value: 'BSC',
          label: 'BSC',
        },
      ],
    });
    this.builderOrg = new VnrTreeViewBuilder({
      label: 'Phòng ban',
      placeholder: 'Chọn phòng ban',
      valueField: 'Name',
      textField: 'Name',
      childKey: 'ListChild',
      options: {
        hasFeedBack: false,
        checkable: true,
        maxTagCount: 1,
        scrollX: true,
      },
      dataSource: appraisalsKpiDepartmentDataSource,
    });
    this.goalTemplateBuilder = new VnrCheckBoxLabelBuilder({
      textField: 'GoalGroup',
      valueField: 'GoalGroup',
      dataSource: [],
      label: 'Chọn từ kho mục tiêu',
    });
    this.askAIBuilder = new VnrTextBoxBuilder({
      placeholder: 'Nhập yêu cầu bổ sung để AI hiểu rõ hơn về mục tiêu của bạn',
      label: 'Yêu cầu bổ sung',
    });
  }

  private builderGridComponent() {
    this.builderTreelist = new VnrTreelistNewBuilder({
      options: {
        configHeightGrid: {
          gridHeight: 400,
        },
        displayField: 'GoalName',
        configSelectable: {
          columnKey: 'ID',
          groupKey: 'ParentID',
        },
        configCommandColumn: {
          isEnabledMenuAction: false,
        },
        configShowHide: {
          isShowViewDetail: false,
          isPageExpand: false,
          isShowColumnCheck: false,
          isShowDelete: false,
          isShowEdit: false,
        },
      },
    });
  }

  private initForm() {
    this.aiForm = this.fb.group({
      goalType: ['BSC', Validators.required],
      allocationType: ['department', Validators.required],
      department: [1, Validators.required],
      representative: [1, Validators.required],
      additionalPrompt: [''],
      goalTemplate: [''],
      // Các trường thông tin công ty
      industry: ['Công nghệ thông tin', Validators.required],
      missionVision: ['Top đầu về giải pháp HRM trong nước', Validators.required],
    });
  }

  submitFormAI() {
    if (this.aiForm.invalid) {
      Object.values(this.aiForm.controls).forEach((control) => {
        if (control.invalid) {
          control.markAsDirty();
          control.updateValueAndValidity();
        }
      });
      return;
    }

    this.done = false;
    this.isLoading = true;

    // Chuẩn bị dữ liệu để gửi đi
    const formData = this.aiForm.value;
    const requestData = {
      type: 'goal',
      question: `Phương pháp tạo mục tiêu: ${formData.goalType}
               
               Thông tin công ty:
               - Ngành nghề: ${formData.industry}
               - Quy mô: 100-500 nhân viên
               - Giai đoạn phát triển: Tăng trưởng
               - Sứ mệnh / tầm nhìn: ${formData.missionVision}
               Phòng ban: ${formData.department}.
               
               ${formData.additionalPrompt}`,
    };

    // Gửi request đến API
    this.vnrAiService
      .aiCreateContent(requestData)
      .pipe(finalize(() => (this.isLoading = false)))
      .subscribe({
        next: (data) => {
          this.message.success('Đã tạo danh sách mục tiêu thành công!');
          this.dataLocal = data['response'];
          this.done = true;
          this.cdr.detectChanges();
        },
        error: (error) => {
          this.message.error('Đã xảy ra lỗi khi tạo mục tiêu!');
          console.error('Error:', error);
        },
      });
  }

  protected saveGoal(dataItem: any) {
    this.dataLocal = this.dataLocal.map((item) => (item.ID === dataItem.ID ? dataItem : item));
    this.vnrTreeList.vnrReloadGrid();
  }

  protected handleOk() {
    this.isVisiblePopup = false;
  }

  protected resetForm() {
    this.aiForm.reset();
    this.aiForm.patchValue({
      goalType: 'KPI',
      developmentStage: 'growth',
    });
  }

  close() {
    this.drawerRef.close();
  }

  submitForm() {
    this.commonService.message({ message: 'common.message.actionSuccess', type: 'success' });
    this.drawerRef.close();
  }
}
