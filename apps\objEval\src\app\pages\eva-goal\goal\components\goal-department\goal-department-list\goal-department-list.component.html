<obj-eval-tab-filter
  [data]="GoalStatusFilterData"
  (selectChange)="onSelectDepartmentChange($event)"
  [tabDefault]="tabDefault"
  [tabCount]="tabCount"
  class="obj-eval-tab-filter"
></obj-eval-tab-filter>
<vnr-toolbar-new
  class="border-0 --custom-bg width-100p manage-personnel-records-grids__toolbar"
  [builder]="builderToolbar"
>
  <vnr-button
    [vnrText]="'Tạo mục tiêu với AI'"
    [vnrDisabled]="false"
    [vnrType]="'primary'"
    [vnrIcon]="'partition'"
    (vnrClick)="onOpenFormAi()"
  >
  </vnr-button>
  <vnr-button
    *ngIf="selectedDataItem.length > 0"
    [vnrText]="'objEval.GoalPeriod.Allocation'"
    [vnrDisabled]="false"
    [vnrType]="'primary'"
    [vnrIcon]="'partition'"
    (vnrClick)="onAllocationGoal()"
  >
  </vnr-button>
  <vnr-button
    [vnrText]="'objEval.GoalPeriod.AddGoal'"
    [vnrDisabled]="false"
    (vnrClick)="onOpenAddGoalModal()"
    [vnrType]="'primary'"
    [vnrIcon]="'plus'"
    rightToolbar
  >
  </vnr-button>
</vnr-toolbar-new>
<vnr-treelist-new
  class="grid-new"
  #vnrTreeList
  [builder]="builderTreelist"
  [dataLocal]="dataLocal"
  [columns]="columns"
  [gridName]="gridName"
  [isSupperAdmin]="isSupperAdmin"
  [defaultColumnTemplate]="tplCustomTemplateByColumn"
  (vnrDoubleClick)="onOpenDetail($event)"
  (getSelectedDataItem)="getSelectedDataItem($event)"
>
</vnr-treelist-new>
<ng-template #tplCustomTemplateByColumn let-dataItem let-column="columnItem" let-field="field">
  <ng-container [ngSwitch]="column['Name']">
    <span class="cursor-pointer" *ngSwitchCase="'GoalName'">
      {{ dataItem[column?.Name] }}
    </span>
    <span *ngSwitchCase="'Type'">
      <vnr-tag
        [vnrColor]="goalTypeColorMap[dataItem[column['Name']]]"
        [vnrTitle]="dataItem[column['Name']]"
      ></vnr-tag>
    </span>
    <span *ngSwitchCase="'Trend'">
      <span *ngIf="dataItem[column['Name']] === 'Tăng'">
        <vnr-tag
          [vnrColor]="'success'"
          [vnrTitle]="dataItem[column['Name']]"
          [vnrIconSvg]="'assets/images/svg/trendup.svg'"
        ></vnr-tag>
      </span>
      <span *ngIf="dataItem[column['Name']] === 'Giảm'">
        <vnr-tag
          [vnrColor]="'error'"
          [vnrTitle]="dataItem[column['Name']]"
          [vnrIconSvg]="'assets/images/svg/trenddown.svg'"
        ></vnr-tag>
      </span>
    </span>
    <span *ngSwitchCase="'Representative'">
      <div class="d-flex align-items-center">
        <app-vnr-letters-avatar
          [avatarName]="dataItem[column['Name']]"
          [circular]="true"
          [width]="32"
          [src]="dataItem && dataItem.ImagePathProcess"
          class="mr-2"
        ></app-vnr-letters-avatar>
        <span>{{ dataItem[column['Name']] }}</span>
      </div>
    </span>
    <span *ngSwitchCase="'Status'">
      <vnr-tag
        [vnrColor]="statusColorMap[dataItem['Status']] || 'default'"
        [vnrTitle]="statusTextMap[dataItem['Status']]"
      ></vnr-tag>
    </span>
    <span *ngSwitchCase="'Process'">
      <nz-progress [nzPercent]="dataItem[column['Name']]"></nz-progress>
    </span>
    <span *ngSwitchCase="'TotalTarget'">
      {{ dataItem['TotalTarget'] | targetFormat : dataItem['Unit'] }}
    </span>
    <span *ngSwitchDefault>
      {{ dataItem[column['Name']] }}
    </span>
  </ng-container>
</ng-template>
