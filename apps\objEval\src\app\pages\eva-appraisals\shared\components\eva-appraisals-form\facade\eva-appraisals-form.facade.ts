import { Injectable } from '@angular/core';
import { vnrUtilities } from '@hrm-frontend-workspace/common';
import { Observable } from 'rxjs';
import { IFormTemplate } from '../../../../../../pages/eva-settings/eva-template/eva-create-review-form/models/form-builder.model';
import { evaTemplateDataSource } from '../../../../../../pages/eva-settings/eva-template/eva-evaluation-form-list/data/datasource.data';
import { EvaAppraisalsFormApi } from '../api/eva-appraisals-form.api';
import { EvaAppraisalsFormState } from '../state/eva-appraisals-form.state';

@Injectable({
  providedIn: 'root',
})
export class EvaAppraisalsFormFacade {
  constructor(
    private _evaAppraisalsFormApi: EvaAppraisalsFormApi,
    private _evaAppraisalsFormState: EvaAppraisalsFormState,
  ) {}

  // Form Template Operations
  getAppraisalsForm$(): Observable<IFormTemplate> {
    return this._evaAppraisalsFormState.getAppraisalsForm$();
  }
  /**
   * Tải template từ API
   * @param id - ID của template cần tải
   */
  loadAppraisalsForm(id: string): void {
    // Trong môi trường thực tế, gọi API để lấy template
    // Hiện tại, chúng ta sẽ tạo một template mẫu
    if (id) {
      const data = evaTemplateDataSource;
      const newTemplate: any = data.find((item) => item.ID === id);
      const evaluationTemplate = newTemplate || {
        ID: vnrUtilities.newGuid(),
        EvaName: 'Mẫu đánh giá mới',
        Description: '',
        sections: [],
        CreatedDate: new Date(),
        ModifiedDate: new Date(),
      };
      this._evaAppraisalsFormState.setAppraisalsForm(evaluationTemplate);
    }
  }
}
