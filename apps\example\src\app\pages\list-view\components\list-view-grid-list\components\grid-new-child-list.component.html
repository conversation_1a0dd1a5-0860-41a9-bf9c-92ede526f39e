<vnr-grid-new
  class="grid-new"
  #vnrGrid
  [builder]="builderGrid"
  [gridName]="gridName"
  [dataLocal]="dataLocal"
  [dataFormSearch]="dataFormSearch"
  [columns]="columns"
  [isSupperAdmin]="isSupperAdmin"
  [isChangeColumnNew]="false"
  (getSelectedID)="getSelectedID($event)"
  (getDataItem)="getDataItem($event)"
  (vnrDoubleClick)="onOpenDetail($event)"
  (vnrViewModeGrid)="onVnRViewModeGrid($event)"
  (vnrEdit)="onGridEdit($event)"
  (vnrDelete)="onGridDelete($event)"
  (vnrViewDetails)="onGridViewDetail($event)"
  (vnrCellClick)="onGridCellClick($event)"
>
</vnr-grid-new>
