<div
  class="alert alert-dismissible fade show"
  role="alert"
  style="background-color: #fefce8; border-radius: 8px; color: #303030; border: 1px solid #f4b606"
  *ngIf="showAlert"
>
  <strong><i class="fa-light fa-triangle-exclamation mr-1"></i></strong> Có 4 mục tiêu rủi ro.
  <button
    type="button"
    class="close"
    (click)="closeAlert()"
    aria-label="Close"
    style="color: #303030"
    (keydown.enter)="closeAlert()"
    (keydown.space)="closeAlert()"
    tabindex="0"
  >
    <span aria-hidden="true">&times;</span>
  </button>
</div>

<obj-eval-tab-filter
  [data]="ProgressOverviewTabData"
  (selectChange)="onSelectDepartmentChange($event)"
  [tabCount]="tabCount"
  class="obj-eval-tab-filter"
></obj-eval-tab-filter>
<vnr-toolbar-new
  class="border-0 --custom-bg width-100p"
  [builder]="builderToolbar"
  (vnrChangeColumn)="
    vnrTreeList.setOpenChangeColumn(true); vnrTreeList.setChangeColumnVersion($event)
  "
>
  <vnr-button rightToolbar [vnrTemplate]="tplViewMode"> </vnr-button>
</vnr-toolbar-new>
<ng-template #tplViewMode>
  <nz-segmented
    [nzOptions]="viewModeViewOptions"
    (nzValueChange)="handleSwitchModeView($event)"
    nzSize="small"
    class="--custom-segmented"
    [(ngModel)]="viewMode"
  ></nz-segmented>
  <span class="mx-1">|</span> <vnr-button rightToolbar [vnrTemplate]="tplBtn"></vnr-button>
</ng-template>
<ng-template #tplBtn>
  <button
    class="ant-btn toolbar-border-radius vnr-button-default ant-btn-default ng-star-inserted mr-1"
    style="border: 1px solid #d9d9d9; border-radius: 6px"
  >
    <i class="fa-light fa-file-export mr-1"></i> Xuất dữ liệu
  </button>
  <button
    class="ant-btn toolbar-border-radius vnr-button-default ant-btn-default ng-star-inserted mr-1"
    style="border: 1px solid #d9d9d9; border-radius: 6px"
  >
    <i class="fa-light fa-chart-pie"></i>
  </button>
  <button
    class="ant-btn toolbar-border-radius vnr-button-default ant-btn-default ng-star-inserted"
    style="border: 1px solid #d9d9d9; border-radius: 6px"
  >
    <i class="fa-light fa-filter"></i>
  </button>
</ng-template>
<!-- Quick filter -->
<div class="d-flex flex-wrap align-items-center">
  <label
    *ngFor="let status of statusList"
    class="status-pill mr-2 mb-2 d-flex align-items-center"
    [ngClass]="[status.checked ? 'status-pill--active' : '', 'status-pill--' + status.value]"
    tabindex="0"
    (click)="toggleStatus(status)"
    (keydown.enter)="toggleStatus(status)"
    (keydown.space)="toggleStatus(status)"
  >
    <input
      type="checkbox"
      class="d-none"
      [checked]="status.checked"
      (change)="toggleStatus(status)"
      tabindex="-1"
    />
    <i nz-icon nzType="check" *ngIf="status.checked" class="mr-1"></i>
    <span>{{ status.label }}</span>
    <span class="ml-1 font-weight-bold">{{ status.count }}</span>
  </label>
</div>
<!-- Đã chuyển CSS sang file SCSS -->
<div [ngSwitch]="viewMode">
  <div *ngSwitchCase="'list'">
    <vnr-treelist-new
      #vnrTreeList
      [builder]="builderTreeList"
      [gridName]="gridName"
      [dataLocal]="dataLocal"
      [columns]="columns"
      [defaultColumnTemplate]="tplCustomTemplateByColumn"
    >
      <ng-template
        #tplCustomTemplateByColumn
        let-dataItem
        let-column="columnItem"
        let-field="field"
      >
        <ng-container [ngSwitch]="column['Name']">
          <!-- Representative Template -->
          <span class="--has-custom-template" *ngSwitchCase="'Representative'">
            <div class="d-flex align-items-center">
              <app-vnr-letters-avatar
                [avatarName]="dataItem['Representative']"
                [circular]="true"
                [width]="32"
                class="mr-2"
              ></app-vnr-letters-avatar>
              <span>{{ dataItem['Representative'] }}</span>
            </div>
          </span>

          <!-- Department Template -->
          <span class="--has-custom-template" *ngSwitchCase="'Department'">
            <span>{{ dataItem['Department'] }}</span>
          </span>

          <!-- Weight Template -->
          <span class="--has-custom-template" *ngSwitchCase="'Weight'">
            <span>{{ dataItem['Weight'] }}%</span>
          </span>

          <!-- Target Template -->
          <span class="--has-custom-template" *ngSwitchCase="'Target'">
            <span>{{ dataItem['Target'] | targetFormat : dataItem['Unit'] || 'VND' }}</span>
          </span>

          <!-- Total Target Template -->
          <span class="--has-custom-template" *ngSwitchCase="'TotalTarget'">
            <span>{{ dataItem['TotalTarget'] | targetFormat : dataItem['Unit'] || 'VND' }}</span>
          </span>

          <!-- Luỹ kế đạt -->
          <span class="--has-custom-template" *ngSwitchCase="'DoneTarget'">
            <span>{{ dataItem['DoneTarget'] | targetFormat : dataItem['Unit'] || 'VND' }}</span>
          </span>

          <!-- Latest Result Template -->
          <span class="--has-custom-template" *ngSwitchCase="'LatestResult'">
            <span>{{ dataItem['LatestResult'] | targetFormat : dataItem['Unit'] || 'VND' }}</span>
          </span>

          <!-- Remaining Day Template -->
          <span class="--has-custom-template" *ngSwitchCase="'RemainingDay'">
            <span>{{ dataItem['RemainingDay'] }} ngày</span>
          </span>

          <!-- Status Template -->
          <span *ngSwitchCase="'Status'">
            <vnr-tag
              [vnrColor]="statusColorMap[dataItem['Status']] || 'default'"
              [vnrTitle]="statusTextMap[dataItem['Status']]"
            ></vnr-tag>
          </span>

          <!-- Process Template -->
          <span *ngSwitchCase="'Process'">
            <nz-progress [nzPercent]="dataItem['Process']"></nz-progress>
          </span>

          <!-- Action Template -->
          <span *ngSwitchCase="'Action'">
            <span
              class="d-flex align-items-center"
              (click)="updateGoal(dataItem)"
              style="
                cursor: pointer;
                border: 1px solid #adb5bd;
                padding: 4px 12px;
                border-radius: 50px;
                color: #212529;
                width: fit-content;
              "
              (keydown.enter)="updateGoal(dataItem)"
              (keydown.space)="updateGoal(dataItem)"
              tabindex="0"
              role="button"
            >
              <i class="fa-solid fa-arrows-rotate mr-2"></i>
              <span>Cập nhật</span>
            </span>
          </span>

          <!-- Default Template -->
          <span class="--has-custom-template" *ngSwitchDefault>
            {{ dataItem[column['Name']] || '-' }}
          </span>
        </ng-container>
      </ng-template>

      <ng-template #templateEmpty>-</ng-template>
    </vnr-treelist-new>
  </div>
  <app-progress-overview-tree *ngSwitchCase="'tree'"></app-progress-overview-tree>
  <app-progress-overview-table *ngSwitchCase="'table'"></app-progress-overview-table>
</div>
