import { CommonModule, Ng<PERSON><PERSON>, Ng<PERSON><PERSON>, Ng<PERSON><PERSON>C<PERSON>, NgSwitchDefault } from '@angular/common';
import {
  Component,
  EventEmitter,
  Inject,
  Input,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { VnrLettersAvatarComponent, VnrTagComponent } from '@hrm-frontend-workspace/ui';
import {
  IVnrModule_Token,
  VNRMODULE_TOKEN,
  VnrButtonFactory,
  VnrButtonNewBuilder,
  VnrButtonNewComponent,
  VnrComboBoxBuilder,
  VnrComboBoxComponent,
  VnrGridNewBuilder,
  VnrGridNewComponent,
  VnrToolbarNewBuilder,
  VnrToolbarNewComponent,
  VnrTreeViewBuilder,
  VnrTreeViewComponent,
} from '@hrm-frontend-workspace/vnr-module';
import { TranslateModule } from '@ngx-translate/core';
import {
  appraisals360StatusFormat,
  appraisalsGradeFormat,
  appraisalsGradeTextFormat,
} from '../../data/appraisals-360.data';
import { gridDefineColumns, gridDefineColumnsSummaryResult } from '../../data/column.data';
import {
  appraisals360DataSource,
  appraisals360SummaryResultDataSource,
} from '../../data/datasource.data';

import {
  FormsModule,
  ReactiveFormsModule,
  UntypedFormBuilder,
  UntypedFormGroup,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { vnrUtilities } from '@hrm-frontend-workspace/common';
import { CommonService } from '@hrm-frontend-workspace/core';
import { ResponseStatus } from '@hrm-frontend-workspace/models';
import { cloneDeep } from 'lodash';
import {
  appraisalsKpiDepartmentDataSource,
  appraisalsKpiPositionDataSource,
} from '../../../appraisals-kpi/data/datasource-component.data';
import { Appraisals360Facade } from '../../facade/appraisals-360.facade';
import { Appraisals360Tab } from '../../models/appraisals-360.model';

@Component({
  selector: 'appraisals-360-grid',
  templateUrl: './appraisals-360-grid.component.html',
  styleUrls: ['./appraisals-360-grid.component.scss'],
  imports: [
    FormsModule,
    ReactiveFormsModule,
    CommonModule,
    NgSwitch,
    NgSwitchCase,
    NgSwitchDefault,
    NgIf,
    TranslateModule,
    VnrToolbarNewComponent,
    VnrGridNewComponent,
    VnrComboBoxComponent,
    VnrTagComponent,
    VnrButtonNewComponent,
    VnrTreeViewComponent,
    VnrLettersAvatarComponent,
  ],
})
export class Appraisals360GridComponent implements OnInit {
  @Input() tabFilter: number = Appraisals360Tab.APPRAISALS_360_TAB_ALL;
  @Output() reloadTabCount: EventEmitter<boolean> = new EventEmitter<boolean>();
  @ViewChild('vnrGrid', { static: true }) gridControl: VnrGridNewComponent;
  @ViewChild('btnApprove', { static: true }) btnApprove: VnrButtonNewComponent;
  private _statusFormat = appraisals360StatusFormat;
  private _gradeFormat = appraisalsGradeFormat;
  private _gradeTextFormat = appraisalsGradeTextFormat;
  private _btnFactory: VnrButtonFactory = VnrButtonFactory.init();
  protected gridName: string = 'appraisals-360-grid';
  protected dataLocal: any = [];
  protected columns: any = [];
  protected builderButtonApprove: VnrButtonNewBuilder;
  protected builderButtonCustom: VnrButtonNewBuilder;
  protected builderOrg: VnrTreeViewBuilder = new VnrTreeViewBuilder();
  protected builderPosition: VnrComboBoxBuilder = new VnrComboBoxBuilder();
  protected selectedItem = [];

  protected formGroup: UntypedFormGroup = this._formBuider.group({
    department: [''],
    position: [''],
  });
  public builderGrid: VnrGridNewBuilder;
  public builderToolbar: VnrToolbarNewBuilder;

  constructor(
    private _formBuider: UntypedFormBuilder,
    private _commonService: CommonService,
    private _appraisals360Facade: Appraisals360Facade,
    @Inject(VNRMODULE_TOKEN) private _vnrModule_Token: IVnrModule_Token,
    private _router: Router,
    private _route: ActivatedRoute,
  ) {}
  ngOnInit() {
    this.builderComponent();
    this.initGridData();
  }
  ngOnChanges(changes: SimpleChanges): void {
    const tabFilterChange = changes['tabFilter'];
    if (tabFilterChange && !tabFilterChange.firstChange) {
      this.tabFilter = tabFilterChange.currentValue;
      this.initGridData();
    }
  }

  private initGridData() {
    this.gridName = 'appraisals-360-grid';
    this.dataLocal = cloneDeep(appraisals360DataSource);
    this.columns = cloneDeep(gridDefineColumns);
    if (this.tabFilter === Appraisals360Tab.APPRAISALS_360_TAB_SUMMARYRESULT) {
      this.gridName = 'appraisals-360-grid-summary-result';
      this.dataLocal = cloneDeep(appraisals360SummaryResultDataSource);
      this.columns = cloneDeep(gridDefineColumnsSummaryResult);
    }
    this.builderGridComponent();
    this.builderToolbarComponent();
  }
  private builderComponent() {
    this.builderOrg.builder({
      label: '',
      placeholder: 'objEval.Appraisals.selectAllOrg',
      valueField: 'OrderNumber',
      textField: 'Name',
      childKey: 'ListChild',
      options: {
        hasFeedBack: false,
        checkable: true,
        maxTagCount: 1,
        scrollX: true,
      },
      dataSource: appraisalsKpiDepartmentDataSource,
      //serverSide: {
      //  urlApi: `${this._vnrModule_Token.apiConfig_Token?.apiUrl}/api/Cat_GetData/GetOrgTreeView`,
      //  method: 'get',
      //},
    });
    this.builderPosition.builder({
      label: '',
      placeholder: 'objEval.Appraisals.selectAllPosition',
      textField: 'PositionNameAndCode',
      valueField: 'ID',
      //serverSide: {
      //  urlApi: `${this._vnrModule_Token.apiConfig_Token?.apiUrl}/api/Att_GetData/GetMultiPosition`,
      //  method: 'GET',
      //  data: { text: '', TextField: 'PositionName' },
      //},
      dataSource: appraisalsKpiPositionDataSource,
      options: {
        allowValueObject: true,
        hasFeedBack: false,
      },
    });
    this.builderButtonApprove = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'objEval.Appraisals.btnAprove',
      options: {
        classNames: ['btn-approve'],
        style: 'green',
        icon: {
          fontIcon: 'check',
        },
      },
    });
    this.builderButtonCustom = new VnrButtonNewBuilder({
      action: 'custom',
    });
  }

  private builderToolbarComponent() {
    this.builderToolbar = new VnrToolbarNewBuilder({
      isShowConfig: true,
      gridName: this.gridName,
      isSupperAdmin: true,
      gridRef: this.gridControl,
      permission: '',
      screenName: 'appraisals-360-grid',
      storeName: '',
      options: {
        configButtonChangeColumn: {
          isShow: true,
          isConfigMenuDisplay: true,
          isChangeColumnNew: true,
          isDisabled: false,
          configButtonSetting: {
            isShow: true,
            isDisabled: true,
          },
        },
        configButtonExport: {
          isShowBtnExcelAll: true,
          isShowBtnWord: true,
          isShowBtnExcelByTemplate: true,
        },
        configButtonDelete: {
          isShow: true,
          isDisabled: false,
        },
        configQuickSearch: {
          textPlaceHolder: 'Tìm kiếm theo tên, mã...',
          searchKey: 'ProfileName',
        },
        isSetBackground: false,
        buttonMoreFlexDirection: 'inherit',
        isExpanded: false,
      },
    });
  }
  private builderGridComponent() {
    this.builderGrid = new VnrGridNewBuilder({
      options: {
        configHeightGrid: {
          isAllowCalcRowHeight: true,
        },
        configIndexColumn: {
          isShow: true,
          width: 40,
        },
        configSelectable: {
          columnKey: 'Id',
        },
        configShowHide: {
          isPageExpand: false,
          isShowDelete: false,
          isShowEdit: false,
        },
        configCommandColumn: {
          isEnabledMenuAction: true,
        },
        configGroupable: {
          isEnabled: false,
        },
      },
    });
  }
  protected getSelectedID($event: any) {
    this.selectedItem = $event;
  }
  getDataItem($event: any) {}
  protected onOpenDetail($event: any) {
    const { record } = $event;
    const appraisalId = record?.PerformanceID;
    const queryParams: any = {
      ProfileID: record?.ProfileID1 || '1',
    };
    this._router.navigate(['/objEval/eva-appraisals/appraisals/form-appraisal', appraisalId], {
      relativeTo: this._route,
      queryParamsHandling: 'merge',
      queryParams: queryParams,
    });
  }
  onGridEdit($event: any) {}
  onGridDelete($event: any) {}
  onGridViewDetail($event: any) {}
  onGridCellClick($event: any) {}
  public setDataFilter(data: any): void {
    this.gridControl.setDataFilter(data);
  }
  public reloadGridData(): void {
    this.gridControl.vnrReadGrid();
  }
  public getSelectedIDs() {
    return this.selectedItem || [];
  }
  protected getColorStatus(value: any): string {
    return this._statusFormat[value];
  }
  protected getColorGrade(value: any): string {
    return this._gradeFormat[value];
  }
  protected getColorGradeText(value: any): string {
    return this._gradeTextFormat[value];
  }
  protected onChangeFitler($event: any) {
    this.reloadTabCount.emit();
  }
  public onChangeTabFilter($event: any): void {
    this.tabFilter = $event;
  }
  protected onModelChangeOrg($event: any) {
    this.setDataFilter({ OrderNumber: $event?.join() });
    this.reloadGridData();
    this.reloadTabCount.emit();
  }
  protected onModelChangePosition($event: any) {
    this.setDataFilter({ PositionId: $event });
    this.reloadGridData();
    this.reloadTabCount.emit();
  }
  protected isShowBtnApprove(): boolean {
    return (
      this.tabFilter === Appraisals360Tab.APPRAISALS_360_TAB_TODO &&
      this.getSelectedIDs().length > 0
    );
  }
  public onApprove($event: any): void {
    const arrIDCheck: any = cloneDeep(this.getSelectedIDs());
    if (!arrIDCheck || !Array.isArray(arrIDCheck) || arrIDCheck.length === 0) {
      this._commonService.notification({
        type: 'error',
        message: 'common.grid.noSelectedId',
      });
      return;
    }
    this._appraisals360Facade.approveAppraisals(arrIDCheck).subscribe((dataResult: any) => {
      if (dataResult && dataResult['Status'] === ResponseStatus.SUCCESS) {
        this._commonService.notification({
          type: 'success',
          message: dataResult['Message'] || 'common.notification.actionSuccess',
        });
      } else {
        this._commonService.notification({
          type: 'error',
          message: dataResult['Message'] || 'common.notification.descErr404',
        });
      }
    });
  }
}
