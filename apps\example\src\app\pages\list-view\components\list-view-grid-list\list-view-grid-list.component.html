<vnr-toolbar-new
  class="border-0 --custom-bg width-100p manage-personnel-records-grids__toolbar"
  [builder]="builderToolbar"
  (vnrChangeColumn)="toggleChangeColumn($event)"
>
</vnr-toolbar-new>
<div class="d-flex mt-3">
  <vnr-button-new
    [builder]="builderbtnOpenColumnCheck"
    [isShow]="isShowOpenColumnCheck"
    (vnrClick)="onOpenColumnCheck($event)"
  ></vnr-button-new>
  <vnr-button-new
    [builder]="builderbtnCancelOpenColumnCheck"
    [isShow]="!isShowOpenColumnCheck"
    (vnrClick)="onCancelOpenColumnCheck($event)"
  ></vnr-button-new>
  <vnr-button-new
    [builder]="builderbtnSelectAll"
    [isShow]="isShowSelectAll"
    (vnrClick)="onSelectAll($event)"
  ></vnr-button-new>
  <vnr-button-new
    [builder]="builderbtnCancelSelectAll"
    [isShow]="!isShowSelectAll"
    (vnrClick)="onCancelSelectAll($event)"
  ></vnr-button-new>
  <vnr-button-new
    [builder]="builderbtnExpandAll"
    [isShow]="isShowExpandAll"
    (vnrClick)="onExpandAll($event)"
  ></vnr-button-new>
  <vnr-button-new
    [builder]="builderbtnCollapseAll"
    [isShow]="!isShowExpandAll"
    (vnrClick)="onCollapseAll($event)"
  ></vnr-button-new>
  <vnr-button-new
    [builder]="builderbtnChangeColumn"
    (vnrClick)="toggleChangeColumn(true)"
  ></vnr-button-new>
</div>
<vnr-listview-new
  class="grid-new"
  #vnrListview
  [builder]="builderListview"
  [gridName]="gridName"
  [dataLocal]="dataLocal"
  [columns]="columns"
  [isSupperAdmin]="isSupperAdmin"
  [isChangeColumnNew]="false"
  [dataLocal]="dataLocal"
  [rowDetailTemplate]="tplMasterDetail"
  [rowActionsTemplate]="rowActionsTemplate"
  [columnHeaderTemplate]="tplHeader"
  (vnrLoadData)="onLoadData($event)"
  (vnrEdit)="onGridEdit($event)"
  (vnrDelete)="onGridDelete($event)"
  (vnrViewDetails)="onGridViewDetail($event)"
  (vnrCellClick)="onGridCellClick($event)"
>
</vnr-listview-new>
<ng-template #tplMasterDetail let-dataItem>
  <grid-new-child-list [columnMaster]="dataItem"></grid-new-child-list>
</ng-template>
<ng-template #tplHeader let-dataItem>
  <div class="grid-item-header">
    <span class="grid-item-header__title"
      >{{ dataItem.GoalGroup }}
      <nz-tag [nzColor]="'default'" [nzBordered]="false"
        >{{ dataItem.TotalItems }} {{ 'grid.pagerItems' | translate }}</nz-tag
      >
    </span>
    <span class="grid-item-header__code">{{ dataItem.GoalGroupCode }}</span>
  </div>
</ng-template>
<ng-template #rowActionsTemplate let-dataItem>
  <vnr-button-new
    [builder]="builderbtnAddGoalItem"
    (vnrClick)="onAddGoalItem($event, dataItem)"
  ></vnr-button-new>
</ng-template>
