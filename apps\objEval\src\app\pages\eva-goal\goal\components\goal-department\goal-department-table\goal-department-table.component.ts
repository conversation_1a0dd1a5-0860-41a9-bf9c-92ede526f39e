import { ChangeDetectionStrategy, Component, OnInit, ViewChild } from '@angular/core';
import { VnrGridNewBuilder, VnrGridNewComponent } from '@hrm-frontend-workspace/vnr-module';

import { ObjSharedModule } from '../../../../../../shared/obj-shared.module';
import { tableDefineColumns } from '../../../data/column.data';
import { goalDepartmentData } from '../../../data/goal-department.data';
import { TargetFormatPipe } from '../../../../shared/pipe/target-format.pipe';
@Component({
  selector: 'goal-department-table',
  templateUrl: './goal-department-table.component.html',
  styleUrls: ['./goal-department-table.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ObjSharedModule, TargetFormatPipe],
})
export class GoalDepartmentTableComponent implements OnInit {
  @ViewChild('vnrGrid', { static: true }) gridControl: VnrGridNewComponent;

  protected builderGrid: VnrGridNewBuilder;
  protected gridName = 'ObjEval_TableGoalPeriod';
  protected isSupperAdmin = true;
  protected dataLocal = goalDepartmentData;
  protected columns = tableDefineColumns;

  ngOnInit() {
    this.builderGridComponent();
  }

  private builderGridComponent() {
    this.builderGrid = new VnrGridNewBuilder({
      options: {
        configSelectable: {
          isEnabled: false,
        },
        configHeightGrid: {
          gridHeight: 600,
        },
        configShowHide: {
          isShowColumnCheck: false,
          isShowButtonMenu: false,
          isShowEdit: false,
          isShowDelete: false,
        },
      },
    });
  }
}
