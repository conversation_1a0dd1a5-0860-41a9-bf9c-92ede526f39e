import { gridPersonalDetailDefineDataSource } from '../../../../eva-goal/goal/data/datasource.data';
import { dataCompetencyEvaluationSection } from '../../eva-create-review-form/data/test/competency-evaluation-section.data';
import { dataCustomEvaluationSection } from '../../eva-create-review-form/data/test/custom-evaluation-section.data';
import { dataEvaluation360Section } from '../../eva-create-review-form/data/test/evaluation-360-section.data';
import { dataGoalEvaluationSection } from '../../eva-create-review-form/data/test/goal-evaluation-section.data';

export const sections = [
  {
    id: 'b6e8cb84-ba48-4930-9e13-70eec2fcfef5',
    title: 'Thông tin đợt đánh giá',
    description: 'Thông tin về đợt đánh giá hiện tại',
    type: 'evaluation-period',
    layout: 'vertical',
    columns: 2,
    controls: [],
    properties: {
      displayConfig: {
        fields: {
          name: {
            visible: true,
            label: 'Tên đợt đánh giá',
            labelKey: 'objEval.evaluationPeriod.name',
            format: 'text',
            order: 1,
            required: true,
          },
          year: {
            visible: true,
            label: 'Năm',
            labelKey: 'objEval.evaluationPeriod.year',
            format: 'number',
            order: 2,
            required: true,
            min: 2000,
            max: 2100,
          },
        },
        layout: {
          columns: 1,
          labelWidth: 30,
          valueWidth: 70,
          spacing: 8,
        },
      },
    },
  },
  {
    id: 'd60cd578-41fd-44fa-a7e5-df3390c21d2b',
    title: 'Thông tin người được đánh giá',
    description: 'Thông tin cơ bản của người được đánh giá',
    type: 'overview',
    layout: 'vertical',
    columns: 2,
    controls: [],
    properties: {
      displayConfig: {
        fields: {
          employeeName: {
            visible: true,
            label: 'Tên nhân viên',
            labelKey: 'objEval.overView.employeeName',
            format: 'text',
            order: 1,
          },
          employeeCode: {
            visible: true,
            label: 'Mã nhân viên',
            labelKey: 'objEval.overView.employeeCode',
            format: 'text',
            order: 2,
          },
          department: {
            visible: true,
            label: 'Phòng ban',
            labelKey: 'objEval.overView.department',
            format: 'text',
            order: 3,
          },
          company: {
            visible: true,
            label: 'Công ty',
            labelKey: 'objEval.overView.company',
            format: 'text',
            order: 4,
          },
          departmentTree: {
            visible: true,
            label: 'Cây phòng ban',
            labelKey: 'objEval.overView.departmentTree',
            format: 'text',
            order: 5,
          },
          position: {
            visible: true,
            label: 'Chức vụ',
            labelKey: 'objEval.overView.position',
            format: 'text',
            order: 6,
          },
          title: {
            visible: true,
            label: 'Chức danh',
            labelKey: 'objEval.overView.title',
            format: 'text',
            order: 7,
          },
          joinDate: {
            visible: true,
            label: 'Ngày vào làm',
            labelKey: 'objEval.overView.joinDate',
            format: 'date|dd/MM/yyyy',
            order: 8,
          },
          email: {
            visible: true,
            label: 'Email',
            labelKey: 'objEval.overView.email',
            format: 'text',
            order: 9,
          },
          seniority: {
            visible: true,
            label: 'Thâm niên',
            labelKey: 'objEval.overView.seniority',
            format: 'text',
            order: 10,
          },
        },
        layout: {
          columns: 2,
          labelWidth: 40,
          valueWidth: 60,
          spacing: 8,
        },
      },
    },
    data: {
      id: '-',
      employeeCode: '-',
      name: '-',
      employeeName: '-',
      position: '-',
      department: '-',
      joinDate: '-',
      manager: '-',
      store: '-',
      company: '-',
      departmentTree: '-',
      evaluator: '-',
      title: '-',
      result: '-',
      rank: '-',
      seniority: '-',
    },
  },
  {
    id: 'bf21da41-89ed-455a-89d8-f6407a6895d7',
    title: 'Thông tin quy trình đánh giá',
    description: 'Thông tin về quy trình đánh giá',
    type: 'process',
    layout: 'vertical',
    columns: 2,
    controls: [],
    properties: {
      displayConfig: {
        fields: {
          processName: {
            visible: true,
            label: 'Tên quy trình',
            labelKey: 'objEval.process.processName',
            format: 'text',
            order: 1,
          },
          processSteps: {
            visible: true,
            label: 'Các bước quy trình',
            labelKey: 'objEval.process.processSteps',
            format: 'text',
            order: 2,
          },
          currentStep: {
            visible: true,
            label: 'Bước hiện tại',
            labelKey: 'objEval.process.currentStep',
            format: 'text',
            order: 3,
          },
          deadlines: {
            visible: true,
            label: 'Thời hạn',
            labelKey: 'objEval.process.deadlines',
            format: 'date|dd/MM/yyyy',
            order: 4,
          },
          progress: {
            visible: true,
            label: 'Tiến độ',
            labelKey: 'objEval.process.progress',
            format: 'percent',
            order: 5,
          },
        },
        layout: {
          columns: 2,
          labelWidth: 40,
          valueWidth: 60,
          spacing: 8,
        },
      },
    },
  },
  {
    id: '475c373e-9b50-4bba-ad34-0ec531896c85',
    title: 'Bảng đánh giá mục tiêu',
    description: 'Đánh giá kết quả thực hiện mục tiêu',
    type: 'goal-evaluation',
    layout: 'vertical',
    columns: 2,
    controls: [],
    properties: {
      gridConfig: {
        fields: {},
        allowEmployeeEdit: true,
        allowManagerEdit: true,
        columns: [
          {
            GroupType: null,
            ReferenceName: null,
            ReferenceDataSource: null,
            TypeControl: 'CustomControl|TextBox',
            MultiColumn: null,
            serverSide: null,
            Name: 'ID',
            HeaderName: 'ID',
            HeaderKey: 'ID',
            Width: 220,
            Sortable: false,
            Filter: false,
            Class: null,
            Hidden: true,
            Format: null,
            OrderColumn: null,
            RowOnPage: 50,
            Group: false,
            Sum: false,
            isNumber: false,
            Disable: true,
            BackgroundFields: null,
            ColorFields: null,
            GroupIndex: null,
            Locked: false,
            Sticky: false,
            Template: null,
            Type: null,
            IsMultiColumn: null,
            IsHiddenConfig: null,
            DisabledFormat: null,
            AllowClickColumn: null,
            ChildrenLink: null,
            ParentLink: null,
            Style: null,
            HeaderStyle: null,
            HeaderClass: null,
            Validators: {
              Required: true,
              MinLength: 5,
              MaxLength: 50,
              Email: true,
            },
          },
          {
            GroupType: null,
            ReferenceName: null,
            ReferenceDataSource: null,
            TypeControl: 'CustomControl|TextBox',
            MultiColumn: null,
            serverSide: null,
            Name: 'Criteria',
            HeaderName: 'Nhóm năng lực',
            HeaderKey: 'Nhóm năng lực',
            Width: 220,
            Sortable: false,
            Filter: false,
            Class: 'section-criteria',
            Hidden: true,
            Format: null,
            OrderColumn: null,
            RowOnPage: null,
            Group: true,
            Sum: false,
            isNumber: false,
            Disable: true,
            BackgroundFields: null,
            ColorFields: null,
            GroupIndex: null,
            Locked: false,
            Sticky: false,
            Template: null,
            Type: null,
            IsMultiColumn: null,
            IsHiddenConfig: null,
            DisabledFormat: null,
            AllowClickColumn: null,
            ChildrenLink: null,
            ParentLink: null,
            Style: null,
            HeaderStyle: null,
            HeaderClass: null,
            Validators: {
              Required: true,
              MinLength: 5,
              MaxLength: 50,
              Email: true,
            },
          },
          {
            GroupType: null,
            ReferenceName: null,
            ReferenceDataSource: null,
            TypeControl: 'CustomControl|TextBox',
            MultiColumn: [
              {
                GroupType: null,
                ReferenceName: null,
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                serverSide: null,
                Name: 'CriteriaName',
                HeaderName: 'Tên mục tiêu',
                HeaderKey: 'Tên mục tiêu',
                Width: 220,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: null,
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Disable: true,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
              {
                GroupType: null,
                ReferenceName: null,
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                serverSide: {
                  isEnum: null,
                  isMulti: null,
                  dataTable: 'Cat_OrgStructure',
                  objectTable: null,
                  childFieldOrigin: null,
                  urlApi: 'http://172.21.30.65:2005/api/Cat_GetData/GetOrgTreeView',
                  valueField: 'id',
                  textField: 'FullName',
                  compareField: null,
                  data: {
                    text: '',
                    textField: null,
                    Type: null,
                  },
                  dataSource: null,
                },
                Name: 'Weight',
                HeaderName: 'Trọng số',
                HeaderKey: 'Trọng số %',
                Width: 150,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: null,
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Calculate: 'sum',
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
              {
                GroupType: 'Category',
                ReferenceName: 'KeyOrigin__bbd44046_08a5_4ca3_9842_05c428f67915',
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                serverSide: {
                  isEnum: null,
                  isMulti: null,
                  dataTable: 'Cat_OrgStructure',
                  objectTable: null,
                  childFieldOrigin: null,
                  urlApi: 'http://172.21.30.65:2005/api/Cat_GetData/GetOrgTreeView',
                  valueField: 'id',
                  textField: 'FullName',
                  compareField: null,
                  data: {
                    text: '',
                    textField: null,
                    Type: null,
                  },
                  dataSource: null,
                },
                Name: 'Key__bbd44046_08a5_4ca3_9842_05c428f67915',
                HeaderName: 'Chỉ tiêu',
                HeaderKey: 'Chỉ tiêu',
                Width: 150,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: 'Select',
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
            ],
            serverSide: null,
            Name: 'InfoGoal',
            HeaderName: 'Thông tin mục tiêu',
            HeaderKey: 'Thông tin mục tiêu',
            Width: 220,
            Sortable: false,
            Filter: false,
            Class: null,
            Hidden: false,
            Format: null,
            OrderColumn: null,
            RowOnPage: null,
            Group: false,
            Sum: false,
            isNumber: false,
            Disable: true,
            BackgroundFields: null,
            ColorFields: null,
            GroupIndex: null,
            Locked: true,
            Sticky: false,
            Template: null,
            Type: 'group',
            IsMultiColumn: true,
            IsHiddenConfig: null,
            DisabledFormat: null,
            AllowClickColumn: null,
            ChildrenLink: null,
            ParentLink: null,
            Style: null,
            HeaderStyle: null,
            HeaderClass: null,
            Validators: {
              Required: true,
              MinLength: 5,
              MaxLength: 50,
              Email: true,
            },
          },
          {
            GroupType: null,
            ReferenceName: null,
            ReferenceDataSource: null,
            TypeControl: 'CustomControl|TextBox',
            MultiColumn: [
              {
                GroupType: 'Category',
                ReferenceName: 'KeyOrigin__bbd44046_08a5_4ca3_9842_05c428f67915',
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                serverSide: {
                  isEnum: null,
                  isMulti: null,
                  dataTable: 'Cat_OrgStructure',
                  objectTable: null,
                  childFieldOrigin: null,
                  urlApi: 'http://172.21.30.65:2005/api/Cat_GetData/GetOrgTreeView',
                  valueField: 'id',
                  textField: 'FullName',
                  compareField: null,
                  data: {
                    text: '',
                    textField: null,
                    Type: null,
                  },
                  dataSource: null,
                },
                Name: 'Key__bbd44046_08a5_4ca3_9842_05c428f67915',
                HeaderName: 'Đơn vị đo',
                HeaderKey: 'Đơn vị đo',
                Width: 220,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: 'Select',
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
              {
                GroupType: 'Category',
                ReferenceName: 'KeyOrigin__bbd44046_08a5_4ca3_9842_05c428f67915',
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                serverSide: {
                  isEnum: null,
                  isMulti: null,
                  dataTable: 'Cat_OrgStructure',
                  objectTable: null,
                  childFieldOrigin: null,
                  urlApi: 'http://172.21.30.65:2005/api/Cat_GetData/GetOrgTreeView',
                  valueField: 'id',
                  textField: 'FullName',
                  compareField: null,
                  data: {
                    text: '',
                    textField: null,
                    Type: null,
                  },
                  dataSource: null,
                },
                Name: 'Key__bbd44046_08a5_4ca3_9842_05c428f67915',
                HeaderName: 'Mô tả',
                HeaderKey: 'Mô tả',
                Width: 220,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: 'Select',
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
              {
                GroupType: 'Category',
                ReferenceName: 'KeyOrigin__bbd44046_08a5_4ca3_9842_05c428f67915',
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                serverSide: {
                  isEnum: null,
                  isMulti: null,
                  dataTable: 'Cat_OrgStructure',
                  objectTable: null,
                  childFieldOrigin: null,
                  urlApi: 'http://172.21.30.65:2005/api/Cat_GetData/GetOrgTreeView',
                  valueField: 'id',
                  textField: 'FullName',
                  compareField: null,
                  data: {
                    text: '',
                    textField: null,
                    Type: null,
                  },
                  dataSource: null,
                },
                Name: 'Key__bbd44046_08a5_4ca3_9842_05c428f67915',
                HeaderName: 'Kết quả cần đạt',
                HeaderKey: 'Kết quả cần đạt',
                Width: 220,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: 'Select',
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
            ],
            serverSide: null,
            Name: 'KeyObject__Rec_RecruitmentPlan',
            HeaderName: 'Chi tiết mục tiêu',
            HeaderKey: 'Chi tiết mục tiêu',
            Width: 220,
            Sortable: false,
            Filter: false,
            Class: null,
            Hidden: false,
            Format: null,
            OrderColumn: null,
            RowOnPage: null,
            Group: false,
            Sum: false,
            isNumber: false,
            Disable: true,
            BackgroundFields: null,
            ColorFields: null,
            GroupIndex: null,
            Locked: false,
            Sticky: false,
            Template: null,
            Type: 'group',
            IsMultiColumn: true,
            IsHiddenConfig: null,
            DisabledFormat: null,
            AllowClickColumn: null,
            ChildrenLink: null,
            ParentLink: null,
            Style: null,
            HeaderStyle: null,
            HeaderClass: null,
            Validators: {
              Required: true,
              MinLength: 5,
              MaxLength: 50,
              Email: true,
            },
          },
          {
            GroupType: null,
            ReferenceName: null,
            ReferenceDataSource: null,
            TypeControl: 'CustomControl|TextBox',
            MultiColumn: [
              {
                GroupType: 'Category',
                ReferenceName: 'KeyOrigin__bbd44046_08a5_4ca3_9842_05c428f67915',
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                serverSide: {
                  isEnum: null,
                  isMulti: null,
                  dataTable: 'Cat_OrgStructure',
                  objectTable: null,
                  childFieldOrigin: null,
                  urlApi: 'http://172.21.30.65:2005/api/Cat_GetData/GetOrgTreeView',
                  valueField: 'id',
                  textField: 'FullName',
                  compareField: null,
                  data: {
                    text: '',
                    textField: null,
                    Type: null,
                  },
                  dataSource: null,
                },
                Name: 'Key__bbd44046_08a5_4ca3_9842_05c428f67915',
                HeaderName: 'Chỉ số đầu',
                HeaderKey: 'Chỉ số đầu',
                Width: 220,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: 'Select',
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
              {
                GroupType: 'Category',
                ReferenceName: 'KeyOrigin__bbd44046_08a5_4ca3_9842_05c428f67915',
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                serverSide: {
                  isEnum: null,
                  isMulti: null,
                  dataTable: 'Cat_OrgStructure',
                  objectTable: null,
                  childFieldOrigin: null,
                  urlApi: 'http://172.21.30.65:2005/api/Cat_GetData/GetOrgTreeView',
                  valueField: 'id',
                  textField: 'FullName',
                  compareField: null,
                  data: {
                    text: '',
                    textField: null,
                    Type: null,
                  },
                  dataSource: null,
                },
                Name: 'Key__bbd44046_08a5_4ca3_9842_05c428f67915',
                HeaderName: 'Hiệu chỉnh ',
                HeaderKey: 'Hiệu chỉnh ',
                Width: 220,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: 'Select',
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
              {
                GroupType: 'Category',
                ReferenceName: 'KeyOrigin__bbd44046_08a5_4ca3_9842_05c428f67915',
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                serverSide: {
                  isEnum: null,
                  isMulti: null,
                  dataTable: 'Cat_OrgStructure',
                  objectTable: null,
                  childFieldOrigin: null,
                  urlApi: 'http://172.21.30.65:2005/api/Cat_GetData/GetOrgTreeView',
                  valueField: 'id',
                  textField: 'FullName',
                  compareField: null,
                  data: {
                    text: '',
                    textField: null,
                    Type: null,
                  },
                  dataSource: null,
                },
                Name: 'Key__bbd44046_08a5_4ca3_9842_05c428f67915',
                HeaderName: 'Hiệu chỉnh',
                HeaderKey: 'Hiệu chỉnh',
                Width: 220,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: 'Select',
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
              {
                GroupType: 'Category',
                ReferenceName: 'KeyOrigin__bbd44046_08a5_4ca3_9842_05c428f67915',
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                serverSide: {
                  isEnum: null,
                  isMulti: null,
                  dataTable: 'Cat_OrgStructure',
                  objectTable: null,
                  childFieldOrigin: null,
                  urlApi: 'http://172.21.30.65:2005/api/Cat_GetData/GetOrgTreeView',
                  valueField: 'id',
                  textField: 'FullName',
                  compareField: null,
                  data: {
                    text: '',
                    textField: null,
                    Type: null,
                  },
                  dataSource: null,
                },
                Name: 'Key__bbd44046_08a5_4ca3_9842_05c428f67915',
                HeaderName: 'Mức độ hoàn thành',
                HeaderKey: 'Mức độ hoàn thành',
                Width: 220,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: 'Select',
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
              {
                GroupType: 'Category',
                ReferenceName: 'KeyOrigin__bbd44046_08a5_4ca3_9842_05c428f67915',
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                serverSide: {
                  isEnum: null,
                  isMulti: null,
                  dataTable: 'Cat_OrgStructure',
                  objectTable: null,
                  childFieldOrigin: null,
                  urlApi: 'http://172.21.30.65:2005/api/Cat_GetData/GetOrgTreeView',
                  valueField: 'id',
                  textField: 'FullName',
                  compareField: null,
                  data: {
                    text: '',
                    textField: null,
                    Type: null,
                  },
                  dataSource: null,
                },
                Name: 'Key__bbd44046_08a5_4ca3_9842_05c428f67915',
                HeaderName: 'Quy đổi',
                HeaderKey: 'Quy đổi',
                Width: 220,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: 'Select',
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
            ],
            serverSide: null,
            Name: 'KeyObject__Rec_RecruitmentPlan',
            HeaderName: 'Tính toán kết quả',
            HeaderKey: 'Tính toán kết quả',
            Width: 220,
            Sortable: false,
            Filter: false,
            Class: null,
            Hidden: false,
            Format: null,
            OrderColumn: null,
            RowOnPage: null,
            Group: false,
            Sum: false,
            isNumber: false,
            Disable: true,
            BackgroundFields: null,
            ColorFields: null,
            GroupIndex: null,
            Locked: false,
            Sticky: false,
            Template: null,
            Type: 'group',
            IsMultiColumn: true,
            IsHiddenConfig: null,
            DisabledFormat: null,
            AllowClickColumn: null,
            ChildrenLink: null,
            ParentLink: null,
            Style: null,
            HeaderStyle: null,
            HeaderClass: null,
            Validators: {
              Required: true,
              MinLength: 5,
              MaxLength: 50,
              Email: true,
            },
          },
          {
            GroupType: null,
            ReferenceName: null,
            ReferenceDataSource: null,
            TypeControl: 'CustomControl|TextBox',
            MultiColumn: [
              {
                GroupType: 'Category',
                ReferenceName: 'KeyOrigin__bbd44046_08a5_4ca3_9842_05c428f67915',
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                serverSide: {
                  isEnum: null,
                  isMulti: null,
                  dataTable: 'Cat_OrgStructure',
                  objectTable: null,
                  childFieldOrigin: null,
                  urlApi: 'http://172.21.30.65:2005/api/Cat_GetData/GetOrgTreeView',
                  valueField: 'id',
                  textField: 'FullName',
                  compareField: null,
                  data: {
                    text: '',
                    textField: null,
                    Type: null,
                  },
                  dataSource: null,
                },
                Name: 'Key__bbd44046_08a5_4ca3_9842_05c428f67915',
                HeaderName: 'Điểm',
                HeaderKey: 'Điểm',
                Width: 220,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: 'Select',
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
              {
                GroupType: 'Category',
                ReferenceName: 'KeyOrigin__bbd44046_08a5_4ca3_9842_05c428f67915',
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                serverSide: {
                  isEnum: null,
                  isMulti: null,
                  dataTable: 'Cat_OrgStructure',
                  objectTable: null,
                  childFieldOrigin: null,
                  urlApi: 'http://172.21.30.65:2005/api/Cat_GetData/GetOrgTreeView',
                  valueField: 'id',
                  textField: 'FullName',
                  compareField: null,
                  data: {
                    text: '',
                    textField: null,
                    Type: null,
                  },
                  dataSource: null,
                },
                Name: 'Key__bbd44046_08a5_4ca3_9842_05c428f67915',
                HeaderName: 'Xếp loại',
                HeaderKey: 'Xếp loại',
                Width: 220,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: 'Select',
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
              {
                GroupType: 'Category',
                ReferenceName: 'KeyOrigin__bbd44046_08a5_4ca3_9842_05c428f67915',
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                serverSide: {
                  isEnum: null,
                  isMulti: null,
                  dataTable: 'Cat_OrgStructure',
                  objectTable: null,
                  childFieldOrigin: null,
                  urlApi: 'http://172.21.30.65:2005/api/Cat_GetData/GetOrgTreeView',
                  valueField: 'id',
                  textField: 'FullName',
                  compareField: null,
                  data: {
                    text: '',
                    textField: null,
                    Type: null,
                  },
                  dataSource: null,
                },
                Name: 'Key__bbd44046_08a5_4ca3_9842_05c428f67915',
                HeaderName: 'Ghi chú/Nhận xét',
                HeaderKey: 'Ghi chú/Nhận xét',
                Width: 220,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: 'Select',
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
            ],
            serverSide: null,
            Name: 'KeyObject__Rec_RecruitmentPlan',
            HeaderName: 'Các cấp đánh giá',
            HeaderKey: 'Các cấp đánh giá',
            Width: 220,
            Sortable: false,
            Filter: false,
            Class: null,
            Hidden: false,
            Format: null,
            OrderColumn: null,
            RowOnPage: null,
            Group: false,
            Sum: false,
            isNumber: false,
            Disable: true,
            BackgroundFields: null,
            ColorFields: null,
            GroupIndex: null,
            Locked: false,
            Sticky: false,
            Template: null,
            Type: 'group',
            IsMultiColumn: true,
            IsHiddenConfig: null,
            DisabledFormat: null,
            AllowClickColumn: null,
            ChildrenLink: null,
            ParentLink: null,
            Style: null,
            HeaderStyle: null,
            HeaderClass: null,
            Validators: {
              Required: true,
              MinLength: 5,
              MaxLength: 50,
              Email: true,
            },
          },
        ],
        allowAdd: false,
        allowEdit: true,
        allowDelete: false,
        allowImport: false,
        allowExport: true,
      },
      data: dataGoalEvaluationSection,
    },
    data: gridPersonalDetailDefineDataSource,
  },
  {
    id: 'e4dbb05f-be87-45b2-bfc8-db1f703cef15',
    title: 'Bảng đánh giá năng lực',
    description: 'Đánh giá năng lực của nhân viên',
    type: 'competency-evaluation',
    layout: 'vertical',
    columns: 2,
    controls: [],
    properties: {
      gridConfig: {
        fields: {},
        allowEmployeeEdit: true,
        allowManagerEdit: true,
        columns: [
          {
            GroupType: null,
            ReferenceName: null,
            ReferenceDataSource: null,
            TypeControl: 'CustomControl|TextBox',
            MultiColumn: null,
            serverSide: null,
            Name: 'ID',
            HeaderName: 'ID',
            HeaderKey: 'ID',
            Width: 80,
            Sortable: false,
            Filter: false,
            Class: null,
            Hidden: true,
            Format: null,
            OrderColumn: null,
            RowOnPage: 50,
            Group: false,
            Sum: false,
            isNumber: false,
            Disable: true,
            BackgroundFields: null,
            ColorFields: null,
            GroupIndex: null,
            Locked: false,
            Sticky: false,
            Template: null,
            Type: null,
            IsMultiColumn: null,
            IsHiddenConfig: null,
            DisabledFormat: null,
            AllowClickColumn: null,
            ChildrenLink: null,
            ParentLink: null,
            Style: null,
            HeaderStyle: null,
            HeaderClass: null,
            Validators: {
              Required: true,
              MinLength: 5,
              MaxLength: 50,
              Email: true,
            },
          },
          {
            GroupType: null,
            ReferenceName: null,
            ReferenceDataSource: null,
            TypeControl: 'CustomControl|TextBox',
            MultiColumn: null,
            serverSide: null,
            Name: 'Criteria',
            HeaderName: 'Nhóm năng lực',
            HeaderKey: 'Nhóm năng lực',
            Width: 220,
            Sortable: false,
            Filter: false,
            Class: null,
            Hidden: true,
            Format: null,
            OrderColumn: null,
            RowOnPage: null,
            Group: true,
            Sum: false,
            isNumber: false,
            Disable: true,
            BackgroundFields: null,
            ColorFields: null,
            GroupIndex: null,
            Locked: false,
            Sticky: false,
            Template: null,
            Type: null,
            IsMultiColumn: null,
            IsHiddenConfig: null,
            DisabledFormat: null,
            AllowClickColumn: null,
            ChildrenLink: null,
            ParentLink: null,
            Style: null,
            HeaderStyle: null,
            HeaderClass: null,
            Show: false,
            Validators: {
              Required: true,
              MinLength: 5,
              MaxLength: 50,
              Email: true,
            },
          },
          {
            GroupType: null,
            ReferenceName: null,
            ReferenceDataSource: null,
            TypeControl: 'CustomControl|TextBox',
            MultiColumn: [
              {
                GroupType: null,
                ReferenceName: null,
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                Name: 'CriteriaName',
                HeaderName: 'Tên năng lực',
                HeaderKey: 'Tên năng lực',
                Width: 220,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: null,
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
              {
                GroupType: null,
                ReferenceName: null,
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                Name: 'Weight',
                HeaderName: 'Trọng số',
                HeaderKey: 'Trọng số',
                Width: 100,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: 'Number|0',
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: true,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Calculate: 'sum',
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
            ],
            serverSide: null,
            Name: 'KeyObject__CompetencyInfo',
            HeaderName: 'Thông tin năng lực',
            HeaderKey: 'Thông tin năng lực',
            Width: 220,
            Sortable: false,
            Filter: false,
            Class: null,
            Hidden: false,
            Format: null,
            OrderColumn: null,
            RowOnPage: null,
            Group: false,
            Sum: false,
            isNumber: false,
            Disable: true,
            BackgroundFields: null,
            ColorFields: null,
            GroupIndex: null,
            Locked: true,
            Sticky: false,
            Template: null,
            Type: 'group',
            IsMultiColumn: true,
            IsHiddenConfig: null,
            DisabledFormat: null,
            AllowClickColumn: null,
            ChildrenLink: null,
            ParentLink: null,
            Style: null,
            HeaderStyle: null,
            HeaderClass: null,
            Validators: {
              Required: true,
              MinLength: 5,
              MaxLength: 50,
              Email: true,
            },
          },
          {
            GroupType: null,
            ReferenceName: null,
            ReferenceDataSource: null,
            TypeControl: 'CustomControl|TextBox',
            MultiColumn: [
              {
                GroupType: null,
                ReferenceName: null,
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                Name: 'description',
                HeaderName: 'Mô tả',
                HeaderKey: 'Mô tả',
                Width: 220,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: null,
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
              {
                GroupType: null,
                ReferenceName: null,
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                Name: 'expectedResult',
                HeaderName: 'Kết quả cần đạt',
                HeaderKey: 'Kết quả cần đạt',
                Width: 220,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: null,
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
            ],
            serverSide: null,
            Name: 'KeyObject__CompetencyInfo',
            HeaderName: 'Chi tiết năng lực',
            HeaderKey: 'Chi tiết năng lực',
            Width: 220,
            Sortable: false,
            Filter: false,
            Class: null,
            Hidden: false,
            Format: null,
            OrderColumn: null,
            RowOnPage: null,
            Group: false,
            Sum: false,
            isNumber: false,
            Disable: true,
            BackgroundFields: null,
            ColorFields: null,
            GroupIndex: null,
            Locked: false,
            Sticky: false,
            Template: null,
            Type: 'group',
            IsMultiColumn: true,
            IsHiddenConfig: null,
            DisabledFormat: null,
            AllowClickColumn: null,
            ChildrenLink: null,
            ParentLink: null,
            Style: null,
            HeaderStyle: null,
            HeaderClass: null,
            Validators: {
              Required: true,
              MinLength: 5,
              MaxLength: 50,
              Email: true,
            },
          },
          {
            GroupType: null,
            ReferenceName: null,
            ReferenceDataSource: null,
            TypeControl: 'CustomControl|TextBox',
            MultiColumn: [
              {
                GroupType: null,
                ReferenceName: null,
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                Name: 'level1',
                HeaderName: 'Cấp độ 1',
                HeaderKey: 'Cấp độ 1',
                Width: 120,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: null,
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
              {
                GroupType: null,
                ReferenceName: null,
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                Name: 'level2',
                HeaderName: 'Cấp độ 2',
                HeaderKey: 'Cấp độ 2',
                Width: 120,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: null,
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
              {
                GroupType: null,
                ReferenceName: null,
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                Name: 'level3',
                HeaderName: 'Cấp độ 3',
                HeaderKey: 'Cấp độ 3',
                Width: 120,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: null,
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
              {
                GroupType: null,
                ReferenceName: null,
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                Name: 'level4',
                HeaderName: 'Cấp độ 4',
                HeaderKey: 'Cấp độ 4',
                Width: 120,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: null,
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
              {
                GroupType: null,
                ReferenceName: null,
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                Name: 'level5',
                HeaderName: 'Cấp độ 5',
                HeaderKey: 'Cấp độ 5',
                Width: 120,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: null,
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
            ],
            serverSide: null,
            Name: 'KeyObject__CompetencyLevels',
            HeaderName: 'Hành vi đo lường',
            HeaderKey: 'Hành vi đo lường',
            Width: 220,
            Sortable: false,
            Filter: false,
            Class: null,
            Hidden: false,
            Format: null,
            OrderColumn: null,
            RowOnPage: null,
            Group: false,
            Sum: false,
            isNumber: false,
            Disable: true,
            BackgroundFields: null,
            ColorFields: null,
            GroupIndex: null,
            Locked: false,
            Sticky: false,
            Template: null,
            Type: 'group',
            IsMultiColumn: true,
            IsHiddenConfig: null,
            DisabledFormat: null,
            AllowClickColumn: null,
            ChildrenLink: null,
            ParentLink: null,
            Style: null,
            HeaderStyle: null,
            HeaderClass: null,
            Validators: {
              Required: true,
              MinLength: 5,
              MaxLength: 50,
              Email: true,
            },
          },
          {
            GroupType: null,
            ReferenceName: null,
            ReferenceDataSource: null,
            TypeControl: 'CustomControl|TextBox',
            MultiColumn: [
              {
                GroupType: null,
                ReferenceName: null,
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                serverSide: null,
                Name: 'Mark',
                HeaderName: 'Điểm',
                HeaderKey: 'Điểm',
                Width: 180,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: 'Number|0',
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
              {
                GroupType: null,
                ReferenceName: null,
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                serverSide: null,
                Name: 'Level',
                HeaderName: 'Xếp loại',
                HeaderKey: 'Xếp loại',
                Width: 180,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: null,
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
              {
                GroupType: null,
                ReferenceName: null,
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                serverSide: null,
                Name: 'Note',
                HeaderName: 'Ghi chú/Nhận xét',
                HeaderKey: 'Ghi chú/Nhận xét',
                Width: 180,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: null,
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
            ],
            serverSide: null,
            Name: 'KeyObject__Rec_RecruitmentPlan',
            HeaderName: 'Các cấp đánh giá',
            HeaderKey: 'Các cấp đánh giá',
            Width: 220,
            Sortable: false,
            Filter: false,
            Class: null,
            Hidden: false,
            Format: null,
            OrderColumn: null,
            RowOnPage: null,
            Group: false,
            Sum: false,
            isNumber: false,
            Disable: true,
            BackgroundFields: null,
            ColorFields: null,
            GroupIndex: null,
            Locked: false,
            Sticky: false,
            Template: null,
            Type: 'group',
            IsMultiColumn: true,
            IsHiddenConfig: null,
            DisabledFormat: null,
            AllowClickColumn: null,
            ChildrenLink: null,
            ParentLink: null,
            Style: null,
            HeaderStyle: null,
            HeaderClass: null,
            Validators: {
              Required: true,
              MinLength: 5,
              MaxLength: 50,
              Email: true,
            },
          },
        ],
        allowAdd: false,
        allowEdit: true,
        allowDelete: false,
        allowImport: false,
        allowExport: true,
      },
    },
    data: dataCompetencyEvaluationSection,
  },
  {
    id: 'c8ff4824-8601-4292-8fe4-4a62463fc6d8',
    title: 'Bảng đánh giá 360',
    description: 'Đánh giá 360 độ từ các bên liên quan',
    type: 'evaluation-360',
    layout: 'vertical',
    columns: 2,
    controls: [],
    properties: {
      gridConfig: {
        fields: {},
        allowEmployeeEdit: true,
        allowManagerEdit: true,
        columns: [
          {
            GroupType: null,
            ReferenceName: null,
            ReferenceDataSource: null,
            TypeControl: 'CustomControl|TextBox',
            MultiColumn: null,
            serverSide: null,
            Name: 'ID',
            HeaderName: 'ID',
            HeaderKey: 'ID',
            Width: 80,
            Sortable: false,
            Filter: false,
            Class: null,
            Hidden: true,
            Format: null,
            OrderColumn: null,
            RowOnPage: 50,
            Group: false,
            Sum: false,
            isNumber: false,
            Disable: true,
            BackgroundFields: null,
            ColorFields: null,
            GroupIndex: null,
            Locked: false,
            Sticky: false,
            Template: null,
            Type: null,
            IsMultiColumn: null,
            IsHiddenConfig: null,
            DisabledFormat: null,
            AllowClickColumn: null,
            ChildrenLink: null,
            ParentLink: null,
            Style: null,
            HeaderStyle: null,
            HeaderClass: null,
            Validators: {
              Required: true,
              MinLength: 5,
              MaxLength: 50,
              Email: true,
            },
          },
          {
            GroupType: null,
            ReferenceName: null,
            ReferenceDataSource: null,
            TypeControl: 'CustomControl|TextBox',
            MultiColumn: null,
            serverSide: null,
            Name: 'Criteria',
            HeaderName: 'Nhóm năng lực',
            HeaderKey: 'Nhóm năng lực',
            Width: 220,
            Sortable: false,
            Filter: false,
            Class: null,
            Hidden: true,
            Format: null,
            OrderColumn: null,
            RowOnPage: null,
            Group: true,
            Sum: false,
            isNumber: false,
            Disable: true,
            BackgroundFields: null,
            ColorFields: null,
            GroupIndex: null,
            Locked: false,
            Sticky: false,
            Template: null,
            Type: null,
            IsMultiColumn: null,
            IsHiddenConfig: null,
            DisabledFormat: null,
            AllowClickColumn: null,
            ChildrenLink: null,
            ParentLink: null,
            Style: null,
            HeaderStyle: null,
            HeaderClass: null,
            Validators: {
              Required: true,
              MinLength: 5,
              MaxLength: 50,
              Email: true,
            },
          },
          {
            GroupType: null,
            ReferenceName: null,
            ReferenceDataSource: null,
            TypeControl: 'CustomControl|TextBox',
            MultiColumn: [
              {
                GroupType: null,
                ReferenceName: null,
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                Name: 'CriteriaName',
                HeaderName: 'Tên năng lực',
                HeaderKey: 'Tên năng lực',
                Width: 220,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: null,
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
              {
                GroupType: null,
                ReferenceName: null,
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                Name: 'Weight',
                HeaderName: 'Trọng số',
                HeaderKey: 'Trọng số',
                Width: 100,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: 'Number|0',
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: true,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Calculate: 'sum',
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
            ],
            serverSide: null,
            Name: 'KeyObject__CompetencyInfo',
            HeaderName: 'Thông tin năng lực',
            HeaderKey: 'Thông tin năng lực',
            Width: 220,
            Sortable: false,
            Filter: false,
            Class: null,
            Hidden: false,
            Format: null,
            OrderColumn: null,
            RowOnPage: null,
            Group: false,
            Sum: false,
            isNumber: false,
            Disable: true,
            BackgroundFields: null,
            ColorFields: null,
            GroupIndex: null,
            Locked: true,
            Sticky: false,
            Template: null,
            Type: 'group',
            IsMultiColumn: true,
            IsHiddenConfig: null,
            DisabledFormat: null,
            AllowClickColumn: null,
            ChildrenLink: null,
            ParentLink: null,
            Style: null,
            HeaderStyle: null,
            HeaderClass: null,
            Validators: {
              Required: true,
              MinLength: 5,
              MaxLength: 50,
              Email: true,
            },
          },
          {
            GroupType: null,
            ReferenceName: null,
            ReferenceDataSource: null,
            TypeControl: 'CustomControl|TextBox',
            MultiColumn: [
              {
                GroupType: null,
                ReferenceName: null,
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                Name: 'description',
                HeaderName: 'Mô tả',
                HeaderKey: 'Mô tả',
                Width: 220,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: null,
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
              {
                GroupType: null,
                ReferenceName: null,
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                Name: 'expectedResult',
                HeaderName: 'Kết quả cần đạt',
                HeaderKey: 'Kết quả cần đạt',
                Width: 220,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: null,
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
            ],
            serverSide: null,
            Name: 'KeyObject__CompetencyInfo',
            HeaderName: 'Chi tiết năng lực',
            HeaderKey: 'Chi tiết năng lực',
            Width: 220,
            Sortable: false,
            Filter: false,
            Class: null,
            Hidden: false,
            Format: null,
            OrderColumn: null,
            RowOnPage: null,
            Group: false,
            Sum: false,
            isNumber: false,
            Disable: true,
            BackgroundFields: null,
            ColorFields: null,
            GroupIndex: null,
            Locked: false,
            Sticky: false,
            Template: null,
            Type: 'group',
            IsMultiColumn: true,
            IsHiddenConfig: null,
            DisabledFormat: null,
            AllowClickColumn: null,
            ChildrenLink: null,
            ParentLink: null,
            Style: null,
            HeaderStyle: null,
            HeaderClass: null,
            Validators: {
              Required: true,
              MinLength: 5,
              MaxLength: 50,
              Email: true,
            },
          },
          {
            GroupType: null,
            ReferenceName: null,
            ReferenceDataSource: null,
            TypeControl: 'CustomControl|TextBox',
            MultiColumn: [
              {
                GroupType: null,
                ReferenceName: null,
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                Name: 'level1',
                HeaderName: 'Cấp độ 1',
                HeaderKey: 'Cấp độ 1',
                Width: 120,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: null,
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
              {
                GroupType: null,
                ReferenceName: null,
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                Name: 'level2',
                HeaderName: 'Cấp độ 2',
                HeaderKey: 'Cấp độ 2',
                Width: 120,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: null,
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
              {
                GroupType: null,
                ReferenceName: null,
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                Name: 'level3',
                HeaderName: 'Cấp độ 3',
                HeaderKey: 'Cấp độ 3',
                Width: 120,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: null,
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
              {
                GroupType: null,
                ReferenceName: null,
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                Name: 'level4',
                HeaderName: 'Cấp độ 4',
                HeaderKey: 'Cấp độ 4',
                Width: 120,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: null,
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
              {
                GroupType: null,
                ReferenceName: null,
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                Name: 'level5',
                HeaderName: 'Cấp độ 5',
                HeaderKey: 'Cấp độ 5',
                Width: 120,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: null,
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
            ],
            serverSide: null,
            Name: 'KeyObject__CompetencyLevels',
            HeaderName: 'Thang đánh giá hành vi',
            HeaderKey: 'Thang đánh giá hành vi',
            Width: 220,
            Sortable: false,
            Filter: false,
            Class: null,
            Hidden: false,
            Format: null,
            OrderColumn: null,
            RowOnPage: null,
            Group: false,
            Sum: false,
            isNumber: false,
            Disable: true,
            BackgroundFields: null,
            ColorFields: null,
            GroupIndex: null,
            Locked: false,
            Sticky: false,
            Template: null,
            Type: 'group',
            IsMultiColumn: true,
            IsHiddenConfig: null,
            DisabledFormat: null,
            AllowClickColumn: null,
            ChildrenLink: null,
            ParentLink: null,
            Style: null,
            HeaderStyle: null,
            HeaderClass: null,
            Validators: {
              Required: true,
              MinLength: 5,
              MaxLength: 50,
              Email: true,
            },
          },
          {
            GroupType: null,
            ReferenceName: null,
            ReferenceDataSource: null,
            TypeControl: 'CustomControl|TextBox',
            MultiColumn: [
              {
                GroupType: 'Category',
                ReferenceName: 'KeyOrigin__bbd44046_08a5_4ca3_9842_05c428f67915',
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                serverSide: {
                  isEnum: null,
                  isMulti: null,
                  dataTable: 'Cat_OrgStructure',
                  objectTable: null,
                  childFieldOrigin: null,
                  urlApi: 'http://172.21.30.65:2005/api/Cat_GetData/GetOrgTreeView',
                  valueField: 'id',
                  textField: 'FullName',
                  compareField: null,
                  data: {
                    text: '',
                    textField: null,
                    Type: null,
                  },
                  dataSource: null,
                },
                Name: 'Key__bbd44046_08a5_4ca3_9842_05c428f67915',
                HeaderName: 'Điểm',
                HeaderKey: 'Điểm',
                Width: 180,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: 'Select',
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
              {
                GroupType: 'Category',
                ReferenceName: 'KeyOrigin__bbd44046_08a5_4ca3_9842_05c428f67915',
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                serverSide: {
                  isEnum: null,
                  isMulti: null,
                  dataTable: 'Cat_OrgStructure',
                  objectTable: null,
                  childFieldOrigin: null,
                  urlApi: 'http://172.21.30.65:2005/api/Cat_GetData/GetOrgTreeView',
                  valueField: 'id',
                  textField: 'FullName',
                  compareField: null,
                  data: {
                    text: '',
                    textField: null,
                    Type: null,
                  },
                  dataSource: null,
                },
                Name: 'Key__bbd44046_08a5_4ca3_9842_05c428f67915',
                HeaderName: 'Xếp loại',
                HeaderKey: 'Xếp loại',
                Width: 180,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: 'Select',
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
              {
                GroupType: 'Category',
                ReferenceName: 'KeyOrigin__bbd44046_08a5_4ca3_9842_05c428f67915',
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                serverSide: {
                  isEnum: null,
                  isMulti: null,
                  dataTable: 'Cat_OrgStructure',
                  objectTable: null,
                  childFieldOrigin: null,
                  urlApi: 'http://172.21.30.65:2005/api/Cat_GetData/GetOrgTreeView',
                  valueField: 'id',
                  textField: 'FullName',
                  compareField: null,
                  data: {
                    text: '',
                    textField: null,
                    Type: null,
                  },
                  dataSource: null,
                },
                Name: 'Key__bbd44046_08a5_4ca3_9842_05c428f67915',
                HeaderName: 'Ghi chú/Nhận xét',
                HeaderKey: 'Ghi chú/Nhận xét',
                Width: 180,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: 'Select',
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
            ],
            serverSide: null,
            Name: 'KeyObject__Rec_RecruitmentPlan',
            HeaderName: 'Các cấp đánh giá',
            HeaderKey: 'Các cấp đánh giá',
            Width: 220,
            Sortable: false,
            Filter: false,
            Class: null,
            Hidden: false,
            Format: null,
            OrderColumn: null,
            RowOnPage: null,
            Group: false,
            Sum: false,
            isNumber: false,
            Disable: true,
            BackgroundFields: null,
            ColorFields: null,
            GroupIndex: null,
            Locked: false,
            Sticky: false,
            Template: null,
            Type: 'group',
            IsMultiColumn: true,
            IsHiddenConfig: null,
            DisabledFormat: null,
            AllowClickColumn: null,
            ChildrenLink: null,
            ParentLink: null,
            Style: null,
            HeaderStyle: null,
            HeaderClass: null,
            Validators: {
              Required: true,
              MinLength: 5,
              MaxLength: 50,
              Email: true,
            },
          },
        ],
        allowAdd: false,
        allowEdit: true,
        allowDelete: false,
        allowImport: false,
        allowExport: true,
      },
    },
    data: dataEvaluation360Section,
  },
  {
    id: '85011ec6-54cb-4709-a5a8-47490b0891b6',
    title: 'Bảng đánh giá tự chỉnh',
    description: 'Bảng đánh giá tùy chỉnh theo yêu cầu',
    type: 'custom-evaluation',
    layout: 'vertical',
    columns: 2,
    controls: [],
    properties: {
      gridConfig: {
        fields: {},
        allowEmployeeEdit: true,
        allowManagerEdit: true,
        columns: [
          {
            GroupType: null,
            ReferenceName: null,
            ReferenceDataSource: null,
            TypeControl: 'CustomControl|TextBox',
            MultiColumn: null,
            serverSide: null,
            Name: 'ID',
            HeaderName: 'ID',
            HeaderKey: 'ID',
            Width: 220,
            Sortable: false,
            Filter: false,
            Class: null,
            Hidden: true,
            Format: null,
            OrderColumn: null,
            RowOnPage: 50,
            Group: false,
            Sum: false,
            isNumber: false,
            Disable: true,
            BackgroundFields: null,
            ColorFields: null,
            GroupIndex: null,
            Locked: false,
            Sticky: false,
            Template: null,
            Type: null,
            IsMultiColumn: null,
            IsHiddenConfig: null,
            DisabledFormat: null,
            AllowClickColumn: null,
            ChildrenLink: null,
            ParentLink: null,
            Style: null,
            HeaderStyle: null,
            HeaderClass: null,
            Validators: {
              Required: true,
              MinLength: 5,
              MaxLength: 50,
              Email: true,
            },
          },
          {
            GroupType: null,
            ReferenceName: null,
            ReferenceDataSource: null,
            TypeControl: 'CustomControl|TextBox',
            MultiColumn: null,
            serverSide: null,
            Name: 'Criteria',
            HeaderName: 'Nhóm năng lực',
            HeaderKey: 'Nhóm năng lực',
            Width: 220,
            Sortable: false,
            Filter: false,
            Class: 'section-criteria',
            Hidden: true,
            Format: null,
            OrderColumn: null,
            RowOnPage: null,
            Group: true,
            Sum: false,
            isNumber: false,
            Disable: true,
            BackgroundFields: null,
            ColorFields: null,
            GroupIndex: null,
            Locked: false,
            Sticky: false,
            Template: null,
            Type: null,
            IsMultiColumn: null,
            IsHiddenConfig: null,
            DisabledFormat: null,
            AllowClickColumn: null,
            ChildrenLink: null,
            ParentLink: null,
            Style: null,
            HeaderStyle: null,
            HeaderClass: null,
            Validators: {
              Required: true,
              MinLength: 5,
              MaxLength: 50,
              Email: true,
            },
          },
          {
            GroupType: null,
            ReferenceName: null,
            ReferenceDataSource: null,
            TypeControl: 'CustomControl|TextBox',
            MultiColumn: [
              {
                GroupType: null,
                ReferenceName: null,
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                serverSide: null,
                Name: 'CriteriaName',
                HeaderName: 'Tên mục tiêu',
                HeaderKey: 'Tên mục tiêu',
                Width: 220,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: null,
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Disable: true,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
              {
                GroupType: null,
                ReferenceName: null,
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                serverSide: null,
                Name: 'Weight',
                HeaderName: 'Trọng số',
                HeaderKey: 'Trọng số %',
                Width: 150,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: null,
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Calculate: 'sum',
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
              {
                GroupType: 'Category',
                ReferenceName: 'KeyOrigin__bbd44046_08a5_4ca3_9842_05c428f67915',
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                serverSide: {
                  isEnum: null,
                  isMulti: null,
                  dataTable: 'Cat_OrgStructure',
                  objectTable: null,
                  childFieldOrigin: null,
                  urlApi: 'http://172.21.30.65:2005/api/Cat_GetData/GetOrgTreeView',
                  valueField: 'id',
                  textField: 'FullName',
                  compareField: null,
                  data: {
                    text: '',
                    textField: null,
                    Type: null,
                  },
                  dataSource: null,
                },
                Name: 'Key__bbd44046_08a5_4ca3_9842_05c428f67915',
                HeaderName: 'Chỉ tiêu',
                HeaderKey: 'Chỉ tiêu',
                Width: 150,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: 'Select',
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
            ],
            serverSide: null,
            Name: 'InfoGoal',
            HeaderName: 'Thông tin mục tiêu',
            HeaderKey: 'Thông tin mục tiêu',
            Width: 220,
            Sortable: false,
            Filter: false,
            Class: null,
            Hidden: false,
            Format: null,
            OrderColumn: null,
            RowOnPage: null,
            Group: false,
            Sum: false,
            isNumber: false,
            Disable: true,
            BackgroundFields: null,
            ColorFields: null,
            GroupIndex: null,
            Locked: true,
            Sticky: false,
            Template: null,
            Type: 'group',
            IsMultiColumn: true,
            IsHiddenConfig: null,
            DisabledFormat: null,
            AllowClickColumn: null,
            ChildrenLink: null,
            ParentLink: null,
            Style: null,
            HeaderStyle: null,
            HeaderClass: null,
            Validators: {
              Required: true,
              MinLength: 5,
              MaxLength: 50,
              Email: true,
            },
          },
          {
            GroupType: null,
            ReferenceName: null,
            ReferenceDataSource: null,
            TypeControl: 'CustomControl|TextBox',
            MultiColumn: [
              {
                GroupType: 'Category',
                ReferenceName: 'KeyOrigin__bbd44046_08a5_4ca3_9842_05c428f67915',
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                serverSide: {
                  isEnum: null,
                  isMulti: null,
                  dataTable: 'Cat_OrgStructure',
                  objectTable: null,
                  childFieldOrigin: null,
                  urlApi: 'http://172.21.30.65:2005/api/Cat_GetData/GetOrgTreeView',
                  valueField: 'id',
                  textField: 'FullName',
                  compareField: null,
                  data: {
                    text: '',
                    textField: null,
                    Type: null,
                  },
                  dataSource: null,
                },
                Name: 'Key__bbd44046_08a5_4ca3_9842_05c428f67915',
                HeaderName: 'Đơn vị đo',
                HeaderKey: 'Đơn vị đo',
                Width: 220,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: 'Select',
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
              {
                GroupType: 'Category',
                ReferenceName: 'KeyOrigin__bbd44046_08a5_4ca3_9842_05c428f67915',
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                serverSide: {
                  isEnum: null,
                  isMulti: null,
                  dataTable: 'Cat_OrgStructure',
                  objectTable: null,
                  childFieldOrigin: null,
                  urlApi: 'http://172.21.30.65:2005/api/Cat_GetData/GetOrgTreeView',
                  valueField: 'id',
                  textField: 'FullName',
                  compareField: null,
                  data: {
                    text: '',
                    textField: null,
                    Type: null,
                  },
                  dataSource: null,
                },
                Name: 'Key__bbd44046_08a5_4ca3_9842_05c428f67915',
                HeaderName: 'Mô tả',
                HeaderKey: 'Mô tả',
                Width: 220,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: 'Select',
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
              {
                GroupType: 'Category',
                ReferenceName: 'KeyOrigin__bbd44046_08a5_4ca3_9842_05c428f67915',
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                serverSide: {
                  isEnum: null,
                  isMulti: null,
                  dataTable: 'Cat_OrgStructure',
                  objectTable: null,
                  childFieldOrigin: null,
                  urlApi: 'http://172.21.30.65:2005/api/Cat_GetData/GetOrgTreeView',
                  valueField: 'id',
                  textField: 'FullName',
                  compareField: null,
                  data: {
                    text: '',
                    textField: null,
                    Type: null,
                  },
                  dataSource: null,
                },
                Name: 'Key__bbd44046_08a5_4ca3_9842_05c428f67915',
                HeaderName: 'Kết quả cần đạt',
                HeaderKey: 'Kết quả cần đạt',
                Width: 220,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: 'Select',
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
            ],
            serverSide: null,
            Name: 'KeyObject__Rec_RecruitmentPlan',
            HeaderName: 'Chi tiết mục tiêu',
            HeaderKey: 'Chi tiết mục tiêu',
            Width: 220,
            Sortable: false,
            Filter: false,
            Class: null,
            Hidden: false,
            Format: null,
            OrderColumn: null,
            RowOnPage: null,
            Group: false,
            Sum: false,
            isNumber: false,
            Disable: true,
            BackgroundFields: null,
            ColorFields: null,
            GroupIndex: null,
            Locked: false,
            Sticky: false,
            Template: null,
            Type: 'group',
            IsMultiColumn: true,
            IsHiddenConfig: null,
            DisabledFormat: null,
            AllowClickColumn: null,
            ChildrenLink: null,
            ParentLink: null,
            Style: null,
            HeaderStyle: null,
            HeaderClass: null,
            Validators: {
              Required: true,
              MinLength: 5,
              MaxLength: 50,
              Email: true,
            },
          },
          {
            GroupType: null,
            ReferenceName: null,
            ReferenceDataSource: null,
            TypeControl: 'CustomControl|TextBox',
            MultiColumn: [
              {
                GroupType: 'Category',
                ReferenceName: 'KeyOrigin__bbd44046_08a5_4ca3_9842_05c428f67915',
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                serverSide: {
                  isEnum: null,
                  isMulti: null,
                  dataTable: 'Cat_OrgStructure',
                  objectTable: null,
                  childFieldOrigin: null,
                  urlApi: 'http://172.21.30.65:2005/api/Cat_GetData/GetOrgTreeView',
                  valueField: 'id',
                  textField: 'FullName',
                  compareField: null,
                  data: {
                    text: '',
                    textField: null,
                    Type: null,
                  },
                  dataSource: null,
                },
                Name: 'Key__bbd44046_08a5_4ca3_9842_05c428f67915',
                HeaderName: 'Chỉ số đầu',
                HeaderKey: 'Chỉ số đầu',
                Width: 220,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: 'Select',
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
              {
                GroupType: 'Category',
                ReferenceName: 'KeyOrigin__bbd44046_08a5_4ca3_9842_05c428f67915',
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                serverSide: {
                  isEnum: null,
                  isMulti: null,
                  dataTable: 'Cat_OrgStructure',
                  objectTable: null,
                  childFieldOrigin: null,
                  urlApi: 'http://172.21.30.65:2005/api/Cat_GetData/GetOrgTreeView',
                  valueField: 'id',
                  textField: 'FullName',
                  compareField: null,
                  data: {
                    text: '',
                    textField: null,
                    Type: null,
                  },
                  dataSource: null,
                },
                Name: 'Key__bbd44046_08a5_4ca3_9842_05c428f67915',
                HeaderName: 'Hiệu chỉnh ',
                HeaderKey: 'Hiệu chỉnh ',
                Width: 220,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: 'Select',
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
              {
                GroupType: 'Category',
                ReferenceName: 'KeyOrigin__bbd44046_08a5_4ca3_9842_05c428f67915',
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                serverSide: {
                  isEnum: null,
                  isMulti: null,
                  dataTable: 'Cat_OrgStructure',
                  objectTable: null,
                  childFieldOrigin: null,
                  urlApi: 'http://172.21.30.65:2005/api/Cat_GetData/GetOrgTreeView',
                  valueField: 'id',
                  textField: 'FullName',
                  compareField: null,
                  data: {
                    text: '',
                    textField: null,
                    Type: null,
                  },
                  dataSource: null,
                },
                Name: 'Key__bbd44046_08a5_4ca3_9842_05c428f67915',
                HeaderName: 'Hiệu chỉnh',
                HeaderKey: 'Hiệu chỉnh',
                Width: 220,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: 'Select',
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
              {
                GroupType: 'Category',
                ReferenceName: 'KeyOrigin__bbd44046_08a5_4ca3_9842_05c428f67915',
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                serverSide: {
                  isEnum: null,
                  isMulti: null,
                  dataTable: 'Cat_OrgStructure',
                  objectTable: null,
                  childFieldOrigin: null,
                  urlApi: 'http://172.21.30.65:2005/api/Cat_GetData/GetOrgTreeView',
                  valueField: 'id',
                  textField: 'FullName',
                  compareField: null,
                  data: {
                    text: '',
                    textField: null,
                    Type: null,
                  },
                  dataSource: null,
                },
                Name: 'Key__bbd44046_08a5_4ca3_9842_05c428f67915',
                HeaderName: 'Mức độ hoàn thành',
                HeaderKey: 'Mức độ hoàn thành',
                Width: 220,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: 'Select',
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
              {
                GroupType: 'Category',
                ReferenceName: 'KeyOrigin__bbd44046_08a5_4ca3_9842_05c428f67915',
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                serverSide: {
                  isEnum: null,
                  isMulti: null,
                  dataTable: 'Cat_OrgStructure',
                  objectTable: null,
                  childFieldOrigin: null,
                  urlApi: 'http://172.21.30.65:2005/api/Cat_GetData/GetOrgTreeView',
                  valueField: 'id',
                  textField: 'FullName',
                  compareField: null,
                  data: {
                    text: '',
                    textField: null,
                    Type: null,
                  },
                  dataSource: null,
                },
                Name: 'Key__bbd44046_08a5_4ca3_9842_05c428f67915',
                HeaderName: 'Quy đổi',
                HeaderKey: 'Quy đổi',
                Width: 220,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: 'Select',
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
            ],
            serverSide: null,
            Name: 'KeyObject__Rec_RecruitmentPlan',
            HeaderName: 'Tính toán kết quả',
            HeaderKey: 'Tính toán kết quả',
            Width: 220,
            Sortable: false,
            Filter: false,
            Class: null,
            Hidden: false,
            Format: null,
            OrderColumn: null,
            RowOnPage: null,
            Group: false,
            Sum: false,
            isNumber: false,
            Disable: true,
            BackgroundFields: null,
            ColorFields: null,
            GroupIndex: null,
            Locked: false,
            Sticky: false,
            Template: null,
            Type: 'group',
            IsMultiColumn: true,
            IsHiddenConfig: null,
            DisabledFormat: null,
            AllowClickColumn: null,
            ChildrenLink: null,
            ParentLink: null,
            Style: null,
            HeaderStyle: null,
            HeaderClass: null,
            Validators: {
              Required: true,
              MinLength: 5,
              MaxLength: 50,
              Email: true,
            },
          },
          {
            GroupType: null,
            ReferenceName: null,
            ReferenceDataSource: null,
            TypeControl: 'CustomControl|TextBox',
            MultiColumn: [
              {
                GroupType: 'Category',
                ReferenceName: 'KeyOrigin__bbd44046_08a5_4ca3_9842_05c428f67915',
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                serverSide: {
                  isEnum: null,
                  isMulti: null,
                  dataTable: 'Cat_OrgStructure',
                  objectTable: null,
                  childFieldOrigin: null,
                  urlApi: 'http://172.21.30.65:2005/api/Cat_GetData/GetOrgTreeView',
                  valueField: 'id',
                  textField: 'FullName',
                  compareField: null,
                  data: {
                    text: '',
                    textField: null,
                    Type: null,
                  },
                  dataSource: null,
                },
                Name: 'Key__bbd44046_08a5_4ca3_9842_05c428f67915',
                HeaderName: 'Điểm',
                HeaderKey: 'Điểm',
                Width: 220,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: 'Select',
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
              {
                GroupType: 'Category',
                ReferenceName: 'KeyOrigin__bbd44046_08a5_4ca3_9842_05c428f67915',
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                serverSide: {
                  isEnum: null,
                  isMulti: null,
                  dataTable: 'Cat_OrgStructure',
                  objectTable: null,
                  childFieldOrigin: null,
                  urlApi: 'http://172.21.30.65:2005/api/Cat_GetData/GetOrgTreeView',
                  valueField: 'id',
                  textField: 'FullName',
                  compareField: null,
                  data: {
                    text: '',
                    textField: null,
                    Type: null,
                  },
                  dataSource: null,
                },
                Name: 'Key__bbd44046_08a5_4ca3_9842_05c428f67915',
                HeaderName: 'Xếp loại',
                HeaderKey: 'Xếp loại',
                Width: 220,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: 'Select',
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
              {
                GroupType: 'Category',
                ReferenceName: 'KeyOrigin__bbd44046_08a5_4ca3_9842_05c428f67915',
                ReferenceDataSource: null,
                TypeControl: 'CustomControl|TextBox',
                MultiColumn: null,
                serverSide: {
                  isEnum: null,
                  isMulti: null,
                  dataTable: 'Cat_OrgStructure',
                  objectTable: null,
                  childFieldOrigin: null,
                  urlApi: 'http://172.21.30.65:2005/api/Cat_GetData/GetOrgTreeView',
                  valueField: 'id',
                  textField: 'FullName',
                  compareField: null,
                  data: {
                    text: '',
                    textField: null,
                    Type: null,
                  },
                  dataSource: null,
                },
                Name: 'Key__bbd44046_08a5_4ca3_9842_05c428f67915',
                HeaderName: 'Ghi chú/Nhận xét',
                HeaderKey: 'Ghi chú/Nhận xét',
                Width: 220,
                Sortable: false,
                Filter: false,
                Class: null,
                Hidden: false,
                Format: 'Select',
                OrderColumn: null,
                RowOnPage: null,
                Group: false,
                Sum: false,
                isNumber: false,
                Disable: true,
                BackgroundFields: null,
                ColorFields: null,
                GroupIndex: null,
                Locked: false,
                Sticky: false,
                Template: null,
                Type: null,
                IsMultiColumn: null,
                IsHiddenConfig: null,
                DisabledFormat: null,
                AllowClickColumn: null,
                ChildrenLink: null,
                ParentLink: null,
                Style: null,
                HeaderStyle: null,
                HeaderClass: null,
                Validators: {
                  Required: true,
                  MinLength: 5,
                  MaxLength: 50,
                  Email: true,
                },
              },
            ],
            serverSide: null,
            Name: 'KeyObject__Rec_RecruitmentPlan',
            HeaderName: 'Các cấp đánh giá',
            HeaderKey: 'Các cấp đánh giá',
            Width: 220,
            Sortable: false,
            Filter: false,
            Class: null,
            Hidden: false,
            Format: null,
            OrderColumn: null,
            RowOnPage: null,
            Group: false,
            Sum: false,
            isNumber: false,
            Disable: true,
            BackgroundFields: null,
            ColorFields: null,
            GroupIndex: null,
            Locked: false,
            Sticky: false,
            Template: null,
            Type: 'group',
            IsMultiColumn: true,
            IsHiddenConfig: null,
            DisabledFormat: null,
            AllowClickColumn: null,
            ChildrenLink: null,
            ParentLink: null,
            Style: null,
            HeaderStyle: null,
            HeaderClass: null,
            Validators: {
              Required: true,
              MinLength: 5,
              MaxLength: 50,
              Email: true,
            },
          },
        ],
        allowAdd: false,
        allowEdit: true,
        allowDelete: false,
        allowImport: false,
        allowExport: true,
      },
    },
    data: dataCustomEvaluationSection,
  },
  {
    id: '19d65801-d677-42f1-8b73-2b8370e5f4d1',
    title: 'Chữ ký & Phê duyệt',
    description: 'Phần chữ ký và phê duyệt đánh giá',
    type: 'signature-and-approval',
    layout: 'vertical',
    columns: 2,
    controls: [],
    properties: {},
  },
];
