export const STATUS_TAG = [
  {
    label: 'Chờ xác nhận',
    backgroundColor: '#FFEFCC',
    textColor: '#A13D0B',
  },
  {
    label: 'Đã huỷ',
    backgroundColor: '#FEF8C3',
    textColor: '#85550E',
  },
  {
    label: 'Tạm dừng',
    backgroundColor: '#EBEBEB',
    textColor: '#303030',
  },
];

export const STATUS_PROGRESS_TAG = [
  {
    label: 'Vượt chỉ tiêu',
    backgroundColor: '#DCECFE',
    textColor: '#1E45AF',
  },
  {
    label: 'Đạt chỉ tiêu',
    backgroundColor: '#D8FDDB',
    textColor: '#13681C',
  },
  {
    label: 'Không đạt',
    backgroundColor: '#FFDBDC',
    textColor: '#AB1316',
  },
  {
    label: 'Đã huỷ',
    backgroundColor: '#FEF8C3',
    textColor: '#85550E',
  },
  {
    label: 'Tạm dừng',
    backgroundColor: '#EBEBEB',
    textColor: '#303030',
  },
];

export const STATUS_PROGRESS_DATA = STATUS_PROGRESS_TAG.map((item, index) => ({
  label: item.label,
  value: Math.floor(Math.random() * 10),
  color: item.textColor,
}));


export const overviewDataSource = [
  {
    ID: '1',
    GoalName: 'Tổng doanh thu toàn tập đoàn năm 2025',
    Department: 'Ban điều hành',
    Representative: 'Hoàng Vũ Anh Quân',
    Process: 40,
    ParentID: null,
    HasChildren: true,
    TotalTarget: 120000000000000,
    DoneTarget: 12000000000000,
    Weight: 40,
    RemainingDay: 30,
    Target: 1200000,
    LatestResult: 12000000000000,
    Unit: 'currency',
    LastUpdate: '24/06/2025',
    Period: 'Năm 2025',
    TotalActual: 12000000000000,
    Variance: 0,
    CompletionRate: 0,
    ResultStatus: '',
    Status: 'Chờ xác nhận',
    Position: 'Giám đốc Kinh doanh',
  },
  {
    ID: '11',
    GoalName: 'Doanh thu ô tô sản xuất & phân phối',
    Department: 'CLD AUTO',
    Representative: 'Lê Hồng Hải',
    Process: 60,
    ParentID: '1',
    HasChildren: true,
    TotalTarget: 60000000000000,
    DoneTarget: 6000000000000,
    Weight: 40,
    RemainingDay: 30,
    Target: 600000,
    LatestResult: 6000000000000,
    Unit: 'currency',
    LastUpdate: '24/06/2025',
    Period: 'Năm 2025',
  },
  {
    ID: '12',
    GoalName: 'Doanh thu trồng trọt & chế biến',
    Department: 'CLD AGRICO',
    Representative: 'Nguyễn Xuân Hoài',
    Process: 65,
    ParentID: '1',
    HasChildren: false,
    TotalTarget: 20000000000000,
    DoneTarget: 2000000000000,
    Weight: 40,
    RemainingDay: 30,
    Target: 200000,
    LatestResult: 2000000000000,
    Unit: 'currency',
    LastUpdate: '24/06/2025',
    Period: 'Năm 2025',
  },
  {
    ID: '13',
    GoalName: 'Doanh thu logistics & vận tải',
    Department: 'CLD LOGI',
    Representative: 'Vũ Văn Thái',
    Process: 55,
    ParentID: '1',
    HasChildren: false,
    TotalTarget: 15000000000000,
    DoneTarget: 1500000000000,
    Weight: 40,
    RemainingDay: 15,
    Target: 150000,
    LatestResult: 1500000000000,
    Unit: 'currency',
    LastUpdate: '24/06/2025',
    Period: 'Năm 2025',
  },
  {
    ID: '14',
    GoalName: 'Doanh thu tổng thầu và đầu tư xây dựng',
    Department: 'CLD DICO',
    Representative: 'Đoàn Trung Hiếu',
    Process: 50,
    ParentID: '1',
    HasChildren: false,
    TotalTarget: 15000000000000,
    DoneTarget: 1500000000000,
    Weight: 40,
    RemainingDay: 0,
    Target: 150000,
    LatestResult: 1500000000000,
    Unit: 'currency',
    LastUpdate: '24/06/2025',
    Period: 'Năm 2025',
  },
  {
    ID: '15',
    GoalName: 'Doanh thu phân phối, bán lẻ & dịch vụ',
    Department: 'CLD INDUSTRIES',
    Representative: 'Nguyễn Thái Hòa',
    Process: 45,
    ParentID: '1',
    HasChildren: false,
    TotalTarget: 10000000000000,
    DoneTarget: 1000000000000,
    Weight: 40,
    RemainingDay: 30,
    Target: 100000,
    LatestResult: 1000000000000,
    Unit: 'currency',
    LastUpdate: '23/06/2025',
    Period: 'Năm 2025',
  },
  {
    ID: '111',
    GoalName: 'Sản lượng ô tô sản xuất',
    Department: 'Cty SX Ô tô Chu Lai',
    Representative: 'Đào Hải',
    Process: 70,
    ParentID: '11',
    HasChildren: false,
    TotalTarget: 120000,
    DoneTarget: 12000,
    Weight: 40,
    RemainingDay: 30,
    Target: 120000,
    LatestResult: 12000,
    Unit: 'number',
    LastUpdate: '23/06/2025',
    Period: 'Tháng 6/2025',
  },
  {
    ID: '2',
    GoalName: 'Lợi nhuận ròng',
    Department: 'Ban điều hành',
    Representative: 'Hoàng Vũ Anh Quân',
    Process: null,
    ParentID: '33',
    HasChildren: false,
    TotalTarget: null,
    DoneTarget: 5000,
    Weight: 10,
    RemainingDay: 30,
    Target: 5000,
    LatestResult: 5000,
    Unit: 'currency',
    LastUpdate: '24/06/2025',
    Period: 'Năm 2025',
    Position: 'Giám đốc Kinh doanh',
  },
  {
    ID: '3',
    GoalName: 'Thị phần trong ngành',
    Department: 'Ban điều hành',
    Representative: 'Hoàng Vũ Anh Quân',
    Process: 25,
    ParentID: null,
    HasChildren: true,
    TotalTarget: 10000000000000,
    DoneTarget: 10000000000000,
    Weight: 10,
    RemainingDay: 30,
    Target: 25,
    LatestResult: 10000000000000,
    Unit: 'currency',
    LastUpdate: '24/06/2025',
    Period: 'Năm 2025',
    TotalActual: 10000000000000,
    Variance: 0,
    CompletionRate: 0,
    ResultStatus: '',
    Status: 'Chờ xác nhận',
    Position: 'Giám đốc Kinh doanh',
  },
  {
    ID: '4',
    GoalName: 'Tỷ lệ giữ chân khách hàng',
    Department: 'Ban điều hành',
    Representative: 'Hoàng Vũ Anh Quân',
    Process: 10,
    ParentID: null,
    HasChildren: true,
    TotalTarget: 10000000000000,
    DoneTarget: 10000000000000,
    Weight: 10,
    RemainingDay: 30,
    Target: 10000000000000,
    LatestResult: 10000000000000,
    Unit: 'currency',
    LastUpdate: '23/06/2025',
    Period: 'Năm 2025',
    TotalActual: 12000000000000,
    Variance: 0,
    CompletionRate: 0,
    ResultStatus: '',
    Status: 'Chờ xác nhận',
    Position: 'Giám đốc Kinh doanh',
  },
  {
    ID: '5',
    GoalName: 'Số lượng khách hàng mới',
    Department: 'Ban điều hành',
    Representative: 'Hoàng Vũ Anh Quân',
    Process: 55,
    ParentID: null,
    HasChildren: true,
    TotalTarget: 100000,
    DoneTarget: 100000,
    Weight: 10,
    RemainingDay: 30,
    Target: 100000,
    LatestResult: 100000,
    Unit: 'currency',
    LastUpdate: '23/06/2025',
    Period: 'Năm 2025',
    TotalActual: 100000,
    Variance: 0,
    CompletionRate: 0,
    ResultStatus: 'Đã huỷ',
    Status: 'Đã huỷ',
    Position: 'Giám đốc Kinh doanh',
  },
  {
    ID: '6',
    GoalName: 'Tỷ lệ giảm chi phí vận hành',
    Department: 'Ban điều hành',
    Representative: 'Hoàng Vũ Anh Quân',
    Process: 55,
    ParentID: null,
    HasChildren: true,
    TotalTarget: 100000,
    DoneTarget: 100000,
    Weight: 10,
    RemainingDay: 30,
    Target: 10,
    LatestResult: 100000,
    Unit: 'currency',
    LastUpdate: '22/06/2025',
    Period: 'Năm 2025',
    TotalActual: 100000,
    Variance: 0,
    CompletionRate: 0,
    ResultStatus: 'Tạm dừng',
    Status: 'Tạm dừng',
    Position: 'Giám đốc Kinh doanh',
  },
  {
    ID: '7',
    GoalName: 'Tỷ lệ tự động hóa quy trình',
    Department: 'Ban điều hành',
    Representative: 'Hoàng Vũ Anh Quân',
    Process: 55,
    ParentID: null,
    HasChildren: true,
    TotalTarget: 50,
    DoneTarget: 5,
    Weight: 10,
    RemainingDay: 30,
    Target: 50,
    LatestResult: 5,
    Unit: 'percent',
    LastUpdate: '22/06/2025',
    Period: 'Năm 2025',
    TotalActual: 0,
    Variance: 0,
    CompletionRate: 0,
    ResultStatus: 'Đã huỷ',
    Status: 'Đã huỷ',
    Position: 'Giám đốc Kinh doanh',
  },
  {
    ID: '8',
    GoalName: 'Số giờ đào tạo trung bình mỗi nhân viên',
    Department: null,
    Representative: 'Hoàng Vũ Anh Quân',
    Process: 55,
    ParentID: null,
    HasChildren: true,
    TotalTarget: 40,
    DoneTarget: 10,
    Weight: 10,
    RemainingDay: 30,
    Target: 40,
    LatestResult: 10,
    Unit: 'giờ',
    LastUpdate: '21/06/2025',
    Period: 'Năm 2025',
    TotalActual: 0,
    Variance: 0,
    CompletionRate: 0,
    ResultStatus: 'Đã huỷ',
    Status: 'Đã huỷ',
    Position: 'Giám đốc Kinh doanh',
  },
  {
    ID: '9',
    GoalName: 'Số sáng kiến cải tiến quy trình từ nhân viên',
    Department: null,
    Representative: 'Hoàng Vũ Anh Quân',
    Process: 55,
    ParentID: null,
    HasChildren: true,
    TotalTarget: 50,
    DoneTarget: 5,
    Weight: 10,
    RemainingDay: 30,
    Target: 50,
    LatestResult: 5,
    Unit: 'number',
    LastUpdate: '21/06/2025',
    Period: 'Năm 2025',
    TotalActual: 0,
    Variance: 0,
    CompletionRate: 0,
    ResultStatus: 'Đã huỷ',
    Status: 'Đã huỷ',
    Position: 'Giám đốc Kinh doanh',
  },
  {
    ID: '21',
    GoalName: 'Lợi nhuận ròng ô tô sản xuất & phân phối',
    Department: 'CLD AUTO',
    Representative: 'Lê Hồng Hải',
    Process: 60,
    ParentID: '2',
    HasChildren: true,
    TotalTarget: 3000000000000,
    DoneTarget: 750000000000,
    Weight: 20,
    RemainingDay: 30,
    Target: 3000,
    LatestResult: 750000000000,
    Unit: 'currency',
    LastUpdate: '20/06/2025',
    Period: 'Quý 2/2025',
  },
  {
    ID: '22',
    GoalName: 'Lợi nhuận ròng trồng trọt & chế biến',
    Department: 'CLD AGRICO',
    Representative: 'Nguyễn Xuân Hoài',
    Process: 65,
    ParentID: '2',
    HasChildren: true,
    TotalTarget: 1000000000000,
    DoneTarget: 250000000000,
    Weight: 20,
    RemainingDay: 30,
    Target: 1000,
    LatestResult: 250000000000,
    Unit: 'currency',
    LastUpdate: '20/06/2025',
    Period: 'Quý 2/2025',
  },
  {
    ID: '23',
    GoalName: 'Lợi nhuận ròng logistics & vận tải',
    Department: 'CLD LOGI',
    Representative: 'Vũ Văn Thái',
    Process: 55,
    ParentID: '2',
    HasChildren: true,
    TotalTarget: 500000000000,
    DoneTarget: 125000000000,
    Weight: 20,
    RemainingDay: 15,
    Target: 500,
    LatestResult: 125000000000,
    Unit: 'currency',
    LastUpdate: '24/06/2025',
    Period: 'Quý 2/2025',
  },
  {
    ID: '24',
    GoalName: 'Lợi nhuận ròng tổng thầu và đầu tư xây dựng',
    Department: 'CLD DICO',
    Representative: 'Đoàn Trung Hiếu',
    Process: 50,
    ParentID: '2',
    HasChildren: true,
    TotalTarget: 200000000000,
    DoneTarget: 50000000000,
    Weight: 20,
    RemainingDay: 0,
    Target: 200,
    LatestResult: 50000000000,
    Unit: 'currency',
    LastUpdate: '24/06/2025',
    Period: 'Quý 2/2025',
  },
  {
    ID: '25',
    GoalName: 'Lợi nhuận ròng phân phối, bán lẻ & dịch vụ',
    Department: 'CLD INDUSTRIES',
    Representative: 'Nguyễn Thái Hòa',
    Process: 45,
    ParentID: '2',
    HasChildren: true,
    TotalTarget: 100000000000,
    DoneTarget: 25000000000,
    Weight: 20,
    RemainingDay: 30,
    Target: 100000,
    LatestResult: 25000000000,
    Unit: 'currency',
    LastUpdate: '23/06/2025',
    Period: 'Quý 2/2025',
  },
  {
    ID: '211',
    GoalName: 'Lợi nhuận từ bán ô tô nội địa',
    Department: 'CLD AUTO',
    Representative: 'Nguyễn Cao Cường',
    Process: 50,
    ParentID: '21',
    HasChildren: false,
    TotalTarget: 1500000000000,
    DoneTarget: 375000000000,
    Weight: 20,
    RemainingDay: 30,
    Target: 1500,
    LatestResult: 375000000000,
    Unit: 'currency',
    LastUpdate: '23/06/2025',
    Period: 'Tháng 4/2025',
  },
  {
    ID: '212',
    GoalName: 'Lợi nhuận từ bán ô tô xuất khẩu',
    Department: 'CLD AUTO',
    Representative: 'Nguyễn Cao Cường',
    Process: 50,
    ParentID: '21',
    HasChildren: false,
    TotalTarget: 1000000000000,
    DoneTarget: 250000000000,
    Weight: 20,
    RemainingDay: 30,
    Target: 1000,
    LatestResult: 250000000000,
    Unit: 'currency',
    LastUpdate: '22/06/2025',
    Period: 'Tháng 4/2025',
  },
  {
    ID: '213',
    GoalName: 'Lợi nhuận từ dịch vụ hậu mãi',
    Department: 'CLD AUTO',
    Representative: 'Nguyễn Cao Cường',
    Process: 50,
    ParentID: '21',
    HasChildren: false,
    TotalTarget: 500000000000,
    DoneTarget: 125000000000,
    Weight: 20,
    RemainingDay: 30,
    Target: 500,
    LatestResult: 125000000000,
    Unit: 'currency',
    LastUpdate: '22/06/2025',
    Period: 'Tháng 4/2025',
  },
  {
    ID: '41',
    GoalName: 'Tỷ lệ giữ chân khách hàng dịch vụ',
    Department: 'Dịch vụ Hậu mãi CLD AUTO',
    Representative: 'Doãn Hoàng Quân',
    Process: 50,
    ParentID: '4',
    HasChildren: false,
    TotalTarget: 88,
    DoneTarget: 22,
    Weight: 10,
    RemainingDay: 30,
    Target: 88,
    LatestResult: 22,
    Unit: 'percent',
    LastUpdate: '21/06/2025',
    Period: 'Tháng 5/2025',
  },
  {
    ID: '42',
    GoalName: 'Số lượng khách hàng quay lại dịch vụ bảo dưỡng',
    Department: 'Dịch vụ Hậu mãi CLD AUTO',
    Representative: 'Doãn Hoàng Quân',
    Process: 22,
    ParentID: '4',
    HasChildren: false,
    TotalTarget: 20000,
    DoneTarget: 5000,
    Weight: 10,
    RemainingDay: 30,
    Target: 20000,
    LatestResult: 5000,
    Unit: 'number',
    LastUpdate: '21/06/2025',
    Period: 'Tháng 5/2025',
  },
  {
    ID: '61',
    GoalName: 'Tỷ lệ giảm chi phí sản xuất',
    Department: 'CLD AUTO',
    Representative: 'Lê Hồng Hải',
    Process: 33,
    ParentID: '6',
    HasChildren: false,
    TotalTarget: 8,
    DoneTarget: 2,
    Weight: 10,
    RemainingDay: 30,
    Target: 8,
    LatestResult: 2,
    Unit: 'percent',
    LastUpdate: '20/06/2025',
    Period: 'Tháng 6/2025',
  },
  {
    ID: '62',
    GoalName: 'Tỷ lệ giảm chi phí vận hành nhà máy',
    Department: 'CLD AUTO',
    Representative: 'Lê Hồng Hải',
    Process: 50,
    ParentID: '6',
    HasChildren: false,
    TotalTarget: 12,
    DoneTarget: 3,
    Weight: 10,
    RemainingDay: 30,
    Target: 12,
    LatestResult: 3,
    Unit: 'percent',
    LastUpdate: '20/06/2025',
    Period: 'Tháng 6/2025',
  },
  {
    ID: '81',
    GoalName: 'Số giờ đào tạo nội bộ nhân viên',
    Department: 'CLD AUTO',
    Representative: 'Lê Hồng Hải',
    Process: 12,
    ParentID: '8',
    HasChildren: false,
    TotalTarget: 40,
    DoneTarget: 10,
    Weight: 10,
    RemainingDay: 30,
    Target: 40,
    LatestResult: 10,
    Unit: 'giờ',
    LastUpdate: '24/06/2025',
    Period: 'Năm 2025',
  },
  {
    ID: '82',
    GoalName: 'Số giờ đào tạo cho nhân viên cấp cao',
    Department: 'CLD AUTO',
    Representative: 'Lê Hồng Hải',
    Process: 60,
    ParentID: '8',
    HasChildren: false,
    TotalTarget: 60,
    DoneTarget: 15,
    Weight: 10,
    RemainingDay: 30,
    Target: 60,
    LatestResult: 15,
    Unit: 'giờ',
    LastUpdate: '24/06/2025',
    Period: 'Năm 2025',
  },
];

export const overviewColumns = [
  {
    Name: 'GoalName',
    HeaderName: 'Tên mục tiêu',
    HeaderKey: 'Tên mục tiêu',
    DisplayName: 'Tên mục tiêu',
    IsDefault: true,
    IsLocked: true,
    Width: 300,
  },
  {
    Name: 'Weight',
    HeaderName: 'Trọng số',
    HeaderKey: 'Trọng số',
    DisplayName: 'Trọng số',
    IsDefault: true,
    Width: 100,
  },
  {
    Name: 'Representative',
    HeaderName: 'Người thực hiện',
    HeaderKey: 'Người thực hiện',
    DisplayName: 'Người thực hiện',
    IsDefault: true,
    Width: 200,
  },
  {
    Name: 'Department',
    HeaderName: 'Phòng ban',
    HeaderKey: 'Phòng ban',
    DisplayName: 'Phòng ban',
    IsDefault: true,
    Width: 200,
  },
  {
    Name: 'Position',
    HeaderName: 'Vị trí công việc',
    HeaderKey: 'Vị trí công việc',
    DisplayName: 'Vị trí công việc',
    IsDefault: true,
    Width: 150,
  },
  {
    Name: 'LastUpdate',
    HeaderName: 'Cập nhật cuối cùng',
    HeaderKey: 'Cập nhật cuối cùng',
    DisplayName: 'Cập nhật cuối cùng',
    IsDefault: true,
    Width: 150,
  },
  {
    Name: 'Attachment',
    HeaderName: 'Tệp đính kèm',
    HeaderKey: 'Tệp đính kèm',
    DisplayName: 'Tệp đính kèm',
    IsDefault: true,
    Width: 150,
  },
  {
    Name: 'TotalTarget',
    HeaderName: 'Tổng mục tiêu',
    HeaderKey: 'Tổng mục tiêu',
    DisplayName: 'Tổng mục tiêu',
    IsDefault: true,
    Width: 120,
  },
  {
    Name: 'TotalActual',
    HeaderName: 'Tổng thực đạt',
    HeaderKey: 'Tổng thực đạt',
    DisplayName: 'Tổng thực đạt',
    IsDefault: true,
    Width: 120,
  },
  {
    Name: 'Variance',
    HeaderName: 'Chênh lệch',
    HeaderKey: 'Chênh lệch',
    DisplayName: 'Chênh lệch',
    IsDefault: true,
    Width: 150,
  },
  {
    Name: 'CompletionRate',
    HeaderName: 'Mức độ hoàn thành',
    HeaderKey: 'Mức độ hoàn thành',
    DisplayName: 'Mức độ hoàn thành',
    IsDefault: true,
    Width: 150,
  },
  {
    Name: 'ResultStatus',
    HeaderName: 'Kết quả',
    HeaderKey: 'Kết quả',
    DisplayName: 'Kết quả',
    IsDefault: true,
    Width: 150,
  },
  {
    Name: 'Status',
    HeaderName: 'Trạng thái',
    HeaderKey: 'Trạng thái',
    DisplayName: 'Trạng thái',
    IsDefault: true,
    Width: 150,
  },
];

export function overviewFilter(): any[] {
  const listStatus = Array.from(
    new Set(overviewDataSource.filter((item) => item.Status).map((item) => item.Status)),
  ).map((status) => ({
    label: status,
    value: status,
  }));
  const listDepartment = Array.from(
    new Set(overviewDataSource.filter((item) => item.Department).map((item) => item.Department)),
  ).map((department) => ({
    label: department,
    value: department,
  }));

  return [
    {
      numberOrder: 1,
      isFavorite: false,
      isUsing: false,
      field: 'Department',
      type: 'multipleCheckbox',
      value: '',
      title: 'Phòng ban',
      dataSource: {
        staticSource: listDepartment,
      },
    },
    {
      numberOrder: 2,
      isFavorite: false,
      isUsing: false,
      field: 'Status',
      type: 'multipleCheckbox',
      value: '',
      title: 'Trạng thái',
      dataSource: {
        staticSource: listStatus,
      },
    },
  ];
}
