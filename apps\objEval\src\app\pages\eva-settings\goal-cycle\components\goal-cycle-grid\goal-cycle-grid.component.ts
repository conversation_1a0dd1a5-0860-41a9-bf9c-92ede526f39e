import { CommonModule } from '@angular/common';
import {
  Component,
  Inject,
  OnChanges,
  OnInit,
  SimpleChanges,
  TemplateRef,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';
import {
  IVnrModule_Token,
  VNRMODULE_TOKEN,
  VnrButtonFactory,
  VnrButtonNewBuilder,
  VnrButtonNewComponent,
  VnrComboBoxBuilder,
  VnrGridNewBuilder,
  VnrGridNewComponent,
  VnrToolbarNewBuilder,
  VnrToolbarNewComponent,
  VnrTreeViewBuilder,
} from '@hrm-frontend-workspace/vnr-module';
import { gridDefineColumns } from '../../data/column.data';

import {
  FormsModule,
  ReactiveFormsModule,
  UntypedFormBuilder,
  UntypedFormGroup,
} from '@angular/forms';
import { vnrUtilities } from '@hrm-frontend-workspace/common';
import { VnrLettersAvatarComponent } from '@hrm-frontend-workspace/ui';
import { TranslateModule } from '@ngx-translate/core';
import { NzModalService } from 'ng-zorro-antd/modal';
import { gridDefineDataFilterQuick } from '../../data/data-filter-advance';
import { goalCycleDataSource } from '../../data/datasource.data';
import { GoalCycleFacade } from '../../facade/goal-cycle.facade';
import { GoalCycleFormComponent } from '../goal-cycle-form/goal-cycle-form.component';

@Component({
  selector: 'goal-cycle-grid',
  templateUrl: './goal-cycle-grid.component.html',
  styleUrls: ['./goal-cycle-grid.component.scss'],
  imports: [
    FormsModule,
    ReactiveFormsModule,
    CommonModule,
    VnrToolbarNewComponent,
    VnrGridNewComponent,
    TranslateModule,
    VnrLettersAvatarComponent,
    VnrButtonNewComponent,
  ],
})
export class GoalCycleGridComponent implements OnInit, OnChanges {
  @ViewChild('vnrGrid', { static: true }) gridControl: VnrGridNewComponent;
  @ViewChild('templateLastUpdatedBy', { static: true })
  private _templateLastUpdatedBy: TemplateRef<any>;

  private _btnFactory: VnrButtonFactory = VnrButtonFactory.init();

  protected builderButtonAddGoalCycle: VnrButtonNewBuilder;
  protected selectedItem = [];
  protected builderOrg: VnrTreeViewBuilder = new VnrTreeViewBuilder();
  protected builderPosition: VnrComboBoxBuilder = new VnrComboBoxBuilder();
  protected builderButtonApprove: VnrButtonNewBuilder;
  protected formGroup: UntypedFormGroup = this._formBuider.group({
    department: [''],
    position: [''],
  });
  protected listColumnTemplates: {};
  protected builderGrid: VnrGridNewBuilder;
  protected builderToolbar: VnrToolbarNewBuilder;
  protected builderButtonCustom: VnrButtonNewBuilder;
  protected gridName = 'goal-cycle-grid';
  protected dataLocal = goalCycleDataSource;
  protected columns = gridDefineColumns;
  protected dataFormSearch = {};

  constructor(
    private _modalService: NzModalService,
    private _formBuider: UntypedFormBuilder,
    private _goalCycleFacade: GoalCycleFacade,
    private viewContainerRef: ViewContainerRef,
    @Inject(VNRMODULE_TOKEN) private _vnrModule_Token: IVnrModule_Token,
  ) {}
  ngOnInit() {
    this.builderTemplate();
    this.builderGridComponent();
    this.builderToolbarComponent();
    this.builderButton();
  }
  ngOnChanges(changes: SimpleChanges): void {}

  private builderButton() {
    this.builderButtonAddGoalCycle = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'objEval.GoalCycle.btnAddGoalCycle',
      options: {
        style: 'primary',
        icon: {
          fontIcon: 'plus',
        },
      },
    });
  }
  private builderTemplate() {
    this.listColumnTemplates = {
      LastUpdatedBy: this._templateLastUpdatedBy,
    };
  }
  private builderToolbarComponent() {
    this.builderToolbar = new VnrToolbarNewBuilder({
      isShowConfig: true,
      gridName: this.gridName,
      isSupperAdmin: true,
      gridRef: this.gridControl,
      permission: '',
      screenName: 'goal-cycle-grid',
      storeName: '',
      options: {
        configButtonChangeColumn: {
          isShow: true,
          isConfigMenuDisplay: true,
          isChangeColumnNew: true,
          isDisabled: false,
          configButtonSetting: {
            isShow: true,
            isDisabled: true,
          },
        },
        configButtonExport: {
          isShowBtnExcelAll: true,
          isShowBtnWord: true,
          isShowBtnExcelByTemplate: true,
        },
        configButtonDelete: {
          isShow: true,
          isDisabled: false,
        },
        configQuickSearch: {
          textPlaceHolder: 'Nhập từ khóa để tìm kiếm...',
          searchKey: 'Name',
        },
        configFilterAdvanceQuick: {
          isShow: true,
          components: gridDefineDataFilterQuick(),
          keyConfig: 'GoalCycle',
          isShowBtnAdvance: false,
        },
        isSetBackground: false,
        buttonMoreFlexDirection: 'inherit',
        isExpanded: false,
      },
    });
  }

  private builderGridComponent() {
    this.builderGrid = new VnrGridNewBuilder({
      options: {
        configHeightGrid: {
          isAllowCalcRowHeight: true,
        },
        configSelectable: {
          columnKey: 'Id',
        },
        configShowHide: {
          isPageExpand: false,
        },
        configCommandColumn: {
          isEnabledMenuAction: true,
        },
        configGroupable: {
          isEnabled: false,
        },
      },
    });
  }

  protected getSelectedID($event: any) {
    this.selectedItem = $event;
  }
  protected getDataItem($event: any) {}
  protected onOpenDetail($event: any) {}
  protected onGridEdit($event: any) {
    this.onAddGoalCycle($event);
  }
  protected onGridDelete($event: any) {}
  protected onGridViewDetail($event: any) {}
  protected onGridCellClick($event: any) {}
  public setDataFilter(data: any): void {
    this.dataFormSearch = data;
  }
  public reloadGridData(): void {
    this.gridControl.vnrReadGrid();
  }
  public getSelectedIDs() {
    return this.selectedItem || [];
  }
  onAddGoalCycle($event: any) {
    const modalRef = this._modalService.create({
      nzTitle: '',
      nzContent: GoalCycleFormComponent,
      nzViewContainerRef: this.viewContainerRef,
      nzClosable: true,
      nzMaskClosable: false,
      nzMask: true,
      nzWidth: '840px',
      nzBodyStyle: { minheight: `512px`, paddingTop: '10px' },
      nzFooter: null,
      nzData: {
        paramEdit: $event,
      },
    });
    modalRef.afterClose.subscribe((modal) => {
      if (!modal || modal.type === 'Confirm') {
        this.reloadGridData();
      }
    });
  }
}
