import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, NgSwitchDefault } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  OnInit,
  TemplateRef,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';
import { UntypedFormBuilder } from '@angular/forms';
import { vnrUtilities } from '@hrm-frontend-workspace/common';
import { VnrModuleModule } from '@hrm-frontend-workspace/ui';
import {
  VnrFilterAdvanceFullBuilder,
  VnrFilterAdvanceFullComponent,
  VnrGridNewBuilder,
  VnrGridNewComponent,
  VnrToolbarNewBuilder,
  VnrToolbarNewComponent,
} from '@hrm-frontend-workspace/vnr-module';
import { TranslateModule } from '@ngx-translate/core';
import { cloneDeep } from 'lodash';
import { gridDefineColumns } from '../../data/grid-new-define-column.data';
import {
  gridDefineDataFilter<PERSON>dvance,
  gridDefineDataFilterQuickOld,
} from '../../data/grid-new-define-data-filter-advance';
import { gridDefineDataSource } from '../../data/grid-new-define-data-source.data';

@Component({
  selector: 'grid-new-list',
  templateUrl: './grid-new-list.component.html',
  styleUrls: ['./grid-new-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    VnrToolbarNewComponent,
    VnrModuleModule,
    NgSwitch,
    NgSwitchCase,
    NgIf,
    NgSwitchDefault,
    TranslateModule,
  ],
})
export class GridNewListComponent implements OnInit, AfterViewInit {
  @ViewChild('buttonTemplate', { static: true })
  public buttonTemplate: TemplateRef<any>;
  @ViewChild('vnrGrid', { static: true }) gridControl: VnrGridNewComponent;
  @ViewChild('vnrFilterAdvanceFull') filterAdvanceFullControl: VnrFilterAdvanceFullComponent;
  @ViewChild('btnControl', { static: true }) btnControl: TemplateRef<any>;
  @ViewChild('jobTemplate', { static: true }) jobTemplate: TemplateRef<any>;
  @ViewChild('FileAttachment', { static: true }) FileAttachment: TemplateRef<any>;
  @ViewChild('tplCustomButtonAction', { static: true })
  tplCustomButtonAction: TemplateRef<any>;
  @ViewChild('tplCustomTemplateByColumn', { static: true })
  tplCustomTemplateByColumn: TemplateRef<any>;
  @ViewChild('tplHeader', { static: true }) public tplHeader: TemplateRef<any>;

  @ViewChild('templateStatusSync', { static: true })
  private _templateStatusSync: TemplateRef<any>;
  @ViewChild('templateCellPhone', { static: true })
  private _templateCellPhone: TemplateRef<any>;

  protected isSupperAdmin: boolean = true;
  protected screenWidth: number;
  protected screenHeight: number;
  protected compRef: ViewContainerRef;
  protected gridHeight: number;
  protected builderGrid: VnrGridNewBuilder;
  protected builderToolbar: VnrToolbarNewBuilder;
  protected builderFilterAdvanceFull: VnrFilterAdvanceFullBuilder;
  private _permission: string = 'New_PortalV3_Personal_Hre_Dependant';
  private _screenName: string = 'grid-new-list';
  private _storeName: string = 'hrm_hr_sp_get_ProfileTest';
  protected gridName: string = 'PortalNew_GridNewExample';
  protected dataLocal = gridDefineDataSource;
  protected columns = gridDefineColumns;

  protected listColumnTemplates: {};

  //#endregion
  constructor(private fb: UntypedFormBuilder) {}
  ngAfterViewInit() {}
  ngOnInit() {
    this.builderTemplate();
    this.builderGridComponent();
    this.builderFilterAdvanceComponent();
    this.builderToolbarComponent();
  }
  private builderTemplate() {
    this.listColumnTemplates = {
      StatusSyn: this._templateStatusSync,
      Cellphone: this._templateCellPhone,
    };
  }

  private builderFilterAdvanceComponent() {
    this.builderFilterAdvanceFull = new VnrFilterAdvanceFullBuilder({
      screenName: this._screenName,
      gridName: this.gridName,
      storeName: this._storeName,
      components: gridDefineDataFilterAdvance(),
      options: {
        isSupperAdmin: this.isSupperAdmin,
        keyConfig: 'PortalNew_GridNewExample_AdvanceFull',
      },
    });
  }
  private builderToolbarComponent() {
    this.builderToolbar = new VnrToolbarNewBuilder({
      isShowConfig: true,
      gridName: this.gridName,
      isSupperAdmin: this.isSupperAdmin,
      gridRef: this.gridControl,
      permission: this._permission,
      screenName: this._screenName,
      storeName: this._storeName,
      options: {
        configButtonChangeColumn: {
          isShow: true,
          isConfigMenuDisplay: true,
          isChangeColumnNew: true,
          isDisabled: false,
          configButtonSetting: {
            isShow: true,
            isDisabled: true,
          },
        },
        configButtonExport: {
          isShowBtnExcelAll: true,
          isShowBtnWord: true,
          isShowBtnExcelByTemplate: true,
        },
        configButtonDelete: {
          isShow: true,
          isDisabled: false,
        },
        configQuickSearch: {
          textPlaceHolder: 'Tìm kiếm theo mã, tên...',
          searchKey: 'ProfileName',
          isShowBtn: true,
        },
        configFilterAdvance: {
          components: gridDefineDataFilterQuickOld(),
          keyConfig: 'PortalNew_GridNewExample_Advance',
          isShow: true,
        },
        isSetBackground: true,
        buttonMoreFlexDirection: 'inherit',
        isExpanded: false,
      },
    });
  }
  private builderGridComponent() {
    this.builderGrid = new VnrGridNewBuilder({
      options: {
        configSelectable: {
          columnKey: 'Id',
        },
        configHeightGrid: {
          isAllowCalcRowHeight: true,
        },
        configShowHide: {
          isPageExpand: false,
        },
        configCommandColumn: {
          isEnabledMenuAction: true,
        },
        configGroupable: {
          isEnabled: true,
        },
      },
    });
  }
  getSelectedID($event) {
    console.log($event, 'getSelectedID');
  }
  getDataItem($event) {
    console.log($event, 'getDataItem');
  }
  onOpenDetail($event) {
    console.log($event, 'onOpenDetail');
  }
  onVnRViewModeGrid($event) {
    console.log($event, 'onVnRViewModeGrid');
  }
  onGridEdit($event) {
    console.log($event, 'onGridEdit');
  }
  onGridDelete($event) {
    console.log($event, 'onGridDelete');
  }
  onGridViewDetail($event) {
    console.log($event, 'onGridViewDetail');
  }
  onGridCellClick($event) {
    console.log($event, 'onGridCellClick');
  }
  selected: string = 'rightTop';
  toggleChangeColumn(isNewVersion: boolean) {
    this.gridControl?.setOpenChangeColumn(true);
    this.gridControl?.setChangeColumnVersion(isNewVersion);
  }
  reload() {
    this.isSupperAdmin = !this.isSupperAdmin;
    setTimeout(() => {
      this.gridControl?.vnrReadGrid();
    });
  }
  reload1() {
    this.isSupperAdmin = !this.isSupperAdmin;
    setTimeout(() => {
      this.gridControl?.vnrReloadGrid();
    });
  }
  protected onChangesFilter(dataSearch: any) {
    //let typeBusiness = this.builder.options?.configFilterAdvance?.typeOfBusiness;
    //this.valuePath[typeBusiness] = dataSearch;
    let searchValue: any = {};
    if (dataSearch) {
      let searchValueClone = cloneDeep(dataSearch);
      searchValue = vnrUtilities.convertArraysToStrings(searchValueClone);
    }
    this.gridControl.setDataFilter(searchValue);
    this.gridControl.vnrReadGrid();
  }
  protected onOpenComponentFilterFull(event: any) {
    this.filterAdvanceFullControl.onOpenFullFilter();
  }
  protected maskPhone(phone: string): string {
    if (!phone) return '';
    return phone.length > 3 ? phone.slice(0, -3) + '***' : '***';
  }
}
