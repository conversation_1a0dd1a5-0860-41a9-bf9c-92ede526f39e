import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { VnrTagComponent, VnrLettersAvatarComponent } from '@hrm-frontend-workspace/ui';

@Component({
  selector: 'app-card',
  imports: [CommonModule, VnrTagComponent, VnrLettersAvatarComponent],
  templateUrl: './card.component.html',
  styleUrl: './card.component.scss',
  standalone: true,
})
export class CardComponent {
  isCollapsed = true;
  @Input() data: any;
}
