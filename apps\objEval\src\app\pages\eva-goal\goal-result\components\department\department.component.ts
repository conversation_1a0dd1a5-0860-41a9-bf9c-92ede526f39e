import { AfterViewInit, Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  VnrToolbarNewBuilder,
  VnrToolbarNewComponent,
  VnrTreelistNewBuilder,
  VnrTreelistNewComponent,
  VnrTreelistNewModule,
} from '@hrm-frontend-workspace/vnr-module';
import {
  VnrLettersAvatarComponent,
  VnrTagComponent,
  VnrButtonModule,
} from '@hrm-frontend-workspace/ui';
import {
  departmentColumn,
  departmentDataSource,
  departmentFilter,
} from '../../data/department.data';
import { FormComponent } from './components/form/form.component';
import { NzDrawerService, NzDrawerRef } from 'ng-zorro-antd/drawer';

@Component({
  selector: 'app-department',
  imports: [
    CommonModule,
    VnrToolbarNewComponent,
    VnrTreelistNewComponent,
    VnrTreelistNewModule,
    VnrLettersAvatarComponent,
    VnrTagComponent,
    VnrButtonModule,
  ],
  templateUrl: './department.component.html',
  styleUrl: './department.component.scss',
})
export class DepartmentComponent implements OnInit, AfterViewInit {
  @ViewChild('vnrTreeList', { static: false }) vnrTreeList: VnrTreelistNewComponent;

  builderToolbar: VnrToolbarNewBuilder;
  builderTreeList: VnrTreelistNewBuilder;

  private drawerRef: NzDrawerRef | null = null;
  private _screenName = 'GoalResult';
  protected gridName = 'Eva_GoalResult_Gird';
  protected isSupperAdmin = true;
  private _storeName = 'eva_sp_get_GoalResult';
  protected dataLocal = departmentDataSource;
  protected columns = departmentColumn;

  constructor(private drawerService: NzDrawerService) {}

  ngOnInit(): void {
    this.builderTreeListComponent();
    this.builderToolbarComponent();
  }

  ngAfterViewInit(): void {
    this.updateToolbarWithGridReference();
  }

  private updateToolbarWithGridReference(): void {
    if (this.builderToolbar && this.vnrTreeList) {
      this.builderToolbar.gridRef = this.vnrTreeList;
    }
  }

  private builderToolbarComponent() {
    this.builderToolbar = new VnrToolbarNewBuilder({
      gridName: this.gridName,
      isSupperAdmin: this.isSupperAdmin,
      storeName: this._storeName,
      screenName: this._screenName,
      isShowConfig: true,
      options: {
        configButtonChangeColumn: { isShow: true },
        configButtonExport: { isShowBtnExcelAll: true },
        configQuickSearch: {
          isShow: true,
        },
        configFilterAdvanceQuick: {
          isShow: true,
          components: departmentFilter(),
          keyConfig: 'GoalResult_FilterAdvanceSeting',
          isShowBtnAdvance: false,
        },
      },
    });
  }

  private builderTreeListComponent() {
    this.builderTreeList = new VnrTreelistNewBuilder({
      options: {
        configHeightGrid: {
          isAllowCalcRowHeight: true,
        },
        displayField: 'Department',
        configSelectable: {
          columnKey: 'ID',
          groupKey: 'ParentID',
        },
        configCommandColumn: {
          isEnabledMenuAction: false,
        },
        configShowHide: {
          isShowViewDetail: false,
          isPageExpand: false,
          isShowDelete: false,
          isShowEdit: false,
        },
      },
    });
  }

  handleDoubleClick(event: any) {
    if (this.drawerRef) {
      this.drawerRef.close();
    }
    this.drawerRef = this.drawerService.create<
      FormComponent,
      { dataItem: any },
      string | TemplateRef<any>
    >({
      nzTitle: 'Kết quả thực hiện mục tiêu',
      nzContent: FormComponent,
      nzClosable: true,
      nzMaskClosable: false,
      nzMask: true,
      nzWidth: '1000px',
      nzContentParams: {
        dataItem: event.record,
      },
      nzFooter: null,
      nzWrapClassName: 'goal-result-department-form-drawer',
    });
    this.drawerRef.afterClose.subscribe(() => {
      this.drawerRef = null;
    });
  }
}
