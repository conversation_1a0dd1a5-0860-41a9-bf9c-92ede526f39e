<div class="eva-proposal-grid">
  <div class="eva-proposal-grid__container">
    <div class="eva-proposal-grid__header">
      <vnr-toolbar-new [builder]="builderToolbar">
        <vnr-button-new
          rightToolbar
          [builder]="builderButtonProposal"
          (vnrClick)="onProposal($event)"
        ></vnr-button-new>
        <vnr-button-new
          rightToolbar
          [builder]="builderButtonDropdownMore"
          nz-dropdown
          [nzDropdownMenu]="btnDropdownMore"
        ></vnr-button-new>
        <nz-dropdown-menu #btnDropdownMore="nzDropdownMenu">
          <ul nz-menu>
            <li nz-menu-item>Xuất dữ liệu</li>
            <li nz-menu-item>Biểu đồ</li>
            <li nz-menu-item>Sắp xếp</li>
            <li nz-menu-item>Nhóm cột</li>
            <li nz-menu-item>Thiết lập hiển thị</li>
          </ul>
        </nz-dropdown-menu>
      </vnr-toolbar-new>
    </div>
    <vnr-grid-new
      #vnrGrid
      [builder]="builderGrid"
      [gridName]="gridName"
      [dataLocal]="dataLocal"
      [columns]="columns"
      [defaultColumnTemplate]="tplCustomTemplateByColumn"
      (getSelectedID)="getSelectedID($event)"
      (getDataItem)="getDataItem($event)"
      (vnrDoubleClick)="onOpenDetail($event)"
      (vnrEdit)="onGridEdit($event)"
      (vnrDelete)="onGridDelete($event)"
      (vnrViewDetails)="onGridViewDetail($event)"
      (vnrCellClick)="onGridCellClick($event)"
    >
    </vnr-grid-new>
    <ng-template #tplCustomTemplateByColumn let-dataItem let-column="columnItem" let-field="field">
      <ng-container [ngSwitch]="column['Name']">
        <span class="--has-custom-template" *ngSwitchCase="'ProfileName'">
          <span>
            <div class="eva-proposal-grid-profile">
              <app-vnr-letters-avatar
                class="eva-proposal-grid-profile__image"
                [avatarName]="dataItem['ProfileName']"
                [circular]="true"
                [width]="32"
                [src]="dataItem['Avatar']"
              ></app-vnr-letters-avatar>
              <div class="eva-proposal-grid-profile__description">
                <div class="eva-proposal-grid-profile__name">
                  {{ dataItem['ProfileName'] }}
                </div>
              </div>
            </div>
          </span>
        </span>
        <span class="--has-custom-template" *ngSwitchCase="'Grade'">
          <div class="eva-proposal-grid-grade">
            <div class="eva-proposal-grid-grade__name">
              <vnr-tag
                *ngIf="dataItem['Grade']; else templateEmpty"
                [vnrColor]="getColorGrade(dataItem['Grade'])"
                [vnrTitle]="dataItem['Grade']"
                [isBordered]="false"
                [vnrNoTranslate]="true"
              ></vnr-tag>
            </div>
            <div class="eva-proposal-grid-grade__text">
              <font [color]="getColorGradeText(dataItem['Grade'])">{{
                dataItem['GradeText']
              }}</font>
            </div>
          </div>
        </span>
        <span class="--has-custom-template" *ngSwitchCase="'Status'">
          <vnr-tag
            *ngIf="dataItem['Status']; else templateEmpty"
            [vnrColor]="getColorStatus(dataItem['Status'])"
            [vnrTitle]="dataItem['StatusView'] || dataItem['Status']"
          ></vnr-tag>
        </span>
        <span class="--has-custom-template" *ngSwitchDefault>
          {{ dataItem[column['Name']] || '-' }}
        </span>
      </ng-container>
    </ng-template>
    <ng-template #templateEmpty>-</ng-template>
  </div>
</div>
