<div
  class="vnr-pager-custom-kendo"
  [ngClass]="{
  'vnr-grid__hide-vertical-scroll': builder.options.configHeightGrid.isAllowCalcRowHeight,
}"
>
  <vnr-treelist-new-change-column
    *ngIf="isOpenChangeColumn && isChangeColumnNew"
    [builder]="builderTreelistChangeColumn"
    (vnrClosePopup)="vnrcloseAndRefreshGird($event)"
    (vnrReloadGridChangeColumn)="vnrReloadGridChangeColumn($event)"
  ></vnr-treelist-new-change-column>
  <kendo-treelist
    kendoTreeListExpandable
    [parentIdField]="groupKey"
    (cellClick)="cellClickHandler($event)"
    (cellClose)="cellCloseHandler($event)"
    [idField]="columnKey"
    kendoTreeListSelectable
    [kendoTreeListFlatBinding]="dataSource$ | async"
    [fetchChildren]="fetchChildren"
    [hasChildren]="hasChildren"
    [initiallyExpanded]="builder.options.isInitiallyExpanded"
    [height]="builder.options.configHeightGrid?.gridHeight"
    [loading]="isLoading"
    [aggregate]="builder.options.aggregates"
    [navigable]="true"
    [reorderable]="true"
    [resizable]="true"
    [pageable]="false"
    [pageSize]="5"
    [rowClass]="rowClass"
    [(expandedKeys)]="builder.options.expandedKeyIds"
    [(selectedItems)]="selectedItems"
    (dblclick)="doubleClicked($event)"
    (expandedKeysChange)="expandedIdsChange($event)"
    (expand)="vnrExpandHandler ? vnrExpandHandler($event) : onExpand($event)"
    (collapse)="vnrCollapseHandler ? vnrCollapseHandler($event) : onCollapse($event)"
  >
    <kendo-treelist-column
      [width]="50"
      [minResizableWidth]="50"
      class="text-center"
      [columnMenu]="false"
      [locked]="hasLockedColumn || builder.options.isLockedColumnCheckBox"
      *ngIf="
        gridColumns && gridColumns.length > 0 && builder.options.configShowHide.isShowColumnCheck
      "
    >
      <ng-template kendoTreeListHeaderTemplate let-column let-columnIndex="rowIndex">
        <div class="text-center">
          <input
            type="checkbox"
            id="selectAllCheckboxId"
            kendoCheckBox
            class="k-checkbox"
            [checked]="allChecked"
            [indeterminate]="someChecked && !allChecked"
            (change)="onSelectAllChange($event)"
          />
        </div>
      </ng-template>
      <ng-template
        kendoTreeListCellTemplate
        let-dataItem
        let-rowIndex="rowIndex"
        let-field="field"
        let-value="value"
        *ngIf="builder.options.configShowHide.isShowColumnCheck"
      >
        <input
          type="checkbox"
          kendoCheckBox
          class="k-checkbox"
          [checked]="dataItem.checked"
          [(ngModel)]="dataItem.checked"
          class="customCheckBtn"
          id="id_{{ field }}_{{ value }}_{{ dataItem[builder.options.configSelectable.columnKey] }}"
          [name]="dataItem[builder.options.configSelectable.columnKey]"
          [ngModelOptions]="{ standalone: true }"
          (change)="checkDataItem(dataItem)"
        />
      </ng-template>
    </kendo-treelist-column>
    <!-- #region Actions Column vnrHiddenCommandColumn ? 0 :  -->
    <kendo-treelist-command-column
      *ngIf="
        gridColumns &&
        gridColumns.length > 0 &&
        (builder.options.configShowHide.isShowEdit ||
          builder.options.configShowHide.isShowDelete ||
          rowActionsTemplate) &&
        !builder.options.configShowHide.isShowButtonMenu &&
        !builder.options.configCommandColumn.isEnabledMenuAction
      "
      [width]="builder.options.configCommandColumn.width"
      [minResizableWidth]="builder.options.configCommandColumn.width || 100"
    >
      <ng-template
        kendoTreeListCellTemplate
        let-dataItem
        let-isNew="isNew"
        let-cellContext="cellContext"
      >
        <div
          class="vnr-grid-action-column__group-btn d-flex justify-content-end align-items-center"
          [ngClass]="{
            'vnr-grid-action-column__group-btn-hidden': !isHovered,
            'vnr-grid-action-column__group-btn-show': isHovered
          }"
        >
          <ng-template [ngTemplateOutlet]="tplCustomBtn"></ng-template>
          <div class="mr-1" *ngIf="builder.options.configShowHide.isShowEdit">
            <button
              nz-button
              nzType="primary"
              (click)="onEdit(dataItem)"
              class="btn-kendoGrid-customize btn-kendoGrid-customize__edit ant-btn ant-btn-primary"
            >
              <span nz-icon nzType="edit" nzTheme="outline"></span>
            </button>
          </div>
          <div class="mr-1" *ngIf="builder.options.configShowHide.isShowViewDetail">
            <button
              nz-button
              nzType="primary"
              (click)="onViewDetails(dataItem)"
              class="btn-kendoGrid-customize ant-btn btn-kendoGrid-customize__ViewDetails ant-btn ant-btn-primary"
            >
              <span nz-icon nzType="eye" nzTheme="outline"></span>
            </button>
          </div>
          <div *ngIf="builder.options.configShowHide.isShowDelete">
            <button
              nz-button
              nzType="primary"
              (click)="onDelete(dataItem)"
              class="btn-kendoGrid-customize btn-kendoGrid-customize__delete ant-btn ant-btn-primary"
            >
              <i class="far fa-trash-alt"></i>
            </button>
          </div>
          <ng-template #tplCustomBtn>
            <ng-container
              *ngIf="rowActionsTemplate"
              [ngTemplateOutletContext]="{ $implicit: dataItem }"
              [ngTemplateOutlet]="rowActionsTemplate"
            ></ng-container>
          </ng-template>
        </div>
      </ng-template>
    </kendo-treelist-command-column>
    <!-- #endregion Actions Column -->
    <ng-container *ngFor="let col of gridColumns; let index = index; trackBy: trackByFn">
      <ng-container [ngSwitch]="!!(col?.MultiColumn && col.MultiColumn?.length > 0)">
        <ng-container *ngSwitchCase="true">
          <kendo-treelist-column-group
            [title]="col.HasChangeDisplayName ? col.DisplayName : (col.HeaderKey | translate)"
            [width]="col.Width"
            [hidden]="col.Hidden"
            [locked]="col.Locked"
            [style]="col.Style"
            [headerStyle]="col.HeaderStyle"
            [headerClass]="['initOrderIndex_' + index, getColumnClassNames(col)]"
          >
            <!-- BEGIN Header Template -->
            <ng-template kendoTreeListHeaderTemplate let-column let-columnIndex="columnIndex">
              <ng-container *ngIf="columnHeaderTemplate; else tplHeaderNoTemplate">
                <ng-container
                  *ngTemplateOutlet="
                    columnHeaderTemplate;
                    context: {
                      $implicit: column,
                      columnIndex: columnIndex,
                      columnInfo: col
                    }
                  "
                ></ng-container>
              </ng-container>
              <ng-template #tplHeaderNoTemplate>
                <span
                  class="d-block"
                  (click)="col.AllowClickColumn && onGroupColumnClick($event, col)"
                >
                  {{ col.HasChangeDisplayName ? col.DisplayName : (col.HeaderKey | translate) }}
                </span>
              </ng-template>
            </ng-template>
            <!-- END Header Template -->
            <!-- BEGIN SubColumn - Framework error when using template -->
            <kendo-treelist-column
              *ngFor="let subColumn of col.MultiColumn; let i = index"
              [field]="subColumn.Name"
              [title]="
                subColumn.HasChangeDisplayName
                  ? subColumn.DisplayName
                  : (subColumn.HeaderKey | translate)
              "
              [width]="subColumn.Width"
              [hidden]="subColumn.Hidden"
              [editable]="!subColumn.Disable"
              [locked]="subColumn.Locked"
              [style]="subColumn.Style"
              [headerStyle]="subColumn.HeaderStyle"
              [headerClass]="getColumnClassNames(subColumn)"
            >
              <ng-container
                *ngIf="
                  subColumn.MultiColumn && subColumn.MultiColumn.length > 0;
                  else tplSubColumnInGroup
                "
              >
                <ng-container
                  *ngTemplateOutlet="tplColumn; context: { col: subColumn, index: index }"
                ></ng-container>
              </ng-container>

              <ng-template #tplSubColumnInGroup>
                <!-- BEGIN Header Template -->
                <ng-template kendoTreeListHeaderTemplate let-column let-columnIndex="columnIndex">
                  <ng-container *ngIf="columnHeaderTemplate; else tplHeaderNoTemplate">
                    <ng-container
                      *ngTemplateOutlet="
                        columnHeaderTemplate;
                        context: {
                          $implicit: column,
                          columnIndex: columnIndex,
                          columnInfo: subColumn
                        }
                      "
                    ></ng-container>
                  </ng-container>
                  <ng-template #tplHeaderNoTemplate>
                    <span
                      class="d-block"
                      (click)="subColumn.AllowClickColumn && onGroupColumnClick($event, subColumn)"
                    >
                      {{
                        subColumn.HasChangeDisplayName
                          ? subColumn.DisplayName
                          : (subColumn.HeaderKey | translate)
                      }}
                    </span>
                  </ng-template>
                </ng-template>
                <!-- END Header Template -->

                <!-- BEGIN Display Template -->
                <ng-template
                  kendoTreeListCellTemplate
                  let-dataItem
                  let-rowIndex="rowIndex"
                  let-column="column"
                  let-level="level"
                  let-isExpanded="isExpanded"
                  let-hasChildren="hasChildren"
                  let-cellContext="cellContext"
                  let-isNew="isNew"
                  let-field="field"
                >
                  <!-- BEGIN Load template của cell -->
                  <ng-container
                    *ngIf="subColumn.template || defaultColumnTemplate; else tplCellWithoutTemplate"
                  >
                    <ng-container
                      *ngTemplateOutlet="
                      subColumn.template || defaultColumnTemplate;
                      context: {
                        $implicit: dataItem,
                        column: column,
                        field: field,
                        columnItem: subColumn,
                        level: level,
                        hasChildren: hasChildren,
                        rowIndex: rowIndex,
                        cellContext: cellContext,
                        isNew: isNew,
                      }
                    "
                    ></ng-container>
                  </ng-container>
                  <ng-template #tplCellWithoutTemplate>
                    <ng-container
                      *ngTemplateOutlet="
                        tplFormat;
                        context: {
                          dataItem: dataItem,
                          column: column,
                          rowIndex: rowIndex,
                          col: subColumn,
                          field: field
                        }
                      "
                    ></ng-container>
                  </ng-template>
                  <!-- END Load template của cell -->
                </ng-template>
                <!-- END Display Template -->
              </ng-template>
              <ng-container *ngIf="columnFooterTemplate">
                <ng-template kendoTreeListFooterTemplate let-aggregates="aggregates">
                  <ng-container
                    *ngTemplateOutlet="
                      columnFooterTemplate;
                      context: { aggregates: aggregates, column: col, index: index }
                    "
                  ></ng-container>
                </ng-template>
              </ng-container>
            </kendo-treelist-column>
            <!-- END SubColumn - Framework error when using template -->
          </kendo-treelist-column-group>
        </ng-container>
        <ng-container *ngSwitchDefault>
          <kendo-treelist-column
            [expandable]="col.Name === builder.options?.displayField"
            [field]="col.Name"
            [title]="col.HasChangeDisplayName ? col.DisplayName : (col.HeaderKey | translate)"
            [width]="col.Width"
            [hidden]="col.Hidden"
            [editable]="!col.Disable"
            [locked]="col.Locked"
            [style]="col.Style"
            [headerStyle]="col.HeaderStyle"
            [headerClass]="getColumnClassNames(col)"
          >
            <!-- BEGIN Header Template -->
            <ng-template kendoTreeListHeaderTemplate let-column let-columnIndex="columnIndex">
              <ng-container *ngIf="columnHeaderTemplate; else tplHeaderNoTemplate">
                <ng-container
                  *ngTemplateOutlet="
                    columnHeaderTemplate;
                    context: {
                      $implicit: column,
                      columnIndex: columnIndex,
                      columnInfo: col
                    }
                  "
                ></ng-container>
              </ng-container>
              <ng-template #tplHeaderNoTemplate>
                <span
                  class="d-block"
                  (click)="col.AllowClickColumn && onGroupColumnClick($event, col)"
                >
                  {{ col.HasChangeDisplayName ? col.DisplayName : (col.HeaderKey | translate) }}
                </span>
              </ng-template>
            </ng-template>
            <!-- END Header Template -->

            <!-- BEGIN Display Template -->
            <ng-template
              kendoTreeListCellTemplate
              let-dataItem
              let-rowIndex="rowIndex"
              let-column="column"
              let-level="level"
              let-isExpanded="isExpanded"
              let-hasChildren="hasChildren"
              let-cellContext="cellContext"
              let-isNew="isNew"
              let-field="field"
            >
              <!-- BEGIN Load template của cell -->
              <ng-container
                *ngIf="col.template || defaultColumnTemplate; else tplCellWithoutTemplate"
              >
                <ng-container
                  *ngTemplateOutlet="
                col.template || defaultColumnTemplate;
                context: {
                  $implicit: dataItem,
                  column: column,
                  field: field,
                  columnItem: col,
                  level: level,
                  hasChildren: hasChildren,
                  rowIndex: rowIndex,
                  cellContext: cellContext,
                  isNew: isNew,
                }
              "
                ></ng-container>
              </ng-container>
              <ng-template #tplCellWithoutTemplate>
                <ng-container
                  *ngTemplateOutlet="
                    tplFormat;
                    context: {
                      dataItem: dataItem,
                      column: column,
                      rowIndex: rowIndex,
                      col: col,
                      field: field
                    }
                  "
                ></ng-container>
              </ng-template>
              <!-- END Load template của cell -->
            </ng-template>
            <!-- END Display Template -->

            <!-- BEGIN Footer -->
            <ng-container *ngIf="columnFooterTemplate">
              <ng-template kendoTreeListFooterTemplate let-aggregates="aggregates">
                <ng-container
                  *ngTemplateOutlet="
                    columnFooterTemplate;
                    context: { aggregates: aggregates, column: col, index: index }
                  "
                ></ng-container>
              </ng-template>
            </ng-container>
            <!-- END Footer -->
          </kendo-treelist-column>
        </ng-container>
      </ng-container>
    </ng-container>

    <!-- #region Cột group -->
    <ng-template #tplColumnGroup let-col="col" let-index="index"> </ng-template>
    <!-- #endregion Cột group -->

    <!-- #region Cột thường -->
    <ng-template #tplColumn let-col="col" let-index="index">
      <kendo-treelist-column
        [expandable]="col.Name === builder.options?.displayField"
        [field]="col.Name"
        [title]="col.HasChangeDisplayName ? col.DisplayName : (col.HeaderKey | translate)"
        [width]="col.Width"
        [hidden]="col.Hidden"
        [editable]="!col.Disable"
        [locked]="col.Locked"
        [class]="getColumnClassNames(col)"
        [style]="col.Style"
        [headerStyle]="col.HeaderStyle"
        [headerClass]="getColumnClassNames(col)"
      >
        <!-- BEGIN Header Template -->
        <ng-template kendoTreeListHeaderTemplate let-column let-columnIndex="columnIndex">
          <ng-container *ngIf="columnHeaderTemplate; else tplHeaderNoTemplate">
            <ng-container
              *ngTemplateOutlet="
                columnHeaderTemplate;
                context: {
                  $implicit: column,
                  columnIndex: columnIndex,
                  columnInfo: col
                }
              "
            ></ng-container>
          </ng-container>
          <ng-template #tplHeaderNoTemplate>
            <span class="d-block" (click)="col.AllowClickColumn && onGroupColumnClick($event, col)">
              {{ col.HasChangeDisplayName ? col.DisplayName : (col.HeaderKey | translate) }}
            </span>
          </ng-template>
        </ng-template>
        <!-- END Header Template -->

        <!-- BEGIN Display Template -->
        <ng-template
          kendoTreeListCellTemplate
          let-dataItem
          let-rowIndex="rowIndex"
          let-column="column"
          let-level="level"
          let-isExpanded="isExpanded"
          let-hasChildren="hasChildren"
          let-cellContext="cellContext"
          let-isNew="isNew"
          let-field="field"
        >
          <!-- BEGIN Load template của cell -->
          <ng-container *ngIf="col.template || defaultColumnTemplate; else tplCellWithoutTemplate">
            <ng-container
              *ngTemplateOutlet="
                col.template || defaultColumnTemplate;
                context: {
                  $implicit: dataItem,
                  column: column,
                  field: field,
                  columnItem: col,
                  level: level,
                  hasChildren: hasChildren,
                  rowIndex: rowIndex,
                  cellContext: cellContext,
                  isNew: isNew,
                }
              "
            ></ng-container>
          </ng-container>
          <ng-template #tplCellWithoutTemplate>
            <ng-container
              *ngTemplateOutlet="
                tplFormat;
                context: {
                  dataItem: dataItem,
                  column: column,
                  rowIndex: rowIndex,
                  col: col,
                  field: field
                }
              "
            ></ng-container>
          </ng-template>
          <!-- END Load template của cell -->
        </ng-template>
        <!-- END Display Template -->

        <!-- BEGIN Footer -->
        <ng-container *ngIf="columnFooterTemplate">
          <ng-template kendoTreeListFooterTemplate let-aggregates="aggregates">
            <ng-container
              *ngTemplateOutlet="
                columnFooterTemplate;
                context: { aggregates: aggregates, column: col, index: index }
              "
            ></ng-container>
          </ng-template>
        </ng-container>
        <!-- END Footer -->
      </kendo-treelist-column>
    </ng-template>
    <!-- #endregion Cột thường -->

    <!-- #region Actions Column vnrHiddenCommandColumn ? 0 :  -->
    <kendo-treelist-command-column
      *ngIf="
        gridColumns &&
        gridColumns.length > 0 &&
        builder.options.configCommandColumn.isEnabledMenuAction &&
        (builder.options.configShowHide.isShowEdit ||
          builder.options.configShowHide.isShowDelete ||
          rowActionsTemplate)
      "
      [width]="builder.options.configCommandColumn.width"
      [minResizableWidth]="builder.options.configCommandColumn.width || 100"
    >
      <ng-template
        kendoTreeListCellTemplate
        let-dataItem
        let-isNew="isNew"
        let-cellContext="cellContext"
      >
        <div
          class="vnr-grid-action-column__group-btn d-flex justify-content-end align-items-center"
          [ngClass]="{
            'vnr-grid-action-column__group-btn-hidden': !isHovered,
            'vnr-grid-action-column__group-btn-show': isHovered
          }"
        >
          <ng-template [ngTemplateOutlet]="tplCustomBtn"></ng-template>
          <div class="mr-1" *ngIf="builder.options.configShowHide.isShowEdit">
            <button
              nz-button
              nzType="primary"
              (click)="onEdit(dataItem)"
              class="btn-kendoGrid-customize btn-kendoGrid-customize__edit ant-btn ant-btn-primary"
            >
              <span nz-icon nzType="edit" nzTheme="outline"></span>
            </button>
          </div>
          <div class="mr-1" *ngIf="builder.options.configShowHide.isShowViewDetail">
            <button
              nz-button
              nzType="primary"
              (click)="onViewDetails(dataItem)"
              class="btn-kendoGrid-customize ant-btn btn-kendoGrid-customize__ViewDetails ant-btn ant-btn-primary"
            >
              <span nz-icon nzType="eye" nzTheme="outline"></span>
            </button>
          </div>
          <div *ngIf="builder.options.configShowHide.isShowDelete">
            <button
              nz-button
              nzType="primary"
              (click)="onDelete(dataItem)"
              class="btn-kendoGrid-customize btn-kendoGrid-customize__delete ant-btn ant-btn-primary"
            >
              <i class="far fa-trash-alt"></i>
            </button>
          </div>
          <ng-template #tplCustomBtn>
            <ng-container
              *ngIf="rowActionsTemplate"
              [ngTemplateOutletContext]="{ $implicit: dataItem }"
              [ngTemplateOutlet]="rowActionsTemplate"
            ></ng-container>
          </ng-template>
        </div>
      </ng-template>
    </kendo-treelist-command-column>
    <!-- #endregion Actions Column -->

    <!-- #region Template No Data -->
    <ng-template kendoTreeListNoRecordsTemplate>
      {{ 'common.grid.noData' | translate }}
    </ng-template>
    <!-- #endregion Template No Data -->

    <!-- #region Custom Messages -->

    <!-- #endregion Custom Messages -->
  </kendo-treelist>
  <!-- #region Pager -->

  <ng-container *ngIf="builder.options.configShowHide.isPageExpand">
    <div
      class="treelist-loadmore d-flex justify-content-center align-items-center"
      style="text-align: center; width: 100%; min-height: 41px"
      *ngIf="
        gridDataSource?.data?.length > 0 &&
        funcCalcCountLoadMore(gridDataSource?.data) < gridDataSource?.total
      "
      [ngClass]="{
        'vnrPageExpand-none': funcCalcCountLoadMore(gridDataSource?.data) === gridDataSource?.total
      }"
    >
      <button
        class="kendo-loadMore-customs"
        nz-button
        nzType="default"
        (click)="loadMore()"
        nzShape="round"
        [disabled]="isLoading"
      >
        <span class="d-flex justify-content-center align-items-center" nz-icon>
          <span class="mr-1 d-flex" nz-icon *ngIf="!isLoading">
            <i class="fas fa-angle-down"></i>
          </span>
          <span class="mr-1" nz-icon [nzType]="'loading'" *ngIf="isLoading"></span>
          {{ 'loadMore' | translate }}
          <span *ngIf="gridDataSource?.data?.length > 0">
            ({{ funcCalcCountLoadMore(gridDataSource.data) }}/{{ gridDataSource.total }})
          </span>
        </span>
      </button>
    </div></ng-container
  >
  <!-- #endregion -->
  <kendo-pager
    *ngIf="!builder.options.configShowHide.isPageExpand"
    [skip]="builder.options.queryOption.skip"
    [pageSize]="builder.options.queryOption.take"
    [type]="builder.options.configPageable.type"
    [responsive]="builder.options.configPageable.isResponsive"
    [total]="gridDataSource?.total"
    (pageChange)="onPageChange($event)"
    (pageSizeChange)="onPageSizeChange($event)"
  >
    <kendo-pager-messages
      previousPage="{{ 'common.modal.PreviousPage' | translate }}"
      nextPage="{{ 'common.modal.NextPage' | translate }}"
      firstPage="{{ 'common.modal.FirstPage' | translate }}"
      lastPage="{{ 'common.modal.LastPage' | translate }}"
      of="{{ 'common.grid.pagerOf' | translate }}"
      itemsPerPage="{{ 'common.grid.pagerItemsPerPage' | translate }}"
      page="{{ 'common.grid.pagerPage' | translate }}"
    >
    </kendo-pager-messages>

    <ng-template kendoPagerTemplate let-totalPages="totalPages" let-currentPage="currentPage">
      <div class="k-pager-numbers-wrap">
        <kendo-pager-prev-buttons></kendo-pager-prev-buttons>
        <kendo-pager-numeric-buttons
          [buttonCount]="builder.options.configPageable.buttonCount"
        ></kendo-pager-numeric-buttons>
        <kendo-pager-next-buttons></kendo-pager-next-buttons>
      </div>
      <kendo-pager-page-sizes
        [pageSizes]="builder.options.configPageable.pageSizes"
      ></kendo-pager-page-sizes>
      <div class="pager-total-custom">
        <p>
          <span>{{ gridDataSource?.total }}</span>
          {{ 'common.modal.reasonContentRowSelected' | translate }}
        </p>
      </div>
    </ng-template>
  </kendo-pager>
  <ng-template
    #tplFormat
    let-dataItem="dataItem"
    let-column="column"
    let-col="col"
    let-rowIndex="rowIndex"
    let-field="field"
  >
    <span [class.bold-text]="dataItem['hasChildren']">
      <ng-template *ngIf="col.Format; then gridFormat; else elseSchema"></ng-template>
      <ng-template #gridFormat>
        <span [ngSwitch]="col.Format.split('|')[0].toLowerCase()">
          <span *ngSwitchCase="'datetime'">
            {{ dataItem[column.field] | date : col.Format.split('|')[1] }}
          </span>
          <span *ngSwitchCase="'number'">
            {{ dataItem[column.field] | kendoNumber : col.Format.split('|')[1] }}
          </span>
          <span *ngSwitchCase="'bool'">
            <label
              nz-checkbox
              [(ngModel)]="dataItem[column.field]"
              nzDisabled
              [name]="column.field + '__' + rowIndex"
            >
            </label>
          </span>
          <span *ngSwitchCase="'link'">
            <a [name]="column.field + '__' + rowIndex" [href]="dataItem[column.field]">
              {{ dataItem[column.field] }}
            </a>
          </span>
          <span *ngSwitchCase="'link'">
            <div [innerHTML]="dataItem[column.field]"></div>
          </span>
          <span *ngSwitchCase="'file'">
            <ng-container
              *ngIf="col.template || defaultColumnTemplate; else tplColumnFileNoTemplate"
              [ngTemplateOutlet]="col.template || defaultColumnTemplate"
              [ngTemplateOutletContext]="{
                $implicit: dataItem,
                rowIndex: rowIndex,
                field: field,
                column: column,
                columnItem: col
              }"
            >
            </ng-container>
            <ng-template #tplColumnFileNoTemplate>
              <a
                [name]="column.field + '__' + rowIndex"
                (click)="onClickFileName(dataItem[column.field])"
              >
                {{ dataItem[column.field] }}
              </a>
            </ng-template>
          </span>
          <span *ngSwitchDefault>
            {{ dataItem[column.field] }}
          </span>
        </span>
      </ng-template>
      <ng-template #elseSchema>
        <span *ngIf="builder.options?.configColumnSchema?.fieldBoolean?.indexOf(column.field) >= 0">
          <label
            nz-checkbox
            [(ngModel)]="dataItem[column.field]"
            nzDisabled
            [name]="column.field + '__' + rowIndex"
          >
          </label>
        </span>
        <span *ngIf="builder.options?.configColumnSchema?.fieldDate?.indexOf(column.field) >= 0">
          {{ dataItem[column.field] | date : dateFormat }}
        </span>

        <span *ngIf="builder.options?.configColumnSchema?.fieldNumber?.indexOf(column.field) >= 0">
          {{ dataItem[column.field] }}
        </span>

        <span
          *ngIf="
            builder.options?.configColumnSchema?.fieldNumberMoney?.indexOf(column.field) >= 0;
            else noSchema
          "
        >
          {{ dataItem[column.field] }}
        </span>
        <ng-template #noSchema>
          {{ dataItem[column.field] }}
        </ng-template>
      </ng-template>
    </span>
  </ng-template>
</div>
