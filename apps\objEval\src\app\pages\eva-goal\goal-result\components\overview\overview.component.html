<div nz-row [nzGutter]="[16, 16]" class="p-3">
  <div nz-col class="gutter-row" [nzSpan]="12">
    <div class="card" style="min-height: 150px; max-height: 150px">
      <div class="card-body">
        <h5 class="card-title">Tỷ lệ hoàn thành</h5>
        <div>
          <div class="font-weight-bold" style="font-size: 24px">50%</div>
          <nz-progress [nzPercent]="50" [nzShowInfo]="false"></nz-progress>
        </div>
      </div>
    </div>
  </div>
  <div nz-col class="gutter-row" [nzSpan]="12">
    <div class="card" style="min-height: 150px; max-height: 150px">
      <div class="card-body">
        <h5 class="card-title">Thống kê kết quả</h5>
        <div class="d-flex align-items-center" style="gap: 16px;">
          <div #statusChart style="width: 150px" class="d-flex justify-content-center"></div>
          <div *ngFor="let item of statusData" class="d-flex flex-column align-items-center">
            <div class="font-weight-bold" [style.font-size.px]="14" [style.color]="item.color">{{ item.label }}</div>
            <div class="font-weight-bold" [style.font-size.px]="16">{{ item.value }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div nz-col class="gutter-row" [nzSpan]="24">
    <app-grid></app-grid>
  </div>
</div>
