export const scheduleColumns = [
  {
    Name: 'reportName',
    HeaderName: 'reportName',
    HeaderKey: 'Tên & mẫu báo cáo',
    Width: 250,
    Sortable: true,
    Class: 'reportName',
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: 20,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: false,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    IsLock: false,
    Template: null,
    AggregateFunc: null,
  },
  {
    Name: 'frequency',
    HeaderName: 'frequency',
    Header<PERSON>ey: 'Tần suất',
    Width: 150,
    Sortable: true,
    Class: 'frequency',
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: 20,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: false,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    IsLock: false,
    Template: null,
    AggregateFunc: null,
  },
  {
    Name: 'nextReport',
    HeaderName: 'nextReport',
    Header<PERSON>ey: '<PERSON><PERSON><PERSON> cáo tiếp theo',
    Width: 180,
    Sortable: true,
    Class: 'nextReport',
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: 20,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: false,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    IsLock: false,
    Template: null,
    AggregateFunc: null,
  },
  {
    Name: 'assignee',
    HeaderName: 'assignee',
    HeaderKey: 'Người tham gia',
    Width: 200,
    Sortable: true,
    Class: 'assignee',
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: 20,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: false,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    IsLock: false,
    Template: null,
    AggregateFunc: null,
  },
  {
    Name: 'status',
    HeaderName: 'status',
    HeaderKey: 'Trạng thái',
    Width: 150,
    Sortable: true,
    Class: 'status',
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: 20,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: true,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    IsLock: false,
    Template: null,
    AggregateFunc: null,
  },
  {
    Name: 'lastReportDate',
    HeaderName: 'lastReportDate',
    HeaderKey: 'Ngày báo cáo gần nhất',
    Width: 150,
    Sortable: true,
    Class: 'lastReportDate',
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: 20,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: false,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    IsLock: false,
    Template: null,
    AggregateFunc: null,
  },
];
