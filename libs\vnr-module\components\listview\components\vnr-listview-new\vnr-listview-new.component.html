<div
  class="vnr-pager-custom-kendo"
  [ngClass]="{
  'vnr-grid__hide-vertical-scroll': builder.options.configHeightGrid.isAllowCalcRowHeight,
}"
>
  <vnr-listview-new-change-column
    *ngIf="isOpenChangeColumn && isChangeColumnNew"
    [builder]="builderListviewChangeColumn"
    (vnrClosePopup)="vnrcloseAndRefreshGird($event)"
    (vnrReloadGridChangeColumn)="vnrReloadGridChangeColumn($event)"
  ></vnr-listview-new-change-column>
  <kendo-listview
    [kendoListViewBinding]="dataSource$ | async"
    [height]="builder.options.configHeightGrid?.gridHeight"
    [loading]="isLoading"
    [pageable]="false"
  >
    <ng-template kendoListViewItemTemplate let-group let-dataItem="dataItem" let-index="index">
      <div
        class="listview-item"
        (click)="onItemClick($event, dataItem)"
        (dblclick)="onItemDoubleClick($event, dataItem)"
        (mouseenter)="onItemMouseEnter($event, dataItem)"
        (mouseleave)="onItemMouseLeave($event, dataItem)"
      >
        <div
          class="listview-item__checkbox"
          *ngIf="builder.options.configShowHide.isShowColumnCheck"
        >
          <input
            type="checkbox"
            kendoCheckBox
            class="k-checkbox"
            [checked]="dataItem.checked"
            [(ngModel)]="dataItem.checked"
            id="id_{{ dataItem[builder.options.configSelectable.columnKey] }}_{{ index }}"
            name="name_{{ dataItem[builder.options.configSelectable.columnKey] }}_{{ index }}"
            [ngModelOptions]="{ standalone: true }"
            (change)="checkDataItem(dataItem)"
          />
        </div>
        <div class="listview-item__box" [ngClass]="{ 'listview-item__expanded': dataItem.checked }">
          <nz-collapse
            [nzExpandIconPosition]="'end'"
            [nzBordered]="false"
            [ngClass]="{
              'vnr-collapse-hidden-arrow': !builder.options.configShowHide.isShowArrow,
              'vnr-collapse-locked': builder.options.isLockedPanelToggle
            }"
          >
            <nz-collapse-panel
              [nzShowArrow]="builder.options.configShowHide.isShowArrow"
              [nzHeader]="headerTpl"
              [nzExtra]="extraTpl"
              [nzDisabled]="builder.options.isLockedPanelToggle"
              [nzActive]="dataItem.expanded"
              (nzActiveChange)="onPanelToggle($event, dataItem)"
            >
              <ng-container *ngIf="dataItem.expanded">
                <ng-container
                  *ngTemplateOutlet="
                    rowDetailTemplate || NoTemplate;
                    context: { $implicit: dataItem, index: index }
                  "
                ></ng-container>
              </ng-container>
            </nz-collapse-panel>
          </nz-collapse>
        </div>
      </div>
      <ng-template #headerTpl>
        <ng-container
          *ngTemplateOutlet="tplHeader; context: { dataItem: dataItem, index: index }"
        ></ng-container>
      </ng-template>
      <ng-template #extraTpl>
        <ng-container
          *ngTemplateOutlet="tplCommandRow; context: { dataItem: dataItem, index: index }"
        ></ng-container>
      </ng-template>
    </ng-template>
    <ng-template #tplHeader let-dataItem="dataItem">
      <ng-container
        *ngIf="columnHeaderTemplate; else tplHeaderDefault"
        [ngTemplateOutlet]="columnHeaderTemplate"
        [ngTemplateOutletContext]="{
          $implicit: dataItem,
        }"
      >
      </ng-container>
    </ng-template>
    <ng-template #tplHeaderDefault let-dataItem="dataItem">
      <span>
        <b>{{ dataItem[displayField] }}</b>
      </span>
    </ng-template>

    <!-- #region Template No Data -->
    <ng-template #NoTemplate>
      {{ 'common.grid.noData' | translate }}
    </ng-template>
    <ng-template #tplCommandRow let-dataItem="dataItem">
      <div
        class="listview-item__row-command"
        [hidden]="!dataItem.isHovered && builder.options.configShowHide.isShowCommandRowOnHover"
      >
        <div>
          <vnr-button-new
            [builder]="builderbtnDelete"
            [isShow]="builder.options.configShowHide.isShowDelete"
            (vnrClick)="onDelete($event, dataItem)"
          ></vnr-button-new>
        </div>
        <div>
          <vnr-button-new
            [builder]="builderbtnEdit"
            [isShow]="builder.options.configShowHide.isShowEdit"
            (vnrClick)="onEdit($event, dataItem)"
          ></vnr-button-new>
        </div>
        <div *ngIf="builder.options.configShowHide.isShowViewDetail">
          <vnr-button-new
            [builder]="builderbtnViewDetail"
            [isShow]="builder.options.configShowHide.isShowViewDetail"
            (vnrClick)="onViewDetails($event, dataItem)"
          ></vnr-button-new>
        </div>
        <div>
          <ng-container
            *ngIf="rowActionsTemplate"
            [ngTemplateOutletContext]="{ $implicit: dataItem }"
            [ngTemplateOutlet]="rowActionsTemplate"
          ></ng-container>
        </div>
      </div>
    </ng-template>
    <!-- #endregion Template No Data -->
  </kendo-listview>
  <!-- #region Pager -->
  <ng-container *ngIf="builder.options.configShowHide.isPageExpand">
    <div
      class="treelist-loadmore d-flex justify-content-center align-items-center"
      style="text-align: center; width: 100%; min-height: 41px"
      *ngIf="
        gridDataSource?.data?.length > 0 &&
        funcCalcCountLoadMore(gridDataSource?.data) < gridDataSource?.total
      "
      [ngClass]="{
        'vnrPageExpand-none': funcCalcCountLoadMore(gridDataSource?.data) === gridDataSource?.total
      }"
    >
      <button
        class="kendo-loadMore-customs"
        nz-button
        nzType="default"
        (click)="loadMore()"
        nzShape="round"
        [disabled]="isLoading"
      >
        <span class="d-flex justify-content-center align-items-center" nz-icon>
          <span class="mr-1 d-flex" nz-icon *ngIf="!isLoading">
            <i class="fas fa-angle-down"></i>
          </span>
          <span class="mr-1" nz-icon [nzType]="'loading'" *ngIf="isLoading"></span>
          {{ 'loadMore' | translate }}
          <span *ngIf="gridDataSource?.data?.length > 0">
            ({{ funcCalcCountLoadMore(gridDataSource.data) }}/{{ gridDataSource.total }})
          </span>
        </span>
      </button>
    </div></ng-container
  >
  <!-- #endregion -->
  <kendo-pager
    *ngIf="!builder.options.configShowHide.isPageExpand"
    [skip]="builder.options.queryOption.skip"
    [pageSize]="builder.options.queryOption.take"
    [type]="builder.options.configPageable.type"
    [responsive]="builder.options.configPageable.isResponsive"
    [total]="gridDataSource?.total"
    (pageChange)="onPageChange($event)"
    (pageSizeChange)="onPageSizeChange($event)"
  >
    <kendo-pager-messages
      previousPage="{{ 'common.modal.PreviousPage' | translate }}"
      nextPage="{{ 'common.modal.NextPage' | translate }}"
      firstPage="{{ 'common.modal.FirstPage' | translate }}"
      lastPage="{{ 'common.modal.LastPage' | translate }}"
      of="{{ 'common.grid.pagerOf' | translate }}"
      itemsPerPage="{{ 'common.grid.pagerItemsPerPage' | translate }}"
      page="{{ 'common.grid.pagerPage' | translate }}"
    >
    </kendo-pager-messages>

    <ng-template kendoPagerTemplate let-totalPages="totalPages" let-currentPage="currentPage">
      <div class="k-pager-numbers-wrap">
        <kendo-pager-prev-buttons></kendo-pager-prev-buttons>
        <kendo-pager-numeric-buttons
          [buttonCount]="builder.options.configPageable.buttonCount"
        ></kendo-pager-numeric-buttons>
        <kendo-pager-next-buttons></kendo-pager-next-buttons>
      </div>
      <kendo-pager-page-sizes
        [pageSizes]="builder.options.configPageable.pageSizes"
      ></kendo-pager-page-sizes>
      <div class="pager-total-custom">
        <p>
          <span>{{ gridDataSource?.total }}</span>
          {{ 'common.modal.reasonContentRowSelected' | translate }}
        </p>
      </div>
    </ng-template>
  </kendo-pager>
</div>
