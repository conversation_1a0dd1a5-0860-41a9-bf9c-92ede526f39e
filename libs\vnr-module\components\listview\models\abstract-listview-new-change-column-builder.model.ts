import { IApiOptionsChangeColumnModel } from '../../grids/models/grid-new-change-column.model';

export abstract class AbstractListviewNewChangeColumnBuilder<TBuilder, TOptions> {
  gridName?: string;
  columns?: any;
  isSupperAdmin?: boolean;
  isOpenChangeColumn?: boolean;
  gridPlacement?: 'leftTop' | 'rightTop' | '';
  data?: IApiOptionsChangeColumnModel;
  options?: TOptions;
  abstract builder?(builder?: TBuilder): void;
}
