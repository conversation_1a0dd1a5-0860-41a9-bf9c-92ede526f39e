<vnr-grid-new
  class="grid-new"
  #vnrGrid
  [builder]="builderGrid"
  [dataLocal]="dataLocal"
  [columns]="columns"
  [gridName]="gridName"
  [isSupperAdmin]="isSupperAdmin"
  [isChangeColumnNew]="true"
  (getSelectedID)="getSelectedID($event)"
  (getDataItem)="getDataItem($event)"
  (vnrDoubleClick)="onOpenDetail($event)"
  (vnrViewModeGrid)="onVnRViewModeGrid($event)"
  (vnrEdit)="onGridEdit($event)"
  (vnrDelete)="onGridDelete($event)"
  (vnrViewDetails)="onGridViewDetail($event)"
  (vnrCellClick)="onGridCellClick($event)"
>
</vnr-grid-new>
