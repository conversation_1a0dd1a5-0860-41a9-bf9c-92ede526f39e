import { State } from '@progress/kendo-data-query';
import {
  VnRGridNewConfigApiSupport,
  VnRGridNewConfigColumnSchema,
  VnRGridNewConfigCommandColumn,
  VnRGridNewConfigEvent,
  VnRGridNewConfigExpandDetail,
  VnRGridNewConfigExpanded,
  VnRGridNewConfigGroupable,
  VnRGridNewConfigHeightGrid,
  VnRGridNewConfigIndexColumn,
  VnRGridNewConfigPageable,
  VnRGridNewConfigSelectable,
  VnRGridNewConfigShowHide,
  VnRGridNewConfigTitle,
} from './grid-new.model';
import { VnrGridDensityOptions } from '../types/vnr-grid.type';

export abstract class AbstractGridOptionBuilder {
  gridDensityClassName?: VnrGridDensityOptions = 'default';
  isResizable?: boolean;
  isLockedColumnCheckBox?: boolean;
  isBorderGrid?: boolean = true;
  isEnabledScrollBottom?: boolean;
  isVirtualColumn?: boolean;
  isEnabledFormat?: boolean;
  queryOption?: State = {
    skip: 0,
    take: 25,
    group: [],
    sort: [],
    filter: {
      filters: [],
      logic: 'and',
    },
  };
  configApiSupport?: VnRGridNewConfigApiSupport = new VnRGridNewConfigApiSupport();
  configShowHide?: VnRGridNewConfigShowHide = new VnRGridNewConfigShowHide();
  configTitle?: VnRGridNewConfigTitle = new VnRGridNewConfigTitle();
  configHeightGrid?: VnRGridNewConfigHeightGrid = new VnRGridNewConfigHeightGrid();
  configColumnSchema?: VnRGridNewConfigColumnSchema = new VnRGridNewConfigColumnSchema();
  configSelectable?: VnRGridNewConfigSelectable = new VnRGridNewConfigSelectable();
  configIndexColumn?: VnRGridNewConfigIndexColumn = new VnRGridNewConfigIndexColumn();
  configExpandDetail?: VnRGridNewConfigExpandDetail = new VnRGridNewConfigExpandDetail();
  configEvent?: VnRGridNewConfigEvent = new VnRGridNewConfigEvent();
  configPageable?: VnRGridNewConfigPageable = new VnRGridNewConfigPageable();
  configGroupable?: VnRGridNewConfigGroupable = new VnRGridNewConfigGroupable();
  configCommandColumn?: VnRGridNewConfigCommandColumn = new VnRGridNewConfigCommandColumn();
  configExpanded?: VnRGridNewConfigExpanded = new VnRGridNewConfigExpanded();
}
