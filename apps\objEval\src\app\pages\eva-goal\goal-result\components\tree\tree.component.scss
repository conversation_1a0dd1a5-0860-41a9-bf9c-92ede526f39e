::ng-deep {
  .toolbar {
    background-color: #fff !important;
  }
}

.goal-tree-diagram-container {
  flex: 1;
  min-height: 800px;
  overflow: auto;
  border: 1px solid #f0f0f0;
  background-color: #ffffff;
  background-image: radial-gradient(#e0e0e0 1px, transparent 1px);
  background-size: 20px 20px;
  position: relative;
}

.goal-tree-diagram {
  width: 100%;
  height: 100%;
  min-height: 800px;
  position: relative;
}

.zoom-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #f0f0f0;
  background: white;
}

.controls {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.zoom-btn {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background-color: white;
  border: none;
  font-size: 16px;
  color: #333;

  &:hover {
    background-color: #f5f5f5;
  }

  &:active {
    background-color: #e8e8e8;
  }
}

.zoom-text {
  padding: 0 10px;
  font-size: 14px;
  color: #333;
  min-width: 50px;
  text-align: center;
}
