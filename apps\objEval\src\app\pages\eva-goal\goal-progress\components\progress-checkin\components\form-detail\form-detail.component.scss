:host {
  display: block;
  background-color: #f0f2f5;
  height: 100%;

  .drawer-body {
    padding: 16px;
    height: calc(100% - 57px); // 57px is the height of drawer-footer
    overflow-y: auto;
  }

  .section-container {
    background: #fff;
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .section-title {
    font-size: 14px;
    font-weight: 700;
    margin-bottom: 16px;
    color: #262626;
  }

  .info-grid {
    display: grid;
    gap: 16px;
    align-items: center;
    .info-label {
      font-size: 14px;
      font-weight: 500;
      color: #616161;
    }

    ul {
      line-height: 24px;
      align-self: baseline;
    }
  }

  .form-grid {
    display: grid;
    gap: 16px;
  }

  .form-item {
    display: flex;
    flex-direction: column;
    gap: 4px;

    label {
      font-size: 14px;
      font-weight: 500;
      color: #616161;
    }

    span {
      font-size: 14px;
      color: #303030;
      font-weight: 500;
    }

    .required::after {
      content: ' *';
      color: red;
    }
  }

  .drawer-footer {
    position: sticky;
    bottom: 0;
    width: 100%;
    border-top: 1px solid #e9e9e9;
    padding: 12px 16px;
    background: #fff;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    z-index: 10;
  }
}

.custom-goal-table {
  min-width: 1300px;
  table-layout: auto;

  th, td {
    vertical-align: middle;
    padding: 8px 12px;
    font-size: 14px;
  }
  th {
    background: #f8f9fa;
    font-weight: 700;
    border-bottom: 2px solid #e3e3e3;
  }
  td {
    border-color: #e3e3e3;
  }
}

.table-responsive {
  overflow-x: auto;
}

// Comment Section Styles
.comment-list {
  margin-bottom: 16px;
}

.comment-item {
  display: flex;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  &:last-child {
    border-bottom: none;
  }
}

.comment-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  margin-right: 12px;
  border: 1px solid #e6e6e6;
  background: #fff;
}

.comment-content {
  flex: 1;
}

.comment-header {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.comment-author {
  font-weight: 500;
  color: #1890ff;
  margin-right: 8px;
  font-size: 15px;
}

.comment-time {
  color: #999;
  font-size: 13px;
}

.comment-text {
  font-size: 15px;
  color: #333;
  background: #fafbfc;
  border-radius: 4px;
  padding: 8px 12px;
  margin-top: 2px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.02);
}

.comment-form {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  background: #f7f7f9;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #e6e6e6;
}

.comment-input {
  flex: 1;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  padding: 8px 12px;
  font-size: 15px;
  min-height: 38px;
  resize: vertical;
  background: #fff;
  transition: border-color 0.2s;
  &:focus {
    border-color: #1890ff;
    outline: none;
    box-shadow: 0 0 0 2px rgba(24,144,255,0.08);
  }
}

.comment-submit {
  min-width: 80px;
  height: 38px;
  border-radius: 4px;
  font-size: 15px;
  font-weight: 500;
  background: #1890ff;
  color: #fff;
  border: none;
  transition: background 0.2s;
  box-shadow: 0 2px 4px rgba(24,144,255,0.04);
  &:hover, &:focus {
    background: #40a9ff;
    color: #fff;
  }
}

@media (max-width: 600px) {
  .comment-item {
    flex-direction: column;
    align-items: stretch;
  }
  .comment-avatar {
    margin-bottom: 8px;
    margin-right: 0;
  }
  .comment-form {
    flex-direction: column;
    align-items: stretch;
    gap: 4px;
  }
  .comment-submit {
    width: 100%;
    min-width: 0;
  }
}
