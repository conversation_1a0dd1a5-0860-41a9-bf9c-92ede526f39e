import {
  ChangeDetectionStrategy,
  Component,
  Input,
  OnInit,
  SimpleChanges,
  OnChanges,
} from '@angular/core';
import { VnrGridNewEditIncellBuilder } from '@hrm-frontend-workspace/vnr-module';
import { TranslateService } from '@ngx-translate/core';
import { NzModalService } from 'ng-zorro-antd/modal';

import { ObjSharedModule } from '../../../../../../app/shared';
import { FormAllocationGoalChildComponent } from '../../../shared/components/form-allocation-goal-child/form-allocation-goal-child.component';
import { FormApprovalGoalComponent } from '../../../shared/components/form-approval-goal/form-approval-goal.component';
import { FormChangeGoalComponent } from '../../../shared/components/form-change-goal/form-change-goal.component';
import { FormRejectGoalComponent } from '../../../shared/components/form-reject-goal/form-reject-goal.component';
import { statusColorMap, statusTextMap } from '../../../shared/enums/status.enum';
import { gridGoalDetailDefineColumns } from '../../data/goal-list-column.data';
import { quarterColumns } from '../../../shared/data/column.data';
import { goalDepartmentData } from '../../../goal/data/goal-department.data';
@Component({
  selector: 'app-goal-child-list',
  imports: [ObjSharedModule],
  templateUrl: './goal-child-list.component.html',
  styleUrl: './goal-child-list.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GoalChildListComponent implements OnInit, OnChanges {
  @Input() dataItem: any;

  protected builderGrid: VnrGridNewEditIncellBuilder;
  protected statusColorMap = statusColorMap;
  protected statusTextMap = statusTextMap;

  protected gridName = 'ObjEval_ListGoalChild';
  protected isSupperAdmin = true;
  protected columns: any[];
  protected dataLocal = [];

  protected listColumnTemplates = {};

  ngOnInit() {
    this.builderGridComponent();
  }

  constructor(private modalService: NzModalService, private translate: TranslateService) {
    this.columns = [...gridGoalDetailDefineColumns];
    this.columns.splice(6, 0, ...quarterColumns);
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['dataItem']) {
      goalDepartmentData.forEach((item) => {
        if (item.ParentID == this.dataItem.ID) {
          this.dataLocal.push({
            ...item,
            Quarter1: 40,
            Quarter2: 40,
            Quarter3: 40,
            Quarter4: 40,
            Allocated: 100,
            Registered: 80,
            Attachment: 'tài liệu.pdf',
          });
        }
      });
      this.builderGridComponent();
    }
  }

  private builderGridComponent() {
    console.log(this.dataItem);
    this.builderGrid = new VnrGridNewEditIncellBuilder({
      options: {
        configSelectable: {
          columnKey: 'Id',
        },
        configPageable: {
          isShowPageSize: false,
          isShowInfo: false,
        },
        configIndexColumn: {
          width: 10,
        },
        configCommandColumn: {
          isEnabledMenuAction: true,
          width: 200,
        },
        configShowHide: {
          isShowEdit: false,
          isShowDelete: false,
          isShowColumnCheck: false,
        },
      },
    });
  }

  protected onApprove(dataItem: any) {
    this.modalService.create({
      nzTitle: this.translate.instant('objEval.GoalPeriod.ApproveGoal'),
      nzContent: FormApprovalGoalComponent,
      nzWidth: 700,
      nzClosable: true,
      nzMaskClosable: false,
      nzData: {
        dataItem: dataItem,
      },
      nzFooter: null,
    });
  }

  protected onReject(dataItem: any) {
    this.modalService.create({
      nzTitle: this.translate.instant('objEval.GoalPeriod.Reject'),
      nzContent: FormRejectGoalComponent,
      nzWidth: 700,
      nzClosable: true,
      nzMaskClosable: false,
      nzData: {
        dataItem: dataItem,
      },
      nzFooter: null,
    });
  }

  protected onEdit(dataItem: any) {
    this.modalService.create({
      nzTitle: this.translate.instant('objEval.GoalPeriod.RequestAdjust'),
      nzContent: FormChangeGoalComponent,
      nzWidth: 700,
      nzClosable: true,
      nzMaskClosable: false,
      nzData: {
        dataItem: dataItem,
      },
      nzFooter: null,
    });
  }

  protected onOpenAllocationGoalChildModal(data: any) {
    this.modalService.create({
      nzTitle: this.translate.instant('objEval.GoalPeriod.AllocationChild'),
      nzContent: FormAllocationGoalChildComponent,
      nzData: data,
      nzWidth: 700,
      nzMaskClosable: false,
      nzFooter: null,
    });
  }
}
