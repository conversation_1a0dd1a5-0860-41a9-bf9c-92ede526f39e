export interface ICompetencyEvaluation {
  ID: string;
  CriteriaName: string;
  Weight: number;
  [key: string]: any;
}

export const ADD_COMPETENCY_EVALUATION_DATA: ICompetencyEvaluation[] = [
  {
    ID: 'd58f1107-5427-45bb-8f33-0b42e2aaa3b3',
    CriteriaName:
      '<PERSON><PERSON><PERSON> năng tổ chức CV (Biểu mẫu.), l<PERSON><PERSON> k<PERSON> ho<PERSON>, và báo cáo công việc (Biểu mẫu, cách báo cáo...)',
    Weight: Math.floor(Math.random() * (20 - 5 + 1)) + 5,
  },
  {
    ID: 'd58f1107-5427-45bb-8f33-0b42e2aaa3b4',
    CriteriaName: '<PERSON><PERSON><PERSON> năng thảo luận, tr<PERSON><PERSON> bày & dẫn dắt vấn đề',
    Weight: Math.floor(Math.random() * (20 - 5 + 1)) + 5,
  },
  {
    ID: 'd58f1107-5427-45bb-8f33-0b42e2aaa3b5',
    CriteriaName: 'Problem Resolving - <PERSON><PERSON><PERSON> năng phân tích và giải quyết vấn đề',
    Weight: Math.floor(Math.random() * (20 - 5 + 1)) + 5,
  },
  {
    ID: 'd58f1107-5427-45bb-8f33-0b42e2aaa3b6',
    CriteriaName: 'Khả năng ngoại ngữ',
    Weight: Math.floor(Math.random() * (20 - 5 + 1)) + 5,
  },
  {
    ID: 'd58f1107-5427-45bb-8f33-0b42e2aaa3b7',
    CriteriaName: 'Tư duy sáng tạo: Mỗi tháng có nhiều hơn 2 ý tưởng mới hiệu quả',
    Weight: Math.floor(Math.random() * (20 - 5 + 1)) + 5,
  },
];
