<div class="goal-selector-container">
  <div class="drawer-body">
    <div class="section-container">
      <div class="grid-container">
        <vnr-toolbar-new [builder]="builderToolbar"></vnr-toolbar-new>
        <vnr-treelist-new
          #vnrTreeList
          [builder]="builderTreeList"
          [gridName]="gridName"
          [dataLocal]="dataLocal"
          [columns]="columns"
          [isSupperAdmin]="isSupperAdmin"
          (vnrSelectionChange)="onSelectionChange($event)"
          [defaultColumnTemplate]="tplCustomTemplateByColumn"
        >
          <ng-template
            #tplCustomTemplateByColumn
            let-dataItem
            let-column="columnItem"
            let-field="field"
          >
            <ng-container [ngSwitch]="column['Name']">
              <!-- Representative Template -->
              <span class="--has-custom-template" *ngSwitchCase="'Representative'">
                <div class="d-flex align-items-center">
                  <app-vnr-letters-avatar
                    [avatarName]="dataItem['Representative']"
                    [circular]="true"
                    [width]="32"
                    class="mr-2"
                  ></app-vnr-letters-avatar>
                  <span>{{ dataItem['Representative'] }}</span>
                </div>
              </span>

              <!-- Department Template -->
              <span class="--has-custom-template" *ngSwitchCase="'Department'">
                <span>{{ dataItem['Department'] }}</span>
              </span>

              <!-- Weight Template -->
              <span class="--has-custom-template" *ngSwitchCase="'Weight'">
                <span>{{ dataItem['Weight'] }}%</span>
              </span>

              <!-- Target Template -->
              <span class="--has-custom-template" *ngSwitchCase="'Target'">
                <span>{{ dataItem['Target'] | targetFormat : dataItem['Unit'] || 'VND' }}</span>
              </span>

              <!-- Total Target Template -->
              <span class="--has-custom-template" *ngSwitchCase="'TotalTarget'">
                <span>{{
                  dataItem['TotalTarget'] | targetFormat : dataItem['Unit'] || 'VND'
                }}</span>
              </span>

              <!-- Luỹ kế đạt -->
              <span class="--has-custom-template" *ngSwitchCase="'DoneTarget'">
                <span>{{ dataItem['DoneTarget'] | targetFormat : dataItem['Unit'] || 'VND' }}</span>
              </span>

              <!-- Latest Result Template -->
              <span class="--has-custom-template" *ngSwitchCase="'LatestResult'">
                <span>{{
                  dataItem['LatestResult'] | targetFormat : dataItem['Unit'] || 'VND'
                }}</span>
              </span>

              <!-- Remaining Day Template -->
              <span class="--has-custom-template" *ngSwitchCase="'RemainingDay'">
                <span>{{ dataItem['RemainingDay'] }} ngày</span>
              </span>

              <!-- Status Template -->
              <span *ngSwitchCase="'Status'">
                <vnr-tag
                  [vnrColor]="statusColorMap[dataItem['Status']] || 'default'"
                  [vnrTitle]="statusTextMap[dataItem['Status']]"
                ></vnr-tag>
              </span>

              <!-- Process Template -->
              <span *ngSwitchCase="'Process'">
                <nz-progress [nzPercent]="dataItem['Process']"></nz-progress>
              </span>

              <span class="--has-custom-template" *ngSwitchDefault>
                {{ dataItem[column['Name']] || '-' }}
              </span>
            </ng-container>
          </ng-template>

          <ng-template #templateEmpty>-</ng-template>
        </vnr-treelist-new>
      </div>
    </div>
  </div>

  <div class="drawer-footer">
    <vnr-button-new (vnrClick)="cancel()" [builder]="cancelBuilder"> </vnr-button-new>
    <vnr-button-new (vnrClick)="selectGoals()" [builder]="selectBuilder"> </vnr-button-new>
  </div>
</div>
