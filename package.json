{"name": "@hrm-frontend-workspace/source", "version": "0.0.0", "license": "MIT", "scripts": {"start": "node scripts/setup-env.js", "nx-run:storybook": "npx nx run design-system:storybook", "start:shell": "cross-env NODE_OPTIONS=--max_old_space_size=32768 nx run shell:serve", "start:dashboard": "cross-env NODE_OPTIONS=--max_old_space_size=32768 nx run dashboard:serve", "start:objEval": "cross-env NODE_OPTIONS=--max_old_space_size=32768 nx run objEval:serve", "start:hr": "cross-env NODE_OPTIONS=--max_old_space_size=32768 nx run human-resources:serve", "nx-run-debug-webpack:storybook": "npx nx run design-system:storybook --port=6006 --debug-webpack", "nx-run-dev:storybook": "npx storybook dev --config-dir ./libs/design-system/.storybook", "test:unit:shell": "cross-env NODE_OPTIONS=--max_old_space_size=16384 nx test shell", "test:unit:shell-spec": "cross-env NODE_OPTIONS=--max_old_space_size=16384 nx test shell --testPathPattern=<FILE_SPEC>", "test:e2e:shell-e2e": "cross-env NODE_OPTIONS=--max_old_space_size=16384 nx e2e shell-e2e", "test:e2e:shell-e2e-spec": "cross-env NODE_OPTIONS=--max_old_space_size=16384 nx e2e shell-e2e --testPathPattern=<SPEC_NAME>", "test:unit:objEval": "cross-env NODE_OPTIONS=--max_old_space_size=16384 nx test objEval", "test:unit:objEval-spec": "cross-env NODE_OPTIONS=--max_old_space_size=16384 nx test objEval --testPathPattern=goal-cycle-grid.component.spec.ts", "test:e2e:objEval": "cross-env NODE_OPTIONS=--max_old_space_size=16384 concurrently \"nx serve shell\" \"nx serve objEval\" \"wait-on http://localhost:4200 && nx e2e objEval-e2e\"", "test:e2e:objEval-spec": "cross-env NODE_OPTIONS=--max_old_space_size=16384  concurrently \"nx serve shell\" \"nx serve objEval\" \"wait-on http://localhost:4200 && nx e2e objEval-e2e\" --grep=goal-cycle.spec.ts", "test:e2e:show-report": "cross-env NODE_OPTIONS=--max_old_space_size=16384 playwright show-report", "test:e2e:ui": "cross-env NODE_OPTIONS=--max_old_space_size=16384 concurrently \"nx serve shell\" \"nx serve objEval\" \"wait-on http://localhost:4200 && playwright test --ui\"", "test:e2e:debug": "cross-env NODE_OPTIONS=--max_old_space_size=16384 concurrently \"nx serve shell\" \"nx serve objEval\" \"wait-on http://localhost:4200 && playwright test --debug\" --grep=goal-cycle.spec.ts", "build-all:prod": "cross-env NODE_OPTIONS=--max_old_space_size=16384 nx build shell --configuration=production --baseHref=/my-app/ --deployUrl=/my-app/ --outputPath=dist/my-app && nx build attendance --configuration=production --outputPath=dist/my-app/attendance && nx build human-resources --configuration=production --outputPath=dist/my-app/human-resources && nx build insurance --configuration=production --outputPath=dist/my-app/insurance && nx build salary --configuration=production --outputPath=dist/my-app/salary && nx build example --configuration=production --outputPath=dist/my-app/example && nx build objEval --configuration=production --outputPath=dist/my-app/objEval", "build-all:prod:parallel": "cross-env NODE_OPTIONS=--max_old_space_size=16384 nx run shell:build:production --baseHref=/my-app/ --deployUrl=/my-app/ --outputPath=dist/my-app && nx run objEval:build:production --outputPath=dist/my-app/apps/objEval", "nx-lint": "nx run-many --target=lint --projects=<LIST_PROJECT_OR_LIBRARIES>", "nx-lint-fix": "nx run-many --target=lint --projects=<LIST_PROJECT_OR_LIBRARIES> --fix", "build-all": "cross-env NODE_OPTIONS=--max_old_space_size=16384 nx run-many --target=build --all", "build-libs": "cross-env NODE_OPTIONS=--max_old_space_size=32768 nx run-many --target=build --projects=vnr-module,models,common,core,store,ui,domain,layout", "build-myapp:prod": "cross-env NODE_OPTIONS=--max_old_space_size=16384 nx build shell --baseHref=/my-app/ --deployUrl=/my-app/ --outputPath=dist/my-app", "build-shell:prod": "cross-env NODE_OPTIONS=--max_old_space_size=16384 nx build shell", "build-apps:prod": "cross-env NODE_OPTIONS=--max_old_space_size=16384 nx build shell && nx build dashboard --outputPath=dist/apps/shell/apps/dashboard && nx build objEval --outputPath=dist/apps/shell/apps/objEval", "build-example:prod": "cross-env NODE_OPTIONS=--max_old_space_size=16384 nx build example", "build-objEval:prod": "cross-env NODE_OPTIONS=--max_old_space_size=16384 nx build objEval", "build-hr:prod": "cross-env NODE_OPTIONS=--max_old_space_size=16384 nx build human-resources", "build:stats": "cross-env NODE_OPTIONS=--max_old_space_size=16384 nx build shell --prod --stats-json", "analyze": "cross-env NODE_OPTIONS=--max_old_space_size=16384 nx build shell --prod --stats-json && webpack-bundle-analyzer dist/apps/shell/stats.json", "nx:dep-graph": "npx nx graph", "nx:affected": "npx nx affected:dep-graph"}, "private": true, "devDependencies": {"@angular-devkit/build-angular": "~19.1.0", "@angular-devkit/core": "~19.1.0", "@angular-devkit/schematics": "~19.1.0", "@angular/cli": "~19.1.0", "@angular/compiler-cli": "~19.1.0", "@angular/language-service": "~19.1.0", "@babel/preset-env": "^7.26.9", "@compodoc/compodoc": "^1.1.26", "@eslint/js": "^9.8.0", "@mdx-js/loader": "^3.1.0", "@module-federation/enhanced": "^0.8.8", "@nx/devkit": "20.7.2", "@nx/eslint": "20.4.6", "@nx/eslint-plugin": "20.4.6", "@nx/jest": "^20.7.2", "@nx/js": "20.5.0", "@nx/module-federation": "20.4.6", "@nx/playwright": "^20.7.2", "@nx/storybook": "^20.5.0", "@nx/web": "20.5.0", "@nx/webpack": "20.4.6", "@nx/workspace": "20.4.6", "@playwright/test": "^1.51.1", "@schematics/angular": "~19.1.0", "@stagewise/toolbar": "^0.4.9", "@storybook/addon-a11y": "^8.6.4", "@storybook/addon-actions": "^8.6.4", "@storybook/addon-docs": "^8.6.14", "@storybook/addon-essentials": "^8.4.6", "@storybook/addon-interactions": "^8.4.6", "@storybook/addon-links": "^8.6.4", "@storybook/angular": "^8.6.4", "@storybook/builder-webpack5": "^8.6.4", "@storybook/core-server": "^8.4.6", "@storybook/jest": "^0.2.3", "@storybook/manager-webpack5": "^6.5.16", "@storybook/mdx2-csf": "^1.1.0", "@storybook/test-runner": "^0.19.0", "@storybook/testing-library": "^0.2.2", "@storybook/theming": "^8.6.4", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@types/chartist": "^0.11.1", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.16", "@types/pdfobject": "^2.2.5", "@types/store": "^2.0.5", "@typescript-eslint/utils": "^8.19.0", "angular-eslint": "^19.0.2", "autoprefixer": "^10.4.0", "concurrently": "^9.1.2", "cross-env": "^7.0.3", "css-loader": "^7.1.2", "dependency-cruiser": "^16.10.2", "eslint": "^9.8.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-playwright": "^2.2.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-preset-angular": "^14.5.4", "jsonc-eslint-parser": "^2.1.0", "msw": "^2.7.3", "msw-storybook-addon": "^2.0.4", "nx": "20.4.6", "path-browserify": "^1.0.1", "postcss": "^8.4.5", "postcss-url": "~10.1.3", "prettier": "^2.6.2", "sass": "^1.89.2", "sass-loader": "^16.0.5", "speed-measure-webpack-plugin": "^1.5.0", "storybook": "^8.4.6", "storybook-addon-themes": "^6.1.0", "storybook-design-token": "^3.2.0", "style-loader": "^4.0.0", "ts-jest": "^29.3.1", "ts-node": "10.9.1", "tslib": "^2.3.0", "typescript": "~5.7.2", "typescript-eslint": "^8.19.0", "webpack-bundle-analyzer": "^4.10.2"}, "dependencies": {"@angular/animations": "~19.1.0", "@angular/cdk": "^19.1.5", "@angular/common": "~19.1.0", "@angular/compiler": "~19.1.0", "@angular/core": "~19.1.0", "@angular/forms": "~19.1.0", "@angular/localize": "^19.1.7", "@angular/material": "^19.1.5", "@angular/platform-browser": "~19.1.0", "@angular/platform-browser-dynamic": "~19.1.0", "@angular/router": "~19.1.0", "@ant-design/icons-angular": "^19.0.0", "@ctrl/ngx-emoji-mart": "^9.2.0", "@hrm-frontend-workspace/common": "^0.0.7", "@hrm-frontend-workspace/core": "^0.0.5", "@hrm-frontend-workspace/domain": "^0.0.6", "@hrm-frontend-workspace/layout": "^0.0.10", "@hrm-frontend-workspace/models": "^0.0.7", "@hrm-frontend-workspace/store": "^0.0.7", "@hrm-frontend-workspace/ui": "^0.0.11", "@hrm-frontend-workspace/vnr-module": "^0.0.13", "@microsoft/signalr": "^8.0.0", "@ng-bootstrap/ng-bootstrap": "^18.0.0", "@ngrx/effects": "^19.0.1", "@ngrx/router-store": "^19.0.1", "@ngrx/store": "^19.0.1", "@ngx-loading-bar/core": "^7.0.0", "@ngx-loading-bar/http-client": "^7.0.0", "@ngx-loading-bar/router": "^7.0.0", "@ngx-progressbar/core": "^5.3.2", "@ngx-progressbar/http": "^5.3.2", "@ngx-progressbar/router": "^5.3.2", "@ngx-translate/core": "^16.0.4", "@ngx-translate/http-loader": "^16.0.1", "@nx/angular": "^20.4.6", "@progress/kendo-angular-barcodes": "^18.1.0", "@progress/kendo-angular-buttons": "18.1.0", "@progress/kendo-angular-chart-wizard": "18.1.0", "@progress/kendo-angular-charts": "18.1.0", "@progress/kendo-angular-common": "18.1.0", "@progress/kendo-angular-dateinputs": "18.1.0", "@progress/kendo-angular-dialog": "18.1.0", "@progress/kendo-angular-dropdowns": "^18.1.0", "@progress/kendo-angular-editor": "18.1.0", "@progress/kendo-angular-excel-export": "18.1.0", "@progress/kendo-angular-grid": "18.1.0", "@progress/kendo-angular-icons": "18.1.0", "@progress/kendo-angular-indicators": "^18.1.0", "@progress/kendo-angular-inputs": "18.1.0", "@progress/kendo-angular-intl": "18.1.0", "@progress/kendo-angular-l10n": "18.1.0", "@progress/kendo-angular-label": "18.1.0", "@progress/kendo-angular-layout": "18.1.0", "@progress/kendo-angular-listview": "^18.1.0", "@progress/kendo-angular-menu": "18.1.0", "@progress/kendo-angular-navigation": "18.1.0", "@progress/kendo-angular-pager": "^18.1.0", "@progress/kendo-angular-pdf-export": "18.1.0", "@progress/kendo-angular-popup": "18.1.0", "@progress/kendo-angular-progressbar": "18.1.0", "@progress/kendo-angular-scheduler": "18.1.0", "@progress/kendo-angular-spreadsheet": "^18.1.0", "@progress/kendo-angular-toolbar": "18.1.0", "@progress/kendo-angular-tooltip": "^18.1.0", "@progress/kendo-angular-treelist": "18.1.0", "@progress/kendo-angular-treeview": "18.1.0", "@progress/kendo-angular-upload": "18.1.0", "@progress/kendo-angular-utils": "18.1.0", "@progress/kendo-data-query": "^1.7.1", "@progress/kendo-date-math": "^1.5.14", "@progress/kendo-drawing": "^1.21.2", "@progress/kendo-file-saver": "^1.1.2", "@progress/kendo-font-icons": "^4.0.0", "@progress/kendo-licensing": "^1.4.0", "@progress/kendo-recurrence": "^1.0.3", "@progress/kendo-svg-icons": "^4.0.0", "@progress/kendo-theme-default": "^7.0.1", "angular-gridster2": "^19.0.0", "angular-highcharts": "^17.0.1", "angular-oauth2-oidc": "^19.0.0", "angular-oauth2-oidc-jwks": "^17.0.2", "angular-us-map": "^1.2.0", "angular2-chartjs": "^0.5.1", "angular2-uuid": "^1.1.1", "ansi-colors": "^4.1.3", "bootstrap": "^4.5.2", "chartist": "^1.3.0", "chartist-plugin-tooltips-updated": "^1.0.0", "core-js": "^3.40.0", "date-fns": "^4.1.0", "gojs": "^2.1.38", "gojs-angular": "2.0.8", "highcharts": "^8.2.2", "jquery": "^3.5.1", "lodash": "^4.17.21", "moment": "^2.30.1", "ng-chartist": "^9.0.0", "ng-packagr": "^19.1.2", "ng-zorro-antd": "^19.0.2", "ngx-captcha": "^13.0.0", "ngx-color-picker": "^17.0.0", "ngx-cookie-service": "^19.1.0", "ngx-indexed-db": "19.4.3", "ngx-infinite-scroll": "^19.0.0", "ngx-mask": "^19.0.6", "ngx-perfect-scrollbar": "^10.1.1", "ngx-scrollbar": "^18.0.0", "ngx-summernote": "^1.0.0", "ngx-toastr": "^19.0.0", "pdfobject": "^2.3.1", "rxjs": "~7.8.0", "store": "^2.0.12", "summernote": "^0.9.1", "unorm": "^1.6.0", "zone.js": "~0.15.0"}, "nx": {"includedScripts": []}}