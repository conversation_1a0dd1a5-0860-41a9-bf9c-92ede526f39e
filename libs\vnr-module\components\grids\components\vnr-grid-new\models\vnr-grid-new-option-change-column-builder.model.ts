import { AbstractGridOptionChangeColumnBuilder } from '../../../models/abstract-grid-option-change-column-builder.model';

export class VnrGridNewOptionChangeColumnBuilder extends AbstractGridOptionChangeColumnBuilder {
  constructor(options?: VnrGridNewOptionChangeColumnBuilder) {
    super();
    if (!options) {
      options = {};
    }
    options.apiRestoreChangeColumn = {
      method: options.apiRestoreChangeColumn?.method ?? 'get',
      url: options.apiRestoreChangeColumn?.url,
    };
    options.apiSaveChangeColumn = {
      method: options.apiSaveChangeColumn?.method ?? 'post',
      url: options.apiSaveChangeColumn?.url,
    };
    options.apiSaveTranslate = {
      method: options.apiSaveTranslate?.method ?? 'post',
      url: options.apiSaveTranslate?.url,
    };
    options.isDisabledModeViewRecommended ??= false;
    options.isDisabledModeViewSimple ??= false;
    options.isRestoreApi ??= true;
    options.position = {
      bottom: options.position?.bottom,
      top: options.position?.top,
      left: options.position?.left,
      right: options.position?.right,
      height: options.position?.height,
      width: options.position?.width,
    };
    Object.assign(this, options);
  }
}
