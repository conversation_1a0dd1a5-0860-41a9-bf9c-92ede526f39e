import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnInit,
  ViewChild,
} from '@angular/core';
import { Validators } from '@angular/forms';
import { FormBuilder, FormGroup } from '@angular/forms';
import {
  VnrGridNewEditInlineComponent,
  VnrGridNewEditInlineBuilder,
  FormulaConfigBuilder,
} from '@hrm-frontend-workspace/vnr-module';
import { gridAllocationCycleColumns } from '../../data/column.data';
import { ObjSharedModule } from '../../../../../shared/obj-shared.module';
import { GoalState } from '../../../shared/states/goal.state';
import { quarterColumns } from '../../../shared/data/column.data';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { FormulaConfigComponent } from '@hrm-frontend-workspace/vnr-module';
import { NzModalService } from 'ng-zorro-antd/modal';
import { AllocationCycle, AllocationMethod } from '../../models/allocation.model';
import { AllocationMethodComponent } from '../allocation-method/allocation-method.component';
import { TargetFormatPipe } from '../../../shared/pipe/target-format.pipe';
import { formulaEnumData } from '../../data/datasource.data';
@Component({
  selector: 'app-allocation-cycle',
  imports: [ObjSharedModule, NzRadioModule, AllocationMethodComponent, TargetFormatPipe],
  templateUrl: './allocation-cycle.component.html',
  styleUrl: './allocation-cycle.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AllocationCycleComponent implements OnInit {
  @ViewChild('gridAllocationCycle') gridAllocationCycle: VnrGridNewEditInlineComponent;
  protected form: FormGroup;
  protected builderGrid: VnrGridNewEditInlineBuilder;
  protected formulaConfigBuilder: FormulaConfigBuilder = new FormulaConfigBuilder();
  protected gridName = 'ObjEval_AllocationCycle';
  protected isSupperAdmin = true;
  protected dataLocal = this.goalState.goalAllocation();
  protected columns = [...gridAllocationCycleColumns, ...quarterColumns];
  protected allocationMethod: AllocationMethod = 'even';
  protected industry = 'Công nghệ thông tin';
  protected vision = 'Trở thành công ty công nghệ thông tin hàng đầu Việt Nam';
  protected goal = 'Tăng trưởng 20% doanh thu năm 2025';
  protected trend = 'Tăng trưởng 20% doanh thu năm 2025';
  protected target = 'Tính toán phân bổ theo quý';
  protected allocationType = 'Tính toán phân bổ theo quý';
  protected allocationCycle: AllocationCycle = 'quarter';
  protected initAISuggestion = 'Tạo công thức phân bổ';
  protected initAIPrompt =
    'Bạn là chuyên gia lập kế hoạch cho Doanh nghiệp thuộc lĩnh vực {industry} có tầm nhìn {vision}. Với mục tiêu: {goal} có chỉ tiêu {target} thì bạn hãy tạo công thức phân bổ theo: {allocationType}';
  protected showGrid = true;
  constructor(
    private fb: FormBuilder,
    private goalState: GoalState,
    private nzModal: NzModalService,
    private cdr: ChangeDetectorRef,
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.initBuilder();
    this.initGridBuilder();
  }

  private initForm() {
    this.form = this.fb.group({
      goalType: [null, [Validators.required]],
    });
  }

  private formatText(template: string, vars: { [key: string]: string }) {
    return template.replace(/\{(\w+)\}/g, (_, key) => vars[key] || '');
  }

  private initBuilder() {
    this.formulaConfigBuilder.builder({
      formula: '',
      options: {
        enumDatas: formulaEnumData,
        isUsingOpenAI: true,
        initAISuggestion: this.initAISuggestion,
      },
    });
  }

  private initGridBuilder() {
    this.builderGrid = new VnrGridNewEditInlineBuilder({
      options: {
        configHeightGrid: {
          gridHeight: 300,
        },
        configSelectable: {
          columnKey: 'Id',
        },
        configCommandColumn: {
          isEnabledMenuAction: true,
        },
        configShowHide: {
          isShowDelete: false,
          isShowEdit: false,
          isPageExpand: true,
        },
      },
    });
  }

  protected onChangeAllocationMethod(event: any) {
    this.allocationMethod = event.event;
    if (event.data.length > 0) {
      this.dataLocal = event.data;
    }
  }

  protected onChangeAllocationCycle(event: any) {
    this.allocationCycle = event;
  }

  protected onOpenFormulaConfig(dataItem: any) {
    this.formulaConfigBuilder.options.initAIPrompt = this.formatText(this.initAIPrompt, {
      industry: this.industry,
      vision: this.vision,
      goal: dataItem.GoalName,
      target: dataItem.TotalTarget,
      allocationType: this.allocationCycle,
    });
    const modalRef = this.nzModal.create({
      nzIconType: '',
      nzWidth: '95%',
      nzStyle: { width: '95%', height: '95%' },
      nzTitle: 'Thiết lập công thức',
      nzContent: FormulaConfigComponent,
      nzData: {
        builder: this.formulaConfigBuilder,
      },
      nzFooter: null,
      nzMaskClosable: true,
      nzClosable: true,
    });
    modalRef.afterClose.subscribe((modal) => {
      if (modal?.type === 'Save') {
        this.goalState.goalAllocation().forEach((item) => {
          if (item.ID === dataItem.ID) {
            item.AllocationMethod = modal.formula;
          }
        });
        this.dataLocal = this.goalState.goalAllocation();
      }
    });
  }
}
