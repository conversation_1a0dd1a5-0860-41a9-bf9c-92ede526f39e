{"appInfo": {"modules": {"default": "Trang chủ", "example": "Trang mẫu", "attendance": "<PERSON><PERSON><PERSON> công", "insurance": "<PERSON><PERSON><PERSON>", "salary": "Lư<PERSON><PERSON>", "humanResources": "<PERSON><PERSON><PERSON> sự", "objEval": "<PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON> gi<PERSON>"}}, "HRM_Dashboard": "Dashboard", "loadMore": "<PERSON><PERSON> ", "E_VACANCY_EXPAND": "<PERSON><PERSON>", "systemFilter": "<PERSON><PERSON> lọc", "filterClearAll": "Bỏ lọc", "filterApply": "<PERSON><PERSON>", "addFilterAdvance": "<PERSON><PERSON><PERSON><PERSON> điều kiện lọc nâng cao", "applyFilterAdvance": "<PERSON><PERSON> dụng điều kiện lọc", "confirmClearAllFilter": "Bạn có muốn xóa tất cả điều kiện lọc đang chọn?", "saveDefaultValue": "<PERSON><PERSON><PERSON> giá trị hiện tại làm giá trị mặc định", "uploading": "<PERSON>ang tải lên", "errorUploadFile": "Lỗi trong quá trình tải lên", "AdditionalInformation": "Thông tin mở rộng", "DisplayNameLabelConfig": "<PERSON><PERSON><PERSON> hiển thị", "FieldNameLabelConfig": "<PERSON><PERSON><PERSON> tr<PERSON>", "ControlNameLabelConfig": "Loại control", "duplicateFieldName": "đ<PERSON> tồn tại, h<PERSON><PERSON> chọn tên k<PERSON>.", "ValidatorsFirstIsnotNumber": "Ký tự đầu tiên không được phép là số hoặc ký tự đặc biệt.", "messConfirmDeleteInConfig": "Bạn có chắc muốn xoá dữ liệu này?", "general": {"btn": {"cancel": "Hủy", "save": "<PERSON><PERSON><PERSON>"}}, "system": {"title": "HỆ THỐNG", "versionInvalid": "<PERSON><PERSON><PERSON> bản không khả dụng", "oldVersionInfo": "<PERSON><PERSON><PERSON> năng đang sử dụng phiên bản cũ 🙁", "suggestUpgrade": "<PERSON><PERSON><PERSON> nâng cấp t<PERSON>h năng để có trải nghiệm tốt hơn 💡", "backToTop": "Trở lại đầu trang", "languages": {"VN": "Tiếng <PERSON>", "EN": "English", "CN": "简体中文", "JP": "日本語"}}, "topBar": {"issuesHistory": "<PERSON><PERSON> l<PERSON> sử...", "projectManagement": "Project Management", "typeToSearch": "<PERSON><PERSON><PERSON> k<PERSON>...", "findPages": "<PERSON><PERSON><PERSON><PERSON> từ khoá để tìm kiếm", "notifyReloadAndMarkedAll": "<PERSON><PERSON><PERSON> dấu tất cả đã đọc", "syncLanguage": "<PERSON><PERSON> đồng bộ ngôn ngữ", "actions": {"Title": "<PERSON><PERSON><PERSON><PERSON> báo", "MarkIsRead": "<PERSON><PERSON><PERSON> dấu tất cả như đã đọc", "Notifi": {"noData": "<PERSON><PERSON><PERSON>ng có thông báo nào"}}, "status": "<PERSON><PERSON><PERSON><PERSON> thái", "profileMenu": {"twoFactorAuth": "<PERSON><PERSON><PERSON> thực hai b<PERSON> (2FA)", "hello": "<PERSON><PERSON> ch<PERSON>o", "billingPlan": "Billing Plan", "role": "Role", "email": "Email", "phone": "Phone", "editProfile": "Edit Profile", "logout": "<PERSON><PERSON><PERSON><PERSON>", "signOut": "<PERSON><PERSON><PERSON> xu<PERSON>", "viewProfile": "<PERSON>em thông tin cá nhân", "activityCalendar": "<PERSON><PERSON><PERSON> ho<PERSON> đ<PERSON>", "changePassWord": {"cancel": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON> mật kh<PERSON>u", "oldpass": "<PERSON><PERSON><PERSON> cũ", "newpass": "<PERSON><PERSON><PERSON> mới", "comfirnpass": "<PERSON><PERSON><PERSON> nh<PERSON>n mật khẩu mới", "EnterOldPass": "<PERSON><PERSON><PERSON><PERSON> cũ", "EnterNewPass": "<PERSON><PERSON><PERSON><PERSON> mới", "EnterConfirmPass": "<PERSON><PERSON><PERSON><PERSON>hận mật khẩu mới", "oldpassword": {"required": "Required to enter old password"}, "newpassword": {"required": "New password is required, but it is not filled out.", "mustMacthOldPwdvNewPwd": "{{newpassword}} and {{oldpassword}} cannot be the same."}, "confirmPassword": {"mustMatchNewPassword": "{{newpassword}} and {{confirmPassword}} must be the same.", "required": "Confirmation of the Password field is required."}, "response": {"errors": "Password change failed"}, "newPassword": {"errors": {"minLength": "Value must be {{minLength}} characters", "pattern": "Use at least 8 characters. Include a number, an upper case letter, a lower case letter and a special character"}}}, "changePassWordSalary": {"title": "<PERSON><PERSON><PERSON> mật kh<PERSON>u phiếu l<PERSON>", "oldpass": "<PERSON><PERSON><PERSON> cũ", "newpass": "<PERSON><PERSON><PERSON> mới", "comfirnpass": "<PERSON><PERSON><PERSON> nh<PERSON>n mật khẩu mới"}, "changePassword": {"oldpassword": {"required": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> nhập mật kh<PERSON>u cũ"}, "newpassword": {"required": "<PERSON><PERSON><PERSON> khẩu mới là bắt buộ<PERSON>, nh<PERSON><PERSON> nó không đư<PERSON><PERSON> điền.", "mustMacthOldPwdvNewPwd": "<PERSON><PERSON>t khẩu mới và mật khẩu cũ không được trùng nhau."}, "confirmPassword": {"mustMatchNewPassword": "<PERSON><PERSON>t khẩu mới và mật khẩu xác nhận phải giống nhau.", "required": "<PERSON><PERSON><PERSON> nhận trường <PERSON><PERSON>t khẩu là bắt buộc."}, "response": {"errors": "Thay đổi mật khẩu không thành công"}, "newPassword": {"errors": {"pattern": "Sử dụng ít nhất 8 ký tự. <PERSON>o gồm: số, chữ hoa, chữ thường và ký tự đặc biệt"}}}, "saveAvatar": "<PERSON><PERSON><PERSON>nh đại di<PERSON>n", "checkVersion": "<PERSON><PERSON><PERSON> tra phiên bản"}, "notification": {"E_New": "<PERSON><PERSON><PERSON><PERSON> báo mới", "E_Seen": "<PERSON><PERSON><PERSON><PERSON> báo đã xem", "tab": {"personal": "Cá nhân", "all": "<PERSON><PERSON><PERSON> c<PERSON>", "manage": "<PERSON><PERSON><PERSON><PERSON> lý"}, "delete": "<PERSON><PERSON><PERSON> thông báo", "nodata": "<PERSON><PERSON><PERSON>ng có thông báo", "registrationData": "dữ liệu đăng ký", "hasBeen": "<PERSON><PERSON> bị", "date": "ng<PERSON>y", "has": "đ<PERSON>", "mySelf": "<PERSON><PERSON><PERSON> t<PERSON>i"}}, "common": {"calculatesum": "Tổng", "calculatecount": "<PERSON><PERSON><PERSON>", "calculateaverage": "<PERSON>rung bình", "btnCancel": "<PERSON><PERSON><PERSON>", "btnRegister": "<PERSON><PERSON><PERSON> ký", "btnSave": "<PERSON><PERSON><PERSON>", "tooltipTitleUpload": "<PERSON><PERSON> g<PERSON>m c<PERSON>c lo<PERSON>i tập tin", "searchProfileNameOrCodeEmp": "<PERSON><PERSON><PERSON> kiếm mã, tên nhân viên", "backHome": "Quay lại trang chủ", "edit": "Chỉnh sửa", "create": "<PERSON><PERSON><PERSON> mới", "typingNotes": "<PERSON><PERSON> ghi chú cho công việc", "delete": "Xóa", "viewDetail": "<PERSON>em chi tiết", "feedback": "<PERSON><PERSON><PERSON>", "noComment": "<PERSON><PERSON><PERSON><PERSON> có thêm ghi chú nào", "deleteComment": "<PERSON><PERSON><PERSON> phản hồi trong ghi chú sẽ mất khi xóa?", "showMore": "<PERSON><PERSON><PERSON> thị thêm", "NotTemplateToExport": "<PERSON>ui lòng chọn mẫu để xuất!", "atDateTime": "Vào lúc", "scanInfoFromFile": "<PERSON><PERSON><PERSON> thông tin từ tập tin", "expand": "<PERSON><PERSON><PERSON> ti<PERSON>", "collapse": "<PERSON><PERSON>", "fileNotFound": "Không tìm thấy file!", "processData": "<PERSON><PERSON> xử lý dữ liệu...", "copied": "Đã sao chép", "copy": "Sao chép", "note": "<PERSON><PERSON><PERSON>", "validate": {"PasswordPrivacyPolicy": "<PERSON><PERSON>t khẩu mới của bạn tối thiểu {{n}} ký tự, bao gồm chữ số, ký tự viết hoa, ký tự viết thường và ký tự đặc biệt.", "PasswordMinDigits": "<PERSON><PERSON> <PERSON>t nhất {{n}} chữ số (0-9)", "PasswordUpperLowercase": "<PERSON><PERSON> ít nhất {{lower}} chữ thường (a-z) và ít nhất {{upper}} chữ hoa (A-Z)", "PasswordLowercase": "<PERSON><PERSON> ít nhất {{lower}} chữ thường (a-z)", "PasswordUppercase": "<PERSON><PERSON> <PERSON>t nhất {{upper}} chữ hoa (A-Z)", "PasswordMinSpecials": "<PERSON><PERSON> <PERSON>t nhất {{n}} ký tự đặc biệt", "PasswordMinLength": "<PERSON><PERSON> <PERSON>t nhất {{min}} ký tự", "PasswordMaxLength": "Chỉ chứa tối đa {{max}} ký tự", "PasswordMaxMinLength": "<PERSON><PERSON> <PERSON>t nhất {{min}} ký tự và chỉ chứa tối đa {{max}} ký tự", "evaMark": "<PERSON><PERSON><PERSON>m [{{kpiCode}} - {{kpiName}}] phải nằm trong {{ScoreList}}!", "evaActual": "Th<PERSON><PERSON> đ<PERSON>t [{{kpiCode}} - {{kpiName}}] phải nằm trong {{ActualList}}!", "evaActual2": "Th<PERSON>c đạt 2 [{{kpiCode}} - {{kpiName}}] phải nằm trong {{ActualList2}}!", "salary": {"PasswordMinDigits": "<PERSON><PERSON> <PERSON>t nhất {{n}} chữ số (0-9)", "PasswordUpperLowercase": "<PERSON><PERSON> ít nhất {{lower}} chữ thường (a-z) và ít nhất {{upper}} chữ hoa (A-Z)", "PasswordLowercase": "<PERSON><PERSON> ít nhất {{lower}} chữ thường (a-z)", "PasswordUppercase": "<PERSON><PERSON> <PERSON>t nhất {{upper}} chữ hoa (A-Z)", "PasswordMinSpecials": "<PERSON><PERSON> <PERSON>t nhất {{n}} ký tự đặc biệt", "PasswordMinLength": "<PERSON><PERSON> <PERSON>t nhất {{min}} ký tự", "PasswordMaxLength": "Chỉ chứa tối đa {{max}} ký tự", "PasswordMaxMinLength": "<PERSON><PERSON> <PERSON>t nhất {{min}} ký tự và chỉ chứa tối đa {{max}} ký tự"}, "system": {"PasswordMinDigits": "<PERSON><PERSON><PERSON> thi<PERSON>u {{n}} ký tự số (0-9)", "PasswordUpperLowercase": "<PERSON><PERSON> ít nhất {{lower}} chữ thường (a-z) và ít nhất {{upper}} chữ hoa (A-Z)", "PasswordLowercase": "<PERSON><PERSON> ít nhất {{lower}} chữ thường (a-z)", "PasswordUppercase": "<PERSON><PERSON><PERSON> có ký tự viết hoa", "PasswordMinSpecials": "<PERSON><PERSON><PERSON> thiểu {{n}} ký tự đặc biệt", "PasswordMinLength": "<PERSON><PERSON><PERSON> thiểu {{min}} ký tự", "PasswordMaxLength": "T<PERSON>i đa {{max}} ký tự", "PasswordMaxMinLength": "<PERSON><PERSON> <PERSON>t nhất {{min}} ký tự và chỉ chứa tối đa {{max}} ký tự", "PassNearExpirationWarningTime": "<PERSON><PERSON>t khẩu trùng với mật khẩu đã dùng trong {{day}} ng<PERSON>y gần nhất"}}, "ON": "<PERSON><PERSON><PERSON>", "OFF": "Ẩn", "YES": "<PERSON><PERSON>", "AGREE": "Đồng ý", "NO": "K<PERSON>ô<PERSON>", "backward": "Quay lại", "confirmDeleteSurvey": "Bạn có muốn xóa khảo sát này không?", "OK": "Đồng ý", "cancel": "<PERSON><PERSON><PERSON>", "loading": "<PERSON><PERSON> tả<PERSON>...", "errors": {"descErr500Prod": "<PERSON>ã có lỗi xảy ra trong quá trình xử lý dữ liệu"}, "from": "Từ", "to": "<PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON>", "searchHistory": "<PERSON><PERSON><PERSON> sử tìm kiếm", "message": {"dataInvalid": "<PERSON><PERSON> liệu trên lư<PERSON><PERSON> không hợp lệ", "actionSuccess": "<PERSON>hao tác thành công!", "successfully": "Th<PERSON>nh công!", "actionError": "<PERSON><PERSON> tác lỗi!", "noChanges": "<PERSON><PERSON><PERSON>ng có thay đổi để lưu!", "empty": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu", "resetTitle": "Bạn có chắc muốn thiết lập lại mặc định không?", "resetContent": "<PERSON><PERSON><PERSON> thứ bạn thiết lập sẽ được quay về mặc định", "actionProgress": "<PERSON><PERSON> thực thi", "pleaseEnterTheMissingInforBeforeSave": "<PERSON><PERSON> lòng nhập thông tin còn thiếu trư<PERSON><PERSON> khi lưu", "columnNameAlreadyExists": "Cột với tên này đã tồn tại. <PERSON><PERSON> lòng đặt tên khác cho cột.", "updateSuccessful": "<PERSON><PERSON><PERSON> nhật thành công!", "columnGroupSuccess": "<PERSON><PERSON><PERSON><PERSON> nhóm cột thành công.", "expired": "<PERSON> tức đã hết hạn!", "deletedRowOfData": "<PERSON><PERSON> xóa {{n}} dòng dữ liệu"}, "disconnectNetwork": "<PERSON><PERSON> mất kết nối mạng...", "connectingNetwork": "<PERSON><PERSON> kết nối lại mạng...", "connectingWebcamError": "<PERSON><PERSON>g tôi không thể tìm thấy máy <PERSON>nh của bạn", "connectingWebcamSupError": "Kiểm tra để chắc chắn rằng nó được kết nối và cài đặt đúng cách, nó không bị chặn bởi phần mềm chống vi-rút và trình điều khiển máy ảnh của bạn đã được cập nhật", "camera": "<PERSON><PERSON><PERSON>", "titleConnectingNetwork": "<PERSON>h<PERSON>ng tin kết nối", "messageConnectingNetwork": "<PERSON><PERSON> lò<PERSON> đợi, <PERSON><PERSON> thống sẽ tự động reload lại trong gi<PERSON>y lát. <PERSON><PERSON><PERSON> hệ thống không tự động reload bạn có thể nhấn phím (F5) hoặc nhấn refresh trên trình duyệt.", "hint": {"dropUploadFile": "Thả file vào đ<PERSON>y để upload"}, "uploadStatus": {"success": "Tải file lên thành công", "failed": "Tải file lên thất bại"}, "button": {"selectFiles": "<PERSON><PERSON><PERSON> tập tin...", "save": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "exportExcelWithTemplate": "<PERSON><PERSON><PERSON>cel", "exportWordWithTemplate": "<PERSON><PERSON><PERSON> xu<PERSON>t <PERSON>", "exportPdf": "Xuất PDF", "exportExcel": "Xuất Excel", "exportWord": "Xuất Word", "reset": "Đặt lại"}, "title": {"error": "Lỗi", "success": "<PERSON><PERSON><PERSON><PERSON> công", "warning": "<PERSON><PERSON><PERSON> b<PERSON>o", "info": "Thông tin"}, "validation": {"duplicate": "bị trùng!", "invalid": " kh<PERSON>ng hợp lệ!", "required": "không thể bỏ trống!", "pattern": "không khớp với mẫu", "minLength": "ph<PERSON><PERSON> chứa ít nhất {{minLength}} kí tự", "maxLength": "Chỉ chứa tối đa {{maxLength}} kí tự", "email": "<PERSON>ải là định dạng kiểu email (VD: <EMAIL>)", "min0": "phải lớn hơn 0", "min": "ph<PERSON>i lớn hơn hoặc bằng {{n}} ", "max": "phải nhỏ hơn hoặc bằng {{n}}", "startNumber": "số bắt đầu phải nhỏ hơn số kết thúc", "warningDuplicate": "đang có dữ liệu bị trùng", "attachmentFile": "<PERSON><PERSON><PERSON><PERSON> hạn đ<PERSON>h kèm tối đa {{limit}} tập tin.", "warningInvalid": "đang có dữ liệu không hợp lệ", "info": "<PERSON><PERSON><PERSON> báo sẽ không ảnh hưởng đến trải nghiệm của người dùng", "IsEqualTo": "{{v}} phải bằng {{n}}.", "IsNotEqualTo": "{{v}} kh<PERSON>ng được bằng {{n}}.", "IsGreaterThanOrEqualTo": "{{v}} ph<PERSON>i lớn hơn hoặc bằng {{n}}.", "IsGreaterThan": "{{v}} ph<PERSON>i lớn hơn{{n}}.", "IsLessThanOrEqualTo": "{{v}} ph<PERSON>i nhỏ hơn hoặc bằng {{n}}.", "IsLessThan": "{{v}} ph<PERSON>i nhỏ hơn {{n}}.", "Contains": "{{v}} ph<PERSON>i chứa kí tự '{{n}}'.", "DoesNotContain": "{{v}} kh<PERSON><PERSON> đ<PERSON><PERSON><PERSON> chứa kí tự '{{n}}'.", "StartsWith": "{{v}} ph<PERSON><PERSON> bắt đầu với {{n}}.", "EndsWith": "{{v}} ph<PERSON><PERSON> kết thúc với {{n}}.", "IsAfter": "{{v}} ph<PERSON>i lớn hơn ngày {{n}}.", "IsAfterOrEqualTo": "{{v}} ph<PERSON>i lớn hơn hoặc bằng ngày {{n}}.", "IsBefore": "{{v}} ph<PERSON>i nhỏ hơn ngày {{n}}.", "IsBeforeOrEqualTo": "{{v}} ph<PERSON>i nhỏ hơn hoặc bằng ngày {{n}}.", "Between": "{{v}} p<PERSON><PERSON><PERSON> b<PERSON>t đầu từ {{n}}.", "NotBetween": "{{v}} kh<PERSON><PERSON> đ<PERSON><PERSON><PERSON> bắt đầu từ {{n}}.", "UseRegex": "{{v}} ph<PERSON>i theo mẫu sau '{{n}}'.", "UseRegex_Customize": "{{v}} ph<PERSON><PERSON> ch<PERSON><PERSON> s<PERSON>.", "minLengthCustom": "ph<PERSON><PERSON> chứa ít nhất {{n}} kí tự", "maxLengthCustom": "chỉ chứa tối đa {{n}} kí tự"}, "grid": {"noData": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu thỏa điều kiện", "orderNumber": "STT", "searchInput": "<PERSON><PERSON><PERSON>", "searchAdvanced": "<PERSON><PERSON><PERSON> kiếm nâng cao", "actionDelete": "Xóa", "actionChangeColumn": "<PERSON><PERSON><PERSON> c<PERSON>t", "actionChangeColumnV2": "<PERSON>ư<PERSON><PERSON> dữ liệu", "actionChangeToolbar": "<PERSON><PERSON> công cụ", "actionCustomizeToolbar": "<PERSON><PERSON><PERSON> chỉnh thanh công cụ", "actionAddNew": "<PERSON><PERSON><PERSON><PERSON> mới", "actionAddMulti": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>", "actionEdit": "Chỉnh sửa", "actionDuplicate": "<PERSON><PERSON><PERSON> b<PERSON>n", "actionCancel": "<PERSON><PERSON><PERSON>", "actionExportExcel": "Xuất excel", "actionCreateTemplate": "Tạo mẫu", "noSelectedIdSync": "<PERSON>ui lòng chọn dữ liệu để đồng bộ!", "noSelectedId": "Vui lòng chọn dữ liệu!", "noSelectedId_Delete": "Vui lòng chọn dữ liệu để xóa!", "columnName": "<PERSON><PERSON><PERSON>", "columnWidth": "<PERSON><PERSON> rộng", "columnGroup": "Nhóm", "columnTotal": "Tổng", "columnReadOnly": "Chỉ đọc", "columnFormat": "<PERSON><PERSON><PERSON> d<PERSON>ng", "actionSaveChange": "<PERSON><PERSON><PERSON> thay đổi", "actionCancelChange": "Hủy thay đổi", "pagerItemsPerPage": "item mỗi trang", "pagerItems": "items", "pagerOf": "of", "pagerPage": "Pager", "sortAscending": "<PERSON><PERSON><PERSON>", "sortDescending": "<PERSON><PERSON><PERSON><PERSON>", "columnMenu": "<PERSON><PERSON><PERSON> chỉnh cột", "columns": "<PERSON><PERSON><PERSON> hiển thị", "columnsReset": "Reset", "columnsApply": "<PERSON><PERSON>", "setDefaultColumn": "Bạn đang sử dụng cấu hình mặc định", "changeColumn": {"collapse": "<PERSON><PERSON>", "expand": "Mở rộng", "changeColumnText": "<PERSON><PERSON><PERSON> c<PERSON>t", "view": "<PERSON><PERSON> độ xem", "showInfoTitle": "<PERSON><PERSON><PERSON> thị thông tin lưới dữ liệu", "pinCal": "<PERSON><PERSON> c<PERSON>", "unpin": "Bỏ ghim", "show": "<PERSON><PERSON><PERSON> thị", "hidden": "Ẩn", "simple": "Đơn g<PERSON>n", "recommended": "<PERSON><PERSON><PERSON><PERSON><PERSON> d<PERSON>ng", "createColumn": "<PERSON><PERSON><PERSON><PERSON>", "notiDeleteCol": "Bạn có đồng ý xóa cột dữ liệu này?", "notiRestore": "Bạn có đồng ý cấu hình mặc định cột dữ liệu không?", "restore": "<PERSON><PERSON><PERSON> hình mặc định", "edit": "Chỉnh sửa", "translate": "<PERSON><PERSON><PERSON> tê<PERSON> c<PERSON>t", "nameColumn": "<PERSON><PERSON><PERSON><PERSON><PERSON> c<PERSON>t", "dropAdd": "<PERSON><PERSON><PERSON><PERSON>", "dropInsertTop": "<PERSON><PERSON><PERSON> trên c<PERSON>ng", "dropInsertMiddle": "<PERSON><PERSON><PERSON>", "dropInsertBottom": "<PERSON><PERSON>n p<PERSON> d<PERSON>", "dropInvalid": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>p l<PERSON>"}, "buttonAction": {"ReRegister": "Đ<PERSON>ng ký lại", "RemoveRequestCanceled": "Bỏ yêu c<PERSON>u hủy", "RequestCanceled": "<PERSON><PERSON><PERSON> c<PERSON>", "Canceled": "<PERSON><PERSON><PERSON>", "Sendmail": "<PERSON><PERSON><PERSON> yêu c<PERSON>u", "Edit": "Chỉnh sửa", "Delete": "Xóa", "Approved": "<PERSON><PERSON><PERSON><PERSON>", "Rejected": "<PERSON><PERSON> chối", "ExpandAll": "Mở rộng tất cả", "CollapseAll": "<PERSON><PERSON> g<PERSON>n tất cả"}, "format": {"Number": "<PERSON><PERSON><PERSON>", "DateTime": "<PERSON><PERSON><PERSON> ng<PERSON> g<PERSON>", "Others": "K<PERSON><PERSON><PERSON>", "Label": "<PERSON><PERSON>ã<PERSON>", "UploadFile": "<PERSON><PERSON><PERSON> tập tin", "File": "<PERSON><PERSON><PERSON> tin", "Link": "Đường dẫn", "Text": "<PERSON><PERSON><PERSON>", "Dropdown": "<PERSON><PERSON>n", "MultiSelect": "<PERSON><PERSON> s<PERSON>ch ch<PERSON>", "Category": "<PERSON><PERSON> m<PERSON>'"}}, "modal": {"createNew": "<PERSON><PERSON><PERSON> mới", "saveEdit": "<PERSON><PERSON><PERSON>", "titleAddNew": "<PERSON><PERSON><PERSON><PERSON> mới", "titleEdit": "Chỉnh sửa", "confirmDelete": "Bạn chắc chắn muốn xóa?", "confirmClearList": "<PERSON><PERSON><PERSON> toàn bộ nhân viên đã chọn?", "employeesSelected": "<PERSON><PERSON> chọn {{n}} nhân viên", "clearSelected": "Bỏ chọn tất cả", "confirmDeleteTable": "Bạn chắc chắn muốn xóa bảng?", "deleteCriteria": "Bạn có muốn xóa tiêu chí đã chọn?", "cancelSave": "Bạn chắc chắn hủy lưu?", "dataSave": "<PERSON>ữ liệu thay đổi chưa đ<PERSON><PERSON><PERSON> lưu?", "buttonOk": "Đồng ý", "buttonContinue": "<PERSON><PERSON><PERSON><PERSON>", "buttonExit": "<PERSON><PERSON><PERSON><PERSON> trang", "buttonNo": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON>", "saveNew": "Lưu và tạo mới", "saveClose": "<PERSON><PERSON><PERSON> và đóng", "invalidForm": "<PERSON><PERSON> li<PERSON><PERSON> không hợp lệ", "confirmExportExcel": "Bạn có chắc muốn xuất tất cả dữ liệu không?", "close": "Đ<PERSON><PERSON>", "saveReload": "Lưu v<PERSON> Tải lại", "confirmDeleteV2_sup1": "Bạn có muốn xóa", "confirmConfirmV2_sup1": "Bạn có muốn xác n<PERSON>n", "confirmConfirmV2_sup2": "dòng?", "confirmDeleteV2_sup2": "dòng dữ liệu?", "confirmRequest_sup1": "Bạn có muốn gửi mail yêu cầu cho ", "confirmSubmitRequest_sup1": "Bạn có muốn gửi mail yêu cầu chỉnh sửa cho", "confirmSendMailRequest": "Bạn có muốn gửi mail cho tất cả dữ liệu?", "confirmSendMailRequest_sup1": "Bạn có muốn gửi mail cho", "confirmSendMailRequest_sup2": "dòng dữ liệu?", "confirmApprove_sup1": "<PERSON><PERSON><PERSON> c<PERSON> chắc muốn <PERSON>", "confirmSubmitRefuse_sup1": "Bạn có chắc muốn từ chối", "confirmTransferData_sup1": "<PERSON><PERSON><PERSON> c<PERSON> chắc muốn chuyển", "confirmExitPage": "Bạn có chắc muốn tho<PERSON>t không?", "titleExitPage": "<PERSON><PERSON><PERSON> có những thay đổi chưa đ<PERSON><PERSON><PERSON> lưu", "applyRegisterWeek": "<PERSON>ã xếp ca cho tuần hiện tại. <PERSON>ạn có muốn sử dụng dữ liệu đăng ký tuần trước thay cho tuần hiện tại?", "applyLastWeek": "<PERSON><PERSON><PERSON><PERSON> thể áp dụng cho tuần hiện tại hoặc quá khứ", "reasonContentCancel": "Bạn đang hủy", "reasonContentRequestCancel": "Bạn đang yêu cầu hủy", "reasonContentApprove": "Bạn đang <PERSON>", "reasonContentReject": "Bạn đang từ chối", "reasonContentRowSelected": "dòng dữ liệu.", "reasonContent2": "<PERSON><PERSON><PERSON> muốn nhập lý do khác nhau bạn cần chọn lại.", "reasonContent_2": "<PERSON><PERSON><PERSON> muốn nhập ghi chú khác nhau bạn cần chọn lại.", "confirmRemoveUploadFile": "Bạn có chắc muốn xoá dữ liệu đã tải lên?", "confirmReplaceFile": "Bạn có chắc muốn thay đổi tập tin đính kèm?", "PreviousPage": "<PERSON><PERSON> đến trang trước", "NextPage": "<PERSON><PERSON> đến trang tiếp theo", "FirstPage": "<PERSON><PERSON> đến trang đầu tiên", "LastPage": "<PERSON><PERSON> đến trang cuối cùng", "PagerOf": "<PERSON><PERSON>", "confirmDeleteData": "X<PERSON>a dữ liệu?", "deletedDataCannotBeRecovered": "Dữ liệu đã xóa không thể khôi phục!"}, "nodata": "Không có dữ liệu", "notification": {"titleInfo": "Thông tin", "titleError": "<PERSON>h<PERSON>ng báo lỗi", "permissionError": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập", "descErr404": "<PERSON><PERSON><PERSON><PERSON> tìm thấy nguồn dữ liệu đang thao tác", "descErr403": "Bạn không có quyền truy cập vào nguồn dữ liệu", "descErr401": "<PERSON><PERSON><PERSON> k<PERSON>n chưa đư<PERSON><PERSON> chứng thực. <PERSON><PERSON> lòng đăng nhập vào hệ thống và thử lại", "descErr400": "<PERSON><PERSON> liệu nhập và<PERSON> không hợp lệ. <PERSON><PERSON> lòng kiểm tra lại dữ liệu đã nhập.", "descErr422": "<PERSON>ã có lỗi xảy ra trong quá trình xử lý dữ liệu. <PERSON>ui lòng liên hệ Admin hoặc IT để được hỗ trợ", "descErr500": "<PERSON>ã có lỗi xảy ra trong quá trình xử lý dữ liệu. <PERSON>ui lòng liên hệ Admin hoặc IT để được hỗ trợ", "descErr201": "<PERSON>ã có lỗi xảy ra trong quá trình xử lý dữ liệu. <PERSON>ui lòng liên hệ Admin hoặc IT để được hỗ trợ", "descErr204": "<PERSON>ã có lỗi xảy ra trong quá trình xử lý dữ liệu. <PERSON>ui lòng liên hệ Admin hoặc IT để được hỗ trợ", "descErr202": "<PERSON>u<PERSON> trình xử lý chưa được hoàn tất. <PERSON><PERSON> lòng liên hệ Admin hoặc IT để được hỗ trợ", "descErrLock": "<PERSON><PERSON><PERSON>n của bạn đã bị ngưng hoạt động.", "titleErr404": "Lỗi lấy nguồn dữ liệu", "titleErrLock": "<PERSON><PERSON><PERSON><PERSON> thể truy cập tà<PERSON>n", "titleErr403": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập", "titleErr401": "Lỗi chứng thực", "titleErr400": "Lỗi nhập liệu", "titleErr500": "Lỗi xử lý dữ liệu", "titleErr422": "Lỗi xử lý dữ liệu", "titleErr201": "Lỗi xử lý dữ liệu", "titleErr202": "Lỗi xử lý chưa hoàn tất", "descErr500Prod": "<PERSON><PERSON> tác thành công", "actionSuccess": "<PERSON>hao tác thành công!", "success": "<PERSON><PERSON><PERSON><PERSON> công"}, "changeColumn": {"title": "<PERSON><PERSON><PERSON> c<PERSON>t", "defaultConfig": "<PERSON><PERSON><PERSON> hình mặc định", "saveConfig": "<PERSON><PERSON><PERSON> c<PERSON>u h<PERSON>nh", "hideColumn": "<PERSON><PERSON><PERSON> không hiển thị", "showColumn": "<PERSON><PERSON><PERSON> hiển thị", "sortASC": "<PERSON><PERSON><PERSON> xếp tăng d<PERSON>n", "sortDESC": "<PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON>", "rowPage": "dòng/trang", "orderGroup": "<PERSON><PERSON><PERSON><PERSON> tiên", "minColumnToOrderGroup": "Nhóm dữ liệu tối thiểu phải 2 cột trở lên", "noColumnToOrderGroup": "<PERSON><PERSON><PERSON> có nhóm dữ liệu"}, "action": {"exportExcel": "Xuất Excel"}, "config": {"restoreConfigSuccess": "Cấu hình mặc định thành công!", "restoreConfigDefault": "Bạn đang sử dụng cấu hình mặc định!", "restoreConfigFailed": "Cấu hình mặc định thất bại!", "saveConfigSuccess": "<PERSON><PERSON><PERSON> nhật cấu hình thành công!", "dropMoreArea": "<PERSON><PERSON><PERSON> thả các chức năng nằm trong vùng \"Xem thêm\"", "dropConfigArea": "<PERSON><PERSON><PERSON> thả các chức năng bạn cần vào thanh công cụ", "noDataDrop": "Kéo & thả công cụ vào vùng cấu hình", "restoreDefaultConfig": "<PERSON><PERSON><PERSON> hình mặc định", "cancel": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON>"}, "process": {"addSteps": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> thự<PERSON> hi<PERSON>n", "save": "<PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "Hủy", "implementationSteps": "<PERSON><PERSON><PERSON><PERSON> th<PERSON> hi<PERSON>n", "implementer": "<PERSON><PERSON><PERSON><PERSON> th<PERSON> hi<PERSON>n", "required": "<PERSON><PERSON><PERSON><PERSON> thực hiện hoặc Người thực hiện không thể bỏ trống!", "exist": "<PERSON><PERSON><PERSON><PERSON> thực hiện và Người thực hiện đã tồn tại!", "selectAssignee": "<PERSON><PERSON> lòng chọn ng<PERSON><PERSON>i thực hiện ch<PERSON>h", "saveStep": "<PERSON><PERSON><PERSON> quy trình"}, "CodeGenerate": {"systemAutomaticCreate": "<PERSON><PERSON> thống tự động sinh"}}, "menuItem": {"news": {"index": "Tin tức V2"}, "survey": {"title": "<PERSON><PERSON><PERSON><PERSON>", "listSurvey": "<PERSON> <PERSON><PERSON>", "listSurveyQuiz": "DS <PERSON><PERSON><PERSON><PERSON>", "listSurveyHistory": "<PERSON><PERSON><PERSON> sử kh<PERSON> s<PERSON> & t<PERSON><PERSON><PERSON>"}, "report": {"title": "<PERSON>́o ca<PERSON>o", "evalue": "Kết quả đánh giá"}, "evaluation": {"title": "Đánh giá V2", "evaluationBoard": "Bảng đ<PERSON>h giá", "orgChartTarget": "<PERSON><PERSON><PERSON> tiêu tổ chức", "achievementReport": "Lưới 9-Box"}, "overtime": {"title": "Tăng ca", "titleApprove": "<PERSON><PERSON> tăng ca", "overtimes": "<PERSON><PERSON> sa<PERSON>ch tăng ca", "hour": " giờ", "otMonth": "Vượt OT Tháng", "otYear": "Vượt OT Năm", "attWorkingTimeOver": "<PERSON><PERSON><PERSON><PERSON> gian t<PERSON>ng ca", "attOvertimeType": "Lo<PERSON><PERSON> tăng ca", "attSignUpToEat": "<PERSON><PERSON>ng ký ăn ngoài giờ", "attRequestShuttleCar": "<PERSON><PERSON><PERSON> c<PERSON>u tuyến xe", "attWrongShift": "Sai ca làm việc", "attProfile": "Nhân viên", "attJobTitle": "<PERSON><PERSON><PERSON> danh", "attPosition": "<PERSON><PERSON><PERSON> v<PERSON>", "otLimit": "Vượt OT", "inputPlaceholderReason": "<PERSON><PERSON> lòng nh<PERSON>p lý do"}, "leaveday": {"title": "Ngày nghỉ", "leavedays": "<PERSON> nghỉ", "attYear": "Năm", "attDay": "<PERSON><PERSON><PERSON>", "hour": " giờ"}, "tamscanlog": {"title": "<PERSON><PERSON><PERSON><PERSON> chấm công", "title-approve": "<PERSON><PERSON>t quên chấm công", "tamscanlogs": "<PERSON><PERSON><PERSON><PERSON> chấm công", "hour": " giờ", "createTamScanlog": "<PERSON><PERSON><PERSON> ký quên chấm công", "updateTamScanlog": "<PERSON><PERSON><PERSON> nhật quên chấm công", "inputPlaceholderComment": "<PERSON><PERSON> lòng nhập mô tả", "reasonMissInOut": "Lý do quên", "reasonMissInOutDetail": "Thê<PERSON> lý do chi tiết hoặc tập tin đính kèm"}, "attendance": {"title": "Chấm công V2", "ATT_ALL": "<PERSON><PERSON><PERSON> c<PERSON>", "ATT_PENDING": "<PERSON>ờ <PERSON>", "ATT_APPROVED": "Đ<PERSON>", "ATT_DENIED": "<PERSON><PERSON> chối", "ATT_CANCEL": "<PERSON><PERSON> hủy", "attCreate": "<PERSON><PERSON><PERSON> ký", "attEdit": "Chỉnh sửa", "attSendMail": "<PERSON><PERSON><PERSON> yêu c<PERSON>u", "attCancel": "<PERSON><PERSON><PERSON>", "attCancelAll": "<PERSON><PERSON><PERSON> tất cả", "attRequestCancel": "<PERSON><PERSON><PERSON> c<PERSON>", "attApprovedAll": "<PERSON><PERSON><PERSON><PERSON> tất cả", "attRejectAll": "<PERSON>ừ chối tất cả", "attMonth": "<PERSON><PERSON><PERSON><PERSON>", "attMonthWork": "<PERSON><PERSON> công", "attMonthWorkPlaceholder": "<PERSON><PERSON><PERSON> cả kỳ công", "attYear": "Năm", "attOrgAll": "<PERSON><PERSON><PERSON> cả phòng ban", "pleaseChoiceOrg": "<PERSON>ui lòng chọn phòng ban", "pleaseChoiceStore": "<PERSON><PERSON> lòng chọn c<PERSON><PERSON> hàng", "attApproved": "<PERSON><PERSON><PERSON><PERSON>", "attReject": "<PERSON><PERSON> chối", "attStatus": "<PERSON><PERSON><PERSON><PERSON> thái", "attDelete": "<PERSON><PERSON><PERSON> dữ liệu đã chọn ({{n}})", "attScope": "Phạm vi", "attFilterByType": "<PERSON><PERSON><PERSON>"}, "training": {"title": "Đào tạo V2", "trainingScheduler": "<PERSON><PERSON><PERSON> đ<PERSON> t<PERSON>o", "viewTitle": "<PERSON><PERSON><PERSON> đào tạo V2"}, "fruit": {"title": "<PERSON><PERSON>", "compliment": "<PERSON><PERSON>"}}, "menuTop": {"Pages": "<PERSON>h mục"}, "message": {"noSupportFeatureMenuTopOnScreen": "Không hỗ trợ menu trên ở màn hình thiết bị này"}, "config": {"Title": "Tiêu đề", "Value": "Giá trị", "Placeholder": "<PERSON><PERSON> lòng chọn", "ViewColumn": "Số cột hiển thị", "colors": {"title": "<PERSON><PERSON><PERSON> hình màu", "text": "<PERSON><PERSON><PERSON> chữ", "Background": "<PERSON><PERSON><PERSON>"}, "Icon": {"Change": "<PERSON><PERSON><PERSON>", "configTitle": "<PERSON>hay đổi biểu tư<PERSON>"}, "simpleChart": {"title": "<PERSON><PERSON><PERSON><PERSON> đồ đơn giản"}, "Link": {"Image": "Đường dẫn hình ảnh"}, "ViewStatistics": "<PERSON><PERSON> th<PERSON> kê", "chartist": {"function": "<PERSON><PERSON><PERSON>"}, "dataType": "Kiểu dữ liệu", "getTheStructure": "<PERSON><PERSON><PERSON><PERSON> câ<PERSON><PERSON> tru<PERSON>c", "getparam": "<PERSON><PERSON><PERSON><PERSON> param", "addField": "Thêm field", "moreConfiguration": "<PERSON><PERSON><PERSON><PERSON> câ<PERSON>u hình", "parameterValue": {"enterString": "Nhập chuỗi", "selectDateWithTime": "Chọn ngày tháng năm có giờ", "selectDate": "Chọn ngày tháng năm", "enterNumber": "Nhập số", "enterNumberFormat": "Nhập số có định dạng", "selectStartDateeEndDate": "<PERSON><PERSON><PERSON> ngày bắt đầu và ngày kết thúc", "selectMonthYear": "<PERSON><PERSON><PERSON> th<PERSON>g n<PERSON>m", "enterStartEndNumber": "<PERSON><PERSON><PERSON><PERSON> số bắt đầu và số kết thúc", "selectDepartment": "<PERSON><PERSON><PERSON> phòng ban", "chooseTheStartEndMonth": "<PERSON><PERSON><PERSON> tháng năm bắt đầu và tháng năm kết thúc"}}, "permission": {"dined": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>y cập bị từ chối"}, "template": {"default": "Mặc định", "widgetTest": {"template1": "Bản mẫu 1", "template2": "Bản mẫu 2", "template3": "Bản mẫu 3"}}, "vnrcontrol": {"common": {"dateFromPlaceholder": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u", "dateToPlaceholder": "<PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c", "dateFromPlaceholderV2": "<PERSON><PERSON><PERSON> đ<PERSON>u", "dateToPlaceholderV2": "<PERSON><PERSON><PERSON>", "selectPlaceholder": "<PERSON><PERSON> lòng ch<PERSON>n", "inputPlaceholder": "<PERSON><PERSON> lòng nh<PERSON>p", "monthFromPlaceholder": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u", "monthToPlaceholder": "<PERSON><PERSON><PERSON><PERSON> kết th<PERSON>c", "dropGroupGrid": "Kéo tiêu đề cột và thả vào đây để nhóm cột dữ liệu", "warningReload": "<PERSON><PERSON>n dịch đã thay đổi. <PERSON><PERSON> lòng tải lại trang để cập nhật bản dịch mới."}, "demo": {"labelOrg": "Phòng ban", "labelTreeView": "TreeView", "labelTreeViewPanel": "TreeView Panel", "labelComboBox": "ComboBox", "labelAutoComplete": "AutoComplete", "labelInput": "Input", "labelInputNumber": "Input Number", "labelInputNumberFormat": "Input Number Format", "labelMulti": "Multi Select", "labelCheckBox": "CheckBox", "labelSwitch": "Switch", "labelColorPicker": "Color Picker", "labelDateTimePicker": "DateTime Picker", "labelTimePicker": "Time Picker", "labelDatePicker": "Date Picker", "labelDateRangePicker": "Date Range Picker", "labelRadioButton": "Radio Button", "labelFileAttachment": "<PERSON> đ<PERSON> k<PERSON>m", "labelTextArea": "<PERSON><PERSON>", "labelEditor": "<PERSON><PERSON>i dung", "jobVacancyName": "<PERSON><PERSON> trí tuyển", "dateRequest": "<PERSON><PERSON><PERSON> c<PERSON>u", "code": "<PERSON><PERSON> vị trí tuyển"}, "uploadFile": {"allowFileExtensions": "Chỉ cho phép tải lên những file sau {{extensions}}", "maxLengthFileName": "Tên file phải nhỏ hơn {{maxLength}} ký tự."}}, "auth": {"titleSuccess": "<PERSON>hao tác thành công!", "titleError": "<PERSON><PERSON> tác thất bại!", "title403": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập", "message403": "<PERSON>n lỗi, bạn không có quyền truy cập vào trang này.", "title403Ids": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập hệ thống", "message403Ids": "<PERSON><PERSON> lỗi, bạn không có quyền truy cập vào hệ thống.", "title404": "<PERSON><PERSON><PERSON><PERSON> tìm thấy trang", "message404": "<PERSON><PERSON> lỗi, trang bạn đang truy cập hiện không tồn tại.", "message500": "<PERSON>n lỗi, có lỗi trên máy chủ.", "title500": "Lỗi máy chủ", "tokenExpired": "QR code đã hết hạn", "common": {"accessDined": "<PERSON><PERSON><PERSON><PERSON> có quyền truy cập, do chưa được phân quyền hoặc chức năng chưa được hỗ trợ!"}, "button": {"backHome": "Quay về trang chủ", "backHomeIds": "<PERSON><PERSON><PERSON><PERSON>"}, "authFailed": "<PERSON><PERSON><PERSON> nhập thất bại. <PERSON>ui lòng kiểm tra lại thông tin đăng nhập!"}, "layout": {"positon": {"leftToRight": "<PERSON><PERSON><PERSON><PERSON> sang phải", "rightToLeft": "<PERSON><PERSON><PERSON> sang trái", "topToBottom": "<PERSON><PERSON><PERSON><PERSON>", "bottomToTop": "<PERSON><PERSON><PERSON><PERSON> lên trên"}}, "portal-general": {"tabs": {"general": "<PERSON><PERSON><PERSON> h<PERSON>nh chung", "configs": "<PERSON><PERSON><PERSON> Chỉnh", "advanced": "<PERSON><PERSON><PERSON> cao"}, "panels": {"settingApi": "API", "settingCommon": "Tiêu đề", "configApi": "<PERSON><PERSON><PERSON> hình điều kiện", "settingProgress": "<PERSON><PERSON><PERSON> hình tiến trình"}}, "common-config": {"chooseLanguage": "Chọn ngôn ngữ", "header": "Ti<PERSON><PERSON> đề trên", "sub-title": "Ti<PERSON><PERSON> đề phụ", "footer": "<PERSON>i<PERSON><PERSON> đề dưới", "template": "Bản mẫu", "show": "<PERSON><PERSON><PERSON>", "hide": "Ẩn", "on": "<PERSON><PERSON><PERSON>", "off": "Tắt", "Dashboard": "Dashboard", "MenuShortcut": "<PERSON><PERSON><PERSON>", "totalWork": "<PERSON><PERSON><PERSON> công việc", "complete": "<PERSON><PERSON><PERSON> th<PERSON>", "permissions": "<PERSON><PERSON> quyền"}, "table-column": {"config": {"data": {"type": "<PERSON><PERSON><PERSON> hình kiểu dữ liệu"}}}, "grid": {"toolbar": {"more": "<PERSON><PERSON><PERSON><PERSON>", "enterKeywordToSearch": "<PERSON><PERSON><PERSON><PERSON> từ khóa để tìm kiếm..."}, "filter": {"contains": "<PERSON><PERSON>", "isEqualTo": "Bằng", "isNotEqualTo": "Không bằng", "doesNotContain": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON><PERSON>", "startsWith": "<PERSON><PERSON><PERSON> đ<PERSON>u với", "endsWith": "<PERSON><PERSON><PERSON> th<PERSON>i", "isNull": "Rỗng", "isNotNull": "Không rỗng", "isEmpty": "<PERSON><PERSON><PERSON><PERSON>", "isNotEmpty": "<PERSON><PERSON><PERSON><PERSON> trống", "isAfter": "<PERSON><PERSON><PERSON>", "isAfterOrEqualTo": "Lớn hơn hoặc bằng", "isBefore": "Nhỏ hơn", "isBeforeOrEqualTo": "Nhỏ hơn hoặc bằng", "andLogic": "Và", "orLogic": "Hoặc", "filterButton": "<PERSON><PERSON><PERSON>"}}, "themeSettings": {"isShowUndoDelete": "Ẩn thông báo x<PERSON>c nhận", "ConfigDrawerFilter": "<PERSON><PERSON><PERSON> <PERSON>lter", "title": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "menuLayout": "Bố cục menu", "noMenu": "Gom nhóm", "topMenu": "<PERSON><PERSON><PERSON><PERSON>", "leftMenu": "Trái", "routerAnimation": "<PERSON><PERSON><PERSON>ng load trang", "breadcrumbs": "<PERSON><PERSON> đ<PERSON><PERSON> h<PERSON>", "show": "<PERSON><PERSON><PERSON> thị", "header": "Header", "leftMenuWidth": "Độ rộng menu trái", "leftMenuCollapsed": "<PERSON><PERSON>", "leftMenuUnFixed": "<PERSON><PERSON><PERSON><PERSON> cố định", "leftMenuShadow": "<PERSON><PERSON><PERSON> đ<PERSON>", "menuColor": "Màu menu", "auth": "<PERSON><PERSON><PERSON> th<PERSON>c", "authBackground": "<PERSON><PERSON><PERSON>", "topbarFixed": "Topbar cố định", "topbarGrayBackground": "Topbar màu xám", "app": "Ứng dụng", "appContentMaxWidth": "<PERSON><PERSON> rộng nội dung tối đa", "appMaxWidth": "<PERSON><PERSON> rộng tối đa", "appGrayBackground": "<PERSON><PERSON><PERSON>", "cards": "Thẻ", "cardsSquaredBorders": "<PERSON><PERSON><PERSON><PERSON> vuông", "cardsShadow": "<PERSON><PERSON><PERSON> đ<PERSON>", "cardsBorderless": "<PERSON><PERSON><PERSON><PERSON> viền", "leftMenuTitle": "<PERSON>u trái", "tenantDashboard": "Ẩn Tenant Dashboard", "tenantOpenNewTab": "Mở trong tab mới", "config": "<PERSON><PERSON><PERSON> h<PERSON>nh", "format": "<PERSON><PERSON><PERSON> d<PERSON>ng", "gridDensity": "<PERSON><PERSON><PERSON> lư<PERSON>i", "startDayOfTheWeek": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u của tu<PERSON>n", "timeFormat": "<PERSON><PERSON><PERSON> dạng thời gian", "configSystem": "<PERSON><PERSON><PERSON> hình hệ thống", "dayOfWeek": {"sunday": "<PERSON>ủ <PERSON>h<PERSON>", "monday": "Thứ 2", "tuesday": "Thứ 3", "wednesday": "Thứ 4", "thursday": "Thứ 5", "friday": "Thứ 6", "saturday": "Thứ 7"}}, "emptyDataFavs": "<PERSON><PERSON><PERSON> thêm Bookmarks", "errorHandler": {"alert": "<PERSON><PERSON> xảy ra lỗi từ hệ thống; vui lòng thử lại sau.", "throwError": "<PERSON><PERSON><PERSON>u xấu đã xảy ra; vui lòng thử lại sau.", "error": "Đã xảy ra lỗi:"}, "modalConfig": {"arrangementPosition": "<PERSON><PERSON> trí", "title": "<PERSON><PERSON><PERSON> h<PERSON>nh", "contentEmpty": "<PERSON>hông hỗ trợ cấu hình với kích thước trên thiết bị này", "currentKeyTranslate": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> hiện tại", "key": "Key", "translate": "<PERSON><PERSON><PERSON>", "order": "<PERSON><PERSON><PERSON>p", "hidden": "Ẩn", "required": "<PERSON><PERSON><PERSON> b<PERSON>", "min": "<PERSON><PERSON> tối thiểu", "max": "Số tối đa", "placeholderMin": "<PERSON><PERSON><PERSON><PERSON> số tối thiểu", "placeholderMax": "<PERSON><PERSON><PERSON><PERSON> số tối đa", "minLength": "<PERSON><PERSON> tự tối thiểu", "maxLength": "<PERSON><PERSON> tự tối đa", "placeholderMinLength": "<PERSON><PERSON><PERSON><PERSON> ký tự tối thiểu", "placeholderMaxLength": "<PERSON><PERSON><PERSON><PERSON> ký tự tối đa", "pattern": "<PERSON> mẫu", "copyTransKey": "<PERSON><PERSON>ấn vào ô để sao chép key", "copied": "Đã sao chép", "copyAll": "<PERSON><PERSON> <PERSON><PERSON><PERSON> bản d<PERSON>ch"}, "ortherInformation": "Thông tin khác", "extendedInformation": {"isUnitAssistant": "Chuyển TLĐV", "isRequestForBenefit": "<PERSON><PERSON><PERSON> c<PERSON>u <PERSON>h toán TCKC", "isTripOTInDay": "Tăng ca công tác trong ngày", "isRequestEntryExitGate": "<PERSON><PERSON><PERSON> c<PERSON>u ra/ vào cổng", "isSignUpToEat": "<PERSON><PERSON>ng ký ăn ngoài giờ", "isRequestShuttleCar": "<PERSON><PERSON><PERSON> c<PERSON>u tuyến xe"}, "dayInWeek": {"Monday": "T2", "Tuesday": "T3", "Wednesday": "T4", "Thursday": "T5", "Friday": "T6", "Saturday": "T7", "Sunday": "CN", "Daily": "Hằng <PERSON><PERSON>y"}, "filter": {"comfirmClosePopup": "Bạn có muốn lưu lại điều kiện lọc chưa áp dụng?", "yes": "Đồng ý", "no": "K<PERSON>ô<PERSON>", "from": "Từ", "to": "<PERSON><PERSON><PERSON>", "config": "<PERSON><PERSON><PERSON> h<PERSON>nh", "currentFilter": "<PERSON><PERSON> l<PERSON> hiện tại", "filterLibrary": "<PERSON><PERSON><PERSON> vi<PERSON><PERSON> bộ lọc", "pleaseconfigureFilter": "<PERSON><PERSON> lòng cấu hình thông tin bộ lọc", "success": "<PERSON><PERSON><PERSON><PERSON> công", "dataSourceType": "<PERSON><PERSON><PERSON> nguồn dữ liệu", "method": "<PERSON><PERSON><PERSON><PERSON> thức", "dataSource": "<PERSON><PERSON><PERSON><PERSON> dữ liệu", "theSelectedFilterWasNotFoundPleaseReselectTheConfig": "<PERSON><PERSON><PERSON><PERSON> tìm thấy bộ lọc đang chọn. <PERSON><PERSON> lòng chọn lại cấu hình", "doYouWantToRestoreThisFilter": "Bạn có muốn khôi phục lại bộ lọc này không?", "doYouWantToCancelChangeThisFilter": "Bạn có muốn Hủy thay đổi bộ lọc này không?", "doYouWantToSaveChangeThisFilter": "Bạn có muốn Lưu thay đổi bộ lọc này không?", "theFilterWillDisappear": "<PERSON>ộ lọc sẽ biến mất?", "warningChangeStore": "Bạn phải thêm lại bộ lọc vì khi chọn [Store] thì các điều kiện lọc hiện tại sẽ thay đổi.", "warningChangeQuickFilter": "Bạn phải thêm lại [<PERSON><PERSON><PERSON>] vì khi chọn lại thì các điều kiện lọc hiện tại sẽ thay đổi.", "warningChangeFullFilter": "Bạn phải thêm lại [<PERSON><PERSON> lọc] vì khi chọn lại thì các điều kiện lọc hiện tại sẽ thay đổi.", "customizeFilters": "<PERSON><PERSON><PERSON> chỉnh bộ lọc", "apiUrl": "Địa chỉ api", "textField": "<PERSON><PERSON><PERSON><PERSON><PERSON> hiển thị", "valueField": "Trường giá trị", "mapperFieldDateTo": "Tr<PERSON><PERSON><PERSON>nh xạ cho [DateTo]", "mapperFieldToDateRangeFrom": "Tr<PERSON><PERSON><PERSON>nh xạ cho [DateRangeFrom]", "mapperFieldToDateRangeTo": "Tr<PERSON><PERSON><PERSON>nh xạ cho [DateRangeTo]", "mapperFieldToDateExpried": "Tr<PERSON><PERSON><PERSON> ánh xạ cho [DateExpried]", "disabledKey": "<PERSON><PERSON> hi<PERSON>u hóa giá trị", "minValue": "<PERSON><PERSON><PERSON> trị tối thiểu", "maxValue": "<PERSON><PERSON><PERSON> trị tối đa", "maxCount": "<PERSON><PERSON> lư<PERSON><PERSON> đếm tối đa", "maxShow": "<PERSON><PERSON> lượng hiển thị tối đa", "placeholderLeft": "<PERSON><PERSON> hiển thị tạm thời [1]", "placeholderRight": "<PERSON><PERSON> hiển thị tạm thời [2]", "expandOrCollapse": "Mở rộng/thu gọn", "pleaseSelect": "<PERSON><PERSON> lòng ch<PERSON>n ", "doYouWantToDeleteThisFilter": "Bạn có muốn xóa bộ lọc này không?", "defaultValue": "<PERSON><PERSON><PERSON> trị mặc định", "pleaseEnterDefaultValue": "<PERSON><PERSON> lòng nhập giá trị mặc định", "fullFilter": "<PERSON><PERSON> lọc", "quickFilter": "<PERSON><PERSON><PERSON>", "availableFilters": "<PERSON><PERSON> lọc có sẵn", "filter": "<PERSON><PERSON> lọc", "showFullFilter": "Hi<PERSON>n thị đầy đủ bộ lọc", "orPressTheKeyCombination": "hoặc nhấn tổ hợp phím", "saveConfig": "<PERSON><PERSON><PERSON> c<PERSON>u h<PERSON>nh", "cancelConfig": "H<PERSON>y bỏ cấu hình", "restoreDefault": "<PERSON><PERSON><PERSON><PERSON> phục mặc định", "DataSource_Api": "<PERSON>u<PERSON>n dữ liệu API", "DataSource_Store": "<PERSON><PERSON><PERSON>n dữ liệu Store", "DataSource_Entity": "<PERSON><PERSON><PERSON><PERSON> dữ liệu <PERSON>", "DataSource_Enum": "<PERSON><PERSON><PERSON><PERSON> dữ liệu <PERSON>", "DataSource_Table": "<PERSON><PERSON><PERSON><PERSON> dữ liệu <PERSON>", "unfiltered": "<PERSON><PERSON><PERSON><PERSON> lọ<PERSON>", "addRow": "<PERSON><PERSON><PERSON><PERSON> dòng", "labelName": "<PERSON><PERSON><PERSON> tr<PERSON>", "value": "<PERSON><PERSON><PERSON> trị"}, "systems": {"DataPosition": "<PERSON><PERSON> trí dữ liệu", "index": "<PERSON><PERSON> thống V3.0", "No": "No", "ConfirmPopup": "Bạn có chắc muốn lưu dữ liệu không?", "Confirm": "Đồng ý", "Cancel": "<PERSON><PERSON><PERSON>", "Please": "<PERSON><PERSON> lòng", "btnDownload": "tải tập tin mẫu", "supPlease": "nếu bạn chưa có mẫu cấu hình này.", "ConfigurationName": "<PERSON><PERSON><PERSON> c<PERSON>u hình", "Function": "<PERSON><PERSON><PERSON>", "ConfigurationList": "<PERSON><PERSON> s<PERSON>ch cấu hình", "SelectDataSourcePage": "<PERSON><PERSON><PERSON> trang lấy dữ liệu", "SpecifyHeaderRow": "<PERSON><PERSON><PERSON> đ<PERSON> vị trí dòng tiêu đề", "SpecifyDataStartRow": "<PERSON><PERSON><PERSON> vị trí dòng dữ liệu bắ<PERSON> đầu", "InitializeData": "Khởi tạo dữ liệu", "initData": "<PERSON><PERSON> khởi tạo dữ liệu...", "initDataErr": "Khởi tạo dữ liệu không thành công!", "initDataSuccess": "Khởi tạo dữ liệu thành công!", "Check": "<PERSON><PERSON><PERSON> tra", "ConfirmImport": "<PERSON><PERSON><PERSON>", "Save": "<PERSON><PERSON><PERSON>", "Edit": "Chỉnh sửa", "DeleteConfiguration": "<PERSON><PERSON><PERSON> c<PERSON>u hình", "ProcessingProposal": "<PERSON><PERSON> xuất xử lý thông tin", "SaveTemplateConfiguration": "<PERSON><PERSON><PERSON> cấu hình mẫu", "DataConfiguration": "<PERSON><PERSON><PERSON> hình dữ liệu", "popupSaveTemplateConfiguration": "Bạn có chắc muốn lưu cấu hình không?", "contentPopupConfirmImport": "C<PERSON> {{n}} ô dữ liệu bị lỗi. Bạn có chắc muốn lưu và bỏ qua dữ liệu không?", "CheckData": "<PERSON><PERSON><PERSON> tra dữ liệu", "FileDataRows": "{{n}} dòng dữ liệu trong tập tin.", "DeleteInvalidData": "<PERSON><PERSON><PERSON> dữ liệu lỗi", "FileName": "<PERSON><PERSON><PERSON> tập tin", "SystemName": "<PERSON><PERSON><PERSON> th<PERSON>ng", "Format": "<PERSON><PERSON><PERSON> d<PERSON>ng", "DefaultValue": "<PERSON><PERSON><PERSON> trị mặc định", "DateUpdated": "<PERSON><PERSON><PERSON> c<PERSON>", "SelectProcessing": "<PERSON><PERSON><PERSON> c<PERSON>ch xử lý dữ liệu", "AddNew": "<PERSON><PERSON><PERSON> mới", "Replace": "<PERSON>hay thế", "error": "<PERSON><PERSON> liệu bị lỗi", "errorNoFile": "Chưa c<PERSON> file cấu hình", "errorNoImport": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu để lưu", "search": "<PERSON><PERSON><PERSON>", "DuplicateDataRows": "<PERSON><PERSON> thống phát hiện {{n}} dòng dữ liệu trùng thông tin.", "OnlyExcelFiles": "Chỉ hỗ trợ tải một tập tin Excel (xls, xlsx, xlsm).", "supportType": "Hỗ trợ tập tin với định dạng (doc hoặc pdf).", "popupEditImport": "Bạn có chắc muốn bỏ dữ liệu nhập vào và đi đến trang cấu hình?", "popupDeleteInvalidImport": "Bạn có chắc muốn xóa những dòng dữ liệu bị lỗi trong tập tin?", "popupDeleteImport": "Bạn có chắc muốn xóa cấu hình?", "noChanges": "<PERSON><PERSON><PERSON> hình không có sự thay đổi.", "noSelectedConfig": "<PERSON><PERSON> lòng chọn danh sách cấu hình trước để nhập dữ liệu.", "noFileImportConfig": "<PERSON><PERSON> lòng tải tập tin lên hệ thống sẽ đề xuất cấu hình khởi tạo gi<PERSON>p bạn.", "sysImport": {"index": "<PERSON><PERSON><PERSON><PERSON> liệu", "onlyDisplayErrData": "Chỉ hiển thị dữ liệu lỗi", "noData": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu", "checkData": "<PERSON><PERSON><PERSON> tra dữ liệu", "importData": "<PERSON><PERSON><PERSON><PERSON> liệu", "pleaseEnterData": "<PERSON><PERSON> lòng nhập dữ liệu", "alertGridDataErr": "Tổng cộng {{n}} lỗi trong tập tin tải lên. <PERSON><PERSON> lòng sửa lỗi và tải lại tập tin", "Executor": "<PERSON><PERSON><PERSON><PERSON> th<PERSON> hi<PERSON>n", "DateExecution": "<PERSON><PERSON><PERSON>n", "Expertise": "<PERSON><PERSON>ệ<PERSON> vụ", "ConfigurationTemplate": "Mẫu cấu hình", "ChangeHistory": "<PERSON><PERSON><PERSON> sử thay đổi", "searchText": "<PERSON><PERSON><PERSON>", "createCofigImport": "<PERSON><PERSON><PERSON> c<PERSON>u h<PERSON>nh"}}, "configv3": {"information": "Thông tin đăng ký", "anotherInfo": "Thông tin khác", "undefined": "Th<PERSON>ng tin chung", "Undefined": "Th<PERSON>ng tin chung", "visible": "<PERSON><PERSON><PERSON> thị", "required": "<PERSON><PERSON><PERSON> b<PERSON>", "checkDuplicate": "<PERSON><PERSON><PERSON> trùng", "disable": "Chỉ xem", "default": "Mặc định", "condition": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>n", "addCondition": "<PERSON>hê<PERSON> điều kiện ràng buộc dữ liệu", "clearCondition": "<PERSON><PERSON><PERSON> tất cả điều kiệu", "clear": "<PERSON><PERSON><PERSON> tất cả", "value": "<PERSON><PERSON><PERSON> trị", "add": "<PERSON><PERSON><PERSON><PERSON>", "actionVisible": "<PERSON><PERSON><PERSON> chỉnh <PERSON>n hiện", "actionRequired": "<PERSON><PERSON> liệu có bắt buộc thêm hay không?", "actionCheckvalidator": "<PERSON>ữ liệu có bắt trùng hay không?", "actionDisable": "Chỉ có quyền xem không được phép chỉnh sửa", "actionCondition": "Mặc định hiểm thị trên giao di<PERSON>n", "blockEditEntity": "<PERSON><PERSON> li<PERSON>, kh<PERSON><PERSON> thể sửa", "blockEditRequired": "<PERSON><PERSON> li<PERSON> b<PERSON><PERSON> bu<PERSON>, kh<PERSON><PERSON> thể <PERSON>n", "blockEditHidden": "<PERSON><PERSON> liệu <PERSON>, <PERSON>h<PERSON><PERSON> thể bắ<PERSON> buộ<PERSON> nhập", "nonePhone": "<PERSON><PERSON><PERSON><PERSON> có trên phiên bản di động", "keyUndefined": "<PERSON><PERSON><PERSON><PERSON> có key dịch"}, "objEval": {"Appraisals": {"execution": "<PERSON><PERSON><PERSON><PERSON> hiện đ<PERSON>h giá", "statusTab": {"all": "<PERSON><PERSON><PERSON> c<PERSON>", "todo": "<PERSON><PERSON><PERSON> th<PERSON><PERSON> hi<PERSON>n", "notsendtome": "<PERSON><PERSON><PERSON> g<PERSON>i đến tôi", "approved": "Đ<PERSON>", "summaryResult": "<PERSON><PERSON><PERSON> qu<PERSON> tổng hợp", "incomplete": "<PERSON><PERSON><PERSON> ho<PERSON>n thành", "complete": "<PERSON><PERSON> hoàn thành", "selfAssessment": "Tự đánh giá", "managerAssessment": "<PERSON><PERSON><PERSON><PERSON> lý đ<PERSON>h giá", "approval": "<PERSON><PERSON>"}, "selectAllOrg": "<PERSON><PERSON><PERSON> tất cả phòng ban", "selectAllPosition": "<PERSON><PERSON><PERSON> tất cả vị trí", "ProfileName": "Nhân viên", "Department": "Phòng ban", "Position": "<PERSON><PERSON> trí công việc", "Grade": "<PERSON><PERSON><PERSON>", "TargetScore": "<PERSON><PERSON><PERSON><PERSON> mục tiêu", "CompetencyScore": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>ng lực", "TotalScore": "<PERSON><PERSON><PERSON><PERSON> tổng", "Status": "<PERSON><PERSON><PERSON><PERSON> thái", "SelfAssessmentScore": "Tự đánh giá", "ManageDirectScore": "Q<PERSON><PERSON><PERSON> lý cấp 1", "ManageSeniorScore": "<PERSON><PERSON><PERSON><PERSON> lý c<PERSON>p 2", "AverageScore": "<PERSON><PERSON><PERSON><PERSON> trung bình", "SuggestedScore": "<PERSON><PERSON> xuất", "Comment": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>t", "btnAprove": "<PERSON><PERSON><PERSON><PERSON>", "btnViewEmployeeList": "<PERSON><PERSON> danh s<PERSON>ch nhân viên"}, "GoalTemplate": {"titleViewDetailTemplateGoal": "Mẫu mục tiêu: {{name}}", "tagGoalItemsPerPage": "{{n}} mục tiêu", "template": "Mẫu mục tiêu", "btnAddGoalGroup": "<PERSON>h<PERSON><PERSON> nhóm mục tiêu", "btnAddGoalDetail": "<PERSON><PERSON><PERSON><PERSON> mục tiêu", "GoalName": "<PERSON><PERSON><PERSON> mục tiêu", "GoalType": "<PERSON><PERSON><PERSON> mục tiêu", "GoalGroup": "<PERSON><PERSON><PERSON><PERSON> mục tiêu", "GroupCode": "<PERSON><PERSON> nhóm mục tiêu", "GroupName": "<PERSON><PERSON><PERSON> nh<PERSON>m mục tiêu", "Description": "<PERSON><PERSON>", "MeasurementalValue": "<PERSON><PERSON><PERSON> trị đo lường", "Unit": "Đơn vị", "addGoalGroup": "<PERSON>h<PERSON><PERSON> nhóm mục tiêu", "addGoalDetail": "<PERSON><PERSON><PERSON><PERSON> mục tiêu", "btnCancel": "<PERSON><PERSON><PERSON>", "btnSave": "<PERSON><PERSON><PERSON>", "pleaseEnterGroupCode": "<PERSON><PERSON> lòng nhập mã nhóm mục tiêu", "pleaseEnterGroupName": "<PERSON><PERSON> lòng nhập tên nhóm mục tiêu", "pleaseEnterDescription": "<PERSON><PERSON> lòng nhập mô tả", "Information": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "SetupCalculation": "<PERSON><PERSON><PERSON><PERSON> lập t<PERSON>h to<PERSON>", "ConnectDataSource": "<PERSON><PERSON><PERSON> n<PERSON>i nguồn dữ liệu", "AllocationBy": "<PERSON><PERSON> bổ theo", "AllocationCorpFormula": "<PERSON><PERSON><PERSON> thứ<PERSON> phân bổ cho công ty", "AllocationEmployeeFormula": "<PERSON><PERSON><PERSON> thứ<PERSON> phân bổ cho nhân viên", "AllocationTimeFormula": "<PERSON><PERSON><PERSON> th<PERSON><PERSON> ph<PERSON> bổ cho thời gian", "CalculatorResultBy": "<PERSON><PERSON><PERSON> to<PERSON> kết quả theo", "CalculatorResultOther": "<PERSON><PERSON><PERSON> to<PERSON> kết qu<PERSON> kh<PERSON>c", "ConnectionToSystem": "<PERSON><PERSON><PERSON> n<PERSON>i vớ<PERSON> hệ thống", "ConfigIntegration": "<PERSON><PERSON><PERSON> hình tích hợp", "pleaseEnterAllocationCorpFormula": "<PERSON><PERSON> lòng nh<PERSON>p công thứ<PERSON> phân bổ cho công ty", "pleaseEnterAllocationEmployeeFormula": "<PERSON><PERSON> lòng nhập công thứ<PERSON> phân bổ cho nhân viên", "pleaseEnterAllocationTimeFormula": "<PERSON><PERSON> lòng nh<PERSON><PERSON> công thứ<PERSON> phân bổ cho thời gian", "pleaseEnterCalculatorResultBy": "<PERSON><PERSON> lòng nh<PERSON>p cách t<PERSON>h toán kết quả", "pleaseEnterCalculatorResultOther": "<PERSON><PERSON> lòng nh<PERSON>p cách t<PERSON>h toán kết quả khác", "pleaseEnterConnectionToSystem": "<PERSON><PERSON> lòng nh<PERSON>p cách kết nối với hệ thống", "pleaseEnterConfigIntegration": "<PERSON><PERSON> lòng nhập cấu hình tích hợp", "pleaseEnterTargetScale": "<PERSON><PERSON> lòng nhập tỷ lệ điểm mục tiêu", "pleaseEnterWeight": "<PERSON><PERSON> lòng nhập trọng số", "pleaseEnterMinimumValue": "<PERSON><PERSON> lòng nhập giá trị tối thiểu", "pleaseEnterMaximumValue": "<PERSON><PERSON> lòng nhập giá trị tối đa", "pleaseEnterUnit": "<PERSON><PERSON> lòng nh<PERSON>p đơn vị", "pleaseEnterGoalCode": "<PERSON><PERSON> lòng nhập mã mục tiêu", "pleaseEnterGoalName": "<PERSON><PERSON> lòng nhập tên mục tiêu", "pleaseEnterGoalGroup": "<PERSON><PERSON> lòng nhập nhóm mục tiêu", "Weight": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "GoalCode": "<PERSON><PERSON> mục tiêu", "MinimumValue": "<PERSON><PERSON><PERSON> trị tối thiểu", "MaximumValue": "<PERSON><PERSON><PERSON> trị tối đa", "MeasurementRange": "<PERSON><PERSON><PERSON><PERSON> đo", "NoMeasurement": "<PERSON><PERSON><PERSON><PERSON> đo", "TargetScale": "<PERSON><PERSON> đo mục tiêu", "AIFormulaSuggestions": "<PERSON><PERSON><PERSON> ý công thức từ AI", "refresh": "<PERSON><PERSON><PERSON>", "warningConfigConnectSystem": "<PERSON><PERSON><PERSON>ng thể cấu hình nguồn dữ liệu khi chọn cách tính kết quả 'Tự nhập thủ công'", "AddNewSource": "+ <PERSON>hê<PERSON> mới nguồn", "NoConnect": "<PERSON><PERSON><PERSON><PERSON> kết n<PERSON>i", "cancelSelect": "<PERSON><PERSON><PERSON>", "selectGoalGroup": "<PERSON><PERSON><PERSON> nh<PERSON>m mục tiêu", "ConfigureAllocationFormula": "<PERSON><PERSON><PERSON> hình công thức phân bổ", "ConfigFormulaAllocationByCorp": "<PERSON><PERSON><PERSON> hình công thức phân bổ theo tổ chức", "ConfigFormulaAllocationByEmployee": "<PERSON><PERSON><PERSON> hình công thức phân bổ theo nhân viên", "ConfigFormulaAllocationByTime": "<PERSON><PERSON><PERSON> hình công thức phân bổ theo thời gian"}, "GoalPeriod": {"Title": "<PERSON><PERSON><PERSON> ti<PERSON>u", "GoalName": "<PERSON><PERSON><PERSON> mục tiêu", "InputGoalName": "<PERSON><PERSON><PERSON><PERSON> tên mục tiêu", "GoalDetail": "<PERSON> tiết mục tiêu", "GoalGroup": "<PERSON><PERSON><PERSON><PERSON> mục tiêu", "Implementation": "<PERSON><PERSON><PERSON><PERSON>", "Representative": "<PERSON><PERSON><PERSON>", "Quarter1": "Quý 1", "Quarter2": "Quý 2", "Quarter3": "Quý 3", "Quarter4": "Quý 4", "Month1": "Tháng 1", "Month2": "Tháng 2", "Month3": "Tháng 3", "Month4": "Tháng 4", "Month5": "Tháng 5", "Month6": "Tháng 6", "Month7": "Tháng 7", "Month8": "Tháng 8", "Month9": "Tháng 9", "Month10": "Tháng 10", "Month11": "Tháng 11", "Month12": "Tháng 12", "TotalTarget": "<PERSON><PERSON><PERSON> ti<PERSON>", "ProfileName": "Nhân viên", "Type": "<PERSON><PERSON><PERSON>", "Department": "Phòng ban", "Trend": "<PERSON>", "Target": "Chỉ tiêu", "Unit": "Đơn vị", "Status": "<PERSON><PERSON><PERSON><PERSON> thái", "Process": "<PERSON><PERSON><PERSON><PERSON> độ", "Allocation": "<PERSON><PERSON> bổ", "Allocated": "<PERSON><PERSON> phân bổ", "PersonalObjective": "<PERSON><PERSON><PERSON> tiêu cá nhân", "DepartmentObjective": "<PERSON><PERSON><PERSON> ti<PERSON> bộ phận", "ExecutionObject": "<PERSON><PERSON><PERSON> t<PERSON> thự<PERSON> hi<PERSON>n", "ConfirmApprove": "<PERSON><PERSON><PERSON>n du<PERSON> mục tiêu", "Reject": "<PERSON><PERSON> chối", "Confirm": "<PERSON><PERSON><PERSON>", "Cancel": "<PERSON><PERSON><PERSON>", "Period": "<PERSON><PERSON> thực hi<PERSON>n", "InputPeriod": "<PERSON><PERSON><PERSON><PERSON> kỳ thực hiện", "TargetValue": "<PERSON><PERSON><PERSON> trị cần phân bổ", "InputTargetValue": "<PERSON><PERSON><PERSON><PERSON> giá trị", "AllocationType": "<PERSON><PERSON> bổ theo", "Employee": "<PERSON><PERSON><PERSON> viên thự<PERSON> hi<PERSON>n", "InputEmployee": "<PERSON><PERSON><PERSON> nhân viên thực hiện", "Reminder": "Nhắc nhở", "RequestAdjust": "<PERSON><PERSON> xuất", "Approve": "<PERSON><PERSON><PERSON><PERSON>", "Adjust": "<PERSON><PERSON><PERSON><PERSON> chỉnh", "InformationDetail": "Th<PERSON>ng tin chi tiết", "CalculationMethod": "<PERSON><PERSON><PERSON> t<PERSON>h kết quả", "Description": "<PERSON><PERSON> tả mục tiêu", "Discussion": "<PERSON><PERSON><PERSON><PERSON>", "History": "<PERSON><PERSON><PERSON> sử hoạt động", "GoalProgress": "<PERSON><PERSON><PERSON><PERSON> độ mục tiêu", "TotalRevenue": "<PERSON><PERSON>ng doanh thu", "Tree": "<PERSON><PERSON><PERSON> mụ<PERSON> tiêu", "List": "<PERSON><PERSON>", "Table": "<PERSON><PERSON><PERSON>", "GoalPersonal": "<PERSON><PERSON><PERSON> tiêu thành viên", "GoalDepartment": "<PERSON><PERSON><PERSON> ti<PERSON> bộ phận", "All": "<PERSON><PERSON><PERSON> c<PERSON>", "WaitingConfirm": "<PERSON>ờ x<PERSON>c n<PERSON>n", "Confirmed": "<PERSON><PERSON> x<PERSON>c <PERSON>n", "Rejected": "<PERSON><PERSON> chối", "Registered": "Chỉ tiêu đăng ký", "GoalRegister": "Số lượng đăng ký", "GoalConfirm": "Số lư<PERSON> x<PERSON>n", "GoalAllocation": "Số lư<PERSON> đ<PERSON><PERSON><PERSON> giao", "Weight": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "Group": "Nhóm", "AddGoal": "<PERSON><PERSON><PERSON><PERSON> mục tiêu", "Month": "<PERSON><PERSON><PERSON><PERSON>", "Quarter": "<PERSON><PERSON><PERSON>", "Year": "Năm", "HalfYear": "<PERSON><PERSON><PERSON> n<PERSON>", "Custom": "<PERSON><PERSON><PERSON>", "AllocationDetail": "<PERSON><PERSON> bổ mục tiêu chi tiết", "AllocationChild": "<PERSON><PERSON> bổ mục tiêu con", "WaitingApprove": "<PERSON>ờ <PERSON>", "Approved": "Đ<PERSON>", "ApprovalTarget": "Chỉ tiêu phê du<PERSON>t", "ApprovalComment": "<PERSON><PERSON><PERSON>", "ApproveGoal": "<PERSON><PERSON> mục tiêu", "AllocationCycle": "<PERSON><PERSON> bổ chu kỳ", "AllocationCycleDescription": "<PERSON><PERSON><PERSON> cách phân bổ mục tiêu theo thời gian", "AllocationPersonDepartment": "<PERSON><PERSON> bổ theo bộ phận/ cá nhân", "AllocationPersonDepartmentDescription": "<PERSON><PERSON>n đối tượng nhận phân bổ và thiết lập chi tiết", "AllocationMethod": "<PERSON><PERSON><PERSON> th<PERSON><PERSON> phân bổ", "DoneTarget": "<PERSON><PERSON> thực hi<PERSON>n", "JobTitle": "<PERSON><PERSON><PERSON> danh", "Position": "<PERSON><PERSON><PERSON> v<PERSON>", "Guideline": "H<PERSON>ớng dẫn thực hiện"}, "GoalCycle": {"GoalCycle": "Chu kỳ đánh giá", "Code": "<PERSON>ã chu kỳ", "Name": "<PERSON><PERSON><PERSON> chu kỳ", "TypeName": "<PERSON><PERSON><PERSON> chu kỳ", "Type": "<PERSON><PERSON><PERSON> chu kỳ", "btnAddGoalCycle": "<PERSON><PERSON><PERSON><PERSON> chu kỳ", "CreatedDate": "<PERSON><PERSON><PERSON>", "LastUpdatedDate": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> nhật cu<PERSON>i", "LastUpdatedBy": "<PERSON><PERSON><PERSON><PERSON> cập nhật cu<PERSON>i", "TimeOfCycle": "<PERSON><PERSON><PERSON><PERSON> gian", "btnSave": "<PERSON><PERSON><PERSON>", "btnCancel": "<PERSON><PERSON><PERSON>", "addGoalCycle": "<PERSON><PERSON><PERSON><PERSON> mới chu kỳ", "pleaseEnterCode": "<PERSON><PERSON> lòng nhập mã chu kỳ", "pleaseEnterName": "<PERSON><PERSON> lòng nhập tên chu kỳ", "pleaseEnterTypeName": "<PERSON><PERSON> lòng nh<PERSON>p lo<PERSON>i chu kỳ", "TimeFrom": "Từ", "TimeTo": "<PERSON><PERSON><PERSON>", "Quarter": "<PERSON><PERSON><PERSON>", "Year": "Năm", "Month": "<PERSON><PERSON><PERSON><PERSON>"}, "GoalResult": {"Title": "<PERSON><PERSON><PERSON> qu<PERSON>", "Extend": "<PERSON><PERSON>", "Passed": "Đạt", "Failed": "<PERSON><PERSON>a đ<PERSON>", "Exceeded": "<PERSON><PERSON><PERSON><PERSON>", "UpdateDate": "<PERSON><PERSON><PERSON> c<PERSON>", "DueDate": "<PERSON>ạ<PERSON> đ<PERSON> giá"}, "EvaPerformanceAppraisals": {"formatNumberTemplate": "{{n}} mẫu đánh giá", "warningEmployeeNoTemplate": "<PERSON><PERSON><PERSON> báo: {{n}} nhân viên chưa có mẫu đánh giá.", "additional": "<PERSON><PERSON> sung", "statusTab": {"All": "<PERSON><PERSON><PERSON> c<PERSON>", "InProgress": "<PERSON><PERSON> th<PERSON> hi<PERSON>n", "Plan": "<PERSON><PERSON> ho<PERSON>", "Completed": "<PERSON><PERSON> hoàn thành"}, "addEmployee": {"title": "<PERSON><PERSON><PERSON><PERSON> nhân viên", "employeeInformation": "Thông tin nhân viên", "listOfEmployeesExpectedToParticipate": "<PERSON><PERSON> s<PERSON>ch nhân viên dự kiến tham gia đánh giá", "btnCreateList": "<PERSON><PERSON><PERSON> da<PERSON> s<PERSON>ch", "btnRefresh": "<PERSON><PERSON><PERSON>"}, "statusPublic": {"Plan": "<PERSON><PERSON> ho<PERSON>", "InProgress": "<PERSON><PERSON> th<PERSON> hi<PERSON>n", "Public": "<PERSON><PERSON><PERSON> khai", "Pending": "<PERSON><PERSON><PERSON>", "Canceled": "<PERSON><PERSON> hủy", "Completed": "<PERSON><PERSON><PERSON> th<PERSON>", "Published": "<PERSON><PERSON> công bố"}, "btnMore": {"edit": "Chỉnh sửa", "viewDetail": "<PERSON>em chi tiết", "clone": "Sao chép", "delete": "Xóa", "transferResult": "<PERSON><PERSON><PERSON><PERSON> kết quả"}, "quickFilter": {"TypeAppraisals": "Loại đ<PERSON>h giá", "Status": {"Label": "<PERSON><PERSON><PERSON><PERSON> thái", "Draft": "<PERSON>ưa đ<PERSON>h giá", "Submitted": "<PERSON><PERSON> th<PERSON> hi<PERSON>n", "InReview": "<PERSON><PERSON><PERSON> giá lại", "Completed": "<PERSON><PERSON> hoàn thành", "Cancel": "<PERSON><PERSON><PERSON> đ<PERSON> giá"}}, "MonthlySalaryReview": "<PERSON><PERSON><PERSON> giá t<PERSON>h lư<PERSON> tháng", "PeriodicSalaryReview": "<PERSON><PERSON><PERSON> giá xét tăng lương đ<PERSON>nh k<PERSON>", "PerformanceReview": "<PERSON><PERSON><PERSON> gi<PERSON> hiệu suất công việc", "RewardReview": "<PERSON><PERSON><PERSON> giá xét khen thưởng", "ProbationaryReview": "<PERSON><PERSON><PERSON> giá thử việc", "SuccessionPlanning": "<PERSON><PERSON><PERSON> giá quy hoạch kế nhiệm", "AppointmentReview": "<PERSON><PERSON><PERSON> g<PERSON><PERSON> b<PERSON> n<PERSON>m", "employeeParticipating": "<PERSON><PERSON><PERSON> viên tham gia", "implementingDepartment": "<PERSON><PERSON><PERSON> ban thực hiện", "noAppraisals": "<PERSON>ưa đ<PERSON>h giá", "inProgress": "<PERSON><PERSON> th<PERSON> hi<PERSON>n", "canceled": "<PERSON><PERSON><PERSON> đ<PERSON> giá", "reAppraisals": "<PERSON><PERSON><PERSON> giá lại", "btnAddAppraisal": "<PERSON><PERSON><PERSON><PERSON> mới", "title": "<PERSON><PERSON><PERSON> đ<PERSON> giá", "cycleName": "<PERSON><PERSON><PERSON> kỳ đ<PERSON>h giá", "totalEmployees": "Tổng số nhân viên", "completed": "<PERSON><PERSON> hoàn thành", "incomplete": "<PERSON><PERSON><PERSON> ho<PERSON>n thành", "departments": "Phòng ban <PERSON> dụng", "positions": "<PERSON><PERSON> trí công việc", "dueDate": "<PERSON>ạ<PERSON> đ<PERSON> giá", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "addAppraisal": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> giá", "editAppraisal": "Chỉnh sửa đợt đ<PERSON>h giá", "closed": "Đ<PERSON><PERSON>", "addNew": "<PERSON>h<PERSON><PERSON> mới đợt đ<PERSON>h giá", "edit": "Chỉnh sửa đợt đ<PERSON>h giá", "continueRegister": "<PERSON><PERSON><PERSON><PERSON> tục đ<PERSON>ng ký", "configFormValidatorRegister": "<PERSON><PERSON><PERSON> ký đợt đ<PERSON>h giá", "configFormValidatorEdit": "Chỉnh sửa đợt đ<PERSON>h giá", "EvaluationType": {"label": "Loại đ<PERSON>h giá", "placeholder": "<PERSON><PERSON> lòng ch<PERSON>n"}, "Description": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON> lòng nh<PERSON>p"}, "AnonymousAppraisals": {"label": "<PERSON><PERSON><PERSON> g<PERSON><PERSON>nh"}, "IsRepeatAppraisals": {"label": "Lặp lại đánh giá"}, "Status": {"label": "<PERSON><PERSON><PERSON><PERSON> thái", "placeholder": "<PERSON><PERSON> lòng ch<PERSON>n"}, "PeriodType": {"label": "<PERSON><PERSON> mục tiêu", "placeholder": "<PERSON><PERSON> lòng ch<PERSON>n"}, "PeriodDate": {"label": "<PERSON>h<PERSON>i gian đ<PERSON>h giá", "placeholder": {"from": "Từ", "to": "<PERSON><PERSON><PERSON>"}}, "ApplyObject": {"label": "<PERSON><PERSON><PERSON> t<PERSON> dụng", "placeholder": "<PERSON><PERSON><PERSON> đ<PERSON>i tư<PERSON> áp dụng"}, "ApplyDepartment": {"label": "Phòng ban <PERSON> dụng", "placeholder": "Chọn phòng ban áp dụng"}, "ApplyPosition": {"label": "<PERSON><PERSON> trí <PERSON> dụng", "placeholder": "<PERSON><PERSON><PERSON> vị trí <PERSON> dụng"}, "ApplyEmployee": {"label": "<PERSON><PERSON><PERSON> viên <PERSON> dụng", "placeholder": "<PERSON><PERSON><PERSON> nhân viên á<PERSON> dụng"}, "AppraisalName": {"label": "<PERSON><PERSON><PERSON> đ<PERSON>t đ<PERSON>h giá", "placeholder": "<PERSON><PERSON><PERSON><PERSON> tên đợt đ<PERSON>h giá"}, "AppraisalTemplate": {"label": "Mẫu phiếu đánh giá", "placeholder": "Chọn mẫu phiếu đánh giá"}, "AppraisalProcess": {"label": "<PERSON>uy trình đ<PERSON>h giá", "placeholder": "<PERSON><PERSON><PERSON> quy trình đ<PERSON>h giá"}, "EvaluationForm": {"label": "Mẫu đánh giá", "placeholder": "Chọn mẫu đánh giá"}, "grid": {"ProfileName": "Nhân viên", "JobPosition": "<PERSON><PERSON> trí công việc", "Department": "Phòng ban", "Status": "<PERSON><PERSON><PERSON><PERSON> thái", "EvaluationForm": "Mẫu đánh giá", "AppraisalsProcess": "<PERSON>uy trình đ<PERSON>h giá", "AppraisalsTime": "<PERSON>h<PERSON>i gian đ<PERSON>h giá", "SelfEvaluation": "Tự đánh giá", "EvaluatorName": "<PERSON><PERSON><PERSON><PERSON> lý đ<PERSON>h giá", "CriteriaName": "<PERSON><PERSON>n tiêu chí", "Direction": "<PERSON><PERSON><PERSON><PERSON>", "Target": "<PERSON><PERSON><PERSON> ti<PERSON>", "MinimumTarget": "<PERSON><PERSON><PERSON> tiêu tối thiểu", "Unit": "Đơn vị", "Weight": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "Actual": "<PERSON><PERSON><PERSON><PERSON> tế", "CompletionRate": "Tỷ lệ hoàn thành"}, "btnSendEvaluation": "<PERSON><PERSON><PERSON> đ<PERSON>h giá", "btnAddEmployee": "<PERSON><PERSON><PERSON><PERSON> nhân viên", "evaluationFormDetails": "<PERSON> tiết phiếu đánh giá"}, "EvaTemplate": {"template": "Mẫu đánh giá", "name": "Tên mẫu đánh giá", "enterName": "<PERSON><PERSON><PERSON><PERSON> tên mẫu đánh giá", "description": "<PERSON><PERSON>", "enterDescription": "<PERSON><PERSON><PERSON><PERSON> mô tả mẫu đánh giá", "evaName": "Tên mẫu đánh giá", "evaType": "Loại mẫu đánh giá", "btnAddEvaGroup": "<PERSON>h<PERSON><PERSON> nhóm đ<PERSON>h giá", "btnAddEvaTemplate": "<PERSON><PERSON><PERSON> mới", "selectTemplate": "<PERSON><PERSON><PERSON> mẫu", "createNewTemplate": "Tạo mới mẫu đánh giá", "createSuccess": "Tạo mới mẫu đánh giá thành công", "updateSuccess": "<PERSON><PERSON><PERSON> nhật mẫu đánh giá thành công", "deleteSuccess": "Xóa mẫu đánh giá thành công", "createTemplate": "Tạo mới mẫu đánh giá", "editTemplate": "Chỉnh sửa mẫu đánh giá", "viewDetail": "<PERSON>em chi tiết mẫu đánh giá", "deleteConfirm": "Bạn có chắc chắn muốn xóa mẫu đánh giá này không?", "startCreate": "<PERSON><PERSON><PERSON> đ<PERSON>u tạo", "applyFor": "<PERSON><PERSON> d<PERSON>ng cho", "method": "Phương ph<PERSON>p đ<PERSON>h giá", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "selectApplyFor": "<PERSON><PERSON><PERSON> dụng cho", "selectMethod": "<PERSON><PERSON>n ph<PERSON><PERSON><PERSON> pháp đ<PERSON>h giá", "department": "Phòng ban", "position": "<PERSON><PERSON> trí công việc", "selectDepartment": "<PERSON><PERSON><PERSON> phòng ban", "selectPosition": "<PERSON><PERSON><PERSON> vị trí công việc", "employee": "Nhân viên", "selectEmployee": "<PERSON><PERSON><PERSON> nhân viên", "templateType": "Loại mẫu đánh giá"}, "EvaCriteria": {"criteriaRepository": "<PERSON>ho tiêu chí", "criteriaType": "<PERSON><PERSON>i tiêu chí", "btnAddCriteriaGroup": "Thêm nhóm tiêu chí", "btnAddCriteriaDetail": "<PERSON>hê<PERSON> tiêu chí", "pleaseEnter": "<PERSON><PERSON> lòng nh<PERSON>p", "Description": "<PERSON><PERSON>", "InUse": "<PERSON><PERSON> sử dụng", "StopUse": "Ngưng sử dụng", "Code": "Mã tiêu chi", "Name": "<PERSON><PERSON>n tiêu chí", "TypeRank": "<PERSON><PERSON><PERSON>", "CriteriaGroup": "Nhóm tiêu chí", "DescriptionCalculatorScore": "<PERSON><PERSON> tả cách t<PERSON>h điểm", "Scale": "<PERSON><PERSON> đi<PERSON>", "UseRateinsteadOfScore": "<PERSON><PERSON> dụng xếp lo<PERSON>i thay cho điểm", "LinkToGoal": "<PERSON><PERSON><PERSON> kết với mục tiêu", "Note": "<PERSON><PERSON><PERSON>", "FileAttach": "<PERSON><PERSON><PERSON> tin đ<PERSON> k<PERSON>m", "selectAttachment": "<PERSON><PERSON><PERSON> tập tin lên", "selectCriteriaGroup": "<PERSON><PERSON><PERSON> nhóm tiêu chí", "cancelSelect": "<PERSON><PERSON><PERSON>", "AddNewCriteriaRate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "AddCriteria": "<PERSON>hê<PERSON> tiêu chí", "EditCriteria": "Chỉnh sửa tiêu chí", "btnAddCriteriaRate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "CriteriaDetail": "<PERSON> tiết tiêu chí"}, "EvaCriteriaGroup": {"CriteriaGroup": "Nhóm tiêu chí", "gridCriteriaGroupHeader": "Nhóm: {{name}} ({{n}} tiêu chí)", "Code": "Mã nhóm tiêu chí", "Name": "<PERSON><PERSON>n nhóm tiêu chí", "CriteriaType": "<PERSON><PERSON>i tiêu chí", "EditCriteriaGroup": "Chỉnh sửa nhóm tiêu chí", "AddCriteriaGroup": "Thêm nhóm tiêu chí"}, "EvaCriteriaType": {"Code": "Mã loại tiêu chí", "Name": "<PERSON><PERSON>n lo<PERSON>i tiêu chí", "CriteriaTypeCode": "Mã loại tiêu chí", "CriteriaTypeName": "<PERSON><PERSON>n lo<PERSON>i tiêu chí", "NumberOrder": "<PERSON><PERSON> thứ tự", "EditCriteriaType": "Chỉnh sửa loại tiêu chí", "AddCriteriaType": "<PERSON><PERSON><PERSON><PERSON> loại tiêu chí", "btnAddNew": "<PERSON><PERSON><PERSON><PERSON> loại tiêu chí", "Appraisals_Goal": "<PERSON><PERSON><PERSON> gi<PERSON> mục tiêu", "Appraisals_Competency": "<PERSON><PERSON><PERSON> giá năng lực", "Appraisals_360": "<PERSON><PERSON><PERSON> giá 360 độ", "Appraisals_Custom": "<PERSON><PERSON><PERSON> chỉnh", "CriteriaType": "<PERSON><PERSON><PERSON>"}, "EvaCriteriaRate": {"Scale": "<PERSON><PERSON> đi<PERSON>", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Description": "<PERSON><PERSON>", "AddNewCriteriaRate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EditCriteriaRate": "Chỉnh sửa xếp lo<PERSON>i", "AddCriteriaRate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "EvaConductAssessmentList": {"title": "<PERSON><PERSON> s<PERSON>ch thực hiện đ<PERSON>h giá"}, "EvaProposalList": {"title": "<PERSON><PERSON> xuất", "list": "<PERSON><PERSON>", "nineBox": "9-Box", "StatusView": "<PERSON><PERSON><PERSON><PERSON> thái", "Status": "<PERSON><PERSON><PERSON><PERSON> thái", "TotalAssessmentScore": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>h giá", "Proposal": "<PERSON><PERSON> xuất", "ConvertToTraining": "<PERSON><PERSON><PERSON><PERSON> sang đào tạo", "PotentialList": "<PERSON><PERSON> s<PERSON>ch tiềm năng", "Promotion": "<PERSON><PERSON> bạt thăng chức", "Grade": "<PERSON><PERSON><PERSON>", "ProfileName": "Nhân viên", "EmpCode": "Mã nhân viên", "PerformanceResult": "<PERSON><PERSON><PERSON> qu<PERSON> năng su<PERSON>t", "CompetencyResult": "<PERSON><PERSON><PERSON> qu<PERSON> năng lực", "Position": "<PERSON><PERSON> trí công việc", "Department": "Phòng ban", "AskAI": "Hỏi Reca", "ViewEmployeeList": "<PERSON><PERSON>ch", "titleViewEmployeeNineBoxDetail": "<PERSON><PERSON> s<PERSON>ch nhân viên: {{boxname}}", "nineBoxGrid": {"warningDeleteLabel": "<PERSON><PERSON><PERSON> dữ liệu đã dán nhãn", "warningDeleteLabelSup": "<PERSON>hãn dán này đang được sử dụng nếu xóa đi sẽ mất các nhãn đã được gán", "warningCancel": "<PERSON><PERSON><PERSON>", "warningAgree": "Đồng ý", "AddIDP": "Thêm IDP", "Add": "<PERSON><PERSON><PERSON><PERSON>", "MarkLabel": "<PERSON><PERSON><PERSON><PERSON> dán", "CancelIDPNomination": "<PERSON><PERSON><PERSON> cử IDP", "AddNewLabel": "<PERSON><PERSON><PERSON><PERSON> nhãn mới", "RemoveLabel": "Bỏ nhãn", "achievementExcellent": "<PERSON><PERSON><PERSON><PERSON> tích xuất s<PERSON>c", "achievementGood": "<PERSON><PERSON><PERSON><PERSON> tích tốt", "achievementBad": "<PERSON><PERSON><PERSON><PERSON> tích kém", "highCapacity": "<PERSON><PERSON><PERSON> l<PERSON> cao", "mediumCapacity": "<PERSON><PERSON><PERSON> lực trung bình", "lowCapacity": "<PERSON><PERSON><PERSON> l<PERSON>c hạn chế", "capacity": "NĂNG LỰC", "achievementResult": "HIỆU QUẢ CÔNG VIỆC", "high": "<PERSON>", "medium": "Vừa", "low": "<PERSON><PERSON><PERSON><PERSON>", "excellent": "<PERSON>", "good": "Vừa", "bad": "<PERSON><PERSON><PERSON><PERSON>", "employeeList": "Nhân viên", "employeeList1": "<PERSON><PERSON> s<PERSON>ch nhân viên", "employeeListTitle": "<PERSON><PERSON> s<PERSON>ch nhân viên ( {{value1}} - {{value2}}% )", "unitStructureAll": "<PERSON><PERSON><PERSON><PERSON>", "orgAll": "Phòng ban", "performanceTypeNew": "Loại đ<PERSON>h giá", "performancePlanNew": "<PERSON><PERSON><PERSON> đ<PERSON> giá", "NominateforIDP": "Ứng cử IDP"}}}, "formulaConfig": {"Config": "<PERSON><PERSON><PERSON> h<PERSON>nh", "copied": "Đã sao chép", "yourBrowserDoesNotSupportVoiceRecognition": "Tr<PERSON><PERSON> du<PERSON>t của bạn không hỗ trợ nhận dạng giọng nói", "voiceNotRecognizedPleaseTryAgain": "<PERSON><PERSON><PERSON>ng nhận dạng đượ<PERSON> giọng nói. <PERSON><PERSON> lòng thử lại", "logicAndConditional": "Logic & điều kiện", "functionIFRequires3ParametersSeparatedBy2Commas": "Hàm IF yêu cầu 3 tham số, c<PERSON>ch nhau bởi 2 dấu ph<PERSON>y", "functionANDRequiresAtLeast2ParametersSeparatedByCommas": "<PERSON><PERSON><PERSON> AND yêu cầu ít nhất 2 tham số, đ<PERSON><PERSON><PERSON> phân tách bằng dấu phẩy", "functionORRequiresAtLeast2ParametersSeparatedByCommas": "<PERSON><PERSON>m <PERSON> yêu cầu ít nhất 2 tham số, đ<PERSON><PERSON><PERSON> phân tách bằng dấu phẩy", "functionNOTRequires1Parameter": "Hàm NOT yêu cầu 1 tham số", "functionISNULLRequires1Parameter": "Hàm ISNULL yêu cầu 1 tham số", "functionIFERRORRequires2ParametersSeparatedByCommas": "Hàm IFERROR yêu cầu 2 tham số, phân tách bằng dấu phẩy", "functionSWITCHRequiresAtLeast2ParametersSeparatedByCommas": "Hàm SWITCH yê<PERSON> cầu ít nhất 2 tham số, đ<PERSON><PERSON><PERSON> phân tách bằng dấu phẩy", "mathAndArithmetic": "<PERSON><PERSON> họ<PERSON> & <PERSON><PERSON> học", "functionSUMRequiresAtLeast1ParametersSeparatedByCommas": "Hàm SUM yêu cầu ít nhất 1 tham số, đ<PERSON><PERSON><PERSON> phân tách bằng dấu phẩy", "functionSUMIFSRequiresAtLeast1ParametersSeparatedByCommas": "Hàm SUMIF yêu cầu ít nhất 1 tham số, đ<PERSON><PERSON><PERSON> phân tách bằng dấu phẩy", "functionCOUNTRequires1Parameter": "Hàm COUNT <PERSON><PERSON><PERSON> c<PERSON>u 1 tham số", "functionCOUNTIFSRequiresAtLeast1ParameterSeparatedByCommas": "Hàm COUNTIFS yêu cầu ít nhất 1 tham số, đ<PERSON><PERSON><PERSON> phân tách bằng dấu phẩy", "functionAVERAGERequiresAtLeast1ParameterSeparatedByCommas": "Hàm AVERAGE yêu cầu ít nhất 1 tham số, đ<PERSON><PERSON><PERSON> phân tách bằng dấu phẩy", "functionROUNDRequires2ParametersSeparatedByCommas": "Hàm ROUND yêu cầu 2 tham số, phân tách bằng dấu phẩy", "functionFLOORRequires1parameter": "Hàm FLOOR yêu cầu 1 tham số", "functionCEILINGRequires1parameter": "Hàm CEILING yêu cầu 1 tham số", "functionMODRequires2ParametersSeparatedByCommas": "<PERSON><PERSON><PERSON> yêu cầu 2 tham số, phân tách bằng dấu phẩy", "functionABSRequires1parameter": "Hàm ABS yêu cầu 1 tham số", "functionPOWERRequires2ParametersSeparatedByCommas": "Hàm POWER yêu cầu 2 tham số, phân tách bằng dấu phẩy", "functionSQRTRequires1parameter": "Hàm SQRT yêu cầu 1 tham số", "dateAndTime": "Ngày & giờ", "functionNOWDoesNotRequireParameters": "Hàm NOW không yêu cầu tham số", "functionTODAYDoesNotRequireParameters": "Hàm TODAY không yêu cầu tham số", "functionDATEDIFFRequires3ParametersSeparatedByCommas": "Hàm DATEDIFF yêu cầu 3 tham số, phân tách bằng dấu phẩy", "functionADDYEARRequires2ParametersSeparatedByCommas": "Hàm ADDYEAR yêu cầu 2 tham số, phân tách bằng dấu phẩy", "functionADDMONTHRequires2ParametersSeparatedByCommas": "Hàm ADDMONTH yêu cầu 2 tham số, phân tách bằng dấu phẩy", "functionADDDAYRequires2ParametersSeparatedByCommas": "Hàm ADDDAY yêu cầu 2 tham số, phân tách bằng dấu phẩy", "functionADDHOURRequires2ParametersSeparatedByCommas": "Hàm ADDHOUR yêu cầu 2 tham số, phân tách bằng dấu phẩy", "functionADDMINUTERequires2ParametersSeparatedByCommas": "Hàm ADDMINUTE yêu cầu 2 tham số, phân tách bằng dấu phẩy", "functionWEEKDAYRequires1Parameter": "Hàm WEEKDAY yêu cầu 1 tham số", "functionDAYRequires1Parameter": "Hàm DAY yêu cầu 1 tham số", "functionMONTHRequires1Parameter": "Hàm MONTH yêu cầu 1 tham số", "functionYEARRequires1Parameter": "Hàm YEAR yêu cầu 1 tham số", "stringProcessing": "<PERSON>ử lý chuỗi", "functionCONCATRequiresAtLeast1ParameterSeparatedByCommas": "Hàm CONCAT yêu cầu ít nhất 1 tham số, đ<PERSON><PERSON><PERSON> phân tách bằng dấu phẩy", "functionLEFTRequires2ParametersSeparatedByCommas": "Hàm LEFT yêu cầu 2 tham số, phân tách bằng dấu phẩy", "functionRIGHTRequires2ParametersSeparatedByCommas": "Hàm RIGHT yêu cầu 2 tham số, phân tách bằng dấu phẩy", "functionLENRequires1Parameter": "Hàm LEN yêu cầu 1 tham số", "functionTRIMRequires1Parameter": "Hàm TRIM yêu cầu 1 tham số", "functionLOWERRequires1Parameter": "Hàm LOWER yêu cầu 1 tham số", "functionUPPERRequires1Parameter": "Hàm UPPER yêu cầu 1 tham số", "functionSUBSTRINGRequires3ParametersSeparatedByCommas": "Hàm SUBSTRING yêu cầu 3 tham số, phân tách bằng dấu phẩy", "functionFINDRequires2ParametersSeparatedByCommas": "Hàm FIND yêu cầu 2 tham số, phân tách bằng dấu phẩy", "advancedCheck": "<PERSON><PERSON><PERSON> tra nâng cao", "functionINRequires2ParametersSeparatedByCommas": "Hàm IN yêu cầu 2 tham số, phân tách bằng dấu phẩy", "functionBETWEENRequires3ParametersSeparatedByCommas": "Hàm BETWEEN yêu cầu 2 tham số, phân tách bằng dấu phẩy", "functionEXISTSRequires1Parameter": "Hàm EXISTS yêu cầu 1 tham số", "functionTYPEOFRequires1Parameter": "Hàm TYPEOF yêu cầu 1 tham số", "functionFORMATRequires2ParametersSeparatedByCommas": "Hàm FORMAT y<PERSON><PERSON> cầu 2 tham số, phân tách bằng dấu phẩy", "operators": "<PERSON><PERSON> tử", "Equal": "Equal", "NotEqual": "Not equal", "GreaterThan": "Greater than", "LessThan": "Less than", "GreaterThanEqual": "Greater than equal", "LessThanEqual": "Less than equal", "Plus": "Plus", "Minus": "Minus", "Multiplication": "Multiplication", "Division": "Division", "Percent": "Percent", "missingOpeningParenthesis": "Thiếu dấu mở ngoặc đơn '('", "missingClosingParenthesis": "<PERSON><PERSON><PERSON>u dấu đóng ngoặc đơn '('", "thisSymbolIsNotSupported": "<PERSON><PERSON> tự {name} không được hỗ trợ", "invalidCharacterOutsideString": "<PERSON><PERSON> tự {name} kh<PERSON><PERSON> hợp lệ", "missingStringTerminator": "Chuỗi chưa có dấu đóng", "correctSyntax": "<PERSON><PERSON> ph<PERSON>p đ<PERSON>g {}", "invalidFunctionSyntax": "Lỗi cú pháp", "functionDoesNotExist": "<PERSON><PERSON><PERSON> không tồn tại", "suggestion": "G<PERSON><PERSON> ý", "enumDoesNotExist": "enum không tồn tại", "doNotUseTheFunctionInTheLogicalConditionOfIF": "<PERSON><PERSON>ông sử dụng hàm {name} trong điều kiện logic của IF", "errorDividingByZero": "Lỗi chia cho 0", "addEnum": "<PERSON>h<PERSON>m enum", "search": "<PERSON><PERSON><PERSON>", "formula": "<PERSON><PERSON><PERSON> thức", "aiSuggestionFormula": "AI gợi ý công thức", "descriptionToAiSuggestFormula": "<PERSON><PERSON> tả để AI gợi ý công thức", "functionAndOperator": "<PERSON><PERSON><PERSON> và toán tử", "cancelConfig": "<PERSON><PERSON><PERSON>", "saveConfig": "<PERSON><PERSON><PERSON>"}}