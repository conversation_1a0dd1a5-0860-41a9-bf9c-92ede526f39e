import { vnrUtilities } from '@hrm-frontend-workspace/vnr-module';

export const performanceAppraisalsDetailDataSource = [
  {
    ID: vnrUtilities.newGuid(),
    ProfileInfo: {
      ProfileName: 'Hoàng Thúy Dung',
      CodeEmp: 'MNV-00450',
      ImagePath: 'path/to/image',
      JobPosition: 'Chuyên viên Hỗ trợ <PERSON> thuật',
      Department: 'INFRA-Phòng Hạ tầng CNTT',
    },
    PerformanceID: 'd58f1107-5427-45bb-8f33-0b42e2aaa3bb',
    Status: 'Draft',
    StatusView: 'Chưa đánh giá',
    EvaluationForm: '',
    JobPosition: 'Chuyên viên Hỗ trợ <PERSON> thuậ<PERSON>',
    Department: 'INFRA-Phòng Hạ tầng CNTT',
    AppraisalsProcess: 'Đánh giá tuần tự',
    DueDate: new Date('2025-06-20'),
    PeriodDate: new Date('2025-06-01'),
    ProfileID1: '1',
  },
  {
    ID: vnrUtilities.newGuid(),
    ProfileInfo: {
      ProfileName: '<PERSON><PERSON><PERSON><PERSON>',
      CodeEmp: 'MNV-00123',
      ImagePath: 'path/to/image',
      JobPosition: 'Chuyên viên Kiểm thử Phần mềm',
      Department: 'INFRA-Phòng Hạ tầng CNTT',
    },
    PerformanceID: 'd58f1107-5427-45bb-8f33-0b42e2aaa3bb',
    Status: 'Submitted',
    StatusView: 'Đang thực hiện',
    EvaluationForm: 'Chuyên viên Kiểm thử Phần mềm',
    JobPosition: 'Chuyên viên Hỗ trợ Kỹ thuật',
    Department: 'INFRA-Phòng Hạ tầng CNTT',
    AppraisalsProcess: 'Đánh giá tuần tự',
    DueDate: new Date('2025-06-15'),
    PeriodDate: new Date('2025-06-01'),
    ProfileID1: '2',
  },
  {
    ID: vnrUtilities.newGuid(),
    ProfileInfo: {
      ProfileName: 'Đặng Nhật Minh',
      CodeEmp: 'MNV-00321',
      ImagePath: 'path/to/image',
      JobPosition: 'Quản lý Phát triển Phần mềm',
      Department: 'INFRA-Phòng Hạ tầng CNTT',
    },
    PerformanceID: 'b1a2c3d4-e5f6-4a1b-9c8d-1234567890ab',
    Status: 'InReview',
    StatusView: 'Đánh giá lại',
    EvaluationForm: 'Chuyên viên Kiểm thử Phần mềm',
    JobPosition: 'Chuyên viên Hỗ trợ Kỹ thuật',
    Department: 'INFRA-Phòng Hạ tầng CNTT',
    AppraisalsProcess: 'Đánh giá tuần tự',
    DueDate: new Date('2025-06-15'),
    PeriodDate: new Date('2025-06-05'),
    ProfileID1: '3',
  },
  {
    ID: vnrUtilities.newGuid(),
    ProfileInfo: {
      ProfileName: 'Lê Phương Linh',
      CodeEmp: 'MNV-00854',
      ImagePath: 'path/to/image',
      JobPosition: 'Kỹ sư Backend',
      Department: 'INFRA-Phòng Hạ tầng CNTT',
    },
    PerformanceID: 'b1a2c3d4-e5f6-421b-9c8d-1234567890ad',
    Status: 'Completed',
    StatusView: 'Đã hoàn thành',
    EvaluationForm: 'Chuyên viên Kiểm thử Phần mềm',
    JobPosition: 'Kỹ sư Backend',
    Department: 'INFRA-Phòng Hạ tầng CNTT',
    AppraisalsProcess: 'Đánh giá tuần tự',
    DueDate: new Date('2025-06-25'),
    PeriodDate: new Date('2025-06-10'),
    ProfileID1: '4',
  },
  {
    ID: vnrUtilities.newGuid(),
    ProfileInfo: {
      ProfileName: 'Đặng Bích Thảo',
      CodeEmp: 'MNV-00987',
      ImagePath: 'path/to/image',
      JobPosition: 'Chuyên viên Quản lý Khách hàng',
      Department: 'INFRA-Phòng Hạ tầng CNTT',
    },
    PerformanceID: 'b1a2c3d4-e5f6-4a1b-9c8d-1434567890ae',
    Status: 'Cancel',
    StatusView: 'Hủy đánh giá',
    EvaluationForm: 'Chuyên viên Kiểm thử Phần mềm',
    JobPosition: 'Chuyên viên Quản lý Khách hàng',
    Department: 'INFRA-Phòng Hạ tầng CNTT',
    AppraisalsProcess: 'Đánh giá tuần tự',
    DueDate: new Date('2025-06-15'),
    PeriodDate: new Date('2025-06-10'),
    ProfileID1: '5',
  },
  {
    ID: vnrUtilities.newGuid(),
    ProfileInfo: {
      ProfileName: 'Bùi Minh Khang',
      CodeEmp: 'MNV-01234',
      ImagePath: 'path/to/image',
      JobPosition: 'Nhà Phân tích Hệ thống',
      Department: 'INFRA-Phòng Hạ tầng CNTT',
    },
    PerformanceID: 'b1a2c3d4-e5f6-4a1b-9c8d-1234567890af',
    Status: 'Draft',
    StatusView: 'Chưa đánh giá',
    EvaluationForm: 'Chuyên viên Kiểm thử Phần mềm',
    JobPosition: 'Nhà Phân tích Hệ thống',
    Department: 'INFRA-Phòng Hạ tầng CNTT',
    AppraisalsProcess: 'Đánh giá tuần tự',
    DueDate: new Date('2025-06-20'),
    PeriodDate: new Date('2025-06-11'),
    ProfileID1: '6',
  },
  {
    ID: vnrUtilities.newGuid(),
    ProfileInfo: {
      ProfileName: 'Hoàng Minh Khang',
      CodeEmp: 'MNV-00789',
      ImagePath: 'path/to/image',
      JobPosition: 'Chuyên viên Đào tạo Công nghệ',
      Department: 'INFRA-Phòng Hạ tầng CNTT',
    },
    PerformanceID: 'b1a2c3d4-e5f6-4a1b-9c8d-1234567q90af',
    Status: 'Submitted',
    StatusView: 'Đang thực hiện',
    EvaluationForm: 'Chuyên viên Kiểm thử Phần mềm',
    JobPosition: 'Chuyên viên Đào tạo Công nghệ',
    Department: 'INFRA-Phòng Hạ tầng CNTT',
    AppraisalsProcess: 'Đánh giá tuần tự',
    DueDate: new Date('2025-06-15'),
    PeriodDate: new Date('2025-06-10'),
  },
  {
    ID: vnrUtilities.newGuid(),
    ProfileInfo: {
      ProfileName: 'Phạm Tuấn Kiệt',
      CodeEmp: 'MNV-00234',
      ImagePath: 'path/to/image',
      JobPosition: 'Chuyên gia Tối ưu Hóa Công Cụ',
      Department: 'INFRA-Phòng Hạ tầng CNTT',
    },
    PerformanceID: 'b1a2c3d4-e5f6-4a1b-9c8d-1234567q90af',
    Status: 'Submitted',
    StatusView: 'Đang thực hiện',
    EvaluationForm: 'Chuyên viên Kiểm thử Phần mềm',
    JobPosition: 'Chuyên gia Tối ưu Hóa Công Cụ',
    Department: 'INFRA-Phòng Hạ tầng CNTT',
    AppraisalsProcess: 'Đánh giá tuần tự',
    DueDate: new Date('2025-06-15'),
    PeriodDate: new Date('2025-06-04'),
  },
  {
    ID: vnrUtilities.newGuid(),
    ProfileInfo: {
      ProfileName: 'Lê Hải Đăng',
      CodeEmp: 'MNV-00890',
      ImagePath: 'path/to/image',
      JobPosition: 'Kỹ sư Hệ thống',
      Department: 'INFRA-Phòng Hạ tầng CNTT',
    },
    PerformanceID: 'b1a2c3d4-e5f6-4a1b-9c8d-1234567q90af',
    Status: 'Draft',
    StatusView: 'Chưa đánh giá',
    EvaluationForm: 'Chuyên viên Kiểm thử Phần mềm',
    JobPosition: 'Kỹ sư Hệ thống',
    Department: 'INFRA-Phòng Hạ tầng CNTT',
    AppraisalsProcess: 'Đánh giá tuần tự',
    DueDate: new Date('2025-06-15'),
    PeriodDate: new Date('2025-06-05'),
  },
  {
    ID: vnrUtilities.newGuid(),
    ProfileInfo: {
      ProfileName: 'Trần Mỹ Linh',
      CodeEmp: 'MNV-00101',
      ImagePath: 'path/to/image',
      JobPosition: 'Chuyên viên Quản lý Tài khoản',
      Department: 'INFRA-Phòng Hạ tầng CNTT',
    },
    PerformanceID: 'b1a2c3d4-e5f6-4a1b-9c8d-1234567q90af',
    Status: 'Submitted',
    StatusView: 'Đang thực hiện',
    EvaluationForm: 'Chuyên viên Kiểm thử Phần mềm',
    JobPosition: 'Chuyên viên Quản lý Tài khoản',
    Department: 'INFRA-Phòng Hạ tầng CNTT',
    AppraisalsProcess: 'Đánh giá tuần tự',
    DueDate: new Date('2025-06-15'),
    PeriodDate: new Date('2025-06-05'),
  },
  {
    ID: vnrUtilities.newGuid(),
    ProfileInfo: {
      ProfileName: 'Lê Hoàng Phúc',
      CodeEmp: 'MNV-00567',
      ImagePath: 'path/to/image',
      JobPosition: 'Giám đốc Nhân sự',
      Department: 'INFRA-Phòng Hạ tầng CNTT',
    },
    PerformanceID: 'b1a2c3d4-e5f6-4a1b-9c8d-1234567q90af',
    Status: 'Draft',
    StatusView: 'Chưa đánh giá',
    EvaluationForm: 'Chuyên viên Kiểm thử Phần mềm',
    JobPosition: 'Giám đốc Nhân sự',
    Department: 'INFRA-Phòng Hạ tầng CNTT',
    AppraisalsProcess: 'Đánh giá tuần tự',
    DueDate: new Date('2025-06-20'),
    PeriodDate: new Date('2025-06-10'),
  },
  {
    ID: vnrUtilities.newGuid(),
    ProfileInfo: {
      ProfileName: 'Trần Hà Quỳnh Chi',
      CodeEmp: 'MNV-00890',
      ImagePath: 'path/to/image',
      JobPosition: 'Giám đốc Tài chính',
      Department: 'INFRA-Phòng Hạ tầng CNTT',
    },
    PerformanceID: 'b1a2c3d4-e5f6-4a1b-9c8d-1234567q90af',
    Status: 'Submitted',
    StatusView: 'Đang thực hiện',
    EvaluationForm: 'Chuyên viên Kiểm thử Phần mềm',
    JobPosition: 'Giám đốc Tài chính',
    Department: 'INFRA-Phòng Hạ tầng CNTT',
    AppraisalsProcess: 'Đánh giá tuần tự',
    DueDate: new Date('2025-06-30'),
    PeriodDate: new Date('2025-06-01'),
  },
];

export const performanceAppraisalsEmployeeDataSource = [
  {
    ID: vnrUtilities.newGuid(),
    ProfileName: 'Hoàng Thúy Dung',
    ProfileInfo: {
      ProfileName: 'Hoàng Thúy Dung',
      CodeEmp: 'MNV-00450',
      ImagePath: 'path/to/image',
      JobPosition: 'Chuyên viên Hỗ trợ Kỹ thuật',
      Department: 'INFRA-Phòng Hạ tầng CNTT',
    },
    PerformanceID: '',
    Status: 'Draft',
    StatusView: 'Chưa đánh giá',
    EvaluationForm: '',
    JobPosition: 'Chuyên viên Hỗ trợ Kỹ thuật',
    Department: 'INFRA-Phòng Hạ tầng CNTT',
    AppraisalsProcess: 'Đánh giá tuần tự',
  },
  {
    ID: vnrUtilities.newGuid(),
    ProfileName: 'Nguyễn Ngọc Hân',
    ProfileInfo: {
      ProfileName: 'Nguyễn Ngọc Hân',
      CodeEmp: 'MNV-00123',
      ImagePath: 'path/to/image',
      JobPosition: 'Chuyên viên Kiểm thử Phần mềm',
      Department: 'INFRA-Phòng Hạ tầng CNTT',
    },
    PerformanceID: 'd58f1107-5427-45bb-8f33-0b42e2aaa3bb',
    Status: 'Draft',
    StatusView: 'Chưa đánh giá',
    EvaluationForm: 'Chuyên viên Kiểm thử Phần mềm',
    JobPosition: 'Chuyên viên Hỗ trợ Kỹ thuật',
    Department: 'INFRA-Phòng Hạ tầng CNTT',
    AppraisalsProcess: 'Đánh giá tuần tự',
  },
  {
    ID: vnrUtilities.newGuid(),
    ProfileName: 'Đặng Nhật Minh',
    ProfileInfo: {
      ProfileName: 'Đặng Nhật Minh',
      CodeEmp: 'MNV-00321',
      ImagePath: 'path/to/image',
      JobPosition: 'Quản lý Phát triển Phần mềm',
      Department: 'INFRA-Phòng Hạ tầng CNTT',
    },
    PerformanceID: 'b1a2c3d4-e5f6-4a1b-9c8d-1234567890ab',
    Status: 'Draft',
    StatusView: 'Chưa đánh giá',
    EvaluationForm: 'Chuyên viên Kiểm thử Phần mềm',
    JobPosition: 'Chuyên viên Hỗ trợ Kỹ thuật',
    Department: 'INFRA-Phòng Hạ tầng CNTT',
    AppraisalsProcess: 'Đánh giá tuần tự',
  },
  {
    ID: vnrUtilities.newGuid(),
    ProfileName: 'Lê Phương Linh',
    ProfileInfo: {
      ProfileName: 'Lê Phương Linh',
      CodeEmp: 'MNV-00854',
      ImagePath: 'path/to/image',
      JobPosition: 'Kỹ sư Backend',
      Department: 'INFRA-Phòng Hạ tầng CNTT',
    },
    PerformanceID: 'b1a2c3d4-e5f6-421b-9c8d-1234567890ad',
    Status: 'Draft',
    StatusView: 'Chưa đánh giá',
    EvaluationForm: 'Chuyên viên Kiểm thử Phần mềm',
    JobPosition: 'Kỹ sư Backend',
    Department: 'INFRA-Phòng Hạ tầng CNTT',
    AppraisalsProcess: 'Đánh giá tuần tự',
  },
  {
    ID: vnrUtilities.newGuid(),
    ProfileName: 'Đặng Bích Thảo',
    ProfileInfo: {
      ProfileName: 'Đặng Bích Thảo',
      CodeEmp: 'MNV-00987',
      ImagePath: 'path/to/image',
      JobPosition: 'Chuyên viên Quản lý Khách hàng',
      Department: 'INFRA-Phòng Hạ tầng CNTT',
    },
    PerformanceID: 'b1a2c3d4-e5f6-4a1b-9c8d-1434567890ae',
    Status: 'Draft',
    StatusView: 'Chưa đánh giá',
    EvaluationForm: 'Chuyên viên Kiểm thử Phần mềm',
    JobPosition: 'Chuyên viên Quản lý Khách hàng',
    Department: 'INFRA-Phòng Hạ tầng CNTT',
    AppraisalsProcess: 'Đánh giá tuần tự',
  },
  {
    ID: vnrUtilities.newGuid(),
    ProfileName: 'Bùi Minh Khang',
    ProfileInfo: {
      ProfileName: 'Bùi Minh Khang',
      CodeEmp: 'MNV-01234',
      ImagePath: 'path/to/image',
      JobPosition: 'Nhà Phân tích Hệ thống',
      Department: 'INFRA-Phòng Hạ tầng CNTT',
    },
    PerformanceID: 'b1a2c3d4-e5f6-4a1b-9c8d-1234567890af',
    Status: 'Draft',
    StatusView: 'Chưa đánh giá',
    EvaluationForm: 'Chuyên viên Kiểm thử Phần mềm',
    JobPosition: 'Nhà Phân tích Hệ thống',
    Department: 'INFRA-Phòng Hạ tầng CNTT',
    AppraisalsProcess: 'Đánh giá tuần tự',
  },
  {
    ID: vnrUtilities.newGuid(),
    ProfileName: 'Hoàng Minh Khang',
    ProfileInfo: {
      ProfileName: 'Hoàng Minh Khang',
      CodeEmp: 'MNV-00789',
      ImagePath: 'path/to/image',
      JobPosition: 'Chuyên viên Đào tạo Công nghệ',
      Department: 'INFRA-Phòng Hạ tầng CNTT',
    },
    PerformanceID: 'b1a2c3d4-e5f6-4a1b-9c8d-1234567q90af',
    Status: 'Draft',
    StatusView: 'Chưa đánh giá',
    EvaluationForm: 'Chuyên viên Kiểm thử Phần mềm',
    JobPosition: 'Chuyên viên Đào tạo Công nghệ',
    Department: 'INFRA-Phòng Hạ tầng CNTT',
    AppraisalsProcess: 'Đánh giá tuần tự',
  },
  {
    ID: vnrUtilities.newGuid(),
    ProfileName: 'Phạm Tuấn Kiệt',
    ProfileInfo: {
      ProfileName: 'Phạm Tuấn Kiệt',
      CodeEmp: 'MNV-00234',
      ImagePath: 'path/to/image',
      JobPosition: 'Chuyên gia Tối ưu Hóa Công Cụ',
      Department: 'INFRA-Phòng Hạ tầng CNTT',
    },
    PerformanceID: 'b1a2c3d4-e5f6-4a1b-9c8d-1234567q90af',
    Status: 'Draft',
    StatusView: 'Chưa đánh giá',
    EvaluationForm: 'Chuyên viên Kiểm thử Phần mềm',
    JobPosition: 'Chuyên gia Tối ưu Hóa Công Cụ',
    Department: 'INFRA-Phòng Hạ tầng CNTT',
    AppraisalsProcess: 'Đánh giá tuần tự',
  },
  {
    ID: vnrUtilities.newGuid(),
    ProfileName: 'Lê Hải Đăng',
    ProfileInfo: {
      ProfileName: 'Lê Hải Đăng',
      CodeEmp: 'MNV-00890',
      ImagePath: 'path/to/image',
      JobPosition: 'Kỹ sư Hệ thống',
      Department: 'INFRA-Phòng Hạ tầng CNTT',
    },
    PerformanceID: 'b1a2c3d4-e5f6-4a1b-9c8d-1234567q90af',
    Status: 'Draft',
    StatusView: 'Chưa đánh giá',
    EvaluationForm: 'Chuyên viên Kiểm thử Phần mềm',
    JobPosition: 'Kỹ sư Hệ thống',
    Department: 'INFRA-Phòng Hạ tầng CNTT',
    AppraisalsProcess: 'Đánh giá tuần tự',
  },
  {
    ID: vnrUtilities.newGuid(),
    ProfileName: 'Trần Mỹ Linh',
    ProfileInfo: {
      ProfileName: 'Trần Mỹ Linh',
      CodeEmp: 'MNV-00101',
      ImagePath: 'path/to/image',
      JobPosition: 'Chuyên viên Quản lý Tài khoản',
      Department: 'INFRA-Phòng Hạ tầng CNTT',
    },
    PerformanceID: 'b1a2c3d4-e5f6-4a1b-9c8d-1234567q90af',
    Status: 'Draft',
    StatusView: 'Chưa đánh giá',
    EvaluationForm: 'Chuyên viên Kiểm thử Phần mềm',
    JobPosition: 'Chuyên viên Quản lý Tài khoản',
    Department: 'INFRA-Phòng Hạ tầng CNTT',
    AppraisalsProcess: 'Đánh giá tuần tự',
  },
  {
    ID: vnrUtilities.newGuid(),
    ProfileName: 'Lê Hoàng Phúc',
    ProfileInfo: {
      ProfileName: 'Lê Hoàng Phúc',
      CodeEmp: 'MNV-00567',
      ImagePath: 'path/to/image',
      JobPosition: 'Giám đốc Nhân sự',
      Department: 'INFRA-Phòng Hạ tầng CNTT',
    },
    PerformanceID: 'b1a2c3d4-e5f6-4a1b-9c8d-1234567q90af',
    Status: 'Draft',
    StatusView: 'Chưa đánh giá',
    EvaluationForm: 'Chuyên viên Kiểm thử Phần mềm',
    JobPosition: 'Giám đốc Nhân sự',
    Department: 'INFRA-Phòng Hạ tầng CNTT',
    AppraisalsProcess: 'Đánh giá tuần tự',
  },
  {
    ID: vnrUtilities.newGuid(),
    ProfileName: 'Trần Hà Quỳnh Chi',
    ProfileInfo: {
      ProfileName: 'Trần Hà Quỳnh Chi',
      CodeEmp: 'MNV-00890',
      ImagePath: 'path/to/image',
      JobPosition: 'Giám đốc Tài chính',
      Department: 'INFRA-Phòng Hạ tầng CNTT',
    },
    PerformanceID: 'b1a2c3d4-e5f6-4a1b-9c8d-1234567q90af',
    Status: 'Draft',
    StatusView: 'Chưa đánh giá',
    EvaluationForm: 'Chuyên viên Kiểm thử Phần mềm',
    JobPosition: 'Giám đốc Tài chính',
    Department: 'INFRA-Phòng Hạ tầng CNTT',
    AppraisalsProcess: 'Đánh giá tuần tự',
  },
];
