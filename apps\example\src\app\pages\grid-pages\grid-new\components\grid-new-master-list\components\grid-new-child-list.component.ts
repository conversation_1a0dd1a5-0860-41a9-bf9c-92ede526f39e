import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  Input,
  OnInit,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';
import { UntypedFormBuilder } from '@angular/forms';
import { VnrGridNewComponent, VnrGridNewBuilder } from '@hrm-frontend-workspace/vnr-module';
import { gridChildDefineDataSource } from '../../../data/grid-new-child-define-data-source.data';
import { gridChildDefineColumns } from '../../../data/grid-new-child-define-column.data';
import { TranslateModule } from '@ngx-translate/core';

import { VnrModuleModule } from '@hrm-frontend-workspace/ui';

@Component({
  selector: 'grid-new-child-list',
  templateUrl: './grid-new-child-list.component.html',
  styleUrls: ['./grid-new-child-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [VnrModuleModule, TranslateModule],
})
export class GridNewChildListComponent implements OnInit, AfterViewInit {
  @Input() public columnMaster: any;

  @ViewChild('vnrGrid', { static: true }) gridControl: VnrGridNewComponent;

  protected isSupperAdmin: boolean = true;
  protected screenWidth: number;
  protected screenHeight: number;
  protected compRef: ViewContainerRef;
  protected gridHeight: number;
  protected builderGrid: VnrGridNewBuilder;
  private _permission: string = 'New_PortalV3_Personal_Hre_Dependant';
  private _screenName: string = 'grid-new-list';
  private _storeName: string = 'hrm_hr_sp_get_ProfileTest';
  protected gridName: string = 'PortalNew_GridNewChildExample';
  protected dataLocal = gridChildDefineDataSource;
  protected columns = gridChildDefineColumns;
  protected listColumnTemplates: {};

  constructor(private fb: UntypedFormBuilder) {}
  ngAfterViewInit() {}
  ngOnInit() {
    this.builderGridComponent();
  }

  private builderGridComponent() {
    this.builderGrid = new VnrGridNewBuilder({
      options: {
        isResizable: true,
        configHeightGrid: {
          isAllowCalcRowHeight: true,
        },
        configSelectable: {
          columnKey: 'Id',
        },
        configShowHide: {
          isPageExpand: false,
        },
        configCommandColumn: {
          isEnabledMenuAction: true,
        },
        configGroupable: {
          isEnabled: false,
        },
      },
    });
  }
  getSelectedID($event) {
    console.log($event, 'getSelectedID');
  }
  getDataItem($event) {
    console.log($event, 'getDataItem');
  }
  onOpenDetail($event) {
    console.log($event, 'onOpenDetail');
  }
  onVnRViewModeGrid($event) {
    console.log($event, 'onVnRViewModeGrid');
  }
  onGridEdit($event) {
    console.log($event, 'onGridEdit');
  }
  onGridDelete($event) {
    console.log($event, 'onGridDelete');
  }
  onGridViewDetail($event) {
    console.log($event, 'onGridViewDetail');
  }
  onGridCellClick($event) {
    console.log($event, 'onGridCellClick');
  }
  selected: string = 'rightTop';
}
