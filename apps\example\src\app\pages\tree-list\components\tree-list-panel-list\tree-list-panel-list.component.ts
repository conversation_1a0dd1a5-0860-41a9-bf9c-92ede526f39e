import {
  AfterViewInit,
  Component,
  OnInit,
  TemplateRef,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';
import { UntypedFormBuilder } from '@angular/forms';
import {
  VnrGridNewBuilder,
  VnrToolbarNewBuilder,
  VnrToolbarNewComponent,
  VnrTreelistNewBuilder,
  VnrTreelistNewComponent,
  VnrTreelistNewModule,
} from '@hrm-frontend-workspace/vnr-module';
import { VnrFilterAdvanceFullBuilder } from '@hrm-frontend-workspace/vnr-module';
import { TranslateModule } from '@ngx-translate/core';
import { RowClassArgs } from '@progress/kendo-angular-treelist';
import { treeListPanelDefineColumns } from '../../data/tree-list-panel-define-column.data';
import { treeListPanelDefineDataSource } from '../../data/tree-list-panel-define-data-source.data';
import { Ng<PERSON><PERSON>, NgSwitchCase, NgSwitchDefault } from '@angular/common';
import { NzButtonModule } from 'ng-zorro-antd/button';

@Component({
  selector: 'tree-list-panel-list',
  templateUrl: './tree-list-panel-list.component.html',
  styleUrls: ['./tree-list-panel-list.component.scss'],
  standalone: true,
  imports: [
    TranslateModule,
    VnrTreelistNewModule,
    VnrToolbarNewComponent,
    NzButtonModule,
    NgSwitch,
    NgSwitchCase,
    NgSwitchDefault,
  ],
})
export class TreeListPanelListComponent implements OnInit, AfterViewInit {
  @ViewChild('vnrTreeList', { static: true }) vnrTreeList: VnrTreelistNewComponent;
  @ViewChild('templateCurrentNumber', { static: true })
  private _templateCurrentNumber: TemplateRef<any>;
  private _permission: string = 'New_PortalV3_Personal_Hre_Dependant';
  private _screenName: string = 'grid-new-treelist';
  private _storeName: string = 'hrm_hr_sp_get_ProfileTest';
  protected gridName: string = 'PortalNew_GridNewTreelistExample';
  protected isSupperAdmin: boolean = true;
  protected dataLocal = treeListPanelDefineDataSource;
  protected columns = treeListPanelDefineColumns;
  protected screenWidth: number;
  protected screenHeight: number;
  protected compRef: ViewContainerRef;
  protected gridHeight: number;
  protected builderGrid: VnrGridNewBuilder;
  protected builderFilterAdvanceFull: VnrFilterAdvanceFullBuilder;
  protected builderFullName: any;
  protected builderCurrentNumber: any;
  protected builderToolbar: VnrToolbarNewBuilder;
  protected builderTreeList: VnrTreelistNewBuilder;
  protected listColumnTemplates: {};

  //#endregion
  constructor(private fb: UntypedFormBuilder) {}
  ngAfterViewInit() {}
  ngOnInit() {
    this.builderTemplate();
    this.builderTreeListComponent();
    this.builderToolbarComponent();
  }
  private builderTemplate() {
    this.listColumnTemplates = {
      CurrentNumber: this._templateCurrentNumber,
    };
  }

  private builderToolbarComponent() {
    this.builderToolbar = new VnrToolbarNewBuilder({
      isShowConfig: true,
      gridName: this.gridName,
      isSupperAdmin: this.isSupperAdmin,
      gridRef: this.vnrTreeList,
      permission: this._permission,
      screenName: this._screenName,
      storeName: this._storeName,
      options: {
        configButtonChangeColumn: {
          isShow: true,
          isConfigMenuDisplay: true,
          isChangeColumnNew: true,
          isDisabled: false,
          configButtonSetting: {
            isShow: true,
            isDisabled: true,
          },
        },
        configFilterAdvanceQuick: {
          isShow: true,
          isShowBtnAdvance: false,
          isUseConfig: true,
          keyConfig: 'GridExam_FilterQuickSeting',
          //components: gridDefineDataFilterQuick(),
        },
        configButtonExport: {
          isShowBtnExcelAll: true,
          isShowBtnWord: true,
          isShowBtnExcelByTemplate: true,
        },
        configButtonDelete: {
          isShow: true,
          isDisabled: false,
        },
        configQuickSearch: {
          textPlaceHolder: 'Tìm kiếm theo mã, tên...',
          searchKey: 'FullName',
          isShowBtn: true,
        },

        isSetBackground: true,
        buttonMoreFlexDirection: 'inherit',
        isExpanded: false,
      },
    });
  }
  private builderTreeListComponent() {
    this.builderTreeList = new VnrTreelistNewBuilder({
      options: {
        queryOption: {
          take: 10,
        },
        displayField: 'FullName',
        configSelectable: {
          columnKey: 'ID',
          groupKey: 'ParentID',
        },
        configHeightGrid: {
          isAllowCalcRowHeight: true,
          gridBottomMargin: 0,
        },
        configShowHide: {
          isShowColumnCheck: true,
          isShowViewDetail: true,
          isPageExpand: true,
        },
        configCommandColumn: {
          isEnabledMenuAction: true,
          width: 200,
        },
      },
    });
  }

  protected rowClass = (context: RowClassArgs) => {
    return context.dataItem.Details ? 'row-details' : '';
  };
  toggleChangeColumn(isNewVersion: boolean) {
    this.vnrTreeList?.setOpenChangeColumn(true);
    this.vnrTreeList?.setChangeColumnVersion(isNewVersion);
  }
  getSelectedID($event) {
    console.log($event, 'getSelectedID');
  }
  getDataItem($event) {
    console.log($event, 'getDataItem');
  }
  getSelectedDataItem($event) {
    console.log($event, 'getSelectedDataItem');
  }
  onGridEdit($event) {
    console.log($event, 'onGridEdit');
  }
  onGridDelete($event) {
    console.log($event, 'onGridDelete');
  }
  onGridViewDetail($event) {
    console.log($event, 'onGridViewDetail');
  }
  onGridCellClick($event) {
    console.log($event, 'onCellClick');
  }
  onAfterCollapse($event) {
    console.log($event, 'onAfterCollapse');
  }
  onAfterExpand($event) {
    console.log($event, 'onAfterExpand');
  }
  onColumnClick($event) {
    console.log($event, 'onColumnClick');
  }
  onDoubleClick($event) {
    console.log($event, 'onDoubleClick');
  }
  onGridCellGroupClick($event) {
    console.log($event, 'onGridCellGroupClick');
  }
}
