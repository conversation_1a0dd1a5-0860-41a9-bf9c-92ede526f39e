{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2020", "module": "esnext", "lib": ["es2022", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "strictNullChecks": false, "strictFunctionTypes": false, "allowSyntheticDefaultImports": true, "noImplicitOverride": false, "noImplicitAny": true, "strictPropertyInitialization": false, "baseUrl": ".", "paths": {"@hrm-frontend-workspace/common": ["libs/common/public-api.ts"], "@hrm-frontend-workspace/core": ["libs/core/public-api.ts"], "@hrm-frontend-workspace/domain": ["libs/domain/public-api.ts"], "@hrm-frontend-workspace/layout": ["libs/layout/public-api.ts"], "@hrm-frontend-workspace/models": ["libs/models/public-api.ts"], "@hrm-frontend-workspace/store": ["libs/store/public-api.ts"], "@hrm-frontend-workspace/ui": ["libs/ui/public-api.ts"], "@hrm-frontend-workspace/vnr-module": ["libs/vnr-module/public-api.ts"], "example/Routes": ["apps/example/src/app/app.routes.ts"], "objEval/Routes": ["apps/objEval/src/app/app.routes.ts"], "human-resources/Routes": ["apps/human-resources/src/app/app.routes.ts"], "dashboard/Routes": ["apps/dashboard/src/app/app.routes.ts"]}}, "exclude": ["node_modules", "tmp", "dist"]}