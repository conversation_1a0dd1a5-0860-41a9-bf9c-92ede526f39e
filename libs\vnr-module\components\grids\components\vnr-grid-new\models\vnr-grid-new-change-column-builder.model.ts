import { AbstractGridConfigChangeColumnBuilder } from '../../../models/abstract-grid-config-change-column-builder.model';
import { VnRGridConfigChangeColumnOptionBuilder } from '../../../models/grid-config-change-column-option-builder.model';
import { VnrGridNewBuilder } from './vnr-grid-new-builder.model';

export class VnRGridNewChangeColumnBuilder extends AbstractGridConfigChangeColumnBuilder<
  VnRGridNewChangeColumnBuilder,
  VnRGridConfigChangeColumnOptionBuilder
> {
  constructor(builder?: VnRGridNewChangeColumnBuilder) {
    super();
    if (!builder === false) {
      builder.data = {
        columnMode: builder?.data?.columnMode,
        gridControlName: builder?.data?.gridControlName ?? builder?.gridName,
        keyChangeCol: builder?.data?.keyChangeCol,
        userInfoID: builder?.data?.userInfoID ?? '',
      };
    }
    Object.assign(this, builder);
    this.options = new VnRGridConfigChangeColumnOptionBuilder(builder?.options);
  }
  builderFromGrid?(builderGrid?: VnrGridNewBuilder) {
    let builder: VnRGridNewChangeColumnBuilder = {
      gridPlacement: builderGrid.optionChangeColumn?.gridPlacement,
      data: builderGrid.optionChangeColumn?.data,
      options: {
        apiGetColumn: {
          apiUrl: builderGrid.options?.configApiSupport?.apiGetColumn?.url,
          method: builderGrid.options?.configApiSupport?.apiGetColumn?.method,
        },
        apiRestoreChangeColumn: {
          apiUrl: builderGrid.optionChangeColumn?.apiRestoreChangeColumn?.url,
          method: builderGrid.optionChangeColumn?.apiRestoreChangeColumn?.method,
        },
        apiSaveChangeColumn: {
          apiUrl: builderGrid.optionChangeColumn?.apiSaveChangeColumn?.url,
          method: builderGrid.optionChangeColumn?.apiSaveChangeColumn?.method,
        },
        apiSaveTranslate: {
          apiUrl: builderGrid.optionChangeColumn?.apiSaveTranslate?.url,
          method: builderGrid.optionChangeColumn?.apiSaveTranslate?.method,
        },
        assemblyName: builderGrid.optionChangeColumn?.assemblyName,
        isDisabledModeViewRecommended:
          builderGrid.optionChangeColumn?.isDisabledModeViewRecommended,
        isDisabledModeViewSimple: builderGrid.optionChangeColumn?.isDisabledModeViewSimple,
        isRestoreApi: builderGrid.optionChangeColumn?.isRestoreApi,
        modelName: builderGrid.optionChangeColumn?.modelName,
        tableDB: builderGrid.optionChangeColumn?.tableDB,
        position: {
          bottom: builderGrid.optionChangeColumn?.position?.bottom,
          top: builderGrid.optionChangeColumn?.position?.top,
          left: builderGrid.optionChangeColumn?.position?.left,
          right: builderGrid.optionChangeColumn?.position?.right,
        },
      },
    };
    Object.assign(this, builder);
    this.options = new VnRGridConfigChangeColumnOptionBuilder(builder?.options);
    return builder;
  }
  builder?(builder?: VnRGridNewChangeColumnBuilder) {
    Object.assign(this, builder);
    this.options = new VnRGridConfigChangeColumnOptionBuilder(builder?.options);
  }
}
