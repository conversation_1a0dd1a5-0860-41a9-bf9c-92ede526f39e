import { Async<PERSON>ipe, CommonModule, Ng<PERSON>lass, NgIf, NgTemplateOutlet } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  HostListener,
  Inject,
  Input,
  OnInit,
  Output,
  TemplateRef,
  ViewContainerRef,
} from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { GridDataResult, PageChangeEvent } from '@progress/kendo-angular-grid';
import { IconsModule, SVGIconModule } from '@progress/kendo-angular-icons';
import { KENDO_CHECKBOX, KENDO_INPUTS } from '@progress/kendo-angular-inputs';
import { KENDO_INTL } from '@progress/kendo-angular-intl';
import { KENDO_LISTVIEW } from '@progress/kendo-angular-listview';
import { KENDO_PAGER, PageSizeChangeEvent } from '@progress/kendo-angular-pager';
import { process, toDataSourceRequest } from '@progress/kendo-data-query';
import { cloneDeep, sumBy, union } from 'lodash';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCollapseModule } from 'ng-zorro-antd/collapse';
import { NzOutletModule } from 'ng-zorro-antd/core/outlet';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { debounceTime, map, Observable, of, Subject, takeUntil } from 'rxjs';
import { VNRMODULE_TOKEN } from '../../../../base/api-config';
import { VnRGridBase } from '../../../../base/grid-base';
import { IVnrModule_Token } from '../../../../common/models/app-api-config.interface';
import { vnrUtilities } from '../../../../common/utils/common.util';
import {
  VnrButtonFactory,
  VnrButtonNewBuilder,
  VnrButtonNewComponent,
} from '../../../button/public-api';
import { IExportExcel, IExportWord } from '../../../grids/interfaces/export-excel.interface';
import { VnrListviewNewColumnModel } from '../../interfaces/listview-new.interface';
import {
  VnRListviewNewChangeColumnPosition,
  VnRListviewNewDataSource,
} from '../../models/vnr-listview.model';
import { ListviewNewService } from '../../services/vnr-listview-new.service';
import { VnRListviewNewChangeColumnBuilder } from '../vnr-listview-new-change-column/models/vnr-listview-new-change-column-builder.model';
import { ListviewNewChangeColumnConfigService } from '../vnr-listview-new-change-column/services/listview-new-change-column-config.service';
import { ListviewNewChangeColumnService } from '../vnr-listview-new-change-column/services/listview-new-change-column.service';
import { VnrListviewNewChangeColumnComponent } from '../vnr-listview-new-change-column/vnr-listview-new-change-column.component';
import { VnrListviewNewBuilder } from './models/vnr-listview-new-builder.model';
@Component({
  selector: 'vnr-listview-new',
  preserveWhitespaces: false,
  templateUrl: './vnr-listview-new.component.html',
  styleUrls: ['./vnr-listview-new.component.scss'],
  standalone: true,
  imports: [
    KENDO_PAGER,
    KENDO_LISTVIEW,
    KENDO_CHECKBOX,
    KENDO_INPUTS,
    KENDO_INTL,
    NgIf,
    NgTemplateOutlet,
    NgClass,
    NzOutletModule,
    CommonModule,
    NzDropDownModule,
    NzButtonModule,
    NzIconModule,
    TranslateModule,
    FormsModule,
    ReactiveFormsModule,
    AsyncPipe,
    IconsModule,
    SVGIconModule,
    VnrListviewNewChangeColumnComponent,
    VnrButtonNewComponent,
    NzCollapseModule,
  ],
})
export class VnrListviewNewComponent extends VnRGridBase<any> implements OnInit, AfterViewInit {
  @Input() override builder: VnrListviewNewBuilder = new VnrListviewNewBuilder();
  @Input() gridName: string = 'grid-name';
  @Input() dataSource: VnRListviewNewDataSource;
  @Input() dataLocal: any[] = [];
  @Input() dataFormSearch?: any;
  @Input() columns: any;
  @Input() pageSize: number = 50;

  @Input() isOpenChangeColumn?: boolean = false;
  @Input() isChangeColumnNew?: boolean = false;
  @Input() isSupperAdmin: boolean = false;
  @Input() isAddColumnDev: boolean = false;

  @Input() columnTemplate: TemplateRef<any>;
  @Input() columnHeaderTemplate: TemplateRef<any>;
  @Input() columnFooterTemplate: TemplateRef<any>;
  @Input() rowDetailTemplate: TemplateRef<any>;
  @Input() rowActionsTemplate: TemplateRef<any>;

  @Output() protected vnrCollapse: EventEmitter<any> = new EventEmitter<any>();
  @Output() protected vnrExpand: EventEmitter<any> = new EventEmitter<any>();
  @Output() protected vnrCellGroupClick: EventEmitter<any> = new EventEmitter<any>();
  @Output() protected vnrCellClick: EventEmitter<any> = new EventEmitter<any>();
  @Output() protected vnrDoubleClick: EventEmitter<any> = new EventEmitter<any>();
  @Output() protected vnrEdit: EventEmitter<any> = new EventEmitter<any>();
  @Output() protected vnrDelete: EventEmitter<any> = new EventEmitter<any>();
  @Output() protected vnrViewDetails = new EventEmitter<any>();
  @Output() protected vnrLoadData = new EventEmitter<any>();
  @Output() protected getSelectedID = new EventEmitter<string[]>();
  @Output() protected getSelectedDataItem = new EventEmitter<any[]>();
  @Output() protected getDataItem = new EventEmitter<any>();

  get displayField(): string {
    return this.builder.options.displayField || 'Name';
  }
  private _destroy$: Subject<any> = new Subject<any>();
  private _calcGridHeight$: Subject<any> = new Subject<any>();
  private cellClickItem: any;
  private _skip = 0;
  private _pageSize = 50;
  private _exportColumns: any[] = [];
  private _defaultState = Object.assign({}, this.builder.options.queryOption);
  private _gridHeight: number = 0;
  private _isLoadScroll = false;
  private _valueFieldsInit: string = '';
  private _expandedIds: string[] = [];
  private _gridDataOriginal: any = [];
  private _btnFactory: VnrButtonFactory = VnrButtonFactory.init();
  protected builderListviewChangeColumn: VnRListviewNewChangeColumnBuilder;
  protected gridColumns: VnrListviewNewColumnModel[] = [];
  protected builderbtnDelete: VnrButtonNewBuilder;
  protected builderbtnEdit: VnrButtonNewBuilder;
  protected builderbtnViewDetail: VnrButtonNewBuilder;
  protected gridDataSource: GridDataResult = {
    data: [],
    total: 0,
  };
  protected gridSelectedKeys: any[] = [];
  protected isLoading = false;
  protected dataSource$: Observable<any[]>;

  constructor(
    @Inject(VNRMODULE_TOKEN) protected _vnrModule_Token: IVnrModule_Token,
    protected _ListviewNewService: ListviewNewService,
    protected _ListviewNewChangeColumnConfigService: ListviewNewChangeColumnConfigService,
    protected _ListviewNewChangeColumnService: ListviewNewChangeColumnService,
    private _cdr: ChangeDetectorRef,
    private _vc: ViewContainerRef,
  ) {
    super();
    this.isLoading = true;
  }
  get allChecked(): boolean {
    return (
      this.gridDataSource?.data?.length > 0 &&
      this.gridDataSource?.data.every((item) => item.checked)
    );
  }
  get someChecked(): boolean {
    return this.gridDataSource?.data?.some((item) => item.checked);
  }
  async ngOnInit() {
    this.subscribeCalcGridHeightEvent();
    this.builderButtonComponent();
    this.ensureApiAvailable();
    this.builderListviewChangeColumnComponent();
    this.initColumns();
    this.initData();
    this.initexpandedKeys();
  }

  ngAfterViewInit() {}
  ngOnDestroy(): void {
    this._destroy$.next(true);
    this._destroy$.complete();
  }
  //#region HostListener
  @HostListener('window:resize', ['$event'])
  onResize(event: UIEvent) {
    this._calcGridHeight$.next(true);
  }
  //#endregion HostListener
  private builderButtonComponent() {
    this.builderbtnDelete = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'common.delete',
      options: {
        style: 'outline-danger',
        icon: {
          fontIcon: 'fas fa-trash-alt',
        },
      },
    });
    this.builderbtnEdit = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'common.edit',
      options: {
        style: 'default',
        icon: {
          fontIcon: 'edit',
        },
      },
    });
    this.builderbtnViewDetail = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'common.viewDetail',
      options: {
        style: 'default',
        icon: {
          fontIcon: 'file-search',
        },
      },
    });
  }
  private subscribeCalcGridHeightEvent(): void {
    this._calcGridHeight$.pipe(takeUntil(this._destroy$), debounceTime(300)).subscribe((res) => {
      if (res && this.builder.options.configHeightGrid?.isAllowCalcRowHeight) {
        setTimeout(() => {
          this.reCalcHeightGrid();
        }, 500);
      }
    });
  }
  private initexpandedKeys(): void {
    if (!this.builder.options.expandedKeyIds) {
      return;
    }
    this.builder.options.expandedKeyIds.forEach((id: string) => {
      const itemExpaned = {
        [this.builder.options.configSelectable?.columnKey]: id,
      };
      //this.fetchChildren(itemExpaned);
    });
  }
  protected initColumns() {
    this.gridColumns = this.columns;
    if (this.gridColumns.length > 0) {
      let lstColumnName = this.gridColumns?.map((x) => x.Name)?.flat();
      if (lstColumnName && lstColumnName.length > 0) {
        this._valueFieldsInit = lstColumnName?.join(',');
        Object.assign(this.builder.options.queryOption, { ValueFields: this._valueFieldsInit });
      }

      if (!this.builder.options.queryOption.take) {
        if (!this.gridColumns[0].RowOnPage) {
          this.gridColumns[0].RowOnPage = this._pageSize;
        }
        this.builder.options.queryOption.take = parseInt(this.gridColumns[0].RowOnPage);
      }
      if (this._pageSize !== this.builder.options.queryOption.take) {
        this._pageSize = this.builder.options.queryOption.take;
      }
      this._exportColumns =
        cloneDeep(this.gridColumns)
          ?.flatMap((col) => col.Name)
          ?.filter((x: any) => x.Name !== 'ID') || [];
    }

    if (this.gridColumns.length > 0 && this.gridColumns[0] && this.gridColumns[0].OrderColumn) {
      let sortConfig: any = this.gridColumns[0]['OrderColumn'];
      typeof sortConfig == 'string' && (sortConfig = JSON.parse(sortConfig));
      this.builder.options.queryOption.sort = [...[sortConfig]];
    }

    this._defaultState = cloneDeep(this.builder.options.queryOption);
  }
  private initData() {
    this.isLoading = true;
    if (!this.dataLocal === false) {
      this._gridDataOriginal = cloneDeep(this.dataLocal);
    }
    this.dataSource$ = this.loadDataSource();
    this._calcGridHeight$.next(true);
    this.isLoading = false;
    this._cdr.detectChanges();
  }
  private loadDataSource(): Observable<any[]> {
    if (!this.dataLocal || this.dataLocal.length === 0) {
      let listDateFormat = this.getDateField();
      Object.assign(this.builder.options.queryOption, { ValueFields: this._valueFieldsInit });
      return this._ListviewNewService
        .loadDataListview(this.dataSource, this.builder.options.queryOption)
        .pipe(
          map((x: any) => {
            if (x.hasOwnProperty('Data')) delete x['Data'];
            if (x.hasOwnProperty('Total')) delete x['Total'];
            if (x.data && x.data.length > 0) {
              x.data.forEach((item: any) => {
                listDateFormat.forEach((field) => {
                  item[field] = this.partDateTime(item[field]);
                });
              });
            }
            this._gridDataOriginal = cloneDeep(x.data);
            let backData = x.data;
            if (this._isLoadScroll) {
              backData = [...this.gridDataSource.data, ...x.data];
            }
            this.gridDataSource = {
              total: this.gridViewTotal(x),
              data: backData,
            };
            this.vnrLoadData.emit(true);
            this.isLoading = false;
            return backData;
          }),
        );
    } else {
      let dataLocal = this._gridDataOriginal;
      const searchForm = this.dataFormSearch;
      if (searchForm) {
        Object.keys(searchForm).forEach((searchKey) => {
          let searchValue = this.dataFormSearch[searchKey];
          dataLocal = this.filterDataLocal(dataLocal, searchKey, searchValue);
        });
      }
      const _gridData = process(dataLocal, this.builder.options.queryOption);
      if (this._isLoadScroll) {
        _gridData.data = [...this.gridDataSource.data, ..._gridData.data];
      }
      this.gridDataSource = {
        total: this.gridViewTotal(_gridData),
        data: _gridData.data,
      };
      this.vnrLoadData.emit(true);
      this.isLoading = false;
      return of(_gridData.data);
    }
  }
  protected onPageSizeChange($event: PageSizeChangeEvent): void {
    const { newPageSize } = $event;
    this.isLoading = true;
    this.builder.options.queryOption.skip = 0;
    this.builder.options.queryOption.take = parseInt(newPageSize as string) || this._pageSize;
    this.dataSource$ = this.loadDataSource();
    this._calcGridHeight$.next(true);
  }

  protected onPageChange($event: PageChangeEvent): void {
    this.isLoading = true;
    const { skip, take } = $event;
    this.builder.options.queryOption.skip = skip;
    this.builder.options.queryOption.take = take;
    this.dataSource$ = this.loadDataSource();
  }

  private filterDataLocal<T>(dataSource: T[], key: keyof T, query: string): T[] {
    if (!query || typeof query === 'object') {
      return dataSource;
    }
    return dataSource.filter(
      (item) =>
        !item[key] || String(item[key]).toLowerCase().includes(query?.toString()?.toLowerCase()),
    );
  }
  private gridViewTotal(rsp: any) {
    let total = 0;
    if (!rsp) return total;

    if (rsp.total && rsp.total > 0) {
      total = rsp.total;
    } else if (rsp.totalPages && rsp.totalPages > 0) {
      total = rsp.totalPages;
    }
    return total;
  }
  private partDateTime(d: any): any {
    if (d && d instanceof Date && !isNaN(d.getTime())) {
      return d;
    } else if (d && /\/Date\((\d*)\)\//.exec(d)) {
      return new Date(+(+/\/Date\((\d*)\)\//.exec(d)![1]));
    } else if (d) {
      return new Date(d);
    }
  }
  private getDateField() {
    return union(
      this.gridColumns
        .filter((x) => {
          return x.Format?.toLowerCase()?.startsWith('datetime|');
        })
        .map((x) => x.Name),
      this.builder.options.configColumnSchema && this.builder.options.configColumnSchema?.fieldDate
        ? this.builder.options.configColumnSchema?.fieldDate
        : [],
    );
  }
  protected ensureApiAvailable(): void {
    if (!this.dataSource) {
      this.dataSource = {
        api: {
          url: this._vnrModule_Token.gridConfig_Token.apiGridGetDataSource?.url,
          method: this._vnrModule_Token.gridConfig_Token.apiGridGetDataSource?.method,
        },
      };
    }
    if (!this.dataSource?.api?.url) {
      this.dataSource.api.url = this._vnrModule_Token.gridConfig_Token.apiGridGetDataSource?.url;
    }
    if (!this.dataSource?.api?.method) {
      this.dataSource.api.method =
        this._vnrModule_Token.gridConfig_Token.apiGridGetDataSource?.method;
    }
    if (!this.builder.options?.configApiSupport?.apiExport.url) {
      this.builder.options.configApiSupport.apiExport.url =
        this._vnrModule_Token.gridConfig_Token.apiGridExport?.url;
    }
    if (!this.builder.options?.configApiSupport?.apiExportAll.url) {
      this.builder.options.configApiSupport.apiExportAll.url =
        this._vnrModule_Token.gridConfig_Token.apiGridExportAll?.url;
    }
    if (!this.builder.optionChangeColumn?.apiRestoreChangeColumn?.url) {
      this.builder.optionChangeColumn.apiRestoreChangeColumn.url =
        this._vnrModule_Token.gridConfig_Token.apiRestoreChangeColumn?.url;
    }
    if (!this.builder.optionChangeColumn?.apiSaveChangeColumn?.url) {
      this.builder.optionChangeColumn.apiSaveChangeColumn.url =
        this._vnrModule_Token.gridConfig_Token.apiSaveChangeColumn?.url;
    }
    if (!this.builder.optionChangeColumn?.apiSaveTranslate?.url) {
      this.builder.optionChangeColumn.apiSaveTranslate.url =
        this._vnrModule_Token.gridConfig_Token.apiSaveTranslate?.url;
    }
  }
  private builderListviewChangeColumnComponent() {
    this.builderListviewChangeColumn = new VnRListviewNewChangeColumnBuilder().builderFromGrid(
      this.builder,
    );

    this.builderListviewChangeColumn.gridName = this.gridName;
    this.builderListviewChangeColumn.columns = this.columns;
    this.builderListviewChangeColumn.isSupperAdmin = this.isSupperAdmin;
    this.builderListviewChangeColumn.options.isAddColumnDev = this.isAddColumnDev;
    this.builderListviewChangeColumn.options.isChangeColumnNew = this.isChangeColumnNew;
  }

  protected expandedIdsChange($event: any) {
    this._expandedIds = $event;
  }

  protected onItemClick($event, $dataItem: any): void {
    this.cellClickItem = $dataItem;
    this.vnrCellClick.emit({ $event: $event, record: this.cellClickItem });
  }

  protected onItemMouseEnter($event, $dataItem: any) {
    $dataItem.isHovered = true;
  }
  protected onItemMouseLeave($event, $dataItem: any) {
    $dataItem.isHovered = false;
  }
  //#endregion Event xử lý của Listview kendo

  //#region Sự kiện xử lý

  protected onItemDoubleClick($event, $dataItem: any) {
    $event.stopPropagation();
    this.vnrDoubleClick.emit({ event: $event, record: this.cellClickItem });
  }

  //#endregion Sự kiện xử lý
  //#region Tính chiều cao grid
  protected reCalcHeightGrid() {
    let _windowHeight = window.innerHeight;
    if (this._gridHeight == _windowHeight) {
      return;
    }
    const gridEle = this._vc?.element?.nativeElement.querySelector('.k-listview') as HTMLElement;
    if (!gridEle) {
      return;
    }
    const _footerWithoutGridHeight = this.getFooterWithoutGridHeight(gridEle);
    const _gridOffsetTop = this.getElementAbsoluteTop(gridEle);
    _windowHeight -= _gridOffsetTop + _footerWithoutGridHeight;
    if (this.builder.options?.configHeightGrid) {
      const _rowScrollHorizontal =
        this.builder.options.configHeightGrid.rowScrollHorizontal +
        this.builder.options.configHeightGrid.gridBottomMargin;
      _windowHeight -= _rowScrollHorizontal;
    }

    this.builder.options.configHeightGrid.gridHeight = _windowHeight;
    this._gridHeight = _windowHeight;
    this._cdr.detectChanges();
  }
  private getElementAbsoluteTop(el: HTMLElement): number {
    let top = 0;
    while (el) {
      top += el.offsetTop;
      el = el.offsetParent as HTMLElement;
    }
    return top;
  }

  private getFooterWithoutGridHeight(grid: HTMLElement) {
    let footerHeight = 0;
    const _gridLoadMore = grid.parentElement?.querySelector('.Listview-loadmore') as HTMLElement;
    const _gridPager = grid.parentElement?.querySelector('.k-pager') as HTMLElement;
    const gridLoadMoreHeight = _gridLoadMore?.offsetHeight || _gridLoadMore?.clientHeight || 0;
    const gridPagerHeight = _gridPager?.offsetHeight || _gridPager?.clientHeight || 0;

    footerHeight += gridLoadMoreHeight;
    footerHeight += gridPagerHeight;
    return footerHeight;
  }
  //#endregion
  protected trackByFn(index: number, item: any): any {
    return item?.name || index;
  }
  protected integerParser = (value: string) => value.replace(/\D/g, '');
  protected getColumnClassNames(col: VnrListviewNewColumnModel): string {
    return (col.Format && col.Format.toLowerCase().startsWith('number|')) ||
      (col.Format && col.Format.toLowerCase().startsWith('datetime|')) ||
      (this.builder.options?.configColumnSchema &&
        this.builder.options?.configColumnSchema.fieldNumber?.indexOf(col.Name) >= 0) ||
      (this.builder.options?.configColumnSchema &&
        this.builder.options?.configColumnSchema.fieldNumberMoney &&
        this.builder.options?.configColumnSchema.fieldNumberMoney.indexOf(col.Name) >= 0)
      ? (col.Class || '') + ' text-right'
      : col.Class || '';
  }

  protected onSelectAllChange($event: any) {
    const isChecked = ($event.target as HTMLInputElement).checked;
    let rawData = this.gridDataSource?.data || [];
    if (!rawData || rawData.length <= 0) {
      return;
    }
    rawData.forEach((item) => {
      item.checked = isChecked;
    });
    if (isChecked) {
      this.getDataItem.emit(rawData);
      this.getSelectedDataItem.emit(rawData);
    } else {
      this.getDataItem.emit([]);
      this.getSelectedDataItem.emit([]);
    }
    this.getSelectedID.emit(this.gridSelectedKeys);
  }
  protected checkDataItem(dataItem: any) {
    if (!dataItem) {
      return;
    }
    let rawData = this.gridDataSource?.data || [];
    rawData.forEach((item) => {
      if (
        item[this.builder.options.configSelectable?.columnKey] ==
        dataItem[this.builder.options.configSelectable?.columnKey]
      ) {
        item.checked = dataItem.checked;
      }
    });
    this.gridSelectedKeys = rawData
      .filter((x) => x.checked)
      .map((item) => item[this.builder.options.configSelectable?.columnKey]);
    if (dataItem.checked) {
      this.getDataItem.emit([dataItem]);
    } else {
      this.getDataItem.emit([]);
    }
    this.getSelectedID.emit(this.gridSelectedKeys);
    const _selectedDataItems =
      rawData.filter((ele) =>
        this.gridSelectedKeys.includes(ele[this.builder.options.configSelectable?.columnKey]),
      ) || [];
    this.getSelectedDataItem.emit(_selectedDataItems);
  }

  //#region tính số total row
  protected funcCalcCountLoadMore(dataGrid: any) {
    let result: number = 0;
    if (dataGrid) {
      if (
        this.builder.options.queryOption?.group &&
        this.builder.options.queryOption.group.length > 0 &&
        dataGrid.length > 0 &&
        ((dataGrid[0]['Items'] && Array.isArray(dataGrid[0]['Items'])) ||
          (dataGrid[0]['items'] && Array.isArray(dataGrid[0]['items'])))
      ) {
        if (dataGrid[0]?.ItemCount) {
          result =
            sumBy(dataGrid, (item: any) => {
              return item?.ItemCount;
            }) || 0;
        } else {
          let data = this._ListviewNewChangeColumnConfigService.extractItems(dataGrid);
          return data?.length || 0;
        }
      } else {
        result = dataGrid?.length;
      }
    }
    return result;
  }
  //#endregion
  //#region Public Method
  public setOpenChangeColumn(isOpenChangeColumn: boolean): void {
    if (!this.builder.optionChangeColumn) {
      return;
    }
    this.isOpenChangeColumn = isOpenChangeColumn;
    this.builderListviewChangeColumn.isOpenChangeColumn = isOpenChangeColumn;
  }
  public setChangeColumnVersion(isNewVersion: boolean): void {
    if (!this.builder.optionChangeColumn) {
      return;
    }
    this.isChangeColumnNew = isNewVersion;
    if (this.builderListviewChangeColumn.options) {
      this.builderListviewChangeColumn.options.isChangeColumnNew = isNewVersion;
    }
  }
  public setChangeColumnPosition(position: VnRListviewNewChangeColumnPosition): void {
    if (!position) {
      return;
    }
    if (this.builder?.optionChangeColumn) {
      this.builder.optionChangeColumn.position = {
        bottom: position.bottom ?? this.builder.optionChangeColumn?.position?.bottom,
        top: position.top ?? this.builder.optionChangeColumn?.position?.top,
        left: position.left ?? this.builder.optionChangeColumn?.position?.left,
        right: position.right ?? this.builder.optionChangeColumn?.position?.right,
        width: position.width ?? this.builder.optionChangeColumn?.position?.width,
        height: position.height ?? this.builder.optionChangeColumn?.position?.height,
      };
    }
    if (this.builderListviewChangeColumn?.options) {
      this.builderListviewChangeColumn.options.position = {
        bottom: position.bottom ?? this.builderListviewChangeColumn.options.position?.bottom,
        top: position.top ?? this.builderListviewChangeColumn.options.position?.top,
        left: position.left ?? this.builderListviewChangeColumn.options.position?.left,
        right: position.right ?? this.builderListviewChangeColumn.options.position?.right,
        width: position.width ?? this.builderListviewChangeColumn.options.position?.width,
        height: position.height ?? this.builderListviewChangeColumn.options.position?.height,
      };
    }
  }
  public removeDataFilter(filterKeys: string[]): void {
    if (this.dataFormSearch) {
      filterKeys.forEach((field) => {
        if (field in this.dataFormSearch) {
          delete this.dataFormSearch[field];
        }
      });
    }
  }
  public setDataFilter(filter: any): void {
    if (!this.dataFormSearch) {
      this.dataFormSearch = {};
    }
    Object.assign(this.dataFormSearch, filter);
  }
  public loadMore(): void {
    this.isLoading = true;
    this._isLoadScroll = true;
    this.builder.options.queryOption.skip += this._pageSize;
    this.dataSource$ = this.loadDataSource();
    this._calcGridHeight$.next(true);
    this.isLoading = false;
    this._cdr.detectChanges();
  }
  public vnrReadGrid() {
    this.isLoading = true;
    this.builder.options.queryOption = this._defaultState;
    this.builder.options.queryOption.take = this._pageSize;
    this.gridSelectedKeys = [];
    this.getSelectedID.emit([]);
    this.getDataItem.emit([]);
    this.getSelectedDataItem.emit([]);
    this.dataSource$ = this.loadDataSource();
    this.isLoading = false;
    this._cdr.detectChanges();
  }

  public vnrReloadGrid() {
    this.builder.options.queryOption = this._defaultState;
    this.builder.options.queryOption.take = this._pageSize;
    this.gridSelectedKeys = [];
    this.getSelectedID.emit([]);
    this.getDataItem.emit([]);
    this.getSelectedDataItem.emit([]);
    this.ngOnInit();
  }

  public vnrcloseAndRefreshGird(evt: any) {
    this.setOpenChangeColumn(evt.isShowPopup);
    if (evt.isRefresh) {
      this.vnrReloadGrid();
    }
  }
  //#endregion
  //#region load cấu hình cột mới
  protected vnrReloadGridChangeColumn($event) {
    if ($event) {
      this.vnrReloadGrid();
    }
  }
  //#endregion
  //#region Export
  public onExcelExport($event: any, param?: any): Observable<any> {
    $event.preventDefault();
    let state = cloneDeep(this.builder.options.queryOption && this.builder.options.queryOption);
    state ? (state.take = 20000 - 1) : {};

    const valueFields = this._getExportValueFields();
    const payload = this._buildExportPayload(state, valueFields, param, 'excel');
    let urlApi =
      this.gridSelectedKeys && this.gridSelectedKeys.length === 0
        ? this.builder.options.configApiSupport?.apiExportAll?.url
        : this.builder.options.configApiSupport?.apiExport?.url;
    return this._ListviewNewService.postExportExcel(urlApi, payload);
  }

  /**
   * New Event Export Excel
   * @param config Using `IExportExcel`
   * @returns Observable<any>
   */
  public onExportExcel(config: IExportExcel): Observable<any> {
    let state = cloneDeep(this.builder.options.queryOption && this.builder.options.queryOption);
    state ? (state.take = 20000 - 1) : {};
    if (this._exportColumns) {
      this._exportColumns = this._exportColumns.filter((x) => !x.Hidden && !x.IsHiddenConfig);
    }

    const defaultValueFields = this._exportColumns?.length > 0 ? this._exportColumns.join(',') : '';
    const valueFields = config?.valueFields || this._getExportValueFields() || defaultValueFields;
    const payload = this._buildExportPayload(state, valueFields, config.params, 'excel');
    let urlApi =
      this.gridSelectedKeys && this.gridSelectedKeys.length === 0
        ? config.urlExportAll
        : config.urlExportSelected;

    return this._ListviewNewService.postExportExcel(urlApi, payload);
  }

  /**
   *
   * @param config Using `IExportWord`
   * @returns Observable<any>
   */
  public onExportWord(config: IExportWord): Observable<any> {
    if (!config.urlExportAll && !config.urlExportSelected) return of(null);
    let state = cloneDeep(this.builder.options.queryOption && this.builder.options.queryOption);
    state ? (state.take = 20000 - 1) : {};

    if (this._exportColumns) {
      this._exportColumns = this._exportColumns.filter((x) => !x.Hidden && !x.IsHiddenConfig);
    }

    const defaultValueFields = this._exportColumns?.length > 0 ? this._exportColumns.join(',') : '';
    const valueFields = config?.valueFields || this._getExportValueFields() || defaultValueFields;
    const payload = this._buildExportPayload(state, valueFields, config.params, 'word');
    let urlApi =
      this.gridSelectedKeys && this.gridSelectedKeys.length === 0
        ? config.urlExportAll
        : config.urlExportSelected;
    return this._ListviewNewService.postExportWord(urlApi, payload);
  }
  private _getExportValueFields(): string {
    return (
      this.gridColumns
        ?.filter((x) => !x.Hidden && !x.IsHiddenConfig)
        ?.flatMap((col) =>
          col.Type === 'group' ? col.MultiColumn?.map((multiCol) => multiCol.Name) : col.Name,
        )
        ?.join(',') || ''
    );
  }
  private _buildExportPayload(
    state: any,
    valueFields: string,
    params: any,
    exportBy: 'excel' | 'word',
  ): any {
    let request = toDataSourceRequest(state);
    let data = {
      ...request,
      selectedIds: this.gridSelectedKeys ? this.gridSelectedKeys.join(',') : '',
      IsExport: true,
      IsPortal: true,
      exportBy,
      valueFields,
      ...(params || {}),
    };

    data['PageIndex'] = data['page'] || 1;
    data['PageSize'] = data['pageSize'] || state?.take;

    if (!this.dataFormSearch === false) {
      Object.assign(data, this.dataFormSearch);
    }

    if (params && !params.params === false) {
      Object.assign(data, params.params);
    }

    if (!this.dataSource.storeName === false) {
      const formData = new FormData();
      for (const key in data) {
        formData.append(key, data[key]);
      }
      return formData;
    }
    return data;
  }

  //#endregion

  protected onPanelToggle(isExpanded: boolean, item: any): void {
    item.expanded = isExpanded;
    this.vnrExpand.emit(isExpanded);
    this.vnrCollapse.emit(!isExpanded);
  }
  public onExpandAll(): void {
    this.gridDataSource?.data?.forEach((item) => {
      item.expanded = true;
    });
  }
  public onCollapseAll(): void {
    this.gridDataSource?.data?.forEach((item) => {
      item.expanded = false;
    });
  }
  public onOpenColumnCheck(): void {
    this.builder.options.configShowHide.isShowColumnCheck = true;
  }
  public onCancelOpenColumnCheck(): void {
    this.builder.options.configShowHide.isShowColumnCheck = false;
  }
  public onSelectAll(): void {
    this.gridDataSource?.data?.forEach((item) => {
      item.checked = true;
    });
  }
  public onCancelSelectAll(): void {
    this.gridDataSource?.data?.forEach((item) => {
      item.checked = false;
    });
  }
  protected onEdit($event, $dataItem: any) {
    $event.stopPropagation();
    this.vnrEdit.emit($dataItem);
  }
  protected onDelete($event, $dataItem: any) {
    $event.stopPropagation();
    this.vnrDelete.emit($dataItem);
  }
  protected onViewDetails($event, $dataItem: any) {
    $event.stopPropagation();
    this.vnrViewDetails.emit($dataItem);
  }
}
