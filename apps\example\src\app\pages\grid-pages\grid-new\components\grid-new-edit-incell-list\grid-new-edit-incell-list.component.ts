import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  Inject,
  OnInit,
  TemplateRef,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import {
  VnrDateRangePickerBuilder,
  VnrGridNewComponent,
  VnrTextBoxBuilder,
  VnrGridNewBuilder,
  VnrToolbarNewBuilder,
  VnrToolbarNewComponent,
  VnrSelectFactory,
  VnrGridNewEditIncellComponent,
  VnrGridNewEditIncellBuilder,
  VnrDateRangePickerComponent,
  IVnrModule_Token,
  VNRMODULE_TOKEN,
} from '@hrm-frontend-workspace/vnr-module';
import { gridEditIncellDefineDataSource } from '../../data/grid-new-edit-incell-define-data-source.data';
import { gridEditIncellDefineColumns } from '../../data/grid-new-edit-incell-define-column.data';
import { TranslateService } from '@ngx-translate/core';
import {
  CommonModule,
  NgClass,
  NgIf,
  NgSwitch,
  NgSwitchCase,
  NgSwitchDefault,
} from '@angular/common';
import { gridDefineDataFilterAdvance } from '../../data/grid-new-define-data-filter-advance';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { IntlModule } from '@progress/kendo-angular-intl';
import { trimEnd } from 'lodash';
import { GroupKey } from '@progress/kendo-angular-grid';
import { CommonService } from '@hrm-frontend-workspace/core';
@Component({
  selector: 'grid-new-edit-incell-list',
  templateUrl: './grid-new-edit-incell-list.component.html',
  styleUrls: ['./grid-new-edit-incell-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [
    VnrGridNewEditIncellComponent,
    NgSwitch,
    NgSwitchCase,
    NgIf,
    NgSwitchDefault,
    NzDividerModule,
    CommonModule,
    IntlModule,
  ],
})
export class GridNewEditIncellListComponent implements OnInit, AfterViewInit {
  @ViewChild('buttonTemplate', { static: true })
  public buttonTemplate: TemplateRef<any>;
  @ViewChild('tplFooter', { static: true })
  public tplFooter: TemplateRef<any>;
  @ViewChild('templateCustomControl', { static: true })
  public templateCustomControl: TemplateRef<any>;
  @ViewChild('vnrEditIncell', { static: true }) gridControl: VnrGridNewEditIncellComponent;
  @ViewChild('btnControl', { static: true }) btnControl: TemplateRef<any>;
  @ViewChild('jobTemplate', { static: true }) jobTemplate: TemplateRef<any>;
  @ViewChild('FileAttachment', { static: true }) FileAttachment: TemplateRef<any>;
  @ViewChild('tplMasterDetail', { static: true }) tplMasterDetail: TemplateRef<any>;
  @ViewChild('tplCustomButtonAction', { static: true })
  tplCustomButtonAction: TemplateRef<any>;
  @ViewChild('tplHeader', { static: true }) public tplHeader: TemplateRef<any>;
  protected isSupperAdmin: boolean = false;
  protected screenWidth: number;
  protected screenHeight: number;
  protected compRef: ViewContainerRef;
  protected gridHeight: number;
  protected builderGrid: VnrGridNewBuilder;
  protected builderToolbar: VnrToolbarNewBuilder;
  protected builder_JobVacancyName: VnrTextBoxBuilder;
  protected builder_Code: VnrTextBoxBuilder;
  protected builderConfigByColumn: {};
  protected builder_DateRequest: VnrDateRangePickerBuilder;
  protected searchForm: UntypedFormGroup;
  protected dataLocal = gridEditIncellDefineDataSource;
  protected columns = gridEditIncellDefineColumns;
  //#endregion
  constructor(
    private fb: UntypedFormBuilder,
    private _commonService: CommonService,
    private _translate: TranslateService,
    @Inject(VNRMODULE_TOKEN) private _vnrModule_Token: IVnrModule_Token,
  ) {}
  ngAfterViewInit() {}
  ngOnInit() {
    setTimeout(() => {
      this.updateGridHeight();
    });
    this.builderColumn();
    this.builderForm();
    this.builderGridComponent();
    this.searchForm = this.fb.group({
      JobVacancyName: [null],
      Code: [null],
      DateFrom: [null],
      DateRequest: [null],
      JobVacancyName1: [null],
      Code1: [null],
      DateFrom1: [null],
      DateRequest1: [null],
    });
  }
  private _expandedGroupKeys: Array<GroupKey> = [
    {
      field: 'KeyObject__Rec_CandidateProfile_1',
      value: '00000000-0000-0000-0000-000000000000',
      parentGroupKeys: [],
    },
    {
      field: 'KeyObject__Rec_Candidate_1',
      value: '00000000-0000-0000-0000-000000000000',
      parentGroupKeys: [
        {
          field: 'KeyObject__Rec_CandidateProfile_1',
          value: '00000000-0000-0000-0000-000000000000',
        },
      ],
    },
  ];
  private builderColumn() {
    this.builderConfigByColumn = {
      KPIID: {
        label: '',
        textField: 'Text',
        valueField: 'ID',
        autoBind: false,
        disabled: false,
        options: {
          hiddenValidationMessage: false,
          allowValueObject: true,
        },
        serverSide: {
          urlApi: `${this._vnrModule_Token.apiConfig_Token?.apiUrl}/Tas_GetData/GetMultiTaskProjectAll`,
          method: 'POST',
          data: {
            text: '',
          },
        },
      },
    };
  }
  private builderForm() {
    this.builder_Code = new VnrTextBoxBuilder({
      label: 'vnrcontrol.demo.code',
      options: {
        hasFeedBack: false,
      },
    });

    this.builder_JobVacancyName = new VnrTextBoxBuilder({
      label: 'vnrcontrol.demo.jobVacancyName',
      options: {
        hasFeedBack: false,
      },
    });

    this.builder_DateRequest = new VnrDateRangePickerBuilder({
      label: 'vnrcontrol.demo.dateRequest',
      options: {
        hasFeedBack: false,
      },
    });
  }
  private builderGridComponent() {
    this.builderGrid = new VnrGridNewEditIncellBuilder({
      optionChangeColumn: {},
      options: {
        isEnabledFormat: true,
        isVirtualColumn: true,
        configShowHide: {
          isPageExpand: true,
          isShowRefresh: true, // => move qua Grid edit incell
        },
        configCommandColumn: {
          isEnabledMenuAction: true,
        },
        configGroupable: {
          isEnabled: true,
        },
        configIndexColumn: {
          isShow: true,
        },
        configExpanded: {
          isExpanded: false,
          groupKey: this._expandedGroupKeys,
        },
        configEdit: {
          builderConfigByColumn: this.builderConfigByColumn,
          isAllowEditOtherCell: true,
          isAllowEditTemplateCellNew: true,
          isAutoSave: false,
          isShowBtnSave: true,
        },
      },
    });
  }
  getSelectedID($event) {
    console.log($event, 'getSelectedID');
  }
  getDataItem($event) {
    console.log($event, 'getDataItem');
  }
  onOpenDetail($event) {
    console.log($event, 'onOpenDetail');
  }
  onVnRViewModeGrid($event) {
    console.log($event, 'onVnRViewModeGrid');
  }
  onGridEdit($event) {
    console.log($event, 'onGridEdit');
  }
  onGridDelete($event) {
    console.log($event, 'onGridDelete');
  }
  onGridViewDetail($event) {
    console.log($event, 'onGridViewDetail');
  }
  onGridCellClick($event) {
    console.log($event, 'onGridCellClick');
  }
  vnrChangeComboBoxDataItem($event) {
    console.log($event, 'vnrChangeComboBoxDataItem');
  }
  vnrOpenComboBox($event) {
    console.log($event, 'vnrChangeComboBoxDataItem');
  }
  updateGridHeight(): void {
    this.screenWidth = window.innerWidth;
    this.screenHeight = window.innerHeight;
    const grid = (this.compRef?.element?.nativeElement as HTMLElement)?.querySelector(
      'vnr-grid-new',
    );
    if (!grid) {
      return;
    }
    const height = this.screenHeight > 912 ? 110 : 120;
    this.gridHeight = window.outerHeight - grid.getBoundingClientRect().top - height;
  }
  selected: string = 'rightTop';
  toggleChangeColumn(isNewVersion: boolean) {
    this.gridControl?.setOpenChangeColumn(true);
    this.gridControl?.setChangeColumnVersion(isNewVersion);
  }
  reload() {
    this.isSupperAdmin = !this.isSupperAdmin;
    setTimeout(() => {
      this.gridControl?.vnrReadGrid();
    });
  }
  reload1() {
    this.isSupperAdmin = !this.isSupperAdmin;
    setTimeout(() => {
      this.gridControl?.vnrReloadGrid();
    });
  }
  onCustomCellClick({ dataItem, column }: any) {}
  onOutSideClick($event: any) {
    this.gridControl.setDataValid();
    const { dataItem, column } = $event;
    let isQuit = JSON.parse(dataItem['IsQuit'] || null);
    let isCreateAccountOnline = JSON.parse(dataItem['IsCreateAccountOnline'] || null);
    if (isQuit && isCreateAccountOnline) {
      let _message = this._translate.instant('common.message.dataInvalid');
      this._commonService.notification({
        type: 'error',
        title: 'common.message.actionError',
        message: _message,
      });
      dataItem[column.field] = null;
      this.gridControl.setDataInvalid();
    }
  }
  onModelChange($event) {
    console.log($event, 'onModelChange');
  }
  onChange($event) {}
}
