# Eva-Goal Module DDD Refactoring Plan

## Executive Summary

This document outlines a comprehensive refactoring plan for the eva-goal module to implement Domain-Driven Design (DDD) and Clean Architecture principles while maintaining all existing functionality. The refactoring will improve code maintainability, testability, and scalability.

## Current State Analysis

### Existing Structure
- **Module Organization**: Feature-based structure with submodules (goal, goal-allocation, goal-detail, goal-progress, goal-result, form-config, shared)
- **Architecture Patterns**: Facade pattern, State management, API services, Container/Component pattern
- **Dependencies**: Heavy reliance on shared modules, NgZorro UI components, VNR custom components
- **Testing**: Jest for unit testing, Playwright for E2E testing, incomplete coverage

### Identified Issues
1. **Lack of Domain Modeling**: No clear domain entities, value objects, or business rules
2. **Mixed Concerns**: API, state, and business logic mixed together
3. **Tight Coupling**: Components directly depend on infrastructure concerns
4. **No Clear Boundaries**: Domain logic scattered across facades and components
5. **Mock Data Structure**: Flat data structures without domain relationships

## DDD Structure Design

### New Folder Structure
```
eva-goal/
├── domain/
│   ├── entities/
│   │   ├── goal.entity.ts
│   │   ├── goal-period.entity.ts
│   │   ├── goal-allocation.entity.ts
│   │   ├── goal-progress.entity.ts
│   │   └── goal-result.entity.ts
│   ├── value-objects/
│   │   ├── goal-status.vo.ts
│   │   ├── goal-target.vo.ts
│   │   ├── goal-weight.vo.ts
│   │   └── period-range.vo.ts
│   ├── aggregates/
│   │   ├── goal-management.aggregate.ts
│   │   └── goal-evaluation.aggregate.ts
│   ├── repositories/
│   │   ├── goal.repository.ts
│   │   ├── goal-allocation.repository.ts
│   │   └── goal-progress.repository.ts
│   ├── services/
│   │   ├── goal-allocation.domain-service.ts
│   │   ├── goal-evaluation.domain-service.ts
│   │   └── goal-validation.domain-service.ts
│   └── events/
│       ├── goal-created.event.ts
│       ├── goal-allocated.event.ts
│       └── goal-completed.event.ts
├── application/
│   ├── use-cases/
│   │   ├── create-goal/
│   │   ├── allocate-goal/
│   │   ├── update-goal-progress/
│   │   ├── evaluate-goal/
│   │   └── configure-goal-form/
│   ├── services/
│   │   ├── goal-management.app-service.ts
│   │   ├── goal-allocation.app-service.ts
│   │   └── goal-evaluation.app-service.ts
│   ├── dtos/
│   │   ├── goal.dto.ts
│   │   ├── goal-allocation.dto.ts
│   │   └── goal-progress.dto.ts
│   └── mappers/
│       ├── goal.mapper.ts
│       └── goal-allocation.mapper.ts
├── infrastructure/
│   ├── repositories/
│   │   ├── goal.repository.impl.ts
│   │   ├── goal-allocation.repository.impl.ts
│   │   └── goal-progress.repository.impl.ts
│   ├── api/
│   │   ├── goal.api.ts
│   │   ├── goal-allocation.api.ts
│   │   └── goal-progress.api.ts
│   ├── mock-data/
│   │   ├── goal.mock-data.ts
│   │   ├── goal-allocation.mock-data.ts
│   │   └── goal-progress.mock-data.ts
│   └── external-services/
│       └── formula-calculation.service.ts
└── presentation/
    ├── components/
    │   ├── goal-list/
    │   ├── goal-detail/
    │   ├── goal-allocation/
    │   ├── goal-progress/
    │   └── goal-form/
    ├── containers/
    │   ├── goal-period.container.ts
    │   ├── goal-allocation.container.ts
    │   └── goal-progress.container.ts
    ├── view-models/
    │   ├── goal.view-model.ts
    │   └── goal-allocation.view-model.ts
    └── shared/
        ├── pipes/
        ├── directives/
        └── validators/
```

## Clean Architecture Implementation

### Dependency Rules
1. **Domain Layer** (innermost): No dependencies on other layers
2. **Application Layer**: Depends only on Domain
3. **Infrastructure Layer**: Depends on Domain and Application
4. **Presentation Layer**: Depends on Application (and Domain for view models)

### Key Implementation Strategies
- **Interface Segregation**: Repository interfaces in domain, implementations in infrastructure
- **Event-Driven Architecture**: Domain events for cross-aggregate communication
- **CQRS Pattern**: Separate read and write models
- **Dependency Injection**: Proper DI configuration for all layers

## Mock Data Restructuring

### Current Issues
- Flat objects without relationships
- No domain validation
- Mixed presentation and domain data

### New Structure
- Domain-driven mock data using entities and value objects
- Factory pattern for complex object creation
- Mock repository implementations
- Proper data relationships and validation

## Migration Strategy

### Phase-by-Phase Approach
1. **Phase 1: Foundation** (Weeks 1-2) - Domain layer structure
2. **Phase 2: Application Layer** (Weeks 3-4) - Use cases and services
3. **Phase 3: Infrastructure Migration** (Weeks 5-6) - API and repositories
4. **Phase 4: Presentation Layer** (Weeks 7-8) - Components and containers
5. **Phase 5: Integration & Testing** (Weeks 9-10) - Testing and cleanup

### Risk Mitigation
- **Feature Flags**: Switch between old and new implementations
- **Parallel Implementation**: Keep old code running while building new
- **Incremental Testing**: Test each phase thoroughly
- **Rollback Plan**: Git branching and configuration-based switching

## Testing Strategy

### Coverage Goals
- **Domain Layer**: 95%+ coverage
- **Application Layer**: 90%+ coverage
- **Infrastructure Layer**: 85%+ coverage
- **Presentation Layer**: 80%+ coverage

### Testing Approach
- Unit tests for each layer
- Integration tests for cross-layer communication
- End-to-end tests for user scenarios
- Performance tests for optimization

## Effort Estimation

### Total Estimated Effort: 100-120 hours (12-15 working days)

**Phase Breakdown:**
- Phase 1: Foundation Setup (16-20 hours)
- Phase 2: Application Layer (20-24 hours)
- Phase 3: Infrastructure Migration (18-22 hours)
- Phase 4: Presentation Layer Migration (22-26 hours)
- Phase 5: Integration & Testing (24-28 hours)

## Dependencies and Critical Path

### Task Dependencies
- Phase 2 depends on Phase 1 completion
- Phase 3 can start after Phase 2.1 is complete
- Phase 4 depends on Phase 2 and 3 completion
- Phase 5 runs parallel with other phases for testing

### Critical Success Factors
1. Maintain existing functionality throughout migration
2. Comprehensive testing at each phase
3. Regular code reviews and pair programming
4. Continuous integration and deployment
5. Clear communication with stakeholders

## Next Steps

1. Review and approve this refactoring plan
2. Set up development environment and branching strategy
3. Begin Phase 1: Foundation Setup
4. Establish testing and review processes
5. Monitor progress and adjust timeline as needed

---

*This document serves as the master plan for the eva-goal module refactoring. All team members should refer to this document and the associated task list for implementation guidance.*
