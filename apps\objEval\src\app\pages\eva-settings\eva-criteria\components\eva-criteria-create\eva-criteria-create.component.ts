import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Inject,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import {
  FormsModule,
  ReactiveFormsModule,
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { CommonService } from '@hrm-frontend-workspace/core';
import { ResponseStatus } from '@hrm-frontend-workspace/models';
import {
  IVnrModule_Token,
  VNRMODULE_TOKEN,
  ValidatorConfigHandleDirective,
  VnrButtonFactory,
  VnrButtonNewBuilder,
  VnrButtonNewComponent,
  VnrCheckBoxLabelBuilder,
  VnrComboBoxBuilder,
  VnrComboBoxComponent,
  VnrInputFactory,
  VnrInputsModule,
  VnrRadioButtonBuilder,
  VnrSelectFactory,
  VnrTextBoxBuilder,
  VnrUploadBuilderV2,
  VnrUploadV2Component,
  vnrUtilities,
} from '@hrm-frontend-workspace/vnr-module';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzDrawerRef } from 'ng-zorro-antd/drawer';
import { NzEmptyModule } from 'ng-zorro-antd/empty';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzTabsModule } from 'ng-zorro-antd/tabs';

import { EvaCriteriaFacade } from '../../facade/eva-criteria.facade';

import { VnrButtonComponent } from '@hrm-frontend-workspace/ui';
import { NzAlertModule } from 'ng-zorro-antd/alert';
import { NzModalService } from 'ng-zorro-antd/modal';
import { EvaCriteriaRateComponent } from '../../../eva-criteria-rate/container/eva-criteria-rate.component';
import {
  CriteriaGroupDataSource,
  CriteriaLinkToGoalDataSource,
  CriteriaTargetScaleDataSource,
  UseRateinsteadOfScoreDataSource,
} from '../../data/datasource-component.data';
import { IEvaCriteriaCreate } from '../../models/eva-criteria-create.model';
import { EvaCriteriaScale } from '../../models/eva-criteria.model';
@Component({
  selector: 'eva-criteria-create',
  templateUrl: './eva-criteria-create.component.html',
  styleUrls: ['./eva-criteria-create.component.scss'],
  imports: [
    NzAlertModule,
    FormsModule,
    ReactiveFormsModule,
    CommonModule,
    NzGridModule,
    NzTabsModule,
    NzCardModule,
    NzIconModule,
    NzButtonModule,
    NzEmptyModule,
    TranslateModule,
    VnrInputsModule,
    VnrButtonNewComponent,
    VnrButtonComponent,
    VnrComboBoxComponent,
    ValidatorConfigHandleDirective,
    VnrUploadV2Component,
    EvaCriteriaRateComponent,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EvaCriteriaCreateComponent implements OnInit, OnChanges {
  @Input() paramEditID: string = null;
  @Input() criteriaTypeID: string = null;
  @Input() criteriaGroupID: string = null;

  private _inputFactory: VnrInputFactory = VnrInputFactory.init();
  private _selectFactory: VnrSelectFactory = VnrSelectFactory.init();
  private _btnFactory: VnrButtonFactory = VnrButtonFactory.init();
  private _paramEdit: any = null;
  private _criteriaTargetScaleDataSource = CriteriaTargetScaleDataSource;
  private _criteriaGroupDataSource = CriteriaGroupDataSource;

  protected formulaSuggestionsFromAIs = [];
  protected formCreateOrUpdateGoalDetail: UntypedFormGroup;
  protected builderRboTargetScale: VnrRadioButtonBuilder;
  protected builderCriteriaCode: VnrTextBoxBuilder;
  protected builderCriteriaName: VnrTextBoxBuilder;
  protected builderCriteriaGroup: VnrComboBoxBuilder;
  protected builderDescriptionCalculatorScore: VnrTextBoxBuilder;
  protected builderSuggetionScale: VnrComboBoxBuilder;
  protected builderScaleRange: VnrTextBoxBuilder;
  protected builderCriteriaLinkToGoal: VnrComboBoxBuilder;
  protected builderDescription: VnrTextBoxBuilder;
  protected builderFileAttach;

  protected builderNote: VnrTextBoxBuilder;
  protected builderUseRate: VnrCheckBoxLabelBuilder;
  protected builderbtnCancel: VnrButtonNewBuilder;
  protected builderbtnSave: VnrButtonNewBuilder;

  protected isDisabledBtn = false;

  constructor(
    @Inject(VNRMODULE_TOKEN) private _vnrModule_Token: IVnrModule_Token,
    private fb: UntypedFormBuilder,
    private _translate: TranslateService,
    private _evaCriteriaFacade: EvaCriteriaFacade,
    private _commonService: CommonService,
    private _drawerRef: NzDrawerRef,
    private _modalService: NzModalService,
    private _cdr: ChangeDetectorRef,
  ) {}

  ngOnInit() {
    this.getParamEdit();
    this.initForm();
    this.initBuilders();
    this.bindDataModel();
  }
  ngOnChanges(changes: SimpleChanges): void {}

  private getParamEdit() {
    if (!this.paramEditID) {
      return;
    }
    this._evaCriteriaFacade.getById(this.paramEditID).subscribe((res) => {
      this._paramEdit = res?.Data;
    });
  }
  private bindDataModel() {
    if (!this.criteriaGroupID === false && !this._paramEdit) {
      this._paramEdit = {
        CriteriaGroupId: this.criteriaGroupID,
      };
    }
    if (!this._paramEdit) {
      return;
    }
    this.formCreateOrUpdateGoalDetail.patchValue(this._paramEdit);
    this.patchValueDefaults(this._paramEdit);
    this.formCreateOrUpdateGoalDetail.markAsDirty();
    this.formCreateOrUpdateGoalDetail.updateValueAndValidity({ emitEvent: true });
  }

  private initForm() {
    this.formCreateOrUpdateGoalDetail = this.fb.group({
      Code: [''],
      Name: ['', [Validators.required]],
      CriteriaGroupId: ['', [Validators.required]],
      Scale: [EvaCriteriaScale.Scale1To5],
      ScaleRange: [{ start: 1, end: 5 }],
      FromScore: [1],
      ToScore: [6],
      Description: [''],
      Note: [''],
      DescriptionCalculatorScore: [''],
      IsUseRate: [false],
      LinkToGoalId: [''],
      FileAttach: [''],
    });
  }
  private initBuilders() {
    this.builderRboTargetScale = this._inputFactory.builderRadioButton({
      label: 'objEval.EvaCriteria.TargetScale',
      valueField: 'value',
      textField: 'label',
      //dataSource: goalTemplateGoalTargetScaleDataSource,
      options: {
        hasFeedBack: false,
        layout: { label: 24, control: 24 },
        //columnRadioButtonItem: goalTemplateGoalTargetScaleDataSource.length,
      },
    });
    this.builderCriteriaCode = this._inputFactory.builderTextBox({
      label: this._translate.instant('objEval.EvaCriteria.Code'),
      placeholder: this._translate.instant('common.CodeGenerate.systemAutomaticCreate'),
      disabled: true,
    });
    this.builderCriteriaName = this._inputFactory.builderTextBox({
      label: this._translate.instant('objEval.EvaCriteria.Name'),
      placeholder: this._translate.instant('objEval.EvaCriteria.pleaseEnter'),
      required: true,
    });
    this.builderCriteriaGroup = this._selectFactory.builderComboBox({
      label: this._translate.instant('objEval.EvaCriteria.CriteriaGroup'),
      textField: 'Name',
      valueField: 'ID',
      disabled: !this.paramEditID === false || !this.criteriaGroupID === false,
      options: { hasFeedBack: false },
      // POC_PENDING
      //serverSide: {
      //  urlApi: this._vnrModule_Token.apiConfig_Token.apiUrl + '/api/cat_CriteriaGroup/'+ this.criteriaTypeID,
      //  method: 'get',
      //},
      dataSource: this._criteriaGroupDataSource.filter(
        (x) => x.CriteriaTypeId == this.criteriaTypeID || !this.criteriaTypeID,
      ),
    });
    this.builderDescriptionCalculatorScore = this._inputFactory.builderTextArea({
      label: this._translate.instant('objEval.EvaCriteria.DescriptionCalculatorScore'),
      placeholder: this._translate.instant('objEval.EvaCriteria.pleaseEnter'),
      rows: 3,
    });
    this.builderScaleRange = this._inputFactory.builderRangeNumber({
      label: '',
      placeHolderRangeStart: this._translate.instant('common.from'),
      placeHolderRangeEnd: this._translate.instant('common.to'),
      disabled: true,
      options: {
        format: (value: number) => value?.toString()?.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
        parser: (value: string) => value?.replace(/,*/g, ''),
        min: 0,
        step: 1,
      },
    });

    this.builderSuggetionScale = this._selectFactory.builderComboBox({
      label: '',
      options: { hasFeedBack: false, allowClear: false },
      textField: 'Name',
      valueField: 'ID',
      dataSource: this._criteriaTargetScaleDataSource,
    });
    this.builderUseRate = this._inputFactory.builderCheckBoxLabel({
      textField: 'Name',
      valueField: 'ID',
      disabled: false,
      showLabelInside: false,
      dataSource: UseRateinsteadOfScoreDataSource,
      options: {
        hasFeedBack: false,
        layout: { label: 24, control: 24 },
      },
    });

    this.builderNote = this._inputFactory.builderTextArea({
      label: this._translate.instant('objEval.EvaCriteria.Note'),
      placeholder: this._translate.instant('objEval.EvaCriteria.pleaseEnter'),
      rows: 3,
    });
    this.builderCriteriaLinkToGoal = this._selectFactory.builderComboBox({
      label: this._translate.instant('objEval.EvaCriteria.LinkToGoal'),
      options: { hasFeedBack: false },
      textField: 'Name',
      valueField: 'ID',
      // POC_PENDING
      //serverSide: {
      //  urlApi: this._vnrModule_Token.apiConfig_Token.apiUrl + '/api/cat_Define/GetType/configIntegration',
      //  method: 'get',
      //},
      dataSource: CriteriaLinkToGoalDataSource,
    });
    this.builderDescription = this._inputFactory.builderTextArea({
      label: this._translate.instant('objEval.EvaCriteria.Description'),
      rows: 3,
    });

    this.builderFileAttach = new VnrUploadBuilderV2({
      label: 'objEval.EvaCriteria.FileAttach',
      saveUrl: `${this._vnrModule_Token.apiConfig_Token.apiUrl}/api/Sys_Common/UploadChunk`,
      removeUrl: `${this._vnrModule_Token.apiConfig_Token.apiUrl}/api/Sys_Common/Chunk_Upload_Remove`,
      autoUpload: true,
      disabled: false,
      options: {
        hasFeedBack: false,
        showUploadList: {
          showPreviewIcon: true,
          showRemoveIcon: true,
          showDownloadIcon: true,
        },
        urlDownload: `${this._vnrModule_Token.apiConfig_Token.apiUrl}/api/Sys_Common/Uploads/`,
        limitFiles: 4,
      },
    });

    this.builderbtnCancel = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'common.btnCancel',
      options: {
        style: 'default',
      },
    });
    this.builderbtnSave = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'common.btnSave',
      options: {
        style: 'primary',
      },
    });
  }
  private patchValueDefaults(data) {
    this.formCreateOrUpdateGoalDetail.patchValue({
      ScaleRange: {
        start: data.FromScore,
        end: data.ToScore,
      },
    });
    setTimeout(() => {
      this.builderCriteriaGroup.dataSource.push({
        ID: data.CriteriaGroupId,
        Name: data.CriteriaGroupName,
      });
    });
  }
  protected isShowCriteriaRate() {
    return this.formCreateOrUpdateGoalDetail.get('IsUseRate')?.value === true;
  }
  protected onSuggetionScaleChange($event) {
    if (!$event) return;
    this.builderScaleRange.disabled = $event !== EvaCriteriaScale.Option;
    const suggetionScale = this._criteriaTargetScaleDataSource.find((x) => x.ID == $event);
    if (suggetionScale) {
      this.formCreateOrUpdateGoalDetail
        .get('ScaleRange')
        ?.setValue({ start: suggetionScale.FromScore, end: suggetionScale.ToScore });
      this.formCreateOrUpdateGoalDetail.get('FromScore')?.setValue(suggetionScale.FromScore);
      this.formCreateOrUpdateGoalDetail.get('ToScore')?.setValue(suggetionScale.ToScore);
    }
  }
  protected onChangeRange($event) {
    if (!$event) return;
    this.formCreateOrUpdateGoalDetail.get('FromScore')?.setValue($event.start);
    this.formCreateOrUpdateGoalDetail.get('ToScore')?.setValue($event.end);
  }
  protected onModelChangeUseRate($event: any) {}
  protected onModelChange($event: any) {}
  protected onChangeControl($event: any) {}
  protected onChange($event: any) {}
  protected onAddNewConnect() {
    this._commonService.message({ message: 'Tính năng đang phát triển', type: 'success' });
  }
  protected onSubmit() {
    if (this.formCreateOrUpdateGoalDetail.invalid) {
      Object.values(this.formCreateOrUpdateGoalDetail.controls).forEach((control) => {
        if (control.invalid) {
          control.markAsDirty();
          control.markAsTouched();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
      return;
    }
    if (this.formCreateOrUpdateGoalDetail.valid) {
      this.isDisabledBtn = true;
      const param: IEvaCriteriaCreate = {
        ID: this._paramEdit ? this._paramEdit.ID : null,
        ...this.formCreateOrUpdateGoalDetail.value,
      };
      this._evaCriteriaFacade.createOrUpdate(param).subscribe((res) => {
        const message =
          res?.Status === ResponseStatus.SUCCESS
            ? this._translate.instant('common.message.actionSuccess')
            : res?.Message;
        const type = res?.Status === ResponseStatus.SUCCESS ? 'success' : 'error';
        this._commonService.message({ message, type });
        this.isDisabledBtn = false;
        if (res?.Status === ResponseStatus.SUCCESS) {
          this._drawerRef?.close({
            isReloadData: true,
          });
        }
      });
    }
  }
  protected onCancel() {
    this._drawerRef?.close({
      isReloadData: false,
    });
  }
}
