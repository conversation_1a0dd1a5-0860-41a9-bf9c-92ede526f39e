<div class="schedule-grid">
  <vnr-toolbar-new [builder]="builderToolbar">
    <vnr-button rightToolbar [vnrTemplate]="tplBtnAddSchedule"></vnr-button>
  </vnr-toolbar-new>
  <ng-template #tplBtnAddSchedule>
    <app-add-schedule-button class="m-auto"></app-add-schedule-button><span class="mx-1"></span>
    <nz-segmented
      [nzOptions]="viewModeViewOptions"
      (nzValueChange)="handleSwitchModeView($event)"
      nzSize="small"
      class="--custom-segmented ml-1"
      [(ngModel)]="viewMode"
    ></nz-segmented>
    <span class="mx-1">|</span>
    <vnr-button rightToolbar [vnrTemplate]="tplBtn"></vnr-button>
  </ng-template>
  <ng-template #tplBtn>
    <button
      class="ant-btn toolbar-border-radius vnr-button-default ant-btn-default ng-star-inserted mr-1"
      style="border: 1px solid #d9d9d9; border-radius: 6px"
    >
      <i class="fa-light fa-file-export mr-1"></i> Xuất dữ liệu
    </button>
    <button
      class="ant-btn toolbar-border-radius vnr-button-default ant-btn-default ng-star-inserted mr-1"
      style="border: 1px solid #d9d9d9; border-radius: 6px"
    >
      <i class="fa-light fa-chart-pie"></i>
    </button>
    <button
      class="ant-btn toolbar-border-radius vnr-button-default ant-btn-default ng-star-inserted"
      style="border: 1px solid #d9d9d9; border-radius: 6px"
    >
      <i class="fa-light fa-filter"></i>
    </button>
  </ng-template>
  <div [ngSwitch]="viewMode">
    <div *ngSwitchCase="'list'">
      <vnr-grid-new
        #vnrGrid
        [builder]="builderGrid"
        [builder]="builderGrid"
        [gridName]="gridName"
        [dataLocal]="dataLocal"
        [columns]="columns"
        (vnrEdit)="onGridEdit($event)"
        (vnrDelete)="onGridDelete($event)"
        (vnrViewDetails)="onGridViewDetail($event)"
        [defaultColumnTemplate]="tplCustomTemplateByColumn"
      >
        <ng-template
          #tplCustomTemplateByColumn
          let-dataItem
          let-column="columnItem"
          let-field="field"
        >
          <ng-container [ngSwitch]="column['Name']">
            <!-- Report Name Template -->
            <span class="--has-custom-template" *ngSwitchCase="'reportName'">
              <div class="report-name">
                <div class="title">{{ dataItem['reportName'] }}</div>
                <div class="subtitle">
                  <i nz-icon nzType="calendar" nzTheme="outline"></i>
                  {{ dataItem['reportType'] || 'Weekly Check-in' }}
                </div>
              </div>
            </span>

            <!-- Frequency Template -->
            <span class="--has-custom-template" *ngSwitchCase="'frequency'">
              <div class="frequency">
                <div class="time">
                  <i nz-icon nzType="calendar" nzTheme="outline"></i>
                  {{ dataItem['frequency']['type'] }}
                </div>
                <div class="detail">{{ dataItem['frequency']['time'] }}</div>
              </div>
            </span>

            <!-- Next Report Template -->
            <span class="--has-custom-template" *ngSwitchCase="'nextReport'">
              <div class="next-report">
                <div class="date">{{ dataItem['nextReport']['date'] }}</div>
                <div class="reminder">Nhắc nhở: {{ dataItem['nextReport']['reminderTime'] }}</div>
              </div>
            </span>

            <!-- Assignee Template -->
            <span class="--has-custom-template" *ngSwitchCase="'assignee'">
              <div class="assignee">
                <app-vnr-letters-avatar
                  class="assignee-avatar"
                  [avatarName]="dataItem['assignee']"
                  [circular]="true"
                  [width]="32"
                  [src]="dataItem['assigneeAvatar']"
                ></app-vnr-letters-avatar>
                <span>{{ dataItem['assignee'] }}</span>
              </div>
            </span>

            <!-- Status Template -->
            <span class="--has-custom-template" *ngSwitchCase="'status'">
              <div class="status">
                <vnr-tag
                  *ngIf="dataItem['status']; else templateEmpty"
                  [vnrColor]="getStatusColor(dataItem['status']['type'])"
                  [vnrTitle]="getStatusLabel(dataItem['status']['type'])"
                  [isBordered]="false"
                ></vnr-tag>
                <div class="last-update">
                  Báo cáo gần nhất: {{ dataItem['status']['lastUpdate'] }}
                </div>
              </div>
            </span>

            <!-- Default Template -->
            <span class="--has-custom-template" *ngSwitchDefault>
              {{ dataItem[column['Name']] || '-' }}
            </span>
          </ng-container>
        </ng-template>

        <ng-template #templateEmpty>-</ng-template>
      </vnr-grid-new>
    </div>
    <div *ngSwitchCase="'calendar'">
      <vnr-grid-new
        #vnrGrid
        [builder]="builderGrid"
        [gridName]="gridName"
        [dataLocal]="dataLocal"
        [columns]="columns"
        (vnrEdit)="onGridEdit($event)"
        (vnrDelete)="onGridDelete($event)"
        (vnrViewDetails)="onGridViewDetail($event)"
        [defaultColumnTemplate]="tplCustomTemplateByColumn"
      >
        <ng-template
          #tplCustomTemplateByColumn
          let-dataItem
          let-column="columnItem"
          let-field="field"
        >
          <ng-container [ngSwitch]="column['Name']">
            <!-- Report Name Template -->
            <span class="--has-custom-template" *ngSwitchCase="'reportName'">
              <div class="report-name">
                <div class="title">{{ dataItem['reportName'] }}</div>
                <div class="subtitle">
                  <i nz-icon nzType="calendar" nzTheme="outline"></i>
                  {{ dataItem['reportType'] || 'Weekly Check-in' }}
                </div>
              </div>
            </span>

            <!-- Frequency Template -->
            <span class="--has-custom-template" *ngSwitchCase="'frequency'">
              <div class="frequency">
                <div class="time">
                  <i nz-icon nzType="calendar" nzTheme="outline"></i>
                  {{ dataItem['frequency']['type'] }}
                </div>
                <div class="detail">{{ dataItem['frequency']['time'] }}</div>
              </div>
            </span>

            <!-- Next Report Template -->
            <span class="--has-custom-template" *ngSwitchCase="'nextReport'">
              <div class="next-report">
                <div class="date">{{ dataItem['nextReport']['date'] }}</div>
                <div class="reminder">Nhắc nhở: {{ dataItem['nextReport']['reminderTime'] }}</div>
              </div>
            </span>

            <!-- Assignee Template -->
            <span class="--has-custom-template" *ngSwitchCase="'assignee'">
              <div class="assignee">
                <app-vnr-letters-avatar
                  class="assignee-avatar"
                  [avatarName]="dataItem['assignee']"
                  [circular]="true"
                  [width]="32"
                  [src]="dataItem['assigneeAvatar']"
                ></app-vnr-letters-avatar>
                <span>{{ dataItem['assignee'] }}</span>
              </div>
            </span>

            <!-- Status Template -->
            <span class="--has-custom-template" *ngSwitchCase="'status'">
              <div class="status">
                <vnr-tag
                  *ngIf="dataItem['status']; else templateEmpty"
                  [vnrColor]="getStatusColor(dataItem['status']['type'])"
                  [vnrTitle]="getStatusLabel(dataItem['status']['type'])"
                  [isBordered]="false"
                ></vnr-tag>
                <div class="last-update">
                  Báo cáo gần nhất: {{ dataItem['status']['lastUpdate'] }}
                </div>
              </div>
            </span>

            <!-- Default Template -->
            <span class="--has-custom-template" *ngSwitchDefault>
              {{ dataItem[column['Name']] || '-' }}
            </span>
          </ng-container>
        </ng-template>
        <ng-template #templateEmpty>-</ng-template>
      </vnr-grid-new>
    </div>
  </div>
</div>
