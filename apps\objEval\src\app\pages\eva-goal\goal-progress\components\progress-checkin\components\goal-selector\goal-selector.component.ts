import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzDrawerRef } from 'ng-zorro-antd/drawer';
import { TranslateModule } from '@ngx-translate/core';
import {
  VnrTreelistNewBuilder,
  VnrTreelistNewComponent,
  VnrTreelistNewModule,
  VnrButtonNewComponent,
  VnrButtonNewBuilder,
  VnrButtonFactory,
  VnrToolbarNewBuilder,
  VnrToolbarNewComponent,
} from '@hrm-frontend-workspace/vnr-module';
import { VnrLettersAvatarComponent, VnrTagComponent } from '@hrm-frontend-workspace/ui';
import { progressOverviewListData } from '../../../../data/progress-overview-list.data';
import { progressOverviewListDefineColumn } from '../../../../data/progress-overview-list-define-column.data';
import { TargetFormatPipe } from '../../../../../shared/pipe/target-format.pipe';
import { statusColorMap, statusTextMap } from '../../../../data/progress.enum';
import { NzProgressModule } from 'ng-zorro-antd/progress';
import { progressOverviewListDefineFilter } from '../../../../data/progress-overview-list-define-filter.data';

@Component({
  selector: 'app-goal-selector',
  templateUrl: './goal-selector.component.html',
  styleUrls: ['./goal-selector.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    VnrTreelistNewModule,
    VnrButtonNewComponent,
    VnrLettersAvatarComponent,
    TargetFormatPipe,
    VnrTagComponent,
    NzProgressModule,
    VnrToolbarNewComponent,
  ],
})
export class GoalSelectorComponent implements OnInit {
  @ViewChild('vnrTreeList', { static: false }) vnrTreeList: VnrTreelistNewComponent;

  builderTreeList: VnrTreelistNewBuilder;
  cancelBuilder: VnrButtonNewBuilder;
  selectBuilder: VnrButtonNewBuilder;
  protected statusColorMap = statusColorMap;
  protected statusTextMap = statusTextMap;

  private readonly _buttonFactory = VnrButtonFactory.init();
  private readonly targetFormatPipe = new TargetFormatPipe();

  selectedGoals: any[] = [];

  private _screenName = 'GoalSelector';
  private _storeName = 'eva_sp_get_GoalProgress';

  protected isSupperAdmin = true;
  protected gridName = 'GoalSelector_Grid';
  protected columns = progressOverviewListDefineColumn;
  protected dataLocal = progressOverviewListData;
  builderToolbar: VnrToolbarNewBuilder;
  constructor(private drawerRef: NzDrawerRef<any[]>) {}

  ngOnInit() {
    this.initTreeList();
    this.initButtons();
    this.initToolbar();
  }

  private initToolbar() {
    this.builderToolbar = new VnrToolbarNewBuilder({
      gridName: this.gridName,
      isSupperAdmin: this.isSupperAdmin,
      gridRef: this.vnrTreeList,
      storeName: this._storeName,
      screenName: this._screenName,
      isShowConfig: true,
      options: {
        configButtonChangeColumn: { isShow: true },
        configButtonExport: { isShowBtnExcelAll: true },
        configQuickSearch: {
          isShow: true,
        },
        configFilterAdvanceQuick: {
          isShow: true,
          components: progressOverviewListDefineFilter(),
          keyConfig: 'GoalSelector_FilterAdvanceSeting',
          isShowBtnAdvance: false,
        },
      },
    });
  }

  private initTreeList() {
    this.builderTreeList = new VnrTreelistNewBuilder({
      options: {
        configHeightGrid: {
          isAllowCalcRowHeight: true,
        },
        displayField: 'GoalName',
        configSelectable: {
          columnKey: 'ID',
          groupKey: 'ParentID',
        },
        configCommandColumn: {
          isEnabledMenuAction: false,
        },
        configShowHide: {
          isShowViewDetail: false,
          isPageExpand: false,
          isShowDelete: false,
          isShowEdit: false,
        },
      },
    });
  }

  private initButtons() {
    this.cancelBuilder = this._buttonFactory.builder({
      action: '',
      text: 'Hủy',
      options: { style: 'default' },
    });

    this.selectBuilder = this._buttonFactory.builder({
      action: '',
      text: 'Chọn',
      options: { style: 'primary' },
    });
  }

  onSelectionChange(event: any) {
    this.selectedGoals = event.selectedItems || [];
  }

  selectGoals() {
    console.log('Selected goals before mapping:', this.selectedGoals);

    // Chuyển đổi dữ liệu từ progress-overview format sang child-goal format
    const selectedChildGoals = this.selectedGoals.map((goal) => {
      const mappedGoal = {
        goalName: goal.GoalName || '',
        representative: goal.Representative || '',
        period: goal.Period || '',
        target: this.targetFormatPipe.transform(goal.Target, goal.Unit, 'vi') || '',
        latestResult: this.targetFormatPipe.transform(goal.LatestResult, goal.Unit, 'vi') || '',
        yearTarget: this.targetFormatPipe.transform(goal.TotalTarget, goal.Unit, 'vi') || '',
        accumulated: this.targetFormatPipe.transform(goal.DoneTarget, goal.Unit, 'vi') || '',
        comment: '',
        attachment: null,
      };

      console.log('Mapped goal:', mappedGoal);
      return mappedGoal;
    });

    console.log('Final selected child goals:', selectedChildGoals);
    this.drawerRef.close(selectedChildGoals);
  }

  cancel() {
    this.drawerRef.close();
  }
}
