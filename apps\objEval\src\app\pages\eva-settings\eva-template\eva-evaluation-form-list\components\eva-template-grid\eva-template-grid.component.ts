import { CommonModule, NgIf } from '@angular/common';
import {
  Component,
  OnChanges,
  OnInit,
  SimpleChanges,
  TemplateRef,
  ViewChild,
  ViewContainerRef,
  OnDestroy,
} from '@angular/core';
import {
  FormsModule,
  ReactiveFormsModule,
  UntypedFormBuilder,
  UntypedFormGroup,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { vnrUtilities } from '@hrm-frontend-workspace/common';
import { VnrTagComponent } from '@hrm-frontend-workspace/ui';
import {
  VnrButtonNewBuilder,
  VnrButtonNewComponent,
  VnrComboBoxBuilder,
  VnrGridNewBuilder,
  VnrGridNewComponent,
  VnrToolbarNewBuilder,
  VnrToolbarNewComponent,
  VnrTreeViewBuilder,
} from '@hrm-frontend-workspace/vnr-module';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { gridDefineColumns } from '../../data/column.data';
import { gridDefineDataFilterQuick } from '../../data/data-filter-advance';
import { evaTemplateDataSource } from '../../data/datasource.data';
import {
  evaTemplateDepartmentFormat,
  evaTemplateEmployeeFormat,
  evaTemplatePositionFormat,
  evaTemplateStatusFormat,
} from '../../data/eva-template.data';
import { EvaTemplateFacade } from '../../facade/eva-template.facade';
import { IFormTemplate } from '../../../eva-create-review-form/models/form-builder.model';
import { Store } from '@ngrx/store';
import * as EvaTemplateSelectors from '../../../store/eva-template.selectors';

@Component({
  selector: 'app-eva-template-grid',
  templateUrl: './eva-template-grid.component.html',
  styleUrls: ['./eva-template-grid.component.scss'],
  imports: [
    FormsModule,
    ReactiveFormsModule,
    CommonModule,
    NgIf,
    VnrToolbarNewComponent,
    VnrGridNewComponent,
    VnrTagComponent,
    TranslateModule,
    VnrButtonNewComponent,
  ],
})
export class EvaTemplateGridComponent implements OnInit, OnDestroy {
  @ViewChild('vnrGrid', { static: true }) gridControl: VnrGridNewComponent;
  @ViewChild('templateType', { static: true }) private _templateType: TemplateRef<any>;
  @ViewChild('templateStatus', { static: true }) private _templateStatus: TemplateRef<any>;
  @ViewChild('templateDepartment', { static: true }) private _templateDepartment: TemplateRef<any>;
  @ViewChild('templatePosition', { static: true }) private _templatePosition: TemplateRef<any>;
  @ViewChild('templateEmployee', { static: true }) private _templateEmployee: TemplateRef<any>;
  private _statusFormat = evaTemplateStatusFormat;
  private _departmentFormat = evaTemplateDepartmentFormat;
  private _positionFormat = evaTemplatePositionFormat;
  private _employeeFormat = evaTemplateEmployeeFormat;
  private _gridName = 'eva-template-grid';

  protected selectedItem = [];
  protected builderOrg: VnrTreeViewBuilder = new VnrTreeViewBuilder();
  protected builderPosition: VnrComboBoxBuilder = new VnrComboBoxBuilder();
  protected builderButtonApprove: VnrButtonNewBuilder;
  protected formGroup: UntypedFormGroup = this._formBuider.group({
    department: [''],
    position: [''],
  });
  protected listColumnTemplates: any = {};
  protected builderGrid: VnrGridNewBuilder;
  protected builderToolbar: VnrToolbarNewBuilder;
  protected builderButtonCustom: VnrButtonNewBuilder;
  protected builderButtonCreate: VnrButtonNewBuilder;
  protected builderButtonEdit: VnrButtonNewBuilder;
  protected builderButtonDelete: VnrButtonNewBuilder;
  protected builderButtonCopy: VnrButtonNewBuilder;
  protected builderbtnDoNo: VnrButtonNewBuilder;
  protected builderbtnDoYes: VnrButtonNewBuilder;
  protected gridName = 'eva-template-grid';
  protected dataLocal: IFormTemplate[] = [];
  protected columns = gridDefineColumns;
  protected dataFormSearch = {};
  // Store selectors
  templates$: Observable<IFormTemplate[]>;
  loading$: Observable<boolean>;
  error$: Observable<any>;

  private destroy$ = new Subject<void>();

  constructor(
    private _modalService: NzModalService,
    private _formBuider: UntypedFormBuilder,
    private viewContainerRef: ViewContainerRef,
    private translateService: TranslateService,
    private notificationService: NzNotificationService,
    private evaTemplateFacade: EvaTemplateFacade,
    private _router: Router,
    private _route: ActivatedRoute,
    private store: Store,
  ) {
    this.templates$ = this.evaTemplateFacade.templates$;
    this.loading$ = this.evaTemplateFacade.loading$;
    this.error$ = this.evaTemplateFacade.error$;
  }

  ngOnInit() {
    this.builderTemplate();
    this.builderToolbarComponent();
    this.builderButtonComponent();
    this.builderDialogButtons();
    this.store.select(EvaTemplateSelectors.selectTemplates).subscribe((templates) => {
      if (templates) {
        this.dataLocal = templates; // Tạo một bản sao mới của mảng
        this.gridControl.vnrReadGrid();
        this.builderGridComponent();
      }
    });

    // Load templates when component initializes
    // this.evaTemplateFacade.loadTemplates();
    // Subscribe to templates to update grid data
    // this.templates$.pipe(takeUntil(this.destroy$)).subscribe((templates) => {
    // });

    // Subscribe to error to show notification
    this.error$.pipe(takeUntil(this.destroy$)).subscribe((error) => {
      if (error) {
        this.notificationService.error(
          this.translateService.instant('common.notification.error'),
          error.message || this.translateService.instant('common.notification.deleteError'),
        );
      }
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    console.log(changes);
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private builderTemplate() {
    this.listColumnTemplates = {
      StatusView: this._templateStatus,
      Department: this._templateDepartment,
      Position: this._templatePosition,
      Employee: this._templateEmployee,
    };
  }

  private builderButtonComponent() {
    this.builderButtonCreate = new VnrButtonNewBuilder({
      id: 'create-btn',
      text: this.translateService.instant('common.create'),
      action: 'create',
      options: {
        style: 'primary',
        icon: { fontIcon: 'plus' },
      },
    });

    this.builderButtonEdit = new VnrButtonNewBuilder({
      id: 'edit-btn',
      text: this.translateService.instant('common.edit'),
      action: 'edit',
      options: {
        style: 'warning',
        icon: { fontIcon: 'edit' },
      },
    });

    this.builderButtonDelete = new VnrButtonNewBuilder({
      id: 'delete-btn',
      text: this.translateService.instant('common.delete'),
      action: 'delete',
      options: {
        style: 'danger',
        icon: { fontIcon: 'trash' },
      },
    });

    this.builderButtonCopy = new VnrButtonNewBuilder({
      id: 'copy-btn',
      text: this.translateService.instant('common.copy'),
      action: '',
      options: {
        style: 'primary',
        icon: { fontIcon: 'copy' },
      },
    });
  }

  private builderToolbarComponent() {
    this.builderToolbar = new VnrToolbarNewBuilder({
      isShowConfig: true,
      gridName: this._gridName,
      isSupperAdmin: true,
      gridRef: this.gridControl,
      permission: '',
      screenName: 'eva-template-grid',
      storeName: '',
      options: {
        configButtonChangeColumn: {
          isShow: true,
          isConfigMenuDisplay: true,
          isChangeColumnNew: true,
          isDisabled: false,
          configButtonSetting: {
            isShow: true,
            isDisabled: false,
          },
        },
        configButtonExport: {
          isShowBtnExcelAll: true,
          isShowBtnExcelByTemplate: true,
        },
        configQuickSearch: {
          textPlaceHolder: this.translateService.instant('common.search'),
          searchKey: 'EvaName',
        },
        configFilterAdvanceQuick: {
          isShow: true,
          components: gridDefineDataFilterQuick(),
          keyConfig: 'EvaTemplate',
          isShowBtnAdvance: false,
        },
        isSetBackground: false,
        buttonMoreFlexDirection: 'inherit',
        isExpanded: false,
      },
    });
  }

  private builderGridComponent() {
    this.builderGrid = new VnrGridNewBuilder({
      options: {
        configHeightGrid: {
          isAllowCalcRowHeight: true,
        },
        configSelectable: {
          columnKey: 'ID',
        },
        configShowHide: {
          isPageExpand: false,
        },
        configCommandColumn: {
          isEnabledMenuAction: true,
        },
        configGroupable: {
          isEnabled: false,
        },
        configPageable: {
          buttonCount: 5,
          pageSizes: [10, 20, 50],
        },
      },
    });
  }

  private builderDialogButtons() {
    this.builderbtnDoNo = new VnrButtonNewBuilder({
      text: this.translateService.instant('common.cancel'),
      action: '',
      options: {
        style: 'default',
      },
    });

    this.builderbtnDoYes = new VnrButtonNewBuilder({
      text: this.translateService.instant('common.AGREE'),
      action: '',
      options: {
        style: 'primary',
      },
    });
  }

  protected getSelectedID($event: any) {
    this.selectedItem = $event;
  }

  protected getDataItem($event: any) {
    console.log($event);
  }

  protected onOpenDetail($event: any) {
    console.log($event);
  }

  protected onGridEdit($event: any) {
    const templateId = $event.ID;
    const queryParams: any = {};
    this._router.navigate(['/objEval/setting/eva-template/form-builder', templateId], {
      relativeTo: this._route,
      queryParamsHandling: 'merge',
      queryParams: queryParams,
    });
  }

  protected onGridCopy($event: any) {
    const templateId = vnrUtilities.newGuid();
    const queryParams: any = {};
    this._router.navigate(['/objEval/setting/eva-template/form-builder', templateId], {
      relativeTo: this._route,
      queryParamsHandling: 'merge',
      queryParams: queryParams,
    });
  }

  protected onGridDelete($event: any) {
    const itemsToDelete = $event
      ? [$event]
      : this.selectedItem
          .map((id) => {
            return this.dataLocal.find((item) => item.ID === id);
          })
          .filter((item) => item);

    if (!itemsToDelete || itemsToDelete.length === 0) {
      return;
    }

    this._modalService.confirm({
      nzTitle: this.translateService.instant(
        itemsToDelete.length > 1
          ? 'objEval.EvaTemplate.deleteMultipleConfirm'
          : 'objEval.EvaTemplate.deleteConfirm',
      ),
      nzOkText: this.translateService.instant('common.AGREE'),
      nzOkType: 'primary',
      nzOkDanger: false,
      nzOnOk: () => this.deleteTemplates(itemsToDelete),
      nzCancelText: this.translateService.instant('common.cancel'),
    });
  }

  private deleteTemplates(templates: IFormTemplate[]) {
    templates.forEach((template) => {
      this.evaTemplateFacade.deleteTemplate(template.ID);
    });
  }

  protected onGridCellClick($event: any) {
    console.log($event);
  }

  public setDataFilter(data: any): void {
    this.dataFormSearch = data;
    this.refreshGrid();
  }

  public refreshGrid(): void {
    this.gridControl.vnrReadGrid();
  }

  public getSelectedIDs() {
    return this.selectedItem;
  }

  protected getColorStatus(value: any): string {
    return this._statusFormat[value] || 'default';
  }

  protected getDepartmentColor(value: any): string {
    return this._departmentFormat[value] || 'default';
  }

  protected getPositionColor(value: any): string {
    return this._positionFormat[value] || 'default';
  }

  protected getEmployeeColor(value: any): string {
    return this._employeeFormat[value] || 'default';
  }

  protected onConfirmDelete($event: any, modalRef: any) {
    modalRef.destroy();
    this.deleteTemplates([$event]);
  }
}
