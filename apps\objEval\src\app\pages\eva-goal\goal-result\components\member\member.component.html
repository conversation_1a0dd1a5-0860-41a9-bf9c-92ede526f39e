<div class="p-3">
  <vnr-toolbar-new [builder]="builderToolbar">
    <vnr-button rightToolbar [vnrTemplate]="tplBtnCore"></vnr-button>
  </vnr-toolbar-new>
  <ng-template #tplBtnCore>
    <button
      class="ant-btn toolbar-border-radius vnr-button-default ant-btn-default ng-star-inserted mr-1"
      style="border: 1px solid #d9d9d9; border-radius: 6px"
    >
      <i class="fa-light fa-file-export mr-1"></i> Xuất dữ liệu
    </button>
    <button
      class="ant-btn toolbar-border-radius vnr-button-default ant-btn-default ng-star-inserted mr-1"
      style="border: 1px solid #d9d9d9; border-radius: 6px"
    >
      <i class="fa-light fa-chart-pie"></i>
    </button>
    <button
      class="ant-btn toolbar-border-radius vnr-button-default ant-btn-default ng-star-inserted"
      style="border: 1px solid #d9d9d9; border-radius: 6px"
    >
      <i class="fa-light fa-filter"></i>
    </button>
  </ng-template>
  <vnr-grid-new
    #vnrGrid
    [builder]="builderGrid"
    [gridName]="gridName"
    [dataLocal]="dataLocal"
    [columns]="columns"
    [isSupperAdmin]="isSupperAdmin"
    [defaultColumnTemplate]="tplCustomTemplateByColumn"
    (vnrDoubleClick)="handleDoubleClick($event)"
  >
    <ng-template #tplCustomTemplateByColumn let-dataItem let-column="columnItem" let-field="field">
      <ng-container [ngSwitch]="column['Name']">
        <span *ngSwitchCase="'UserName'">
          <div class="d-flex align-items-center">
            <app-vnr-letters-avatar
              [avatarName]="dataItem['UserName']"
              [circular]="true"
              [width]="32"
              class="mr-2"
            ></app-vnr-letters-avatar>
            <span>{{ dataItem['UserName'] }}</span>
          </div>
        </span>
        <span *ngSwitchCase="'CompletionRate'">
          <!-- Nếu completionRate = 100 thì hiển thị đỏ, nếu completionRate < 100 thì hiển thị xanh -->
          <span *ngIf="dataItem['CompletionRate'] === 100" style="color: #10a91e">
            {{ dataItem['CompletionRate'] }}%
          </span>
          <span *ngIf="dataItem['CompletionRate'] < 100"> {{ dataItem['CompletionRate'] }}% </span>
        </span>

        <!-- Kết quả -->
        <span *ngSwitchCase="'Resut'">
          <span class="text-muted">{{ dataItem['Resut'] }}</span>
        </span>

        <!-- Xếp hạng -->
        <span *ngSwitchCase="'Ranking'">
          <span class="text-muted">{{ dataItem['Ranking'] }}</span>
        </span>

        <!-- Default Template -->
        <span *ngSwitchDefault>
          {{ dataItem[column['Name']] || '' }}
        </span>
      </ng-container>
    </ng-template>

    <ng-template #templateEmpty>-</ng-template>
  </vnr-grid-new>
</div>
