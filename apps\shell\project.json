{"name": "shell", "$schema": "../.node_modules/nx/schemas/project-schema.json", "projectType": "application", "prefix": "hrm-frontend-workspace", "sourceRoot": "apps/shell/src", "tags": [], "targets": {"build": {"executor": "@nx/angular:webpack-browser", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/shell", "index": "apps/shell/src/index.html", "main": "apps/shell/src/main.ts", "polyfills": ["zone.js", "apps/shell/src/polyfills.ts"], "tsConfig": "apps/shell/tsconfig.app.json", "inlineStyleLanguage": "scss", "allowedCommonJsDependencies": ["lodash", "rxjs-compat", "rxjs/internal/observable/combineLatest", "angular2-chartjs", "@ant-design/colors", "@ant-design/icons-angular", "@ant-design/icons-angular/icons", "<PERSON><PERSON><PERSON>", "highcharts/highmaps", "highcharts", "highcharts/highstock", "@babel/runtime-corejs3", "date-fns", "raf", "jspdf", "canvg", "core-js", "core-js-pure", "@babel/runtime", "@angularclass/hmr", "qs", "store", "apps/shell/*", "libs/*", "commonjs", "deepmerge", "rgbcolor", "inputmask", "angular2-uuid", "j<PERSON>y", "moment", "pdfobject", "unorm"], "assets": [{"glob": "**/*", "input": "apps/shell/assets", "ignore": ["**/styles/**/*"], "output": "/assets"}, {"glob": "**/*", "input": "node_modules/@ant-design/icons-angular/src/inline-svg/", "output": "/assets/"}], "styles": [{"input": "node_modules/@progress/kendo-theme-default/dist/all.css"}, "node_modules/ng-zorro-antd/ng-zorro-antd.min.css", "libs/ui/styles/kit/vendors/antd/themes/default.less", "libs/ui/styles/kit/vendors/antd/themes/dark.less", "node_modules/bootstrap/dist/css/bootstrap.min.css", "node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "node_modules/summernote/dist/summernote-lite.min.css", "apps/shell/src/global.scss", "node_modules/@ctrl/ngx-emoji-mart/picker.css"], "scripts": ["node_modules/jquery/dist/jquery.min.js", "node_modules/summernote/dist/summernote-lite.min.js", "libs/ui/assets/fonts/fontawesome-pro-6.5.1/js/all.min.js"], "stylePreprocessorOptions": {"includePaths": ["libs", "apps/shell/src", "apps/shell", "node_modules", "libs/ui/styles", "libs/ui/assets", "libs/vnr-module", "libs/vnr-survey", "libs/domain"]}, "customWebpackConfig": {"path": "apps/shell/webpack.config.ts"}}, "configurations": {"production": {"assets": [{"glob": "**/*", "input": "apps/shell/assets/", "ignore": ["**/styles/**/*", "**/config.json", "**/auth-config.json", "**/*.config", "**/module-federation.manifest.json", "**/config/text-config.json"], "output": "/assets/"}, {"glob": "**/*", "input": "node_modules/@ant-design/icons-angular/src/inline-svg/", "output": "/assets/"}], "fileReplacements": [{"replace": "apps/shell/src/environments/environment.ts", "with": "apps/shell/src/environments/environment.prod.ts"}], "budgets": [{"type": "initial", "maximumWarning": "4mb", "maximumError": "200mb"}], "outputHashing": "all", "customWebpackConfig": {"path": "apps/shell/webpack.prod.config.ts"}, "buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "verbose": false, "fileReplacements": [{"replace": "apps/shell/src/environments/environment.ts", "with": "apps/shell/src/environments/environment.dev.ts"}]}}, "defaultConfiguration": "production"}, "serve": {"executor": "@nx/angular:module-federation-dev-server", "options": {"port": 4200, "publicHost": "http://localhost:4200"}, "configurations": {"production": {"buildTarget": "shell:build:production"}, "development": {"buildTarget": "shell:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "shell:build"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/shell/jest.config.ts"}}, "serve-static": {"executor": "@nx/web:file-server", "options": {"buildTarget": "shell:build", "port": 4200, "spa": true}}}}