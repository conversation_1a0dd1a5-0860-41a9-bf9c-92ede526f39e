import { Async<PERSON>ipe, CommonModule, NgIf } from '@angular/common';
import { Component, OnInit, ViewContainerRef } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { NzModalService } from 'ng-zorro-antd/modal';
import { FormCanvasComponent } from '../../../../../../pages/eva-settings/eva-template/eva-create-review-form/components/form-canvas/form-canvas.component';
import { EnumFormType } from '../../../../../../pages/eva-settings/eva-template/eva-create-review-form/models/enums/form-canvas.enum';
import { IFormTemplate } from '../../../../../../pages/eva-settings/eva-template/eva-create-review-form/models/form-builder.model';
import { EvaAppraisalsFormApi } from '../api/eva-appraisals-form.api';
import { EvaAppraisalsFormFacade } from '../facade/eva-appraisals-form.facade';
import { EvaAppraisalsFormState } from '../state/eva-appraisals-form.state';
import { listEmpData } from '../data/list-emp-test.data';

@Component({
  selector: 'app-eva-appraisals-form',
  templateUrl: './eva-appraisals-form.component.html',
  styleUrls: ['./eva-appraisals-form.component.scss'],
  imports: [CommonModule, TranslateModule, NgIf, FormCanvasComponent, AsyncPipe],
  providers: [EvaAppraisalsFormFacade, EvaAppraisalsFormState, EvaAppraisalsFormApi],
  standalone: true,
})
export class EvaAppraisalsFormComponent implements OnInit {
  protected templateId: string;
  protected appraisalsForm: IFormTemplate;

  protected _edit = EnumFormType.EDIT;
  protected _preview = EnumFormType.PREVIEW;
  protected _selectedEmployeeID = '1';
  constructor(
    private _modalService: NzModalService,
    private viewContainerRef: ViewContainerRef,
    private routerState: ActivatedRoute,
    private _router: Router,
    private _evaAppraisalsFormFacade: EvaAppraisalsFormFacade,
  ) {
    // Lấy template ID và profile ID từ route params
    this._selectedEmployeeID = this.routerState?.snapshot?.paramMap?.get('ProfileID') || '1';
    console.log(this._selectedEmployeeID, 'ProfileID');
    this.routerState.params.subscribe((params) => {
      this.templateId = params['id'];
      this._evaAppraisalsFormFacade.loadAppraisalsForm(this.templateId);
    });
  }

  ngOnInit() {
    this._evaAppraisalsFormFacade.getAppraisalsForm$().subscribe((form) => {
      const updatedTemplate: any = listEmpData.find((item) => item.ID === this._selectedEmployeeID);

      // Cập nhật sections bằng map thay vì forEach
      this.appraisalsForm = {
        ...form,
        sections: form.sections.map((section) => {
          if (section.type === 'overview') {
            return {
              ...section,
              data: {
                ...section.data,
                employeeCode: updatedTemplate.CodeEmp,
                employeeName: updatedTemplate.ProfileName,
                department: updatedTemplate.OrgStructureName,
                company: updatedTemplate.CompanyName,
                departmentTree: updatedTemplate.OrgStructureName,
                position: updatedTemplate.PositionName || '-',
                evaluator: updatedTemplate.EvaluatorName || '-',
                title: updatedTemplate.TitleName || '-',
                result: updatedTemplate.Result || '-',
                rank: updatedTemplate.Rank || '-',
                seniority: updatedTemplate.Seniority || '-',
                email: updatedTemplate.Email || '-',
                joinDate: updatedTemplate.JoinDate || '-',
              },
            };
          }
          return section; // Trả về section gốc nếu không phải type overview
        }),
      };
      console.log(this.appraisalsForm);
    });
  }

  onBack() {
    const queryParams: any = {};
    this._router.navigate(['/objEval/eva-appraisals/appraisals/list'], {
      relativeTo: this.routerState,
      queryParams: queryParams,
      queryParamsHandling: 'merge',
    });
  }
}
