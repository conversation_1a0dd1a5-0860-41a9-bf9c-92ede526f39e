<vnr-toolbar-new
  class="border-0 --custom-bg width-100p manage-personnel-records-grids__toolbar"
  [builder]="builderToolbar"
  (vnrChangeColumn)="toggleChangeColumn($event)"
>
  <vnr-button-new
    [builder]="builderButtonApprove"
    (vnrClick)="onActionApprove($event)"
  ></vnr-button-new>
  <vnr-button-new
    [builder]="builderButtonReject"
    (vnrClick)="onActionReject($event)"
  ></vnr-button-new>
  <vnr-button-new
    [builder]="builderButtonAddNew"
    (vnrClick)="onActionAddNew($event)"
  ></vnr-button-new>
</vnr-toolbar-new>
<vnr-grid-new
  class="grid-new"
  #vnrGrid
  [builder]="builderGrid"
  [dataLocal]="dataSource"
  [columns]="columns"
  [gridName]="gridName"
  [isSupperAdmin]="isSupperAdmin"
  [isChangeColumnNew]="true"
  [defaultColumnTemplate]="tplCustomTemplateByColumn"
  (getSelectedID)="getSelectedID($event)"
  (getDataItem)="getDataItem($event)"
  (vnrDoubleClick)="onOpenDetail($event)"
  (vnrViewModeGrid)="onVnRViewModeGrid($event)"
  (vnrEdit)="onGridEdit($event)"
  (vnrDelete)="onGridDelete($event)"
  (vnrViewDetails)="onGridViewDetail($event)"
  (vnrCellClick)="onGridCellClick($event)"
>
</vnr-grid-new>

<ng-template #tplCustomTemplateByColumn let-dataItem let-column="columnItem" let-field="field">
  <ng-container [ngSwitch]="column['Name']">
    <span *ngSwitchCase="'ProfileName'">
      <span class="--has-custom-template">
        {{ dataItem[column['Name']] }}
      </span>
    </span>
    <span *ngSwitchDefault>
      {{ dataItem[column['Name']] }}
    </span>
  </ng-container>
</ng-template>
