import { ChangeDetectionStrategy, Component, OnInit, ViewChild } from '@angular/core';
import { Validators } from '@angular/forms';
import { FormBuilder, FormGroup } from '@angular/forms';
import {
  gridAllocationDepartmentColumns,
  gridAllocationGoalColumns,
  GroupDepartmentColumns,
  GroupGoalColumns,
  columnTotalTarget,
  columnAllocated,
} from '../../data/column.data';
import {
  VnrRadioButtonBuilder,
  VnrMultiSelectBuilder,
  VnrGridNewEditInlineComponent,
  VnrGridNewEditInlineBuilder,
  FormulaConfigBuilder,
} from '@hrm-frontend-workspace/vnr-module';
import { gridAllocationCycleColumns } from '../../data/column.data';
import { ObjSharedModule } from '../../../../../shared/obj-shared.module';
import { GoalState } from '../../../shared/states/goal.state';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzModalService } from 'ng-zorro-antd/modal';
import { AllocationMethod, AllocationType, AllocationView } from '../../models/allocation.model';
import { quarterColumns, monthColumns } from '../../../shared/data/column.data';
import { AllocationMethodComponent } from '../allocation-method/allocation-method.component';
import { AllocationFormulaFormComponent } from '../allocation-formula-form/allocation-formula-form.component';
import { TranslateService } from '@ngx-translate/core';
import { TargetFormatPipe } from '../../../shared/pipe/target-format.pipe';
import { formulaEnumData } from '../../data/datasource.data';
@Component({
  selector: 'app-allocation-department',
  imports: [ObjSharedModule, NzRadioModule, AllocationMethodComponent, TargetFormatPipe],
  templateUrl: './allocation-department.component.html',
  styleUrl: './allocation-department.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AllocationDepartmentComponent implements OnInit {
  @ViewChild('gridAllocationDepartment') gridAllocationDepartment: VnrGridNewEditInlineComponent;
  protected departmentRadioBuilder = new VnrRadioButtonBuilder();
  protected builderGrid: VnrGridNewEditInlineBuilder;
  protected gridName = 'ObjEval_AllocationCycle';
  protected isSupperAdmin = true;
  protected builderDepartment = new VnrMultiSelectBuilder();
  protected builderEmployee = new VnrMultiSelectBuilder();
  protected formulaConfigBuilder: FormulaConfigBuilder = new FormulaConfigBuilder();
  protected form: FormGroup;
  protected allocationType: AllocationType = 'department';
  protected allocationView: AllocationView;
  protected allocationMethod: AllocationMethod;

  protected showGrid = true;
  protected CycleColumn: any = quarterColumns;
  protected dataLocal = this.goalState.goalAllocation();
  protected columns: any = [...gridAllocationCycleColumns, ...quarterColumns];
  constructor(
    private fb: FormBuilder,
    private goalState: GoalState,
    private nzModal: NzModalService,
    private translate: TranslateService,
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.initBuilder();
    this.initGridBuilder();
  }

  private initForm() {
    this.form = this.fb.group({
      allocationType: ['department', [Validators.required]],
      department: [null, [Validators.required]],
      employee: [null, [Validators.required]],
    });
  }

  private initBuilder() {
    this.departmentRadioBuilder.builder({
      dataSource: [
        { value: 'department', label: 'Theo phòng ban' },
        { value: 'employee', label: 'Theo nhân viên' },
      ],
      textField: 'label',
      valueField: 'value',
    });
    this.builderDepartment.builder({
      dataSource: this.goalState.goalAllocation(),
      textField: 'label',
      valueField: 'value',
      placeholder: 'Chọn phòng ban',
    });
    this.builderEmployee.builder({
      dataSource: this.goalState.goalAllocation(),
      textField: 'label',
      valueField: 'value',
      placeholder: 'Chọn nhân viên',
    });
    this.formulaConfigBuilder.builder({
      formula: '',
      options: {
        enumDatas: formulaEnumData,
        isUsingOpenAI: true,
      },
    });
  }

  private initGridBuilder() {
    this.builderGrid = new VnrGridNewEditInlineBuilder({
      options: {
        configHeightGrid: {
          gridHeight: 300,
        },
        configSelectable: {
          columnKey: 'Id',
        },
        configCommandColumn: {
          isEnabledMenuAction: true,
        },
        configShowHide: {
          isShowDelete: false,
          isShowEdit: false,
        },
      },
    });
  }

  protected onChangeAllocationView(view: AllocationView): void {
    this.allocationView = view;
    if (view === 'department-allocation') {
      this.columns = [];
      this.columns.push(...GroupGoalColumns);
      this.goalState.goalAllocation().forEach((item) => {
        const MultiColumn = [columnTotalTarget, columnAllocated, ...this.CycleColumn];
        this.columns.push({
          Name: 'Department',
          HeaderName: item.Department,
          HeaderKey: item.Department,
          DisplayName: item.Department,
          Width: 150,
          Type: 'group',
          MultiColumn: MultiColumn,
        });
      });
    } else if (view === 'target-allocation') {
      this.columns = [];
      const MultiColumn = [columnTotalTarget, columnAllocated, ...this.CycleColumn];
      this.columns.push(...GroupDepartmentColumns);
      this.goalState.goalAllocation().forEach((item) => {
        this.columns.push({
          Name: 'GoalName',
          HeaderName: item.GoalName,
          HeaderKey: item.GoalName,
          DisplayName: item.GoalName,
          Width: 150,
          Type: 'group',
          MultiColumn: MultiColumn,
        });
      });
    } else if (view === 'department-group') {
      this.columns = [
        ...gridAllocationCycleColumns,
        ...this.CycleColumn,
        ...gridAllocationDepartmentColumns,
      ];
    } else if (view === 'target-group') {
      this.columns = [
        ...gridAllocationCycleColumns,
        ...this.CycleColumn,
        ...gridAllocationGoalColumns,
      ];
    }
  }

  protected onChangeAllocationType(event: any) {
    this.allocationType = event;
  }

  protected onChangeAllocationCycle(event: any) {
    this.CycleColumn = event === 'quarter' ? quarterColumns : monthColumns;
  }

  protected onOpenFormulaConfig() {
    this.nzModal.create({
      nzTitle: this.translate.instant('objEval.GoalTemplate.btnAddGoalDetail'),
      nzContent: AllocationFormulaFormComponent,
      nzWidth: 700,
      nzMaskClosable: false,
      nzFooter: null,
    });
  }

  protected onChangeAllocationMethod(event: any) {
    this.allocationMethod = event.event;
    if (event.data.length > 0) {
      this.dataLocal = event.data;
    }
    this.gridAllocationDepartment.vnrReloadGrid();
  }
}
