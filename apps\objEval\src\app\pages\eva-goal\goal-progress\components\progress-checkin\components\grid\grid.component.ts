import { Component, OnInit, ViewChild, TemplateRef, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { VnrGridNewComponent, VnrGridNewBuilder } from '@hrm-frontend-workspace/vnr-module';
import { VnrTagComponent, VnrLettersAvatarComponent } from '@hrm-frontend-workspace/ui';
import {
  progressCheckinGridDefineColumns,
  progressCheckinDataSource,
} from '../../../../data/progress-checkin.data';
import { NzDrawerService, NzDrawerRef } from 'ng-zorro-antd/drawer';
import { FormDetailComponent } from '../form-detail/form-detail.component';

@Component({
  selector: 'app-grid',
  standalone: true,
  imports: [VnrGridNewComponent, VnrTagComponent, VnrLettersAvatarComponent, CommonModule],
  templateUrl: './grid.component.html',
  styleUrl: './grid.component.scss',
})
export class GridComponent implements OnInit {
  @ViewChild('vnrGrid', { static: false }) vnrGrid: VnrGridNewComponent;
  public gridBuilder: VnrGridNewBuilder;
  @ViewChild('drawerTitle', { static: false }) drawerTitleTpl: TemplateRef<any>;
  selectedDataItem: any;
  private drawerRef: NzDrawerRef | null = null;
  protected gridName = 'mainGrid';
  protected dataLocal = progressCheckinDataSource;
  protected columns = progressCheckinGridDefineColumns;
  constructor(private drawerService: NzDrawerService) {}
  ngOnInit(): void {
    this.initGridBuilder();
  }

  private initGridBuilder() {
    this.gridBuilder = new VnrGridNewBuilder({
      options: {
        configHeightGrid: {
          isAllowCalcRowHeight: true,
        },
        configIndexColumn: {
          isShow: true,
          width: 40,
        },
        configSelectable: {
          columnKey: 'Id',
        },
        configShowHide: {
          isPageExpand: false,
          isShowDelete: false,
          isShowEdit: false,
        },
        configCommandColumn: {
          isEnabledMenuAction: true,
        },
        configGroupable: {
          isEnabled: false,
        },
      },
    });
  }

  onGridCellClick(event: any) {
    this.selectedDataItem = event.dataItem;
    if (this.drawerRef) {
      this.drawerRef.close();
    }
    this.drawerRef = this.drawerService.create<
      FormDetailComponent,
      { dataItem: any },
      string | TemplateRef<any>
    >({
      nzTitle: this.drawerTitleTpl,
      nzContent: FormDetailComponent,
      nzClosable: true,
      nzMaskClosable: false,
      nzMask: true,
      nzWidth: '1000px',
      nzContentParams: {
        dataItem: event.dataItem,
      },
      nzFooter: null,
      nzWrapClassName: 'checkin-form-drawer',
    });
    this.drawerRef.afterClose.subscribe(() => {
      this.drawerRef = null;
    });
  }
}
