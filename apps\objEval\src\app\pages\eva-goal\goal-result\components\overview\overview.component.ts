import { AfterViewInit, Component, ElementRef, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzProgressComponent } from 'ng-zorro-antd/progress';
import { NzButtonModule } from 'ng-zorro-antd/button';
import * as Highcharts from 'highcharts';
import { STATUS_PROGRESS_DATA } from '../../data/overview.data';
import { GridComponent } from './components/grid/grid.component';

@Component({
  selector: 'app-overview',
  imports: [CommonModule, NzGridModule, NzProgressComponent, NzButtonModule, GridComponent],
  templateUrl: './overview.component.html',
  styleUrl: './overview.component.scss',
})
export class OverviewComponent implements AfterViewInit, <PERSON><PERSON><PERSON><PERSON> {
  @ViewChild('statusChart', { static: false }) statusChartRef!: ElementRef;

  chartOptionsStatus!: Highcharts.Options;
  statusChart!: Highcharts.Chart;

  // Dữ liệu trạng thái cập nhật theo Figma
  statusData = STATUS_PROGRESS_DATA;

  constructor() {
    this.initChartStatus();
  }

  ngAfterViewInit(): void {
    this.renderChart();
  }

  private initChartStatus(): void {
    const chartData = this.statusData.map((item) => ({
      name: item.label,
      y: item.value,
      color: item.color,
    }));

    this.chartOptionsStatus = {
      chart: {
        type: 'pie',
        plotBackgroundColor: null,
        plotBorderWidth: 0,
        plotShadow: false,
        height: 80,
        width: 80,
        marginTop: 0,
        marginBottom: 0,
        marginLeft: 0,
        marginRight: 0,
        backgroundColor: 'transparent',
      },
      tooltip: {
        enabled: false,
      },
      title: {
        text: '',
      },
      accessibility: {
        point: {
          valueSuffix: '',
        },
      },
      plotOptions: {
        pie: {
          dataLabels: {
            enabled: false,
          },
          showInLegend: false,
          innerSize: '80%',
          borderWidth: 1,
          borderColor: '#fff',
          startAngle: -90,
          endAngle: 270,
          center: ['50%', '50%'],
          size: '100%',
          states: {
            hover: {
              enabled: false,
            },
          },
        },
      },
      series: [
        {
          type: 'pie',
          name: 'Trạng thái',
          data: chartData,
        },
      ],
      credits: {
        enabled: false,
      },
    };
  }

  private renderChart(): void {
    if (this.statusChartRef && this.statusChartRef.nativeElement) {
      this.statusChart = Highcharts.chart(
        this.statusChartRef.nativeElement,
        this.chartOptionsStatus,
      );
    }
  }

  ngOnDestroy(): void {
    if (this.statusChart) {
      this.statusChart.destroy();
    }
  }
}
