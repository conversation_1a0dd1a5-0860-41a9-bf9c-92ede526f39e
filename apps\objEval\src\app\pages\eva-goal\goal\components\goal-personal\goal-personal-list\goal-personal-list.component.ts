import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import {
  VnrButtonNewBuilder,
  VnrFilterAdvanceFullBuilder,
  VnrFilterAdvanceFullComponent,
  VnrGridNewBuilder,
  VnrGridNewComponent,
  VnrToolbarNewBuilder,
} from '@hrm-frontend-workspace/vnr-module';
import { NzDrawerService } from 'ng-zorro-antd/drawer';
import { NzModalService } from 'ng-zorro-antd/modal';

import { ObjSharedModule } from '../../../../../../shared/obj-shared.module';
import { GoalRegisterFormComponent } from '../../../../shared/components/goal-register-form/goal-register-form.component';
import { statusColorMap, statusTextMap } from '../../../../shared/enums/status.enum';
import { gridPersonalDefineColumns } from '../../../data/column.data';
import { gridDefineDataFilterQuick } from '../../../data/data-filter-advance';
import { goalPersonalData } from '../../../data/goal-personal.data';
import { GoalStatusFilterData } from '../../../../shared/enums/menu.data';
import { TabEnum } from '../../../models/goal-period.model';
import { GoalPersonalDetailComponent } from '../goal-personal-detail/goal-personal-detail.component';

@Component({
  selector: 'goal-personal-list',
  templateUrl: './goal-personal-list.component.html',
  styleUrls: ['./goal-personal-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ObjSharedModule],
})
export class GoalPersonalListComponent implements OnInit {
  @ViewChild('vnrGrid', { static: true }) gridControl: VnrGridNewComponent;
  @ViewChild('vnrFilterAdvanceFull') filterAdvanceFullControl: VnrFilterAdvanceFullComponent;
  @Output() actionApprove = new EventEmitter<any>();
  @Output() actionReject = new EventEmitter<any>();

  protected builderGrid: VnrGridNewBuilder;
  protected builderToolbar: VnrToolbarNewBuilder;
  protected builderFilterAdvanceFull: VnrFilterAdvanceFullBuilder;
  protected builderButtonApprove: VnrButtonNewBuilder;
  protected builderButtonReject: VnrButtonNewBuilder;
  protected builderButtonAddNew: VnrButtonNewBuilder;
  protected statusColorMap = statusColorMap;
  protected statusTextMap = statusTextMap;
  protected GoalStatusFilterData = GoalStatusFilterData;
  protected tabCount: any;
  protected tabDefault = 0;

  private _permission = 'New_PortalV3_Personal_Hre_Dependant';
  private _screenName = 'grid-new-list';
  private _storeName = 'hrm_hr_sp_get_ProfileTest';
  protected gridName = 'ObjEval_ListGoalPeriod';
  protected dataLocal = goalPersonalData;
  protected columns = gridPersonalDefineColumns;
  protected isSupperAdmin = true;
  private _selectedDataItem: any;
  private optionGrid: any = {};

  constructor(private drawerService: NzDrawerService, private modalService: NzModalService) {}

  ngOnInit() {
    this.getCountDataTab();
    this.builderGridComponent();
    this.builderToolbarComponent();
  }

  private builderToolbarComponent() {
    this.builderToolbar = new VnrToolbarNewBuilder({
      isShowConfig: true,
      gridName: this.gridName,
      isSupperAdmin: this.isSupperAdmin,
      gridRef: this.gridControl,
      permission: this._permission,
      screenName: this._screenName,
      storeName: this._storeName,
      options: {
        configButtonChangeColumn: {
          isShow: true,
          isConfigMenuDisplay: true,
          isChangeColumnNew: true,
          isDisabled: false,
          configButtonSetting: {
            isShow: true,
            isDisabled: true,
          },
        },
        configButtonDelete: {
          isShow: false,
          isDisabled: false,
        },
        configQuickSearch: {
          textPlaceHolder: 'Tìm kiếm theo mã, tên...',
          searchKey: 'ProfileName',
        },
        configFilterAdvanceQuick: {
          isShow: true,
          components: gridDefineDataFilterQuick(),
          keyConfig: 'PortalNew_GridNewExample_Advance',
        },
        isSetBackground: true,
        buttonMoreFlexDirection: 'inherit',
        isExpanded: false,
      },
    });
  }

  private builderGridComponent() {
    this.builderGrid = new VnrGridNewBuilder({
      options: {
        configHeightGrid: {
          gridHeight: 600,
        },
        configSelectable: {
          columnKey: 'Id',
        },
        configCommandColumn: {
          isEnabledMenuAction: true,
        },
        configShowHide: {
          isShowDelete: false,
          isShowEdit: false,
        },
      },
    });
  }

  protected onOpenComponentFilterFull(event: any) {
    this.filterAdvanceFullControl.onOpenFullFilter();
  }

  protected getSelectedDataItem($event) {
    this._selectedDataItem = $event;
  }

  protected onOpenDetail(eventData: any) {
    this.drawerService.create({
      nzTitle: 'Chi tiết mục tiêu cá nhân',
      nzContent: GoalPersonalDetailComponent,
      nzWidth: 1200,
      nzClosable: true,
      nzContentParams: {
        dataItem: eventData.record,
      },
    });
  }
  protected onOpenAddGoalModal() {
    this.drawerService.create({
      nzTitle: 'Thêm mục tiêu cá nhân',
      nzContent: GoalRegisterFormComponent,
      nzWidth: 700,
      nzClosable: true,
    });
  }

  protected onSelectPersonalChange(_tabFilter: any) {
    switch (_tabFilter) {
      case TabEnum.E_WAITING_CONFIRM:
        Object.assign(this.optionGrid, {
          Status: 'E_WAITING_CONFIRM',
          tabEnum: TabEnum.E_WAITING_CONFIRM,
        });
        break;
      case TabEnum.E_CONFIRMED:
        Object.assign(this.optionGrid, {
          Status: 'E_CONFIRMED',
          tabEnum: TabEnum.E_CONFIRMED,
        });
        break;
      case TabEnum.E_REJECTED:
        Object.assign(this.optionGrid, {
          Status: 'E_REJECTED',
          tabEnum: TabEnum.E_REJECTED,
        });
        break;
      case TabEnum.ALL:
        Object.assign(this.optionGrid, {
          Status: null,
          tabEnum: TabEnum.ALL,
        });
        break;
      case TabEnum.E_WAITING_APPROVE:
        Object.assign(this.optionGrid, {
          Status: 'E_WAITING_APPROVE',
          tabEnum: TabEnum.E_WAITING_APPROVE,
        });
        break;
    }
    this.gridControl.setDataFilter(this.optionGrid);
    this.gridControl.vnrReadGrid();
  }

  private getCountDataTab() {
    this.tabCount = [
      {
        id: TabEnum.ALL,
        count: goalPersonalData.length,
      },
      {
        id: TabEnum.E_WAITING_APPROVE,
        count: goalPersonalData.filter((item) => item.Status === 'E_WAITING_APPROVE').length,
      },
      {
        id: TabEnum.E_REJECTED,
        count: goalPersonalData.filter((item) => item.Status === 'E_REJECTED').length,
      },
      {
        id: TabEnum.E_CONFIRMED,
        count: goalPersonalData.filter((item) => item.Status === 'E_CONFIRMED').length,
      },
      {
        id: TabEnum.E_WAITING_CONFIRM,
        count: goalPersonalData.filter((item) => item.Status === 'E_WAITING_CONFIRM').length,
      },
    ];
  }
}
