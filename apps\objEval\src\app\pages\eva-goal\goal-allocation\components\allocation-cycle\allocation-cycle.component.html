<app-allocation-method
  [gridRef]="gridAllocationCycle"
  (changeAllocationMethod)="onChangeAllocationMethod($event)"
  (changeAllocationCycle)="onChangeAllocationCycle($event)"
></app-allocation-method>
<vnr-grid-new-Edit-Inline
  *ngIf="showGrid"
  class="grid-new"
  #gridAllocationCycle
  [builder]="builderGrid"
  [gridName]="gridName"
  [dataLocal]="dataLocal"
  [columns]="columns"
  [isSupperAdmin]="isSupperAdmin"
  [defaultColumnTemplate]="tplCustomTemplateByColumn"
></vnr-grid-new-Edit-Inline>

<ng-template #tplCustomTemplateByColumn let-dataItem let-column="columnItem" let-field="field">
  <ng-container [ngSwitch]="column['Name']">
    <span class="cursor-pointer" *ngSwitchCase="'AllocationMethod'">
      {{ dataItem['AllocationMethod'] }}
      <vnr-button
        *ngIf="allocationMethod === 'formula'"
        class="border-0"
        [vnrIcon]="'edit'"
        [vnrType]="'default'"
        [vnrSize]="'small'"
        (vnrClick)="onOpenFormulaConfig(dataItem)"
      ></vnr-button>
    </span>
    <span *ngSwitchCase="'TotalTarget'">
      {{ dataItem['TotalTarget'] | targetFormat : dataItem['Unit'] }}
    </span>
    <span *ngSwitchCase="'DoneTarget'">
      {{ dataItem['DoneTarget'] | targetFormat : dataItem['Unit'] }}
    </span>
    <span *ngSwitchDefault>
      {{ dataItem[column['Name']] }}
    </span>
  </ng-container>
</ng-template>
