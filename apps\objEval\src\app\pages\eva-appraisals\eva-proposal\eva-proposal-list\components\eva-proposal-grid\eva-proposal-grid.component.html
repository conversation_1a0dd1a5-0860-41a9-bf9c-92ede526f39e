<div class="eva-proposal-grid__header">
  <div>
    <ai-analysis [isShow]="true">
      <div class="ai-approve-suggestion__desc">
        <label>Xin chào <b>Tr<PERSON><PERSON></b>, tôi là Reca</label><br />
        <label>Bạn đang xem danh sách đề xuất sau đợt đánh giá Tháng 4/2025</label><br />
        <label>
          <b class="text-red">Cảnh báo</b>: Có 1 nhân viên phòng Kinh doanh “Chưa đạt” vì chưa hoàn
          thành mục tiêu trong 3 kỳ liên tiếp. </label
        ><br />
        <label><b class="text-blue">Đề xuất</b>: Có 8 nhân viên cần tham gia đào tạo bổ sung</label
        ><br />
      </div>
      <div class="ai-approve-suggestion__actions">
        <vnr-button-new
          [builder]="builderButtonAskAI"
          (vnrClick)="onAskAI($event)"
        ></vnr-button-new>
      </div>
    </ai-analysis>
  </div>
</div>
<div class="eva-proposal-grid__container">
  <div class="form-header" nz-form [formGroup]="formGroup">
    <vnr-toolbar-new [builder]="builderToolbar" (onChangeFitler)="onChangeFitler($event)">
      <vnr-button-new [builder]="builderButtonCustom">
        <vnr-treeview
          [builder]="builderOrg"
          [formGroup]="formGroup"
          formControlName="department"
          (ngModelChange)="onModelChangeOrg($event)"
        ></vnr-treeview>
      </vnr-button-new>
      <vnr-button-new [builder]="builderButtonCustom">
        <vnr-combobox
          [builder]="builderPosition"
          [formGroup]="formGroup"
          formControlName="position"
          (ngModelChange)="onModelChangePosition($event)"
        ></vnr-combobox>
      </vnr-button-new>
    </vnr-toolbar-new>
  </div>
  <vnr-grid-new
    #vnrGrid
    [builder]="builderGrid"
    [gridName]="gridName"
    [dataLocal]="dataLocal"
    [columns]="columns"
    [defaultColumnTemplate]="tplCustomTemplateByColumn"
    (getSelectedID)="getSelectedID($event)"
    (getDataItem)="getDataItem($event)"
    (vnrDoubleClick)="onOpenDetail($event)"
    (vnrEdit)="onGridEdit($event)"
    (vnrDelete)="onGridDelete($event)"
    (vnrViewDetails)="onGridViewDetail($event)"
    (vnrCellClick)="onGridCellClick($event)"
  >
  </vnr-grid-new>

  <ng-template #tplCustomTemplateByColumn let-dataItem let-column="columnItem" let-field="field">
    <ng-container [ngSwitch]="column['Name']">
      <span class="--has-custom-template" *ngSwitchCase="'ProfileName'">
        <span>
          <div class="eva-proposal-grid-profile">
            <app-vnr-letters-avatar
              class="eva-proposal-grid-profile__image"
              [avatarName]="dataItem['ProfileName']"
              [circular]="true"
              [width]="32"
              [src]="dataItem['Avatar']"
            ></app-vnr-letters-avatar>
            <div class="eva-proposal-grid-profile__description">
              <div class="eva-proposal-grid-profile__name">
                {{ dataItem['ProfileName'] }}
              </div>
              <div class="eva-proposal-grid-profile__code">
                {{ dataItem['EmpCode'] }}
              </div>
            </div>
          </div>
        </span>
      </span>
      <span class="--has-custom-template" *ngSwitchCase="'Grade'">
        <div class="eva-proposal-grid-grade">
          <div class="eva-proposal-grid-grade__name">
            <vnr-tag
              *ngIf="dataItem['Grade']; else templateEmpty"
              [vnrColor]="getColorGrade(dataItem['Grade'])"
              [vnrTitle]="dataItem['Grade']"
              [isBordered]="false"
              [vnrNoTranslate]="true"
            ></vnr-tag>
          </div>
          <div class="eva-proposal-grid-grade__text">
            <font [color]="getColorGradeText(dataItem['Grade'])">{{ dataItem['GradeText'] }}</font>
          </div>
        </div>
      </span>
      <span class="--has-custom-template" *ngSwitchCase="'Status'">
        <vnr-tag
          *ngIf="dataItem['Status']; else templateEmpty"
          [vnrColor]="getColorStatus(dataItem['Status'])"
          [vnrTitle]="dataItem['StatusView'] || dataItem['Status']"
        ></vnr-tag>
      </span>
      <span class="--has-custom-template" *ngSwitchDefault>
        {{ dataItem[column['Name']] || '-' }}
      </span>
    </ng-container>
  </ng-template>
  <ng-template #templateEmpty>-</ng-template>
</div>
