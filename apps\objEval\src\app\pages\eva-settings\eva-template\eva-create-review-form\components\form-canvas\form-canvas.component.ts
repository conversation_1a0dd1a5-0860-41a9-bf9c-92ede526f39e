import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import {
  VnrButtonFactory,
  VnrButtonNewBuilder,
  VnrButtonNewComponent,
} from '@hrm-frontend-workspace/vnr-module';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzEmptyModule } from 'ng-zorro-antd/empty';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzModalModule, NzModalService } from 'ng-zorro-antd/modal';
import {
  IFormControl,
  IFormSection,
  IFormTemplate,
  SectionTemplate,
} from '../../models/form-builder.model';

// Import các section type components
import { VnrSelectEmpComponent } from '@hrm-frontend-workspace/ui';
import { NzFormModule } from 'ng-zorro-antd/form';
import { EnumFormType, FormType } from '../../models/enums/form-canvas.enum';
import { CompetencyEvaluationSectionComponent } from '../section-types/competency-evaluation-section/competency-evaluation-section.component';
import { CustomEvaluationSectionComponent } from '../section-types/custom-evaluation-section/custom-evaluation-section.component';
import { Evaluation360SectionComponent } from '../section-types/evaluation-360-section/evaluation-360-section.component';
import { GeneralReviewCommentSectionComponent } from '../section-types/general-review-comment-section/general-review-comment-section.component';
import { GoalEvaluationSectionComponent } from '../section-types/goal-evaluation-section/goal-evaluation-section.component';
import { OverviewSectionComponent } from '../section-types/overview-section/overview-section.component';
import { PersonalGoalsSectionComponent } from '../section-types/personal-goals-section/personal-goals-section.component';
import { ProcessSectionComponent } from '../section-types/process-section/process-section.component';
import { SignatureAndApprovalSectionComponent } from '../section-types/signature-and-approval-section/signature-and-approval-section.component';
import { TotalScoreSectionComponent } from '../section-types/total-score-section/total-score-section.component';

@Component({
  selector: 'app-form-canvas',
  templateUrl: './form-canvas.component.html',
  styleUrls: ['./form-canvas.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NzEmptyModule,
    NzButtonModule,
    NzIconModule,
    NzModalModule,
    TranslateModule,
    VnrButtonNewComponent,
    // Import các section type components
    OverviewSectionComponent,
    ProcessSectionComponent,
    PersonalGoalsSectionComponent,
    GoalEvaluationSectionComponent,
    CompetencyEvaluationSectionComponent,
    Evaluation360SectionComponent,
    CustomEvaluationSectionComponent,
    TotalScoreSectionComponent,
    GeneralReviewCommentSectionComponent,
    SignatureAndApprovalSectionComponent,
    VnrSelectEmpComponent,
    NzFormModule,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FormCanvasComponent implements OnChanges, OnInit {
  @Input() formType: FormType = EnumFormType.CREATE;
  @Input() formTemplate: IFormTemplate;
  @Input() selectedControl: IFormControl;
  @Input() selectedSection: IFormSection;

  @Output() controlClick = new EventEmitter<IFormControl>();
  @Output() dropControl = new EventEmitter<{ control: IFormControl; sectionId: string }>();
  @Output() addSection = new EventEmitter<void>();
  @Output() removeSection = new EventEmitter<string>();
  @Output() removeControl = new EventEmitter<{ controlId: string; sectionId: string }>();
  @Output() sectionClick = new EventEmitter<IFormSection>();
  @Output() dropSection = new EventEmitter<SectionTemplate>();
  @Output() sectionOrderChanged = new EventEmitter<IFormSection[]>();
  @Output() sectionTypesChanged = new EventEmitter<string[]>();
  @Output() editSection = new EventEmitter<IFormSection>();
  @Output() formValueChange = new EventEmitter<any>(); // Emit khi giá trị form thay đổi

  // Form group cho toàn bộ form
  formGroup: FormGroup;

  // Biến lưu trạng thái kéo thả
  draggedSectionId: string | null = null;
  dragOverSectionId: string | null = null;

  // Button builder cho nút chỉnh sửa
  protected builderButtonEdit: VnrButtonNewBuilder;
  // Button builder cho nút xóa
  protected builderButtonDelete: VnrButtonNewBuilder;
  // Builder cho select employee
  protected SelectEmployeeBuilder: any;
  // Selected employee
  protected staffSelected: any;
  // Button factory
  private buttonFactory: VnrButtonFactory;
  // Data overview
  protected dataOverview: any;
  // Data goal evaluation
  protected dataGoalEvaluation: any;
  // Data competency evaluation
  protected dataCompetencyEvaluation: any;
  // Data evaluation 360
  protected dataEvaluation360: any;
  // Data custom evaluation
  protected dataCustomEvaluation: any;

  protected _edit = EnumFormType.EDIT;
  protected _preview = EnumFormType.PREVIEW;
  protected _create = EnumFormType.CREATE;
  constructor(
    private modalService: NzModalService,
    private translateService: TranslateService,
    private formBuilder: FormBuilder,
    private cdr: ChangeDetectorRef,
  ) {
    // Initialize button factory
    this.buttonFactory = VnrButtonFactory.init();
  }

  ngOnInit() {
    this.initButtonBuilders();
    this.initSectionData();
    this.initFormGroup();
  }

  /**
   * Khởi tạo form group cho toàn bộ form
   */
  private initFormGroup() {
    if (!this.formTemplate?.sections) {
      this.formGroup = this.formBuilder.group({});
      return;
    }

    const formGroupConfig = {};

    // Tạo form controls cho từng section
    this.formTemplate.sections.forEach((section) => {
      if (!section.id) {
        console.warn('Section missing ID:', section);
        return;
      }

      const sectionGroup = this.formBuilder.group({});

      // Tạo form controls cho từng control trong section
      if (section.controls?.length) {
        section.controls.forEach((control) => {
          if (!control.id) {
            console.warn('Control missing ID in section:', section.id, control);
            return;
          }
          sectionGroup.addControl(
            control.id,
            this.formBuilder.control(control.defaultValue || null),
          );
        });
      }

      formGroupConfig[section.id] = sectionGroup;
    });

    this.formGroup = this.formBuilder.group(formGroupConfig);

    // Subscribe to form value changes
    this.formGroup.valueChanges.subscribe((value) => {
      this.formValueChange.emit(value);
    });

    // Force change detection
    this.cdr.detectChanges();
  }

  private initSectionData() {
    this.formTemplate?.sections?.forEach((section) => {
      if (section.type === 'overview') {
        if (!section.data) {
          const aa = {
            id: '-',
            employeeCode: '-',
            name: '-',
            employeeName: '-',
            position: '-',
            department: '-',
            joinDate: '-',
            manager: '-',
            store: '-',
            company: '-',
            departmentTree: '-',
            evaluator: '-',
            title: '-',
            result: '-',
            rank: '-',
            seniority: '-',
          };
          section.data = { ...aa };
        }
      }
    });
  }

  /**
   * Khởi tạo các button builder
   */
  private initButtonBuilders(): void {
    // Initialize button builders using the factory
    this.builderButtonEdit = this.buttonFactory.builder({
      text: 'Chỉnh sửa',
      action: 'edit',
      options: {
        style: 'default',
        icon: { fontIcon: 'edit' },
        tooltipTitle: 'Chỉnh sửa thông tin',
        tooltipPlacement: 'top',
      },
    });

    this.builderButtonDelete = this.buttonFactory.builder({
      text: 'Xóa',
      action: 'delete',
      options: {
        style: 'danger',
        icon: { fontIcon: 'delete' },
        tooltipTitle: 'Xóa vùng',
        tooltipPlacement: 'top',
      },
    });

    this.SelectEmployeeBuilder = {
      label: 'Chọn nhân viên muốn xem trước',
      urlApi: 'api/Att_GetData/GetProfileDetailForAttendance',
    };
  }

  /**
   * Xử lý khi input properties thay đổi
   * @param changes - Các thay đổi của input properties
   */
  ngOnChanges(changes: SimpleChanges): void {
    if (changes['formTemplate'] && !changes['formTemplate'].firstChange) {
      // Re-initialize form group when template changes
      this.initFormGroup();
    }

    // Xử lý khi selectedSection thay đổi
    if (changes['selectedSection'] && !changes['selectedSection'].firstChange) {
      // Có thể thực hiện các xử lý khác khi section được chọn thay đổi
    }
  }

  /**
   * Phát ra danh sách các loại section đang được sử dụng
   */
  private emitUsedSectionTypes(): void {
    if (this.formTemplate && this.formTemplate.sections) {
      const usedTypes = this.formTemplate.sections.map((section) => section.type);
      this.sectionTypesChanged.emit(usedTypes);
    }
  }

  /**
   * Kiểm tra xem có cho phép kéo thả hay không
   */
  isDragDropEnabled(): boolean {
    return this.formType === this._create;
  }

  /**
   * Xử lý khi thả vùng mẫu vào canvas
   */
  onDropSection(event: DragEvent): void {
    if (!this.isDragDropEnabled()) {
      return;
    }

    event.preventDefault();

    // Nếu đang kéo thả section từ bên ngoài vào
    if (event.dataTransfer && !this.draggedSectionId) {
      const sectionData = event.dataTransfer.getData('section');
      if (sectionData) {
        try {
          const sectionTemplate: SectionTemplate = JSON.parse(sectionData);
          this.dropSection.emit(sectionTemplate);

          // Sau khi thêm section, cập nhật danh sách các loại section đang được sử dụng
          setTimeout(() => {
            this.emitUsedSectionTypes();
            this.initFormGroup(); // Khởi tạo lại form group khi thêm section mới
          }, 0);
        } catch (error) {
          console.error('Error parsing dropped section data:', error);
        }
      }
    }
  }

  /**
   * Xử lý sự kiện dragover để cho phép thả
   */
  onDragOver(event: DragEvent): void {
    event.preventDefault();
  }

  /**
   * Bắt đầu kéo một section
   */
  onDragStart(event: DragEvent, section: IFormSection): void {
    if (!this.isDragDropEnabled()) {
      return;
    }

    if (event.dataTransfer) {
      this.draggedSectionId = section.id;
      event.dataTransfer.effectAllowed = 'move';
      event.dataTransfer.setData('text/plain', section.id);

      const element = event.target as HTMLElement;
      if (element) {
        setTimeout(() => {
          element.classList.add('dragging');
        }, 0);
      }
    }
  }

  /**
   * Khi kéo section qua một section khác
   */
  onDragEnter(event: DragEvent, section: IFormSection): void {
    if (this.draggedSectionId && this.draggedSectionId !== section.id) {
      this.dragOverSectionId = section.id;

      const element = event.target as HTMLElement;
      if (element) {
        element.classList.add('drag-over');
      }
    }
  }

  /**
   * Khi kéo section ra khỏi một section khác
   */
  onDragLeave(event: DragEvent): void {
    const element = event.target as HTMLElement;
    if (element) {
      element.classList.remove('drag-over');
    }
  }

  /**
   * Khi thả section vào một vị trí mới
   */
  onDragDrop(event: DragEvent, targetSection: IFormSection): void {
    event.preventDefault();

    if (this.draggedSectionId && this.draggedSectionId !== targetSection.id) {
      // Tìm vị trí của section nguồn và đích
      const sections = [...this.formTemplate.sections];
      const sourceIndex = sections.findIndex((s) => s.id === this.draggedSectionId);
      const targetIndex = sections.findIndex((s) => s.id === targetSection.id);

      if (sourceIndex !== -1 && targetIndex !== -1) {
        // Di chuyển section từ vị trí cũ sang vị trí mới
        const [movedSection] = sections.splice(sourceIndex, 1);
        sections.splice(targetIndex, 0, movedSection);

        // Cập nhật trực tiếp formTemplate
        this.formTemplate = {
          ...this.formTemplate,
          sections: sections,
        };

        // Emit sự kiện thay đổi thứ tự
        this.sectionOrderChanged.emit(sections);
      }
    }

    // Xóa các class hiệu ứng
    const element = event.target as HTMLElement;
    if (element) {
      element.classList.remove('drag-over');
    }

    this.resetDragState();
  }

  /**
   * Kết thúc kéo section
   */
  onDragEnd(event: DragEvent): void {
    // Xóa các class hiệu ứng
    const element = event.target as HTMLElement;
    if (element) {
      element.classList.remove('dragging');
    }

    this.resetDragState();
  }

  /**
   * Reset trạng thái kéo thả
   */
  private resetDragState(): void {
    this.draggedSectionId = null;
    this.dragOverSectionId = null;
  }

  // Xử lý khi click vào section
  onSectionClick(section: IFormSection): void {
    this.sectionClick.emit(section);
  }

  /**
   * Xử lý khi xóa section
   * @param sectionId - ID của section cần xóa
   * @param event - Sự kiện chuột (nếu có)
   */
  onRemoveSectionClick(sectionId: string, event?: MouseEvent): void {
    // Ngăn chặn sự kiện lan truyền nếu có event
    if (event) {
      event.stopPropagation();
    }

    // Hiển thị modal xác nhận
    this.modalService.confirm({
      nzTitle: this.translateService.instant('Bạn có chắc chắn muốn xóa vùng này không?'),
      nzOkText: this.translateService.instant('common.AGREE'),
      nzOkType: 'primary',
      nzOkDanger: false,
      nzOnOk: () => {
        this.removeSection.emit(sectionId);
        this.emitUsedSectionTypes();
      },
      nzCancelText: this.translateService.instant('common.cancel'),
    });
  }

  // Xử lý khi xóa control
  onRemoveControlClick(event: { controlId: string; sectionId: string }): void {
    this.removeControl.emit(event);
  }

  // Kiểm tra xem section có được chọn hay không
  isSectionSelected(sectionId: string): boolean {
    return this.selectedSection?.id === sectionId;
  }

  // Kiểm tra xem control có được chọn hay không
  isControlSelected(controlId: string): boolean {
    return this.selectedControl?.id === controlId;
  }

  /**
   * Xử lý khi click vào nút chỉnh sửa
   */
  onEditClick(section: IFormSection): void {
    this.editSection.emit(section);
  }

  /**
   * Lấy form group của một section
   */
  getSectionFormGroup(sectionId: string): FormGroup {
    const control = this.formGroup?.get(sectionId);
    if (!control) {
      return this.formBuilder.group({});
    }
    if (control instanceof FormGroup) {
      return control;
    }
    return this.formBuilder.group({});
  }

  /**
   * Xử lý sự kiện keyboard cho section
   */
  onSectionKeyEvent(event: Event, section: IFormSection): void {
    if (event instanceof KeyboardEvent) {
      event.preventDefault();
      this.onSectionClick(section);
    }
  }

  /**
   * Xử lý sự kiện keyboard cho nút xóa
   */
  onRemoveKeyEvent(event: Event, sectionId: string): void {
    if (event instanceof KeyboardEvent) {
      event.preventDefault();
      // Tạo một MouseEvent giả lập từ KeyboardEvent
      const mouseEvent = new MouseEvent('click', {
        bubbles: true,
        cancelable: true,
        view: window,
      });
      this.onRemoveSectionClick(sectionId, mouseEvent);
    }
  }

  /**
   * Xử lý sự kiện keyboard cho nút chỉnh sửa
   */
  onEditKeyEvent(event: Event, section: IFormSection): void {
    if (event instanceof KeyboardEvent) {
      event.preventDefault();
      this.onEditClick(section);
    }
  }

  onChangeProfileID(event: any): void {
    console.log(event);
  }
  onSelectedItem(event: any): void {
    // Create a new form template with updated sections
    const updatedTemplate = {
      ...this.formTemplate,
      sections: this.formTemplate.sections.map((section) => {
        if (section.type === 'overview') {
          return {
            ...section,
            data: {
              ...section.data,
              employeeCode: event.CodeEmp,
              employeeName: event.ProfileName,
              department: event.OrgStructureName,
              company: event.CompanyName,
              departmentTree: event.OrgStructureName,
              position: event.PositionName || '-',
              evaluator: event.EvaluatorName || '-',
              title: event.TitleName || '-',
              result: event.Result || '-',
              rank: event.Rank || '-',
              seniority: event.Seniority || '-',
              email: event.Email || '-',
              joinDate: event.JoinDate || '-',
            },
          };
        }
        return section;
      }),
    };

    // Update the form template
    this.formTemplate = updatedTemplate;

    // Update form group if needed
    this.formTemplate.sections.forEach((section) => {
      if (section.type === 'overview') {
        const sectionFormGroup = this.formGroup?.get(section.id);
        if (sectionFormGroup) {
          sectionFormGroup.patchValue({ data: section.data }, { emitEvent: false });
        }
        this.dataOverview = section.data;
      }
    });
  }
  onFormValueChange(event: any): void {
    const { sectionId, data, type } = event;

    // Create a new form template with updated sections
    const updatedTemplate = {
      ...this.formTemplate,
      sections: this.formTemplate.sections.map((section) => {
        if (section.id === sectionId) {
          // Create a new section with updated data
          return {
            ...section,
            data: Array.isArray(data) ? [...data] : { ...data },
          };
        }
        return section;
      }),
    };

    // Update form group if needed
    const sectionFormGroup = this.formGroup?.get(sectionId);
    if (sectionFormGroup) {
      sectionFormGroup.patchValue({ data }, { emitEvent: false });
    }

    // Emit the updated template
    this.formValueChange.emit(updatedTemplate);
  }
}
