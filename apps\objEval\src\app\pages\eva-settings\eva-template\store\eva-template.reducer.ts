import { createReducer, on } from '@ngrx/store';
import { IFormTemplate } from '../eva-create-review-form/models/form-builder.model';
import * as EvaTemplateActions from './eva-template.actions';
import { evaTemplateDataSource } from '../eva-evaluation-form-list/data/datasource.data';

export const evaTemplateFeatureKey = 'evaTemplate';

export interface State {
  templates: IFormTemplate[];
  selectedTemplate: IFormTemplate | null;
  loading: boolean;
  error: any;
}

export const initialState: State = {
  templates: evaTemplateDataSource as unknown as IFormTemplate[],
  selectedTemplate: null,
  loading: false,
  error: null,
};

export const reducer = createReducer(
  initialState,

  // Load Templates
  on(EvaTemplateActions.loadTemplates, (state) => ({
    ...state,
    loading: true,
    error: null,
  })),
  on(EvaTemplateActions.loadTemplatesSuccess, (state, { templates }) => ({
    ...state,
    templates,
    loading: false,
  })),
  on(EvaTemplateActions.loadTemplatesFailure, (state, { error }) => ({
    ...state,
    error,
    loading: false,
  })),

  // Load Single Template
  on(EvaTemplateActions.loadTemplate, (state) => ({
    ...state,
    loading: true,
    error: null,
  })),
  on(EvaTemplateActions.loadTemplateSuccess, (state, { template }) => ({
    ...state,
    selectedTemplate: template,
    loading: false,
  })),
  on(EvaTemplateActions.loadTemplateFailure, (state, { error }) => ({
    ...state,
    error,
    loading: false,
  })),

  // Create Template
  on(EvaTemplateActions.createTemplate, (state) => ({
    ...state,
    loading: true,
    error: null,
  })),
  on(EvaTemplateActions.createTemplateSuccess, (state, { template }) => {
    const newTemplates = [...state.templates, template];
    return {
      ...state,
      templates: newTemplates,
      loading: false,
    };
  }),
  on(EvaTemplateActions.createTemplateFailure, (state, { error }) => ({
    ...state,
    error,
    loading: false,
  })),

  // Update Template
  on(EvaTemplateActions.updateTemplate, (state) => ({
    ...state,
    loading: true,
    error: null,
  })),
  on(EvaTemplateActions.updateTemplateSuccess, (state, { template }) => ({
    ...state,
    templates: state.templates.map((t) => (t.ID === template.ID ? template : t)),
    selectedTemplate: template,
    loading: false,
  })),
  on(EvaTemplateActions.updateTemplateFailure, (state, { error }) => ({
    ...state,
    error,
    loading: false,
  })),

  // Delete Template
  on(EvaTemplateActions.deleteTemplate, (state) => ({
    ...state,
    loading: true,
    error: null,
  })),
  on(EvaTemplateActions.deleteTemplateSuccess, (state, { id }) => ({
    ...state,
    templates: state.templates.filter((t) => t.ID !== id),
    selectedTemplate: state.selectedTemplate?.ID === id ? null : state.selectedTemplate,
    loading: false,
  })),
  on(EvaTemplateActions.deleteTemplateFailure, (state, { error }) => ({
    ...state,
    error,
    loading: false,
  })),
);
