export enum FormControlType {
  TEXTBOX = 'textbox',
  TEXTAREA = 'textarea',
  COMBOBOX = 'combobox',
  MULTISELECT = 'multiselect',
  CHECKBOX = 'checkbox',
  RADIO = 'radio',
  FILE = 'file',
  DATEPICKER = 'datepicker',
  GRID = 'grid',
}

/**
 * Các loại vùng trong mẫu đánh giá
 */
export enum FormSectionType {
  DEFAULT = 'default',
  OVERVIEW = 'overview',
  PROCESS = 'process',
  PERSONAL_GOALS = 'personal-goals',
  GOAL_EVALUATION = 'goal-evaluation',
  COMPETENCY_EVALUATION = 'competency-evaluation',
  EVALUATION_PERIOD = 'evaluation-period',
  EVALUATION_360 = 'evaluation-360',
  CUSTOM_EVALUATION = 'custom-evaluation',
  TOTAL_SCORE = 'total-score',
  RANKING = 'ranking',
  GENERAL_REVIEW_COMMENT = 'general-review-comment', //nhận xét và đánh giá tổng thể
  SIGNATURE_AND_APPROVAL = 'signature-and-approval', //chữ ký và phê duyệt
  REVIEW_HISTORY = 'review-history', //lịch sử đánh giá
}

export interface IControlOption {
  label: string;
  value: string;
}

export interface IValidation {
  type: 'required' | 'minLength' | 'maxLength' | 'pattern' | 'email';
  message: string;
  value?: any;
}

export interface IFormControl {
  id: string;
  type: FormControlType;
  label: string;
  name: string;
  required: boolean;
  placeholder?: string;
  defaultValue?: any;
  options?: IControlOption[];
  validations?: IValidation[];
  properties?: Record<string, any>;
  width?: number; // Chiều rộng theo % hoặc px
  order?: number; // Thứ tự trong section
}

/**
 * Cấu hình chi tiết cho từng trường thông tin
 */
export interface IFieldConfig {
  visible: boolean;
  required?: boolean;
  label: string;
  labelKey?: string; // Key dịch cho label
  format?: string; // Định dạng hiển thị (date, number, text, etc.)
  width?: number; // Chiều rộng (%)
  order?: number; // Thứ tự hiển thị
}

/**
 * Cấu hình hiển thị cho Overview section
 */
export interface IDisplayConfig {
  // Cấu hình chi tiết cho từng trường
  fields?: { [key: string]: IFieldConfig };

  // Cấu hình layout
  layout?: {
    columns?: number;
    labelWidth?: number;
    valueWidth?: number;
    spacing?: number;
  };
}

/**
 * Cấu hình hiển thị cho vùng thông tin đợt đánh giá
 */
export interface IEvaluationPeriodDisplayConfig {
  // Cấu hình chi tiết cho từng trường
  fields?: { [key: string]: IFieldConfig };

  // Cấu hình layout
  layout?: {
    columns?: number;
    labelWidth?: number;
    valueWidth?: number;
    spacing?: number;
  };
}

/**
 * Cấu hình cho cột trong lưới dữ liệu
 */
export interface IColumnConfig {
  key: string;
  title: string;
  width?: number;
  hidden?: boolean;
  format?: string;
  sortable?: boolean;
  filterable?: boolean;
  editable?: boolean;
  editableBy?: string;
  required?: boolean;
}

/**
 * Cấu hình cho vùng lưới đánh giá mục tiêu
 */
export interface IGoalEvaluationGridConfig {
  // Cấu hình chi tiết cho từng trường
  fields?: { [key: string]: IFieldConfig };

  allowEmployeeEdit?: boolean;
  allowManagerEdit?: boolean;
  columns?: IColumnConfig[];
  allowAdd?: boolean;
  allowEdit?: boolean;
  allowDelete?: boolean;
  allowImport?: boolean;
  allowExport?: boolean;
}

/**
 * Cấu hình cho vùng lưới đánh giá năng lực
 */
export interface ICompetencyEvaluationGridConfig {
  // Cấu hình chi tiết cho từng trường
  fields?: { [key: string]: IFieldConfig };

  allowEmployeeEdit?: boolean;
  allowManagerEdit?: boolean;
  columns?: IColumnConfig[];
  allowAdd?: boolean;
  allowEdit?: boolean;
  allowDelete?: boolean;
  allowImport?: boolean;
  allowExport?: boolean;
}

/**
 * Thuộc tính mở rộng cho các vùng
 */
export interface ISectionProperties {
  displayConfig?: IDisplayConfig;
  gridConfig?: IGoalEvaluationGridConfig | ICompetencyEvaluationGridConfig;
  [key: string]: any;
}

export interface IFormSection {
  id: string;
  title: string;
  description?: string;
  controls: IFormControl[];
  layout: 'vertical' | 'horizontal' | 'grid';
  columns?: number; // Số cột nếu layout là grid
  order?: number; // Thứ tự trong form
  type?: string; // Loại vùng: overview, process, personal-goals, goal-evaluation, competency-evaluation
  properties?: ISectionProperties | any; // Thuộc tính mở rộng cho các vùng
  data?: any; // Dữ liệu liên quan đến section, có thể là mảng hoặc đối tượng
}

export interface IFormTemplate {
  ID: string;
  EvaName: string;
  Description?: string;
  Department?: string;
  Position?: string;
  Employee?: string;
  sections: IFormSection[];
  CreatedDate: Date;
  ModifiedDate: Date;
  Status?: string;
  TemplateType?: string;
  Data?: any;
}

/**
 * Interface đại diện cho một vùng mẫu trong form builder
 * @interface SectionTemplate
 */
export interface SectionTemplate {
  /** ID duy nhất của section template */
  id: string;

  /** Icon hiển thị cho section */
  icon: string;

  /** Tên hiển thị của section */
  name: string;

  /** Mô tả về section */
  description: string;

  /** Loại section (overview, process, etc.) */
  type: FormSectionType;

  /** Cấu hình mặc định cho section */
  config: ISectionProperties;

  /** Thứ tự hiển thị */
  order: number;

  /** Danh mục của section */
  category?: string;

  /** Trạng thái đã được sử dụng */
  isUsed?: boolean;
}
