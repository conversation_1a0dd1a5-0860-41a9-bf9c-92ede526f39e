<div class="evaluation-grid" nz-form [formGroup]="formGroup">
  @if(false){
  <div class="evaluation-grid__header">
    <div class="evaluation-grid__header-title">
      {{ index | evaNumberToLetter }}. {{ dataItemInput?.criteriaName }}
    </div>
  </div>
  }
  <div class="evaluation-grid__content">
    <vnr-grid-new-Edit-Incell
      #vnrGridEditIncell
      [builder]="builderGrid"
      [gridName]="gridName"
      [dataLocal]="dataLocal"
      [columns]="columnGridLocal"
      [aggregates]="aggregates"
      [customTemplateColumnFooter]="listCustomFooter"
      [customTemplateGroupColumnFooter]="listCustomFooterGroup"
      [defaultColumnTemplate]="tplCustomTemplateByColumn"
      [customControlTemplate]="templateCustomControl"
      [headerGroupTemplate]="templateHeaderGroup"
    >
    </vnr-grid-new-Edit-Incell>
  </div>
  @if(isShowButton){
  <div class="evaluation-grid__footer m-2 d-flex">
    <vnr-button
      class="custom--btn"
      leftToolbar
      [vnrType]="'default'"
      [vnrIcon]="'fal fa-plus'"
      [vnrText]="titleButtonAddRow | translate"
      (vnrClick)="openAddDialog(null, type, 'addRow')"
    >
    </vnr-button>
  </div>
  }
</div>
<ng-template
  #templateHeaderGroup
  let-group
  let-column="column"
  let-columnIndex="columnIndex"
  let-fieldIndex="fieldIndex"
  let-value="value"
>
  <div class="eva-header-group d-flex justify-content-between align-items-center">
    <div>
      <strong>{{ value }}</strong> <span class="ml-1">({{ group.items.length | json }})</span>
    </div>
    @if(isShowButton){
    <div class="ml-2">
      <vnr-button
        class="custom--btn"
        leftToolbar
        [vnrType]="'default'"
        [vnrIcon]="'fal fa-plus'"
        [vnrText]="titleButtonAddRowGroup | translate"
        (vnrClick)="
          openAddDialog({ group: group, column: column, value: value }, type, 'addRowGroup')
        "
      >
      </vnr-button>
    </div>
    }
    <div *ngIf="false">
      Understands rapidly changing environment, market opportunities, competitive threats and
      strengths, and weakness of own and company. Share to employees and push up all for changing to
      find the better results Am hiểu sự thay đổi mạnh mẽ của môi trường/cơ hội trên thị trường,
      nguy cơ của sự cạnh tranh, điểm mạnh & điểm yếu của Cty. Chia sẻ với NV và thúc đẩy họ thay
      đổi để đạt kết quả tốt hơn.
    </div>
  </div>
</ng-template>

<!-- #region Custom template by column -->
<ng-template #tplCustomTemplateByColumn let-dataItem let-column="columnItem" let-field="field">
  <!-- TODO: TH chưa có format thì hiển thị theo default -->
  <div class="eva-grid-content">
    <ng-container *ngIf="column?.Format; else default">
      <ng-container
        *ngTemplateOutlet="tplFormatTemplate; context: { $implicit: column, dataItem: dataItem }"
      ></ng-container>
    </ng-container>
    <ng-template #default>
      <ng-container [ngSwitch]="column['Name']">
        <span class="--has-custom-template" *ngSwitchCase="'Weight'">
          {{ dataItem[column['Name']] ? (dataItem[column['Name']] | number : '1.0-2') : '-' }}
        </span>
        <span class="--has-custom-template" *ngSwitchCase="'Score'">
          {{ dataItem[column['Name']] ? (dataItem[column['Name']] | number : '1.0-2') : '-' }}
        </span>
        <span class="--has-custom-template" *ngSwitchDefault>
          {{ dataItem[column['Name']] ? dataItem[column['Name']] : '-' }}
        </span>
      </ng-container>
    </ng-template>
  </div>
</ng-template>
<!-- #endregion -->

<!-- #region tpl format with custom tpl column -->
<ng-template #tplFormatTemplate let-column let-dataItem="dataItem" let-rowIndex="rowIndex">
  <div [ngSwitch]="column?.Format?.split('|')[0]?.toLowerCase()">
    <span *ngSwitchCase="'datetime'" class="sys-import-datetime">
      <ng-container>
        {{ dataItem[column.Name] | date : (column?.Format?.split('|'))[1] }}
      </ng-container>
    </span>
    <span *ngSwitchCase="'number'" class="sys-import-number">
      <ng-container *ngIf="(column?.Format?.split('|'))[1] === 'cp'; else tplColumnNoCustom">
        {{
          dataItem[column.Name] !== null
            ? (dataItem[column.Name] | kendoNumber : '#,##0.00 \\%')
            : '%'
        }}
      </ng-container>
      <ng-template #tplColumnNoCustom>
        {{ dataItem[column.Name] | kendoNumber : column.Format.split('|')[1] }}
      </ng-template>
    </span>
    <span *ngSwitchCase="'bool'">
      <label
        nz-checkbox
        [(ngModel)]="dataItem[column.Name]"
        nzDisabled
        [name]="column.Name + '__' + rowIndex"
        [for]="column.Name + '__' + rowIndex"
      >
      </label>
    </span>
    <span *ngSwitchCase="'link'">
      <a [name]="column.Name + '__' + rowIndex" [href]="dataItem[column.Name]">
        {{ dataItem[column.Name] }}
      </a>
    </span>
    <!-- <span *ngSwitchCase="'file'">
      <ng-container
        *ngIf="column.template; else tplColumnFileNoTemplate1"
        [ngTemplateOutlet]="column.template"
        [ngTemplateOutletContext]="{
          $implicit: dataItem,
          rowIndex: rowIndex,
          field: column.Name,
          column: column,
          columnItem: column
        }"
      >
      </ng-container>
      <ng-template #tplColumnFileNoTemplate1>
        <span class="d-flex">
          <ng-container
            *ngFor="
              let file of dataItem[column.Name] | vnrConvertFileNameToList : appConfigURL : 25;
              let last = last
            "
          >
            <a
              class="vnr-grids-upload__box"
              (click)="onClickFileName(file.name); $event.preventDefault()"
              [title]="file && file?.showTooltip && file?.name ? file.name : file?.nameFormat"
              [name]="column.Name + '__' + rowIndex"
              tabindex="0"
              (keyup)="$event.preventDefault()"
            >
              <div class="vnr-grids-upload__nameFile">
                <span nz-icon [nzType]="'paper-clip'" style="color: #262626"></span>
                {{ file && file.nameFormat }}
              </div>
            </a>
            <span class="mr-1" *ngIf="!last">,</span>
          </ng-container>
        </span>
      </ng-template>
    </span> -->
    <span *ngSwitchCase="'html'">
      <div [innerHTML]="dataItem[column.Name]"></div>
    </span>
    <span *ngSwitchCase="'money'" class="sys-import-money">
      {{ dataItem[column.Name] | currency : 'VND' : '' : '1.0-3' : '' }}
    </span>
    <!-- <span *ngSwitchCase="'uploadfile'" class="--has-custom-template vnr-format-type-uploadfile">
      <ng-container *ngIf="dataItem[column.Name]; else tplUploadIcon">
        <div class="d-flex">
          <span
            (click)="onClickUploadFileToColumn(column.Name, dataItem, column); $event.preventDefault()"
            class="mr-2"
            style="cursor: pointer"
            tabindex="0"
            (keyup)="$event.preventDefault()"
          >
            <i nz-icon nzType="edit" nzTheme="twotone"></i>
          </span>
          <ng-container
            *ngFor="
              let file of dataItem[column.Name] | vnrConvertFileNameToList : appConfigURL : 25;
              let last = last
            "
          >
            <a
              class="vnr-grids-upload__box"
              (click)="onClickFileName(file.name); $event.preventDefault()"
              [title]="file && file?.showTooltip && file?.name ? file.name : file?.nameFormat"
              [name]="column.Name + '__' + rowIndex"
              tabindex="0"
              (keyup)="$event.preventDefault()"
            >
              <div class="vnr-grids-upload__nameFile">
                <span nz-icon [nzType]="'paper-clip'" style="color: #262626"></span>
                {{ file && file.nameFormat }}
              </div>
            </a>
            <span class="mr-1" *ngIf="!last">,</span>
          </ng-container>
        </div>
      </ng-container>
      <ng-template #tplUploadIcon>
        <button
          nz-button
          [nzType]="'default'"
          [nzSize]="'small'"
          (click)="onClickUploadFileToColumn(column.Name, dataItem, column)"
          class="d-flex justify-content-center align-items-center m-auto"
          tabindex="0"
          (keyup)="$event.preventDefault()"
        >
          <i nz-icon nzType="upload" nzTheme="outline"></i>
        </button>
      </ng-template>
    </span> -->
    <span *ngSwitchDefault>
      {{ dataItem && dataItem[column['Name']] }}
    </span>
  </div>
</ng-template>
<!-- #endregion tpl format with custom tpl column -->

<!-- #region Custom template footer Group -->
<ng-template
  #templateFooterGroup
  let-dataItem
  let-aggregates="aggregates"
  let-aggregateItem="aggregateItem"
  let-columnItem="columnItem"
>
  @if(columnItem?.Name=== columnNameTotal){
  <div>{{ 'common.calculatesum' | translate }}:</div>
  } @else {
  <div
    class="footer-custom"
    *ngIf="aggregates && aggregates[columnItem.Name] && columnItem.Calculate"
  >
    {{ aggregates[columnItem.Name][columnItem.Calculate] || 0 }}
  </div>
  }
</ng-template>
<!-- #endregion Custom template footer Group -->

<!-- #region Custom template footer -->
<ng-template
  #templateFooter
  let-dataItem
  let-aggregates="aggregates"
  let-aggregateItem="aggregateItem"
  let-columnItem="columnItem"
>
  @if(columnItem?.Name === columnNameTotal){
  <div>{{ 'common.calculatesum' | translate }}:</div>
  } @else {
  <div
    class="footer-custom"
    *ngIf="aggregateItem && aggregateItem[columnItem.Name] && columnItem.Calculate"
  >
    {{ aggregateItem[columnItem.Name][columnItem.Calculate] || 0 }}
  </div>
  }
</ng-template>
<!-- #endregion -->

<!-- #region Custom template control -->
<ng-template
  #templateCustomControl
  let-dataItem
  let-rowIndex="rowIndex"
  let-columnItem="columnItem"
  let-formGroup="formGroup"
  let-isNew="isNew"
  let-required="required"
  let-maxlength="maxlength"
  let-minlength="minlength"
  let-min="min"
  let-max="max"
>
  <ng-container
    *ngTemplateOutlet="
      tplDefault;
      context: {
        $implicit: dataItem,
        rowIndex: rowIndex,
        columnItem: columnItem,
        formGroup: formGroup,
        isNew: isNew,
        required: columnItem | sharedGridColumnsValidators : _Required,
        max: columnItem | sharedGridColumnsValidators : _Max,
        min: columnItem | sharedGridColumnsValidators : _Min,
        maxlength: columnItem | sharedGridColumnsValidators : _MaxLength,
        minlength: columnItem | sharedGridColumnsValidators : _MinLength,
        email: columnItem | sharedGridColumnsValidators : _Email
      }
    "
  ></ng-container>
</ng-template>
<ng-template
  #tplDefault
  let-dataItem
  let-rowIndex="rowIndex"
  let-columnItem="columnItem"
  let-formGroup="formGroup"
  let-isNew="isNew"
  let-required="required"
  let-maxlength="maxlength"
  let-minlength="minlength"
  let-min="min"
  let-max="max"
  let-email="email"
>
  <ng-container
    [ngSwitch]="
    dataItem?.controls?.[columnItem.Name]
      ? (dataItem?.controls
        | sharedCustomByTypeControls
          :dataItem:{ isDataItem: true, typeControl: dataItem?.controls?.[columnItem.Name]?.TypeControl, fieldName: columnItem.Name })
      : (columnItem | sharedCustomByTypeControls:dataItem)
  "
  >
    <ng-container *ngSwitchCase="'DatePicker'">
      <ng-template
        [ngTemplateOutlet]="controlDatePicker"
        [ngTemplateOutletContext]="{
          $implicit: dataItem,
          rowIndex: rowIndex,
          columnItem: columnItem,
          formGroup: formGroup,
          isNew: isNew,
          required: required,
          disabled: false
        }"
      ></ng-template>
    </ng-container>
    <ng-container *ngSwitchCase="'ComboBox'">
      <ng-template
        *ngIf="dataItem && columnItem?.ReferenceName"
        [ngTemplateOutlet]="controlComboBox"
        [ngTemplateOutletContext]="{
          $implicit: dataItem,
          rowIndex: rowIndex,
          columnItem: columnItem,
          formGroup: formGroup,
          isNew: isNew,
          required: required,
          disabled: false
        }"
      ></ng-template>
    </ng-container>
    <ng-container *ngSwitchCase="'MultiSelect'">
      <ng-template
        *ngIf="dataItem && columnItem?.ReferenceName"
        [ngTemplateOutlet]="controlMultiSelect"
        [ngTemplateOutletContext]="{
          $implicit: dataItem,
          rowIndex: rowIndex,
          columnItem: columnItem,
          formGroup: formGroup,
          isNew: isNew,
          required: required,
          disabled: false
        }"
      ></ng-template>
    </ng-container>
    <ng-container *ngSwitchCase="'InputNumber'">
      <ng-template
        [ngTemplateOutlet]="controlInputNumber"
        [ngTemplateOutletContext]="{
          $implicit: dataItem,
          rowIndex: rowIndex,
          columnItem: columnItem,
          formGroup: formGroup,
          isNew: isNew,
          required: required,
          disabled: false,
          max: max,
          min: min
        }"
      ></ng-template>
    </ng-container>
    <ng-container *ngSwitchCase="'TreeViewV2'">
      <ng-template
        *ngIf="dataItem && columnItem?.ReferenceName"
        [ngTemplateOutlet]="controlTreeViewV2orOrg"
        [ngTemplateOutletContext]="{
          $implicit: dataItem,
          rowIndex: rowIndex,
          columnItem: columnItem,
          formGroup: formGroup,
          isNew: isNew,
          required: required,
          disabled: false
        }"
      ></ng-template>
    </ng-container>
    <ng-container *ngSwitchCase="'Org'">
      <ng-template
        *ngIf="dataItem && columnItem?.ReferenceName"
        [ngTemplateOutlet]="controlTreeViewV2orOrg"
        [ngTemplateOutletContext]="{
          $implicit: dataItem,
          rowIndex: rowIndex,
          columnItem: columnItem,
          formGroup: formGroup,
          isNew: isNew,
          required: required,
          disabled: false
        }"
      ></ng-template>
    </ng-container>
    <ng-container *ngSwitchCase="'SelectEmp'">
      <ng-template
        *ngIf="dataItem && columnItem?.ReferenceName"
        [ngTemplateOutlet]="controlSelectEmp"
        [ngTemplateOutletContext]="{
          $implicit: dataItem,
          rowIndex: rowIndex,
          columnItem: columnItem,
          formGroup: formGroup,
          isNew: isNew,
          required: required,
          disabled: false
        }"
      ></ng-template>
    </ng-container>
    <ng-container *ngSwitchCase="'CheckBox'">
      <ng-template
        [ngTemplateOutlet]="controlCheckBox"
        [ngTemplateOutletContext]="{
          $implicit: dataItem,
          rowIndex: rowIndex,
          columnItem: columnItem,
          formGroup: formGroup,
          isNew: isNew,
          required: required,
          disabled: false
        }"
      ></ng-template>
    </ng-container>
    <ng-container *ngSwitchCase="'Switch'">
      <ng-template
        [ngTemplateOutlet]="controlSwitch"
        [ngTemplateOutletContext]="{
          $implicit: dataItem,
          rowIndex: rowIndex,
          columnItem: columnItem,
          formGroup: formGroup,
          isNew: isNew,
          required: required,
          disabled: false
        }"
      ></ng-template>
    </ng-container>
    <ng-container *ngSwitchCase="'TextArea'">
      <ng-template
        [ngTemplateOutlet]="controlTextArea"
        [ngTemplateOutletContext]="{
          $implicit: dataItem,
          rowIndex: rowIndex,
          columnItem: columnItem,
          formGroup: formGroup,
          isNew: isNew,
          required: required,
          disabled: false,
          maxlength: maxlength,
          minlength: minlength
        }"
      ></ng-template>
    </ng-container>
    <ng-container *ngSwitchCase="'MaskedTextbox'">
      <ng-template
        [ngTemplateOutlet]="controlMaskedTextbox"
        [ngTemplateOutletContext]="{
          $implicit: dataItem,
          rowIndex: rowIndex,
          columnItem: columnItem,
          formGroup: formGroup,
          isNew: isNew,
          required: required,
          disabled: false,
          maxlength: maxlength,
          minlength: minlength
        }"
      ></ng-template>
    </ng-container>
    <ng-container *ngSwitchDefault>
      <ng-template
        [ngTemplateOutlet]="controlText"
        [ngTemplateOutletContext]="{
          $implicit: dataItem,
          rowIndex: rowIndex,
          columnItem: columnItem,
          formGroup: formGroup,
          isNew: isNew,
          required: required,
          maxlength: maxlength,
          minlength: minlength,
          disabled: false,
          email: email
        }"
      ></ng-template>
    </ng-container>
  </ng-container>
</ng-template>
<!-- #endregion -->

<!-- #region control Input  -->
<ng-template
  #controlText
  let-dataItem
  let-rowIndex="rowIndex"
  let-columnItem="columnItem"
  let-formGroup="formGroup"
  let-isNew="isNew"
  let-required="required"
  let-maxlength="maxlength"
  let-minlength="minlength"
  let-disabled="disabled"
  let-email="email"
>
  <!-- [index]="dataItem?.ID" -->
  <vnr-input
    [ngModelOptions]="{ standalone: true }"
    [(ngModel)]="dataItem[columnItem.Name]"
    [name]="columnItem.Name"
    [builder]="columnItem | vnrGridEditInitBuilderControls : 'TextBox'"
    [required]="required"
    [maxlength]="maxlength"
    [minlength]="minlength"
    (ngModelChange)="onModelChange({
    event: $event,
    dataItem: dataItem,
    column: columnItem,
    index: rowIndex,
    isNew: isNew,
    group: formGroup,
  })"
    (keydown)="preventKeyEnter($event)"
  >
  </vnr-input>
  <!-- Hiển thị thông báo lỗi -->
  <div
    class="validation"
    *ngIf="
      email && dataItem && dataItem[columnItem.Name] && !isEmailValid(dataItem[columnItem.Name])
    "
  >
    <div class="text-danger">{{ 'common.validation.email' | translate }}</div>
  </div>
</ng-template>
<!-- #endregion -->

<!-- #region control TextArea   -->
<ng-template
  #controlTextArea
  let-dataItem
  let-rowIndex="rowIndex"
  let-columnItem="columnItem"
  let-formGroup="formGroup"
  let-isNew="isNew"
  let-required="required"
  let-disabled="disabled"
  let-maxlength="maxlength"
  let-minlength="minlength"
>
  <!-- [index]="dataItem?.ID" -->
  <vnr-textarea
    [ngModelOptions]="{ standalone: true }"
    [(ngModel)]="dataItem[columnItem.Name]"
    [name]="columnItem.Name"
    [builder]="columnItem | sharedRenderBuilderControls : 'TextArea' : dataItem"
    [required]="required"
    [maxlength]="maxlength"
    [minlength]="minlength"
    (ngModelChange)="onModelChange({
    event: $event,
    dataItem: dataItem,
    column: columnItem,
    index: rowIndex,
    isNew: isNew,
    group: formGroup,
  })"
    (keydown)="preventKeyEnter($event)"
  >
  </vnr-textarea>
</ng-template>
<!-- #endregion -->

<!-- #region control Switch   -->
<ng-template
  #controlSwitch
  let-dataItem
  let-rowIndex="rowIndex"
  let-columnItem="columnItem"
  let-formGroup="formGroup"
  let-isNew="isNew"
  let-required="required"
  let-disabled="disabled"
>
  <!-- [index]="dataItem?.ID" -->
  <vnr-switch
    createControl
    class="sys-import-grid--switch"
    [ngModelOptions]="{ standalone: true }"
    [(ngModel)]="dataItem[columnItem.Name]"
    [name]="columnItem.Name"
    [builder]="columnItem | sharedRenderBuilderControls : 'Switch' : dataItem"
    [required]="required"
    (ngModelChange)="onModelChange({
    event: $event,
    dataItem: dataItem,
    column: columnItem,
    index: rowIndex,
    isNew: isNew,
    group: formGroup,
  })"
  >
  </vnr-switch>
</ng-template>
<!-- #endregion -->

<!-- #region control Check box   -->
<ng-template
  #controlCheckBox
  let-dataItem
  let-rowIndex="rowIndex"
  let-columnItem="columnItem"
  let-formGroup="formGroup"
  let-isNew="isNew"
  let-required="required"
  let-disabled="disabled"
>
  <!-- <label
    class="sys-import-grid--checkbox"
    nz-checkbox
    [name]="columnItem?.Name"
    [ngModelOptions]="{ standalone: true }"
    [(ngModel)]="dataItem[columnItem.Name]"
    [nzDisabled]="disabled"
    (ngModelChange)="onModelChange({
    event: $event,
    dataItem: dataItem,
    column: columnItem,
    index: rowIndex,
    isNew: isNew,
    group: formGroup,
  })"
  ></label> -->
</ng-template>
<!-- #endregion -->

<!-- #region control DatePicker  -->
<ng-template
  #controlDatePicker
  let-dataItem
  let-rowIndex="rowIndex"
  let-columnItem="columnItem"
  let-formGroup="formGroup"
  let-isNew="isNew"
  let-required="required"
  let-disabled="disabled"
>
  <!-- [index]="dataItem?.ID" -->
  <!-- [isUsingFormatSettings]="false" -->
  <vnr-datepicker
    [name]="columnItem?.Name"
    [builder]="columnItem | sharedRenderBuilderControls : 'DatePicker' : dataItem"
    [ngModelOptions]="{ standalone: true }"
    [(ngModel)]="dataItem[columnItem.Name]"
    [required]="required"
    (keydown)="preventKeyEnter($event)"
  ></vnr-datepicker>
</ng-template>
<!-- #endregion -->

<!-- #region control Number  -->
<ng-template
  #controlInputNumber
  let-dataItem
  let-rowIndex="rowIndex"
  let-columnItem="columnItem"
  let-formGroup="formGroup"
  let-isNew="isNew"
  let-required="required"
  let-disabled="disabled"
  let-max="max"
  let-min="min"
>
  <!-- [index]="dataItem?.ID" -->
  <!-- [min]="min"
  [max]="max" -->
  <vnr-inputnumber
    createControl
    [name]="columnItem?.Name"
    [ngModelOptions]="{ standalone: true }"
    [(ngModel)]="dataItem[columnItem.Name]"
    [builder]="columnItem | sharedRenderBuilderControls : 'InputNumber' : dataItem"
    [required]="required"
    (ngModelChange)="onModelChange({
    event: $event,
    dataItem: dataItem,
    column: columnItem,
    index: rowIndex,
    isNew: isNew,
    group: formGroup,
  })"
    (keydown)="preventKeyEnter($event)"
  ></vnr-inputnumber>
</ng-template>
<!-- #endregion -->

<!-- #region control Multi select  -->
<ng-template
  #controlMultiSelect
  let-dataItem
  let-rowIndex="rowIndex"
  let-columnItem="columnItem"
  let-formGroup="formGroup"
  let-isNew="isNew"
  let-required="required"
  let-disabled="disabled"
  let-dataSource="dataSource"
>
  <vnr-multiselect
    #vnrMulti
    class="custom-control-multi-check-import"
    id="{{ dataItem?.ID }}"
    [builder]="columnItem | sharedRenderBuilderControls: 'MultiSelect':dataItem:vnrBuilderConfigByColumn:dataItem?.controls?.[columnItem.Name]"
    [name]="columnItem?.Name"
    [ngModelOptions]="{ standalone: true }"
    [(ngModel)]="dataItem[columnItem?.ReferenceName]"
    (ngModelChange)="onModelChange({
    event: $event,
    dataItem: dataItem,
    column: columnItem,
    index: rowIndex,
    isNew: isNew,
    group: formGroup,
  })"
    (selectDataListItem)="
      onSelectDataItemMultiselect({
        event: $event,
        dataItem: dataItem,
        column: columnItem,
        index: rowIndex,
        isNew: isNew,
        group: formGroup
      })
    "
    (keydown)="preventKeyEnter($event)"
  ></vnr-multiselect>
</ng-template>
<!-- #endregion -->

<!-- #region control Combobox  -->
<ng-template
  #controlComboBox
  let-dataItem
  let-rowIndex="rowIndex"
  let-columnItem="columnItem"
  let-formGroup="formGroup"
  let-isNew="isNew"
  let-required="required"
  let-disabled="disabled"
  let-dataSource="dataSource"
>
  <vnr-combobox
    createControl
    [builder]="
      columnItem | sharedRenderBuilderControls : 'ComboBox' : dataItem : vnrBuilderConfigByColumn
    "
    [index]="dataItem?.ID"
    [name]="columnItem?.Name"
    [ngModelOptions]="{ standalone: true }"
    [(ngModel)]="dataItem[columnItem?.ReferenceName]"
    (selectDataItem)="
      onSelectDataItem({
        event: $event,
        dataItem: dataItem,
        column: columnItem,
        index: rowIndex,
        isNew: isNew,
        group: formGroup
      })
    "
    [vnrDataSource]="dataSource"
    (ngModelChange)="onModelChange({
    event: $event,
    dataItem: dataItem,
    column: columnItem,
    index: rowIndex,
    isNew: isNew,
    group: formGroup,
  })"
    (keydown)="preventKeyEnter($event)"
  ></vnr-combobox>
</ng-template>
<!-- #endregion -->

<!-- #region control Nhân viên   -->
<ng-template
  #controlSelectEmp
  let-dataItem
  let-rowIndex="rowIndex"
  let-columnItem="columnItem"
  let-formGroup="formGroup"
  let-isNew="isNew"
  let-required="required"
  let-disabled="disabled"
>
  <!-- <vnr-select-emp
    createControl
    #EmployeeControl
    [name]="columnItem?.Name"
    class="sys-import-form__selectedEmployee"
    [builder]="columnItem | sharedRenderBuilderControls: 'SelectEmp':dataItem:vnrBuilderConfigByColumn:dataItem?.controls?.[columnItem.Name]"
    [ngModelOptions]="{ standalone: true }"
    [(ngModel)]="dataItem[columnItem?.ReferenceName]"
    [isShowCheckAll]="false"
    [vnrAdvanceSearch]="false"
    [vnrShowMoreInfo]="false"
    [vnrMode]="'default'"
    (ngModelChange)="onModelChange({
    event: $event,
    dataItem: dataItem,
    column: columnItem,
    index: rowIndex,
    isNew: isNew,
    group: formGroup,
  })"
    (keydown)="preventKeyEnter($event)"
  ></vnr-select-emp> -->
</ng-template>
<!-- #endregion -->

<!-- #region control Phòng ban/treeview v2   -->
<ng-template
  #controlTreeViewV2orOrg
  let-dataItem
  let-rowIndex="rowIndex"
  let-columnItem="columnItem"
  let-formGroup="formGroup"
  let-isNew="isNew"
  let-required="required"
  let-disabled="disabled"
>
  <vnr-treeview-v2
    class="custom-control-multi-treeview-import"
    createControl
    #tplSysTreeview
    [name]="columnItem?.Name"
    [isOrgTree]="true"
    [builder]="columnItem | sharedRenderBuilderControls: 'TreeViewV2':dataItem:vnrBuilderConfigByColumn:dataItem?.controls?.[columnItem.Name]"
    [ngModelOptions]="{ standalone: true }"
    [(ngModel)]="dataItem[columnItem?.ReferenceName]"
    (ngModelChange)="onModelChange({
    event: $event,
    dataItem: dataItem,
    column: columnItem,
    index: rowIndex,
    isNew: isNew,
    group: formGroup,
  })"
    (vnrSelectionChangeItem)="
      onSelectDataItemTreeViewV2orOrg({
        event: $event,
        dataItem: dataItem,
        column: columnItem,
        index: rowIndex,
        isNew: isNew,
        group: formGroup
      })
    "
    (keydown)="preventKeyEnter($event)"
  ></vnr-treeview-v2>
</ng-template>
<!-- #endregion -->

<!-- #region control MaskedTextbox  -->
<ng-template
  #controlMaskedTextbox
  let-dataItem
  let-rowIndex="rowIndex"
  let-columnItem="columnItem"
  let-formGroup="formGroup"
  let-isNew="isNew"
  let-required="required"
  let-maxlength="maxlength"
  let-minlength="minlength"
  let-disabled="disabled"
>
  <!-- [index]="dataItem?.ID" -->
  <vnr-masked-textbox
    #vnrMaskedTextbox
    [ngModelOptions]="{ standalone: true }"
    [(ngModel)]="dataItem[columnItem.Name]"
    [name]="columnItem.Name"
    [builder]="columnItem | sharedRenderBuilderControls : 'MaskedTextbox' : dataItem"
    [required]="required"
    [maxlength]="maxlength"
    [minlength]="minlength"
    (ngModelChange)="onModelChange({
    event: $event,
    dataItem: dataItem,
    column: columnItem,
    index: rowIndex,
    isNew: isNew,
    group: formGroup,
  })"
  >
  </vnr-masked-textbox>
</ng-template>
<!-- #endregion -->
