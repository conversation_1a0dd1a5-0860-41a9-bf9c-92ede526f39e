<div nz-row [nzGutter]="[16, 16]" class="p-3 detail-goal-result">
  <div nz-col class="gutter-row" [nzSpan]="24">
    <div class="card mb-0">
      <div class="card-body">
        <h6 class="mb-3 font-weight-bold">Phòng ban: {{ dataItem?.Department }}</h6>
        <div class="d-flex align-items-center my-4">
          <div class="text-title mr-4" style="width: 140px">Chu kỳ mục tiêu</div>
          <div class="text-content">01/01/2025</div>
          <span class="mx-2 text-content"><i class="fa-light fa-arrow-right"></i></span>
          <div class="text-content">31/12/2025</div>
        </div>
        <div class="d-flex align-items-center my-4">
          <div class="text-title mr-4 font-weight-bold" style="width: 140px"><PERSON><PERSON><PERSON><PERSON> đ<PERSON></div>
        </div>
        <div class="d-flex align-items-center my-4">
          <div class="text-title mr-4" style="width: 140px">Họ và tên</div>
          <div class="text-content">
            <div class="d-flex align-items-center">
              <app-vnr-letters-avatar
                [avatarName]="dataItem?.Representative"
                [circular]="true"
                [width]="32"
              ></app-vnr-letters-avatar>
              <span class="ml-2">{{ dataItem?.Representative }}</span>
            </div>
          </div>
        </div>
        <div class="d-flex align-items-center my-4">
          <div class="text-title mr-4" style="width: 140px">Chức danh</div>
          <div class="text-content">{{ dataItem?.Position }}</div>
        </div>
        <div class="grid-container">
          <div class="item1">
            <div><img src="assets/icon/objEval/completionrate-icon.png" alt="Status Icon" /></div>
            <div class="text-title">Mức độ hoàn thành</div>
            <div class="text-result">90%</div>
          </div>
          <div class="item2">
            <div><img src="assets/icon/objEval/successrate-icon.png" alt="Status Icon" /></div>
            <div class="text-title">Tỉ lệ đạt</div>
            <div class="text-result">78% (7/9)</div>
          </div>
          <div class="item3">
            <div><img src="assets/icon/objEval/failrate-icon.png" alt="Status Icon" /></div>
            <div class="text-title">Tỉ lệ không đạt</div>
            <div class="text-result">22% (2/9)</div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div nz-col class="gutter-row" [nzSpan]="24">
    <div class="card">
      <div class="card-body">
        <h6 class="mb-3 font-weight-bold">Danh sách mục tiêu</h6>
        <!-- Filter with toggle LastResult -->
        <div class="goal-result__filter mb-3 d-flex flex-wrap align-items-center gap-2">
          <ng-container *ngFor="let status of resultStatusFilterList">
            <button
              type="button"
              class="status-pill"
              [ngClass]="[
                getStatusPillClass(status.value),
                status.checked ? 'status-pill--active' : ''
              ]"
              (click)="toggleResultStatusFilter(status)"
            >
              <i nz-icon nzType="check" *ngIf="status.checked" class="mr-1"></i>
              <span class="goal-result__filter-label">{{ status.label }}</span>
              <span class="goal-result__filter-count">({{ status.count }})</span>
            </button>
          </ng-container>
        </div>

        <vnr-grid-new
          #vnrGrid
          [builder]="builderGrid"
          [gridName]="gridName"
          [dataLocal]="dataLocal"
          [columns]="columns"
          [isSupperAdmin]="isSupperAdmin"
          [defaultColumnTemplate]="tplCustomTemplateByColumn"
        >
          <ng-template
            #tplCustomTemplateByColumn
            let-dataItem
            let-column="columnItem"
            let-field="field"
          >
            <ng-container [ngSwitch]="column['Name']">
              <!-- Tên mục tiêu -->
              <span *ngSwitchCase="'GoalName'">
                <span class="btn-link">{{ dataItem['GoalName'] }}</span>
              </span>

              <!-- Tệp đính kèm -->
              <span *ngSwitchCase="'Attachment'">
                <span href="#" class="btn-link">
                  <i class="fa-light fa-download mr-1"></i>
                  {{ dataItem['Attachment'] }}
                </span>
              </span>

              <!-- Kết quả -->
              <span *ngSwitchCase="'ResultStatus'">
                <div class="wrapper-vnrTag">
                  <nz-tag
                    [class]="'wrapper__vnrtag ' + getStatusProgressTag(dataItem).backgroundColor"
                    [nzColor]="getStatusProgressTag(dataItem).backgroundColor"
                    [nzBordered]="true"
                  >
                    <span [style.color]="getStatusProgressTag(dataItem).textColor">{{
                      getStatusProgressTag(dataItem).label
                    }}</span>
                  </nz-tag>
                </div>
              </span>

              <!-- Mức độ hoàn thành -->
              <span *ngSwitchCase="'CompletionRate'">
                <div class="text-success" *ngIf="dataItem['CompletionRate'] >= 100">
                  {{ dataItem['CompletionRate'] }}%
                </div>
                <div *ngIf="dataItem['CompletionRate'] < 100">
                  {{ dataItem['CompletionRate'] }}%
                </div>
              </span>

              <!-- Trạng thái -->
              <span *ngSwitchCase="'Status'">
                <div class="wrapper-vnrTag">
                  <nz-tag
                    [class]="'wrapper__vnrtag ' + getStatusTag(dataItem).backgroundColor"
                    [nzColor]="getStatusTag(dataItem).backgroundColor"
                    [nzBordered]="true"
                  >
                    <span [style.color]="getStatusTag(dataItem).textColor">{{
                      getStatusTag(dataItem).label
                    }}</span>
                  </nz-tag>
                </div>
              </span>

              <!-- Default Template -->
              <span *ngSwitchDefault>
                {{ dataItem[column['Name']] || '' }}
              </span>
            </ng-container>
          </ng-template>

          <ng-template #templateEmpty>-</ng-template>
        </vnr-grid-new>
      </div>
    </div>
  </div>
</div>
