<vnr-toolbar-new [builder]="builderToolbar">
  <vnr-button-new
    rightToolbar
    [builder]="builderbtnOpenColumnCheck"
    [isShow]="isShowOpenColumnCheck"
    (vnrClick)="onOpenColumnCheck($event)"
  ></vnr-button-new>
  <vnr-button-new
    rightToolbar
    [builder]="builderbtnCancelOpenColumnCheck"
    [isShow]="!isShowOpenColumnCheck"
    (vnrClick)="onCancelOpenColumnCheck($event)"
  ></vnr-button-new>
  <vnr-button-new
    rightToolbar
    [builder]="builderbtnExpandAll"
    [isShow]="isShowExpandAll"
    (vnrClick)="onExpandAll($event)"
  ></vnr-button-new>
  <vnr-button-new
    rightToolbar
    [builder]="builderbtnCollapseAll"
    [isShow]="!isShowExpandAll"
    (vnrClick)="onCollapseAll($event)"
  ></vnr-button-new>
</vnr-toolbar-new>
<vnr-listview-new
  #vnrGrid
  [builder]="builderGrid"
  [gridName]="gridName"
  [dataLocal]="dataLocal"
  [columns]="columns"
  [dataFormSearch]="dataFormSearch"
  [rowDetailTemplate]="tplMasterDetail"
  [rowActionsTemplate]="rowActionsTemplate"
  [columnHeaderTemplate]="tplHeader"
  (vnrLoadData)="onLoadData($event)"
  (vnrEdit)="onGridEdit($event)"
  (vnrDelete)="onGridDelete($event)"
  (getSelectedID)="onGridSelectedID($event)"
  (vnrViewDetails)="onGridOpenDetail($event)"
>
</vnr-listview-new>

<ng-template #tplMasterDetail let-dataItem>
  <eva-criteria-grid-list
    [columnMaster]="dataItem"
    (onSelectItem)="onSelectItemCriteria($event)"
  ></eva-criteria-grid-list>
</ng-template>
<ng-template #tplHeader let-dataItem>
  <div class="grid-item-header">
    <span class="grid-item-header__title">
      {{
        'objEval.EvaCriteriaGroup.gridCriteriaGroupHeader'
          | translate : { name: dataItem.Name, n: dataItem.TotalItems }
      }}
      <vnr-tag
        *ngIf="!dataItem.Status"
        [vnrStatus]="dataItem.StatusString"
        [vnrTitle]="dataItem.StatusStringView"
      ></vnr-tag>
      <vnr-tag
        *ngIf="dataItem.CriteriaTypeName"
        [vnrStatus]="'E_ACTIVATE'"
        [vnrTitle]="dataItem.CriteriaTypeName"
      ></vnr-tag>
    </span>
    <span class="grid-item-header__code">{{ dataItem.Description }}</span>
  </div>
</ng-template>
<ng-template #rowActionsTemplate let-dataItem>
  <vnr-button-new
    [builder]="builderbtnAddCriteriaItem"
    (vnrClick)="onAddCriteriaDetailFromGroup($event, dataItem)"
  ></vnr-button-new>
</ng-template>
<ng-template #tplContentDelete let-params>
  <div class="template-delete">
    <div class="template-delete__header">
      <img src="assets/icon/vnr-icon/icon-confirm-delete.png" alt="delete" />
    </div>
    <div class="template-delete__content">
      <div class="template-delete__content-title">
        <span>{{ 'common.modal.confirmDeleteData' | translate }}</span>
      </div>
      <div class="template-delete__content-description">
        {{ 'common.modal.deletedDataCannotBeRecovered' | translate }}
      </div>
    </div>
  </div>
</ng-template>
<ng-template #tplFooterDelete let-ref="modalRef">
  <div class="template-view-detail__action">
    <vnr-button-new [builder]="builderbtnDoNo" (vnrClick)="ref.destroy()"></vnr-button-new>
    <vnr-button-new
      [builder]="builderbtnDoYes"
      (vnrClick)="onConfirmDeleteCriteriaGroup($event, ref)"
    ></vnr-button-new>
  </div>
</ng-template>
<ng-template #tplFooterDeleteCriteria let-ref="modalRef">
  <div class="template-view-detail__action">
    <vnr-button-new [builder]="builderbtnDoNo" (vnrClick)="ref.destroy()"></vnr-button-new>
    <vnr-button-new
      [builder]="builderbtnDoYes"
      (vnrClick)="onConfirmDeleteCriteria($event, ref)"
    ></vnr-button-new>
  </div>
</ng-template>
<ng-template #tplContentViewDetail let-params>
  <div class="template-view-detail">
    <div nz-row [nzGutter]="[20, 24]">
      <div nz-col class="gutter-row" nzSpan="7">
        {{ 'objEval.EvaCriteriaGroup.Name' | translate }}
      </div>
      <div nz-col class="gutter-row" nzSpan="17">
        {{ selectedItem.Name }}
      </div>
    </div>
    <div nz-row [nzGutter]="[20, 24]">
      <div nz-col class="gutter-row" nzSpan="7">
        {{ 'objEval.EvaCriteriaGroup.Code' | translate }}
      </div>
      <div nz-col class="gutter-row" nzSpan="17">
        {{ selectedItem.Code }}
      </div>
    </div>
    <div nz-row [nzGutter]="[20, 24]">
      <div nz-col class="gutter-row" nzSpan="7">
        {{ 'objEval.EvaCriteria.Description' | translate }}
      </div>
      <div nz-col class="gutter-row" nzSpan="17">
        {{ selectedItem.Description }}
      </div>
    </div>
  </div>
</ng-template>
<ng-template #tplFooterViewDetail let-ref="modalRef">
  <div class="template-view-detail__action">
    <vnr-button-new
      [builder]="builderbtnDelete"
      (vnrClick)="onOpenConfirmDeleteCriteriaGroupFromDetail(ref)"
    ></vnr-button-new>
    <vnr-button-new
      [builder]="builderbtnEdit"
      (vnrClick)="onConfirmEditCriteriaGroup(ref)"
    ></vnr-button-new>
  </div>
</ng-template>
