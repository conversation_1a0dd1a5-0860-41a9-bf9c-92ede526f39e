/* eslint-disable max-len */

import { vnrUtilities } from '@hrm-frontend-workspace/vnr-module';
export const EvaProcessStepTest1: any[] = [
  {
    ID: vnrUtilities.newGuid(), //ID e trả cũng đc không cũng đc
    Label: 'Tự đánh giá', // không trả cũng được a tự dịch hoặc e tự dịch cho a
    FieldName: 'UserApproved1',
    UserInfoName: 'Hoàng Vũ Anh Quân', // tên
    ImagePath: null, // hình ảnh
    Comment: null, //note hay gì đó của user đó
    PositionName: 'Quản lý trực tiếp', //Position
    JobTitleName: 'Quản lý trực tiếp', //Position
    IsShow: true, //e hk cần quan tâm a dựa vào levelApprove để check chỗ này, e trả cho a thì càng tốt
    Content: 'đã tự đánh giá và gửi lý do', // nội dung của ng duyệt đã từ chối hay đã duyệt.....
    IsShownComment: false, // có hiển thị coment hay không
    Status: 'E_APPROVED', // trạng thái enum
    StatusView: 'đã duyệt', // trạng thái viêw
    StatusProcess: 'success', // trạng thái yêu cầu thay đổi
    DateUpdated: '2023/12/29 00:00:00', // ngày uppdate
    keyTranslate: '',
    Order: 0,
  },
  {
    ID: vnrUtilities.newGuid(), //ID e trả cũng đc không cũng đc
    Label: 'Đánh giá cấp 2', // không trả cũng được a tự dịch hoặc e tự dịch cho a
    FieldName: 'UserApproved2',
    UserInfoName: 'Hoàng Vũ Anh Quân', // tên
    ImagePath: null, // hình ảnh
    Comment: null, //note hay gì đó của user đó
    PositionName: 'Quản lý trực tiếp', //Position
    JobTitleName: 'Quản lý trực tiếp', //Position
    IsShow: true, //e hk cần quan tâm a dựa vào levelApprove để check chỗ này, e trả cho a thì càng tốt
    Content: null, // nội dung của ng duyệt đã từ chối hay đã duyệt.....
    IsShownComment: false, // có hiển thị coment hay không
    Status: null, // trạng thái enum
    StatusView: null, // trạng thái viêw
    StatusProcess: 'process', // trạng thái yêu cầu thay đổi
    DateUpdated: '2023/12/29 00:00:00', // ngày uppdate
    keyTranslate: '',
    Order: 1,
  },
  {
    ID: vnrUtilities.newGuid(), //ID e trả cũng đc không cũng đc
    Label: 'Đánh giá cấp cuối', // không trả cũng được a tự dịch hoặc e tự dịch cho a
    FieldName: 'UserApproved1',
    UserInfoName: 'Hoàng Vũ Anh', // tên
    ImagePath: null, // hình ảnh
    Comment: null, //note hay gì đó của user đó
    PositionName: 'Quản lý trực tiếp', //Position
    JobTitleName: 'Quản lý trực tiếp', //Position
    IsShow: true, //e hk cần quan tâm a dựa vào levelApprove để check chỗ này, e trả cho a thì càng tốt
    Content: null, // nội dung của ng duyệt đã từ chối hay đã duyệt.....
    IsShownComment: false, // có hiển thị coment hay không
    Status: 'E_APPROVED', // trạng thái enum
    StatusView: 'đã duyệt', // trạng thái viêw
    StatusProcess: null, // trạng thái yêu cầu thay đổi
    DateUpdated: '2023/12/29 00:00:00', // ngày uppdate
    keyTranslate: '',
    Order: 2,
  },
];
export const EvaProcessStepTest: any[] = [
  {
    ID: vnrUtilities.newGuid(), //ID e trả cũng đc không cũng đc
    Label: 'Yêu cầu thay đổi', // không trả cũng được a tự dịch hoặc e tự dịch cho a
    FieldName: 'UserApproved1',
    UserInfoName: 'Hoàng Thùy Trang', // tên
    ImagePath: null, // hình ảnh
    Comment: 'Đăng ký sai quy định công ty', //note hay gì đó của user đó
    PositionName: 'Quản lý trực tiếp', //Position
    JobTitleName: 'Quản lý trực tiếp', //Position
    IsShow: true, //e hk cần quan tâm a dựa vào levelApprove để check chỗ này, e trả cho a thì càng tốt
    Content: 'đã yêu cầu thay đổi và gửi lý do', // nội dung của ng duyệt đã từ chối hay đã duyệt.....
    IsShownComment: false, // có hiển thị coment hay không
    Status: 'E_APPROVED', // trạng thái enum
    StatusView: 'đã duyệt', // trạng thái viêw
    StatusProcess: 'submitChange', // trạng thái yêu cầu thay đổi
    DateUpdated: '2023/12/29 00:00:00', // ngày uppdate
    keyTranslate: '',
    Order: 0,
  },
  {
    ID: vnrUtilities.newGuid(), //ID e trả cũng đc không cũng đc
    Label: 'Người yêu cầu hủy 1', // không trả cũng được a tự dịch hoặc e tự dịch cho a
    FieldName: 'UserApproved1',
    UserInfoName: 'Hoàng Thùy Trang', // tên
    ImagePath: null, // hình ảnh
    Comment: 'Đăng ký sai quy định công ty', //note hay gì đó của user đó
    PositionName: 'Quản lý trực tiếp', //Position
    IsShow: true, //e hk cần quan tâm a dựa vào levelApprove để check chỗ này, e trả cho a thì càng tốt
    Content: 'đã yêu cầu thay đổi và gửi lý do', // nội dung của ng duyệt đã từ chối hay đã duyệt.....
    IsShownComment: false, // có hiển thị coment hay không
    Status: 'E_APPROVED', // trạng thái enum
    StatusView: 'đã duyệt', // trạng thái viêw
    StatusProcess: 'processCancelRequest', // trạng thái yêu cầu hủy
    DateUpdated: '2023/12/29 00:00:00', // ngày uppdate
    keyTranslate: '',
    Order: 0,
    ListDetails: [
      {
        ID: vnrUtilities.newGuid(), //ID e trả cũng đc không cũng đc
        Label: 'Yêu cầu thay đổi', // không trả cũng được a tự dịch hoặc e tự dịch cho a
        FieldName: 'UserApproved1',
        UserInfoName: 'Hoàng Thùy Trang', // tên
        ImagePath: null, // hình ảnh
        Comment: 'Đăng ký sai quy định công ty', //note hay gì đó của user đó
        PositionName: 'Quản lý trực tiếp', //Position
        IsShow: true, //e hk cần quan tâm a dựa vào levelApprove để check chỗ này, e trả cho a thì càng tốt
        Content: 'đã yêu cầu thay đổi và gửi lý do', // nội dung của ng duyệt đã từ chối hay đã duyệt.....
        IsShownComment: false, // có hiển thị coment hay không
        Status: 'E_APPROVED', // trạng thái enum
        StatusView: 'đã duyệt', // trạng thái viêw
        StatusProcess: 'submitChange', // trạng thái yêu cầu thay đổi
        DateUpdated: '2023/12/29 00:00:00', // ngày uppdate
        keyTranslate: '',
        Order: 0,
      },
      {
        ID: vnrUtilities.newGuid(), //ID e trả cũng đc không cũng đc
        Label: 'Người yêu cầu hủy 1', // không trả cũng được a tự dịch hoặc e tự dịch cho a
        FieldName: 'UserApproved1',
        UserInfoName: 'Hoàng Thùy Trang', // tên
        ImagePath: null, // hình ảnh
        Comment: 'Đăng ký sai quy định công ty', //note hay gì đó của user đó
        PositionName: 'Quản lý trực tiếp', //Position
        IsShow: true, //e hk cần quan tâm a dựa vào levelApprove để check chỗ này, e trả cho a thì càng tốt
        Content: 'đã yêu cầu thay đổi và gửi lý do', // nội dung của ng duyệt đã từ chối hay đã duyệt.....
        IsShownComment: false, // có hiển thị coment hay không
        Status: 'E_APPROVED', // trạng thái enum
        StatusView: 'đã duyệt', // trạng thái viêw
        StatusProcess: 'processCancelRequest', // trạng thái yêu cầu hủy
        DateUpdated: '2023/12/29 00:00:00', // ngày uppdate
        keyTranslate: '',
        Order: 0,
      },
      {
        ID: vnrUtilities.newGuid(), //ID e trả cũng đc không cũng đc
        Label: 'Người duyệt yêu cầu hủy 1', // không trả cũng được a tự dịch hoặc e tự dịch cho a
        FieldName: 'UserApproved1',
        UserInfoName: 'Hoàng Thùy Trang', // tên
        ImagePath: null, // hình ảnh
        Comment: 'Đăng ký sai quy định công ty', //note hay gì đó của user đó
        PositionName: 'Quản lý trực tiếp', //Position
        IsShow: true, //e hk cần quan tâm a dựa vào levelApprove để check chỗ này, e trả cho a thì càng tốt
        Content: 'đã yêu cầu thay đổi và gửi lý do', // nội dung của ng duyệt đã từ chối hay đã duyệt.....
        IsShownComment: false, // có hiển thị coment hay không
        Status: 'E_APPROVED', // trạng thái enum
        StatusView: 'đã duyệt', // trạng thái viêw
        StatusProcess: 'successCancelRequest', // trạng thái duyệt yêu cầu hủy
        DateUpdated: '2023/12/29 00:00:00', // ngày uppdate
        keyTranslate: '',
        Order: 0,
      },
      {
        ID: vnrUtilities.newGuid(), //ID e trả cũng đc không cũng đc
        Label: 'Người duyệt 1', // không trả cũng được a tự dịch hoặc e tự dịch cho a
        FieldName: 'UserApproved1',
        UserInfoName: 'Hoàng Thùy Trang', // tên
        ImagePath: null, // hình ảnh
        Comment: 'Đăng ký sai quy định công ty', //note hay gì đó của user đó
        PositionName: 'Quản lý trực tiếp', //Position
        IsShow: true, //e hk cần quan tâm a dựa vào levelApprove để check chỗ này, e trả cho a thì càng tốt
        Content: 'đã yêu cầu thay đổi và gửi lý do', // nội dung của ng duyệt đã từ chối hay đã duyệt.....
        IsShownComment: false, // có hiển thị coment hay không
        Status: 'E_APPROVED', // trạng thái enum
        StatusView: 'đã duyệt', // trạng thái viêw
        StatusProcess: 'process', // trạng thái mà nó đang đứng
        DateUpdated: '2023/12/29 00:00:00', // ngày uppdate
        keyTranslate: '',
        Order: 0,
      },
      {
        ID: vnrUtilities.newGuid(),
        Label: 'Người duyệt 2',
        UserInfoName: 'Vũ Quốc Thiên Đăng',
        FieldName: 'UserApproved1',
        ImagePath: null,
        Comment: 'Đăng ký sai quy định công ty',
        PositionName: 'Phó nhóm thiết kế',
        IsShow: true,
        Content: null,
        IsShownComment: false,
        Status: 'E_APPROVED', // trạng thái enum
        StatusView: 'đã duyệt', // trạng thái viêw
        StatusProcess: 'success', // nếu trạng thái đã duyệt
        DateUpdated: '2023/12/29 00:00:00',
      },
      {
        ID: vnrUtilities.newGuid(),
        Label: 'Người duyệt 3',
        UserInfoName: 'Nguyễn Lê Xuân Phước',
        ImagePath: null,
        FieldName: 'UserApproved1',
        Comment:
          'Ngày nghỉ việc em yêu cầu hơi gấp nên anh chưa sắp xếp được nhân sự để thay thế nên cảm phiền em dời ngày nghi việc.Cảm ơn em!!! Ngày nghỉ việc em yêu cầu hơi gấp nên anh chưa sắp xếp được nhân sự để thay thế nên cảm phiền em dời ngày nghi việc.Cảm ơn em!!! Ngày nghỉ việc em yêu cầu hơi gấp nên anh chưa sắp xếp được nhân sự để thay thế nên cảm phiền em dời ngày nghi việc.Cảm ơn em!!!',
        IsShow: true,
        Content: 'đã yêu cầu thay đổi và gửi lý do',
        IsShownComment: true,
        StatusProcess: 'error', // nếu trạng thái từ chối
        DateUpdated: '2023/12/29 00:00:00',
      },
      {
        ID: vnrUtilities.newGuid(),
        Label: 'Người duyệt 4',
        UserInfoName: 'Hoàng Thùy Lương',
        ImagePath: null,
        FieldName: 'UserApproved1',
        Comment: 'Đăng ký sai quy định công ty',
        IsShow: true,
        Content: 'đã yêu cầu thay đổi và gửi lý do',
        StatusProcess: 'warning', // nếu trạng thái cầu thay đổi
        IsShownComment: true,
        DateUpdated: '2023/12/29 00:00:00',
      },
      {
        ID: vnrUtilities.newGuid(),
        Label: 'Người yêu cầu thay đổi',
        UserInfoName: 'Nguyễn Hưng',
        ImagePath: null,
        FieldName: 'UserApproved1',
        Comment: 'Đăng ký sai quy định công ty',
        IsShow: true,
        Content: 'đã yêu cầu thay đổi và gửi lý do',
        IsShownComment: true,
        DateUpdated: '2023/12/29 00:00:00',
        StatusProcess: null, //thì nó chưa tới người này
      },
    ],
  },
  {
    ID: vnrUtilities.newGuid(), //ID e trả cũng đc không cũng đc
    Label: 'Người duyệt yêu cầu hủy 1', // không trả cũng được a tự dịch hoặc e tự dịch cho a
    FieldName: 'UserApproved1',
    UserInfoName: 'Hoàng Thùy Trang', // tên
    ImagePath: null, // hình ảnh
    Comment: 'Đăng ký sai quy định công ty', //note hay gì đó của user đó
    PositionName: 'Quản lý trực tiếp', //Position
    IsShow: true, //e hk cần quan tâm a dựa vào levelApprove để check chỗ này, e trả cho a thì càng tốt
    Content: 'đã yêu cầu thay đổi và gửi lý do', // nội dung của ng duyệt đã từ chối hay đã duyệt.....
    IsShownComment: false, // có hiển thị coment hay không
    Status: 'E_APPROVED', // trạng thái enum
    StatusView: 'đã duyệt', // trạng thái viêw
    StatusProcess: 'successCancelRequest', // trạng thái duyệt yêu cầu hủy
    DateUpdated: '2023/12/29 00:00:00', // ngày uppdate
    keyTranslate: '',
    Order: 0,
    ListDetails: [
      {
        ID: vnrUtilities.newGuid(), //ID e trả cũng đc không cũng đc
        Label: 'Yêu cầu thay đổi', // không trả cũng được a tự dịch hoặc e tự dịch cho a
        FieldName: 'UserApproved1',
        UserInfoName: 'Hoàng Thùy Trang', // tên
        ImagePath: null, // hình ảnh
        Comment: 'Đăng ký sai quy định công ty', //note hay gì đó của user đó
        PositionName: 'Quản lý trực tiếp', //Position
        IsShow: true, //e hk cần quan tâm a dựa vào levelApprove để check chỗ này, e trả cho a thì càng tốt
        Content: 'đã yêu cầu thay đổi và gửi lý do', // nội dung của ng duyệt đã từ chối hay đã duyệt.....
        IsShownComment: false, // có hiển thị coment hay không
        Status: 'E_APPROVED', // trạng thái enum
        StatusView: 'đã duyệt', // trạng thái viêw
        StatusProcess: 'submitChange', // trạng thái yêu cầu thay đổi
        DateUpdated: '2023/12/29 00:00:00', // ngày uppdate
        keyTranslate: '',
        Order: 0,
      },
    ],
  },
  {
    ID: vnrUtilities.newGuid(), //ID e trả cũng đc không cũng đc
    Label: 'Người duyệt 1', // không trả cũng được a tự dịch hoặc e tự dịch cho a
    FieldName: 'UserApproved1',
    UserInfoName: 'Hoàng Thùy Trang', // tên
    ImagePath: null, // hình ảnh
    Comment: 'Đăng ký sai quy định công ty', //note hay gì đó của user đó
    PositionName: 'Quản lý trực tiếp', //Position
    IsShow: true, //e hk cần quan tâm a dựa vào levelApprove để check chỗ này, e trả cho a thì càng tốt
    Content: 'đã yêu cầu thay đổi và gửi lý do', // nội dung của ng duyệt đã từ chối hay đã duyệt.....
    IsShownComment: false, // có hiển thị coment hay không
    Status: 'E_APPROVED', // trạng thái enum
    StatusView: 'đã duyệt', // trạng thái viêw
    StatusProcess: 'process', // trạng thái mà nó đang đứng
    DateUpdated: '2023/12/29 00:00:00', // ngày uppdate
    keyTranslate: '',
    Order: 0,
  },
  {
    ID: vnrUtilities.newGuid(),
    Label: 'Người duyệt 2',
    UserInfoName: 'Vũ Quốc Thiên Đăng',
    FieldName: 'UserApproved1',
    ImagePath: null,
    Comment: 'Đăng ký sai quy định công ty',
    PositionName: 'Phó nhóm thiết kế',
    IsShow: true,
    Content: null,
    IsShownComment: false,
    Status: 'E_APPROVED', // trạng thái enum
    StatusView: 'đã duyệt', // trạng thái viêw
    StatusProcess: 'success', // nếu trạng thái đã duyệt
    DateUpdated: '2023/12/29 00:00:00',
  },
  {
    ID: vnrUtilities.newGuid(),
    Label: 'Người duyệt 3',
    UserInfoName: 'Nguyễn Lê Xuân Phước',
    ImagePath: null,
    FieldName: 'UserApproved1',
    Comment:
      'Ngày nghỉ việc em yêu cầu hơi gấp nên anh chưa sắp xếp được nhân sự để thay thế nên cảm phiền em dời ngày nghi việc.Cảm ơn em!!! Ngày nghỉ việc em yêu cầu hơi gấp nên anh chưa sắp xếp được nhân sự để thay thế nên cảm phiền em dời ngày nghi việc.Cảm ơn em!!! Ngày nghỉ việc em yêu cầu hơi gấp nên anh chưa sắp xếp được nhân sự để thay thế nên cảm phiền em dời ngày nghi việc.Cảm ơn em!!!',
    IsShow: true,
    Content: 'đã yêu cầu thay đổi và gửi lý do',
    IsShownComment: true,
    StatusProcess: 'error', // nếu trạng thái từ chối
    DateUpdated: '2023/12/29 00:00:00',
  },
  {
    ID: vnrUtilities.newGuid(),
    Label: 'Người duyệt 4',
    UserInfoName: 'Hoàng Thùy Lương',
    ImagePath: null,
    FieldName: 'UserApproved1',
    Comment: 'Đăng ký sai quy định công ty',
    IsShow: true,
    Content: 'đã yêu cầu thay đổi và gửi lý do',
    StatusProcess: 'warning', // nếu trạng thái cầu thay đổi
    IsShownComment: true,
    DateUpdated: '2023/12/29 00:00:00',
  },
  {
    ID: vnrUtilities.newGuid(),
    Label: 'Người yêu cầu thay đổi',
    UserInfoName: 'Nguyễn Hưng',
    ImagePath: null,
    FieldName: 'UserApproved1',
    Comment: 'Đăng ký sai quy định công ty',
    IsShow: true,
    Content: 'đã yêu cầu thay đổi và gửi lý do',
    IsShownComment: true,
    DateUpdated: '2023/12/29 00:00:00',
    StatusProcess: null, //thì nó chưa tới người này
  },
];
