import { CommonModule } from '@angular/common';
import { Component, EventEmitter, inject, OnInit, Output } from '@angular/core';
import {
  FormsModule,
  ReactiveFormsModule,
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { VnrButtonComponent } from '@hrm-frontend-workspace/ui';
import {
  VnrMultiSelectBuilder,
  VnrMultiSelectComponent,
  VnrSelectFactory,
} from '@hrm-frontend-workspace/vnr-module';
import { TranslateModule } from '@ngx-translate/core';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzModalModule, NzModalRef } from 'ng-zorro-antd/modal';
import { NzSpaceModule } from 'ng-zorro-antd/space';
import { ADD_COMPETENCY_EVALUATION_DATA } from '../../data/add-competency-evaluation.data';

@Component({
  selector: 'app-add-competency-evaluation',
  templateUrl: './add-competency-evaluation.component.html',
  styleUrls: ['./add-competency-evaluation.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NzFormModule,
    NzGridModule,
    VnrMultiSelectComponent,
    VnrButtonComponent,
    NzSpaceModule,
    TranslateModule,
    NzModalModule,
  ],
})
export class AddCompetencyEvaluationComponent implements OnInit {
  private _fb: UntypedFormBuilder = inject(UntypedFormBuilder);
  private modal: NzModalRef = inject(NzModalRef);
  @Output() competencyValue: EventEmitter<any> = new EventEmitter();
  builderCompetencyName: VnrMultiSelectBuilder;
  competencyFormGroup: UntypedFormGroup = this._fb.group({
    CompetencyName: [''],
    CompetencyID: ['', [Validators.required]],
  });
  private _selectFactory: VnrSelectFactory = VnrSelectFactory.init();
  ngOnInit(): void {
    this.initBuilders();
  }
  private initBuilders(): void {
    this.builderCompetencyName = this._selectFactory.builderMultiSelect({
      label: 'Năng lực',
      placeholder: 'Vui lòng chọn',
      textField: 'CriteriaName',
      valueField: 'ID',
      required: true,
      dataSource: ADD_COMPETENCY_EVALUATION_DATA,
    });
  }
  onSelectCompetencyName($event: any) {
    console.log($event);
  }

  onSubmit(typeAction = 'confirm') {
    if (typeAction === 'close') {
      this.modal.destroy();
    }
    if (typeAction === 'confirm') {
      this.modal.destroy({ status: false, data: this.competencyFormGroup.value });
    }
  }
}
