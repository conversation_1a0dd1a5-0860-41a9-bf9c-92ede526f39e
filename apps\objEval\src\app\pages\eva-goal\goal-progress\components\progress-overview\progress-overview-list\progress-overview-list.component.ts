import {
  ChangeDetectionStrategy,
  Component,
  OnInit,
  ViewChild,
  AfterViewInit,
} from '@angular/core';
import {
  VnrToolbarNewBuilder,
  VnrToolbarNewComponent,
  VnrTreelistNewBuilder,
  VnrTreelistNewComponent,
  VnrTreelistNewModule,
} from '@hrm-frontend-workspace/vnr-module';
import {
  VnrLettersAvatarComponent,
  VnrTagComponent,
  VnrButtonModule,
} from '@hrm-frontend-workspace/ui';
import { TranslateModule } from '@ngx-translate/core';
import {
  ProgressOverviewTabEnum,
  statusColorMap,
  statusTextMap,
} from '../../../data/progress.enum';
import { progressOverviewListDefineColumn } from '../../../data/progress-overview-list-define-column.data';
import { progressOverviewListData } from '../../../data/progress-overview-list.data';
import { progressOverviewListDefineFilter } from '../../../data/progress-overview-list-define-filter.data';
import { CommonModule } from '@angular/common';
import { NzProgressModule } from 'ng-zorro-antd/progress';
import { TargetFormatPipe } from '../../../../shared/pipe/target-format.pipe';
import { NzDrawerModule, NzDrawerService, NzDrawerRef } from 'ng-zorro-antd/drawer';
import { NzSegmentedModule } from 'ng-zorro-antd/segmented';
import { ProgressOverviewFormComponent } from './components/progress-overview-form/progress-overview-form.component';
import { ProgressOverviewTabData } from '../../../data/progress.enum';
import { ObjEvalTabFilterComponent } from '../../../../../../shared/components/vnr-tab-filter/vnr-tab-filter.component';
import { ProgressOverviewTreeComponent } from '../progress-overview-tree/progress-overview-tree.component';
import { FormsModule } from '@angular/forms';
import { ProgressOverviewTableComponent } from '../progress-overview-table/progress-overview-table.component';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { ProgressOverviewStatus } from '../../../data/progress.enum';

@Component({
  selector: 'app-progress-overview-list',
  templateUrl: './progress-overview-list.component.html',
  styleUrls: ['./progress-overview-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [
    TranslateModule,
    VnrTreelistNewModule,
    VnrToolbarNewComponent,
    VnrTagComponent,
    CommonModule,
    VnrLettersAvatarComponent,
    NzProgressModule,
    TargetFormatPipe,
    NzDrawerModule,
    ObjEvalTabFilterComponent,
    VnrButtonModule,
    NzSegmentedModule,
    NzIconModule,
    ProgressOverviewTreeComponent,
    ProgressOverviewTableComponent,
    FormsModule,
  ],
})
export class ProgressOverviewListComponent implements OnInit, AfterViewInit {
  @ViewChild('vnrTreeList', { static: false }) vnrTreeList: VnrTreelistNewComponent;

  builderToolbar: VnrToolbarNewBuilder;
  builderTreeList: VnrTreelistNewBuilder;

  protected statusColorMap = statusColorMap;
  protected statusTextMap = statusTextMap;
  protected ProgressOverviewTabData = ProgressOverviewTabData;
  protected tabCount: any;
  protected showAlert = true;
  private _gridName = 'Eva_GoalProgress_Gird';
  private _isSupperAdmin = true;
  private _screenName = 'GoalProgress';
  private _storeName = 'eva_sp_get_GoalProgress';
  protected viewMode: 'list' | 'tree' | 'table' = 'list';
  protected showOnlyRootLevel = false;

  protected viewModeViewOptions = [
    {
      value: 'tree',
      icon: 'apartment',
    },
    {
      value: 'list',
      icon: 'unordered-list',
    },
    {
      value: 'table',
      icon: 'table',
    },
  ];

  private drawerRef: NzDrawerRef | null = null;
  protected gridName = 'Eva_GoalProgress_Gird';
  protected columns = progressOverviewListDefineColumn;
  protected dataLocal = progressOverviewListData;
  protected statusList: any[] = [];

  constructor(private drawerService: NzDrawerService) {}

  ngOnInit() {
    this.builderToolbarComponent();
    this.builderTreeListComponent();
    this.initializeStatusList();
    this.getCountDataTab();
    this.viewMode = 'list';
  }

  ngAfterViewInit(): void {
    // Update toolbar builder sau khi view được init
    this.updateToolbarWithTreeListReference();
  }

  private updateToolbarWithTreeListReference(): void {
    if (this.builderToolbar && this.vnrTreeList) {
      this.builderToolbar.gridRef = this.vnrTreeList;
    }
  }

  handleSwitchModeView(value: any) {
    this.viewMode = value;
    // Update tree list reference sau khi switch
    setTimeout(() => {
      this.updateToolbarWithTreeListReference();
    }, 100);
    if (value === 'list') {
      this.applyStatusFilterWithTree();
    }
  }

  onSelectDepartmentChange(tab: any) {
    console.log('tab', tab);
  }

  private builderToolbarComponent() {
    this.builderToolbar = new VnrToolbarNewBuilder({
      gridName: this._gridName,
      isSupperAdmin: this._isSupperAdmin,
      storeName: this._storeName,
      screenName: this._screenName,
      isShowConfig: true,
      options: {
        configButtonChangeColumn: { isShow: true },
        configButtonExport: { isShowBtnExcelAll: true },
        configQuickSearch: {
          isShow: true,
        },
        configFilterAdvanceQuick: {
          isShow: true,
          components: progressOverviewListDefineFilter(),
          keyConfig: 'ProgressOverviewList_FilterAdvanceSeting',
          isShowBtnAdvance: false,
        },
      },
    });
  }

  private builderTreeListComponent() {
    console.log('progressOverviewListData', progressOverviewListData);
    this.builderTreeList = new VnrTreelistNewBuilder({
      options: {
        configHeightGrid: {
          isAllowCalcRowHeight: true,
        },
        displayField: 'GoalName',
        configSelectable: {
          columnKey: 'ID',
          groupKey: 'ParentID',
        },
        configCommandColumn: {
          isEnabledMenuAction: false,
        },
        configShowHide: {
          isShowViewDetail: false,
          isPageExpand: false,
          isShowDelete: false,
          isShowEdit: false,
        },
      },
    });
  }

  filterByParentId(data: any[], parentId: string | null): any[] {
    return data.filter((item) => item.ParentID === parentId);
  }

  filterByStatus(status: any) {
    return progressOverviewListData.filter((item) => {
      if (item.ParentID === null) {
        return item.Status === status;
      }
      return false;
    });
  }

  // Phương thức filter thông minh để lấy cả cấu trúc tree
  private filterByStatusWithTree(statuses: ProgressOverviewStatus[]): any[] {
    if (statuses.length === 0) {
      return progressOverviewListData; // Trả về tất cả nếu không có filter
    }

    const selectedItems = new Set<string>();
    const parentItems = new Set<string>();
    const childItems = new Set<string>();

    // Bước 1: Tìm tất cả mục có status được chọn
    progressOverviewListData.forEach((item) => {
      if (statuses.includes(item.Status)) {
        selectedItems.add(item.ID);
      }
    });

    // Bước 2: Tìm tất cả mục cha của những mục được chọn
    selectedItems.forEach((selectedId) => {
      let currentId = selectedId;
      while (true) {
        const parent = progressOverviewListData.find((item) => item.ID === currentId);
        if (parent && parent.ParentID) {
          parentItems.add(parent.ParentID);
          currentId = parent.ParentID;
        } else {
          break;
        }
      }
    });

    // Bước 3: Tìm tất cả mục con của những mục được chọn
    const findChildren = (parentId: string) => {
      progressOverviewListData.forEach((item) => {
        if (item.ParentID === parentId) {
          childItems.add(item.ID);
          findChildren(item.ID); // Đệ quy tìm con của con
        }
      });
    };

    selectedItems.forEach((selectedId) => {
      findChildren(selectedId);
    });

    // Bước 4: Kết hợp tất cả và trả về
    const allRelatedIds = new Set([...selectedItems, ...parentItems, ...childItems]);
    return progressOverviewListData.filter((item) => allRelatedIds.has(item.ID));
  }

  // Helper method để map status value sang enum
  private getStatusEnum(statusValue: string): ProgressOverviewStatus {
    const statusMap: Record<string, ProgressOverviewStatus> = {
      doing: ProgressOverviewStatus.E_IN_PROGRESS,
      notstart: ProgressOverviewStatus.E_NOT_START,
      done: ProgressOverviewStatus.E_DONE,
      notdone: ProgressOverviewStatus.E_NOT_DONE,
      cancel: ProgressOverviewStatus.E_CANCELED,
      pause: ProgressOverviewStatus.E_PAUSED,
    };
    return statusMap[statusValue];
  }

  private getCountDataTab() {
    // Filter dữ liệu để chỉ lấy các mục có ParentID = null
    const rootLevelData = this.filterByParentId(progressOverviewListData, null);

    this.tabCount = [
      {
        id: ProgressOverviewTabEnum.E_ALL_REPORT,
        count: rootLevelData.length,
      },
      {
        id: ProgressOverviewTabEnum.E_MY_REPORT,
        count: 5,
      },
      {
        id: ProgressOverviewTabEnum.E_DEPARTMENT,
        count: 3,
      },
    ];
  }

  updateGoal(dataItem: any) {
    if (this.drawerRef) {
      this.drawerRef.close();
    }
    this.drawerRef = this.drawerService.create<
      ProgressOverviewFormComponent,
      { dataItem: any },
      string
    >({
      nzTitle: dataItem.GoalName,
      nzWidth: '50vw',
      nzContent: ProgressOverviewFormComponent,
      nzContentParams: {
        dataItem: dataItem,
      },
    });
    this.drawerRef.afterClose.subscribe(() => {
      this.drawerRef = null;
    });
  }

  toggleStatus(status: any) {
    console.log('toggleStatus called with:', status);

    // Nếu đang check filter "Tất cả" và user click vào filter khác
    if (status.value === 'all' && !status.checked) {
      // Uncheck tất cả filter khác
      this.statusList.forEach((s) => {
        if (s.value !== 'all') {
          s.checked = false;
        }
      });
    } else if (status.value !== 'all' && !status.checked) {
      // Nếu user check filter khác, uncheck filter "Tất cả"
      const allFilter = this.statusList.find((s) => s.value === 'all');
      if (allFilter) {
        allFilter.checked = false;
      }
    }

    status.checked = !status.checked;
    console.log('Status checked:', status.checked);
    this.applyStatusFilterWithTree();
  }

  toggleShowOnlyRootLevel() {
    this.showOnlyRootLevel = !this.showOnlyRootLevel;
    this.applyStatusFilterWithTree();
  }

  public refreshTreeList(): void {
    this.vnrTreeList.vnrReloadGrid();
  }

  // Phương thức filter mới để hiển thị cả cấu trúc tree
  private applyStatusFilterWithTree() {
    console.log('applyStatusFilterWithTree called');
    if (this.viewMode !== 'list') return;

    const checkedStatus = this.statusList.filter((s) => s.checked).map((s) => s.value);
    console.log('Checked statuses:', checkedStatus);

    // Nếu không có filter nào được chọn, mặc định set filter "Tất cả"
    if (checkedStatus.length === 0) {
      console.log('No filter selected, defaulting to "all" filter');
      // Tìm và set filter "Tất cả"
      const allFilter = this.statusList.find((s) => s.value === 'all');
      if (allFilter) {
        allFilter.checked = true;
        console.log('Set "all" filter to checked');
      }
      this.dataLocal = progressOverviewListData;
      return;
    }

    // Xử lý trường hợp "all"
    if (checkedStatus.includes('all')) {
      console.log('All filter selected, showing all data');
      this.dataLocal = progressOverviewListData;
      return;
    }

    // Filter theo status với cấu trúc tree
    const statusMap: Record<string, ProgressOverviewStatus> = {
      doing: ProgressOverviewStatus.E_IN_PROGRESS,
      notstart: ProgressOverviewStatus.E_NOT_START,
      done: ProgressOverviewStatus.E_DONE,
      notdone: ProgressOverviewStatus.E_NOT_DONE,
      cancel: ProgressOverviewStatus.E_CANCELED,
      pause: ProgressOverviewStatus.E_PAUSED,
    };

    const filterStatus = checkedStatus.map((v) => statusMap[v]).filter(Boolean);
    console.log('Filter statuses:', filterStatus);
    const filtered = this.filterByStatusWithTree(filterStatus);
    console.log('Filtered data length:', filtered.length);

    this.dataLocal = filtered;
  }

  // Thêm method để đóng alert
  protected closeAlert(): void {
    this.showAlert = false;
  }

  private initializeStatusList() {
    this.statusList = [
      {
        label: 'Tất cả',
        value: 'all',
        count: this.filterByParentId(progressOverviewListData, null).length,
        color: 'default',
        checked: true,
      },
      {
        label: 'Chưa bắt đầu',
        value: 'notstart',
        count: this.filterByStatus(ProgressOverviewStatus.E_NOT_START).length,
        color: 'default',
        checked: false,
      },
      {
        label: 'Đang thực hiện',
        value: 'doing',
        count: this.filterByStatus(ProgressOverviewStatus.E_IN_PROGRESS).length,
        color: 'blue',
        checked: false,
      },
      {
        label: 'Hoàn thành',
        value: 'done',
        count: this.filterByStatus(ProgressOverviewStatus.E_DONE).length,
        color: 'green',
        checked: false,
      },
      {
        label: 'Chưa hoàn thành',
        value: 'notdone',
        count: this.filterByStatus(ProgressOverviewStatus.E_NOT_DONE).length,
        color: 'red',
        checked: false,
      },
      {
        label: 'Đã huỷ',
        value: 'cancel',
        count: this.filterByStatus(ProgressOverviewStatus.E_CANCELED).length,
        color: 'orange',
        checked: false,
      },
      {
        label: 'Tạm dừng',
        value: 'pause',
        count: this.filterByStatus(ProgressOverviewStatus.E_PAUSED).length,
        color: 'default',
        checked: false,
      },
    ];
  }

  private updateStatusCounts() {
    this.statusList.forEach((status) => {
      if (status.value === 'all') {
        status.count = this.filterByParentId(progressOverviewListData, null).length;
      } else {
        status.count = this.filterByStatus(this.getStatusEnum(status.value)).length;
      }
    });
  }
}
