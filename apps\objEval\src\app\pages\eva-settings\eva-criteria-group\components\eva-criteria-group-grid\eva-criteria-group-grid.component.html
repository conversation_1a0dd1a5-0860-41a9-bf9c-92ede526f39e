<vnr-grid-new
  class="grid-new"
  #vnrGrid
  [builder]="builderGrid"
  [gridName]="gridName"
  [dataLocal]="dataLocal"
  [columns]="columns"
  (getSelectedID)="getSelectedID($event)"
  (vnrEdit)="onGridEdit($event)"
  (vnrDelete)="onGridDelete($event)"
>
</vnr-grid-new>
<ng-template #tplContentDelete let-params>
  <div class="template-delete">
    <div class="template-delete__header">
      <img src="assets/icon/vnr-icon/icon-confirm-delete.png" alt="delete" />
    </div>
    <div class="template-delete__content">
      <div class="template-delete__content-title">
        <span>{{ 'common.modal.confirmDeleteData' | translate }}</span>
      </div>
      <div class="template-delete__content-description">
        {{ 'common.modal.deletedDataCannotBeRecovered' | translate }}
      </div>
    </div>
  </div>
</ng-template>
<ng-template #tplFooterDelete let-ref="modalRef">
  <div class="template-view-detail__action">
    <vnr-button-new [builder]="builderbtnDoNo" (vnrClick)="ref.destroy()"></vnr-button-new>
    <vnr-button-new
      [builder]="builderbtnDoYes"
      (vnrClick)="onConfirmDelete($event, ref)"
    ></vnr-button-new>
  </div>
</ng-template>
