import { AfterViewInit, Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  VnrToolbarNewBuilder,
  VnrToolbarNewComponent,
  VnrGridNewBuilder,
  VnrGridNewComponent,
} from '@hrm-frontend-workspace/vnr-module';
import {
  VnrButtonComponent,
  VnrLettersAvatarComponent,
  VnrTagComponent,
} from '@hrm-frontend-workspace/ui';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { memberFilter, memberColumns, memberDataSource } from '../../data/member.data';
import { FormComponent } from './components/form/form.component';
import { NzDrawerService, NzDrawerRef } from 'ng-zorro-antd/drawer';

@Component({
  selector: 'app-member',
  imports: [
    CommonModule,
    VnrButtonComponent,
    VnrLettersAvatarComponent,
    VnrTagComponent,
    VnrToolbarNewComponent,
    VnrGridNewComponent,
    NzGridModule,
  ],
  templateUrl: './member.component.html',
  styleUrl: './member.component.scss',
})
export class MemberComponent implements OnInit, AfterViewInit {
  @ViewChild('vnrGrid', { static: false }) vnrGrid: VnrGridNewComponent;

  builderToolbar: VnrToolbarNewBuilder;
  builderGrid: VnrGridNewBuilder;
  private drawerRef: NzDrawerRef | null = null;

  protected gridName = 'GoalResult_Grid';
  protected screenName = 'GoalResult_Screen';
  protected isSupperAdmin = false;
  protected storeName = 'eva_sp_get_GoalResult';

  protected dataLocal = memberDataSource;
  protected columns = memberColumns;
  constructor(private drawerService: NzDrawerService) {}

  ngOnInit() {
    this.builderGridComponent();
    this.builderToolbarComponent();
  }

  ngAfterViewInit(): void {
    this.updateToolbarWithGridReference();
  }

  private updateToolbarWithGridReference(): void {
    if (this.builderToolbar && this.vnrGrid) {
      this.builderToolbar.gridRef = this.vnrGrid;
    }
  }

  private builderToolbarComponent() {
    this.builderToolbar = new VnrToolbarNewBuilder({
      gridName: this.gridName,
      isSupperAdmin: this.isSupperAdmin,
      storeName: this.storeName,
      screenName: this.screenName,
      isShowConfig: true,
      options: {
        configButtonChangeColumn: { isShow: true },
        configButtonExport: { isShowBtnExcelAll: true },
        configQuickSearch: {
          isShow: true,
        },
        configFilterAdvanceQuick: {
          isShow: true,
          components: memberFilter(),
          keyConfig: 'Member_FilterAdvanceSeting',
          isShowBtnAdvance: false,
        },
      },
    });
  }

  private builderGridComponent() {
    this.builderGrid = new VnrGridNewBuilder({
      options: {
        configHeightGrid: {
          isAllowCalcRowHeight: true,
        },
        configSelectable: {
          columnKey: 'Id',
        },
        configShowHide: {
          isPageExpand: false,
          isShowDelete: false,
          isShowEdit: false,
        },
        configCommandColumn: {
          isEnabledMenuAction: true,
        },
        configGroupable: {
          isEnabled: false,
        },
      },
    });
  }

  handleDoubleClick(event: any) {
    if (this.drawerRef) {
      this.drawerRef.close();
    }
    this.drawerRef = this.drawerService.create<
      FormComponent,
      { dataItem: any },
      string | TemplateRef<any>
    >({
      nzTitle: 'Kết quả thực hiện mục tiêu',
      nzContent: FormComponent,
      nzClosable: true,
      nzMaskClosable: false,
      nzMask: true,
      nzWidth: '1000px',
      nzContentParams: {
        dataItem: event.record,
      },
      nzFooter: null,
      nzWrapClassName: 'goal-result-member-form-drawer',
    });
    this.drawerRef.afterClose.subscribe(() => {
      this.drawerRef = null;
    });
  }
}
