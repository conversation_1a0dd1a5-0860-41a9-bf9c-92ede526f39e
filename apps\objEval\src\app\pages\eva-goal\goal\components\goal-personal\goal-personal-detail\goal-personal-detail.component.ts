import { Component, Input, OnInit } from '@angular/core';
import { ObjSharedModule } from '../../../../../../shared/obj-shared.module';
import { VnrGridNewBuilder } from '@hrm-frontend-workspace/vnr-module';
import { gridPersonalDetailDefineDataSource } from '../../../../goal/data/datasource.data';
import { gridPersonalDetailDefineColumns } from '../../../../goal/data/column.data';
import { NzModalService } from 'ng-zorro-antd/modal';
import { TranslateService } from '@ngx-translate/core';
import { GoalRegisterFormComponent } from '../../../../shared/components/goal-register-form/goal-register-form.component';
import { FormApprovalGoalComponent } from '../../../../shared/components/form-approval-goal/form-approval-goal.component';
import { FormChangeGoalComponent } from '../../../../shared/components/form-change-goal/form-change-goal.component';
import { FormRejectGoalComponent } from '../../../../shared/components/form-reject-goal/form-reject-goal.component';
import { FormAllocationGoalComponent } from '../../../../../../shared/components/form-allocation-goal/form-allocation-goal.component';
import {
  statusColorMap,
  statusTextMap,
  goalTypeColorMap,
} from '../../../../shared/enums/status.enum';
import { NzSegmentedModule } from 'ng-zorro-antd/segmented';
import { NzDrawerService } from 'ng-zorro-antd/drawer';
@Component({
  selector: 'app-goal-personal-detail',
  standalone: true,
  imports: [ObjSharedModule, NzSegmentedModule],
  templateUrl: './goal-personal-detail.component.html',
  styleUrl: './goal-personal-detail.component.scss',
})
export class GoalPersonalDetailComponent implements OnInit {
  @Input() dataItem: any;

  protected statusColorMap = statusColorMap;
  protected statusTextMap = statusTextMap;
  protected goalTypeColorMap = goalTypeColorMap;
  protected builderGrid: VnrGridNewBuilder;
  protected viewModeOptions = [
    { value: 'table', icon: 'table' },
    { value: 'card', icon: 'appstore' },
  ];
  protected selectedViewMode = 'table';
  protected goalData = gridPersonalDetailDefineDataSource;

  protected statistics = {
    totalGoals: 5,
    waitingConfirm: 2,
    confirmed: 2,
    rejected: 1,
  };

  protected gridName = 'ObjEval_ListGoalChild';
  protected isSupperAdmin = true;
  protected dataLocal = gridPersonalDetailDefineDataSource;
  protected columns = gridPersonalDetailDefineColumns;

  ngOnInit(): void {
    if (this.dataItem) {
      this.initGrid();
    }
  }

  constructor(
    private modalService: NzModalService,
    private translate: TranslateService,
    private drawerService: NzDrawerService,
  ) {}

  private initGrid() {
    this.builderGrid = new VnrGridNewBuilder({
      options: {
        configSelectable: {
          columnKey: 'Id',
        },
        configPageable: {
          isShowPageSize: false,
          isShowInfo: false,
        },
        configIndexColumn: {
          width: 10,
        },
        configCommandColumn: {
          isEnabledMenuAction: true,
          width: 200,
        },
        configShowHide: {
          isShowEdit: false,
          isShowDelete: false,
          isShowColumnCheck: false,
        },
      },
    });
  }

  protected onAddGoal(): void {
    this.drawerService.create({
      nzTitle: 'Thêm mục tiêu cá nhân',
      nzContent: GoalRegisterFormComponent,
      nzWidth: 700,
      nzClosable: true,
    });
  }

  protected onAllocation(): void {
    this.modalService.create({
      nzTitle: this.translate.instant('objEval.GoalPeriod.Allocation'),
      nzContent: FormAllocationGoalComponent,
      nzData: this.dataItem,
      nzWidth: 1200,
      nzFooter: null,
    });
  }

  protected onApprove(dataItem: any): void {
    this.modalService.create({
      nzTitle: this.translate.instant('objEval.GoalPeriod.ApproveGoal'),
      nzContent: FormApprovalGoalComponent,
      nzData: {
        dataItem: dataItem,
      },
      nzWidth: 700,
      nzFooter: null,
    });
  }

  protected onEdit(dataItem: any): void {
    this.modalService.create({
      nzTitle: this.translate.instant('objEval.GoalPeriod.RequestAdjust'),
      nzContent: FormChangeGoalComponent,
      nzData: {
        dataItem: dataItem,
      },
      nzWidth: 700,
      nzFooter: null,
    });
  }

  protected onReject(dataItem: any): void {
    this.modalService.create({
      nzTitle: this.translate.instant('objEval.GoalPeriod.Reject'),
      nzContent: FormRejectGoalComponent,
      nzData: {
        dataItem: dataItem,
      },
      nzWidth: 700,
      nzFooter: null,
    });
  }

  protected onViewModeChange(value): void {
    this.selectedViewMode = value;
  }
}
