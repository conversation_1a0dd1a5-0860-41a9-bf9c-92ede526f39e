import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  Inject,
  OnInit,
  TemplateRef,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import {
  VnrDateRangePickerBuilder,
  VnrGridNewComponent,
  VnrTextBoxBuilder,
  VnrGridNewBuilder,
  VnrToolbarNewBuilder,
  VnrToolbarNewComponent,
  VnrSelectFactory,
  VnrGridNewEditInlineComponent,
  VnrGridNewEditInlineBuilder,
} from '@hrm-frontend-workspace/vnr-module';
import { gridDefineDataSource } from '../../data/grid-new-define-data-source.data';
import { gridDefineColumns } from '../../data/grid-new-define-column.data';
import { TranslateService } from '@ngx-translate/core';
import {
  CommonModule,
  Ng<PERSON>lass,
  <PERSON>I<PERSON>,
  NgS<PERSON>,
  NgSwitchCase,
  NgSwitchDefault,
} from '@angular/common';
import { gridDefineDataFilterAdvance } from '../../data/grid-new-define-data-filter-advance';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { IntlModule } from '@progress/kendo-angular-intl';

@Component({
  selector: 'grid-new-edit-inline-list',
  templateUrl: './grid-new-edit-inline-list.component.html',
  styleUrls: ['./grid-new-edit-inline-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [
    VnrGridNewEditInlineComponent,
    NgSwitch,
    NgSwitchCase,
    NgIf,
    NgClass,
    NgSwitchDefault,
    NzDividerModule,
    CommonModule,
    IntlModule,
  ],
})
export class GridNewEditInlineListComponent implements OnInit, AfterViewInit {
  @ViewChild('buttonTemplate', { static: true })
  public buttonTemplate: TemplateRef<any>;
  @ViewChild('gridEditInLine', { static: true }) gridControl: VnrGridNewEditInlineComponent;
  @ViewChild('btnControl', { static: true }) btnControl: TemplateRef<any>;
  @ViewChild('jobTemplate', { static: true }) jobTemplate: TemplateRef<any>;
  @ViewChild('FileAttachment', { static: true }) FileAttachment: TemplateRef<any>;
  @ViewChild('tplCustomButtonAction', { static: true })
  tplCustomButtonAction: TemplateRef<any>;
  @ViewChild('tplCustomTemplateByColumn', { static: true })
  tplCustomTemplateByColumn: TemplateRef<any>;
  @ViewChild('tplHeader', { static: true }) public tplHeader: TemplateRef<any>;
  protected isSupperAdmin: boolean = false;
  protected screenWidth: number;
  protected screenHeight: number;
  protected compRef: ViewContainerRef;
  protected gridHeight: number;
  protected builderGrid: VnrGridNewBuilder;
  protected builderToolbar: VnrToolbarNewBuilder;
  protected builder_JobVacancyName: VnrTextBoxBuilder;
  protected builder_Code: VnrTextBoxBuilder;
  protected builder_DateRequest: VnrDateRangePickerBuilder;
  protected searchForm: UntypedFormGroup;
  protected gridName: string = 'PortalNew_GridNewExample';
  protected dataLocal: any[] = gridDefineDataSource;
  protected columns: any[] = gridDefineColumns;

  //#endregion
  constructor(private fb: UntypedFormBuilder) {}
  ngAfterViewInit() {}
  ngOnInit() {
    setTimeout(() => {
      this.updateGridHeight();
    });
    this.builderForm();
    this.builderGridComponent();
    this.searchForm = this.fb.group({
      JobVacancyName: [null],
      Code: [null],
      DateFrom: [null],
      DateRequest: [null],
      JobVacancyName1: [null],
      Code1: [null],
      DateFrom1: [null],
      DateRequest1: [null],
    });
  }
  private builderForm() {
    this.builder_Code = new VnrTextBoxBuilder({
      label: 'vnrcontrol.demo.code',
      options: {
        hasFeedBack: false,
      },
    });

    this.builder_JobVacancyName = new VnrTextBoxBuilder({
      label: 'vnrcontrol.demo.jobVacancyName',
      options: {
        hasFeedBack: false,
      },
    });

    this.builder_DateRequest = new VnrDateRangePickerBuilder({
      label: 'vnrcontrol.demo.dateRequest',
      options: {
        hasFeedBack: false,
      },
    });
  }
  private builderGridComponent() {
    this.builderGrid = new VnrGridNewEditInlineBuilder({
      options: {
        configHeightGrid: {
          isAllowCalcRowHeight: true,
        },
        configShowHide: {
          isPageExpand: true,
        },
        configCommandColumn: {
          isEnabledMenuAction: true,
        },
        configGroupable: {
          isEnabled: true,
        },
        configEdit: {
          isShowColumnAction: false,
          isEnableEditRowClick: true,
          configValidate: {
            //Key__f382146a_27c0_401c_baf2_977d757dfa65: { required: true },
          },
        },
      },
    });
  }
  getSelectedID($event) {
    console.log($event, 'getSelectedID');
  }
  getDataItem($event) {
    console.log($event, 'getDataItem');
  }
  onOpenDetail($event) {
    console.log($event, 'onOpenDetail');
  }
  onVnRViewModeGrid($event) {
    console.log($event, 'onVnRViewModeGrid');
  }
  onGridEdit($event) {
    console.log($event, 'onGridEdit');
  }
  onGridDelete($event) {
    console.log($event, 'onGridDelete');
  }
  onGridViewDetail($event) {
    console.log($event, 'onGridViewDetail');
  }
  onGridCellClick($event) {
    console.log($event, 'onGridCellClick');
  }
  updateGridHeight(): void {
    this.screenWidth = window.innerWidth;
    this.screenHeight = window.innerHeight;
    const grid = (this.compRef?.element?.nativeElement as HTMLElement)?.querySelector(
      'vnr-grid-new',
    );
    if (!grid) {
      return;
    }
    const height = this.screenHeight > 912 ? 110 : 120;
    this.gridHeight = window.outerHeight - grid.getBoundingClientRect().top - height;
  }
  selected: string = 'rightTop';
  toggleChangeColumn(isNewVersion: boolean) {
    this.gridControl?.setOpenChangeColumn(true);
    this.gridControl?.setChangeColumnVersion(isNewVersion);
  }
  reload() {
    this.isSupperAdmin = !this.isSupperAdmin;
    setTimeout(() => {
      this.gridControl?.vnrReadGrid();
    });
  }
  reload1() {
    this.isSupperAdmin = !this.isSupperAdmin;
    setTimeout(() => {
      this.gridControl?.vnrReloadGrid();
    });
  }
}
