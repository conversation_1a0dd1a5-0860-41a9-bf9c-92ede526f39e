import { CommonModule } from '@angular/common';
import { Component, EventEmitter, inject, OnInit, Output } from '@angular/core';
import {
  FormsModule,
  ReactiveFormsModule,
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { VnrButtonComponent } from '@hrm-frontend-workspace/ui';
import {
  VnrMultiSelectBuilder,
  VnrMultiSelectComponent,
  VnrSelectFactory,
} from '@hrm-frontend-workspace/vnr-module';
import { TranslateModule } from '@ngx-translate/core';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzModalModule, NzModalRef } from 'ng-zorro-antd/modal';
import { NzSpaceModule } from 'ng-zorro-antd/space';
import { ADD_CRITERIA_EVALUATION_DATA } from '../../data/add-criteria-evaluation.data';

@Component({
  selector: 'app-add-criteria-evaluation',
  templateUrl: './add-criteria-evaluation.component.html',
  styleUrls: ['./add-criteria-evaluation.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NzFormModule,
    NzGridModule,
    VnrMultiSelectComponent,
    VnrButtonComponent,
    NzSpaceModule,
    TranslateModule,
    NzModalModule,
  ],
})
export class AddCriteriaEvaluationComponent implements OnInit {
  private _fb: UntypedFormBuilder = inject(UntypedFormBuilder);
  private modal: NzModalRef = inject(NzModalRef);
  @Output() criteriaValue: EventEmitter<any> = new EventEmitter();
  builderCriteriaName: VnrMultiSelectBuilder;
  criteriaFormGroup: UntypedFormGroup = this._fb.group({
    CriteriaName: [''],
    CriteriaID: ['', [Validators.required]],
  });
  private _selectFactory: VnrSelectFactory = VnrSelectFactory.init();
  ngOnInit(): void {
    this.initBuilders();
  }
  private initBuilders(): void {
    this.builderCriteriaName = this._selectFactory.builderMultiSelect({
      label: 'Chất lượng công việc',
      placeholder: 'Vui lòng chọn',
      textField: 'CriteriaName',
      valueField: 'ID',
      dataSource: ADD_CRITERIA_EVALUATION_DATA,
    });
  }
  onSelectCriteriaName($event: any) {
    console.log($event);
  }
  onSubmit(typeAction = 'confirm', isCopy = false) {
    if (typeAction === 'close') {
      this.modal.destroy();
    }
    if (typeAction === 'confirm') {
      this.modal.destroy({ status: false, data: this.criteriaFormGroup.value });
    }
  }
}
