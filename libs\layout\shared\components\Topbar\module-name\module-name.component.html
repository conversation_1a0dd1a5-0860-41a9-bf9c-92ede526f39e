<div
  class="topbar-module-name d-flex mr-auto align-items-center h-100p"
  [style.cursor]="'pointer'"
  [routerLink]="[moduleName === 'default' ? '/' : '/' + moduleName]"
>
  <span class="d-flex mr-2">
    <ng-container *ngIf="moduleName !== 'default'; else tplLogo">
      <img
        src="assets/icon/modules/{{ moduleName ? moduleName : 'unknown' }}.svg"
        [style.max-width.px]="32"
        [style.max-height.px]="32"
        alt="icon-module"
      />
    </ng-container>

    <ng-template #tplLogo>
      <a [routerLink]="[moduleName === 'default' ? '/' : '/' + moduleName]" class="d-block">
        <img src="assets/images/logo.png" class="max-width-100p max-height-60" alt="logo" />
      </a>
    </ng-template>
  </span>
  <span *ngIf="moduleName !== 'default'" class="topbar-module-name-text d-flex">
    {{ 'appInfo.modules.' + (moduleName || 'unknown') | translate }}
  </span>
</div>
