import { CommonModule } from '@angular/common';
import {
  Component,
  Inject,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonService } from '@hrm-frontend-workspace/core';
import { VnrLettersAvatarComponent, VnrTagComponent } from '@hrm-frontend-workspace/ui';
import {
  IVnrModule_Token,
  VnrButtonFactory,
  VnrButtonNewBuilder,
  VnrButtonNewComponent,
  VnrGridNewBuilder,
  VnrGridNewComponent,
  VNRMODULE_TOKEN,
  VnrToolbarNewBuilder,
  VnrToolbarNewComponent,
  vnrUtilities,
} from '@hrm-frontend-workspace/vnr-module';
import { TranslateService } from '@ngx-translate/core';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzModalService } from 'ng-zorro-antd/modal';
import { gridProposalDefineDetailColumns } from '../../data/column.data';
import { gridDefineDataFilterQuick } from '../../data/datasource-filter';
import { gridProposalDetailDataSource } from '../../data/datasource.data';
import {
  proposalGradeFormat,
  proposalGradeTextFormat,
  proposalStatusFormat,
} from '../../data/proposal.data';
import { EvaProposalDetailComponent } from '../eva-proposal-detail/eva-proposal-detail.component';

@Component({
  selector: 'eva-proposal-grid-detail',
  templateUrl: './eva-proposal-grid-detail.component.html',
  styleUrls: ['./eva-proposal-grid-detail.component.scss'],
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    CommonModule,
    VnrGridNewComponent,
    VnrToolbarNewComponent,
    VnrLettersAvatarComponent,
    VnrTagComponent,
    VnrButtonNewComponent,
    NzDropDownModule,
  ],
})
export class EvaProposalGridDetailComponent implements OnInit, OnChanges {
  @Input() KPICategoryID: string;
  @Input() CompetencyCategoryID: string;
  @ViewChild('vnrGrid', { static: true }) gridControl: VnrGridNewComponent;

  private _statusFormat = proposalStatusFormat;
  private _gradeFormat = proposalGradeFormat;
  private _gradeTextFormat = proposalGradeTextFormat;
  private _btnFactory: VnrButtonFactory = VnrButtonFactory.init();
  protected gridName = 'eva-proposal-grid-detail';
  protected dataLocal = gridProposalDetailDataSource;
  protected columns = gridProposalDefineDetailColumns;
  protected selectedItem = [];
  protected builderGrid: VnrGridNewBuilder;
  protected builderToolbar: VnrToolbarNewBuilder;
  protected builderButtonProposal: VnrButtonNewBuilder;
  protected builderButtonDropdownMore: VnrButtonNewBuilder;
  constructor(
    @Inject(VNRMODULE_TOKEN) private _vnrModule_Token: IVnrModule_Token,
    private _translate: TranslateService,
    private _modalService: NzModalService,
    private _commonService: CommonService,
    private _vc: ViewContainerRef,
  ) {}
  ngOnInit() {
    this.buildButton();
    this.initGridData();
  }
  ngOnChanges(changes: SimpleChanges): void {
    const tabFilterChange = changes['tabFilter'];
    if (tabFilterChange && !tabFilterChange.firstChange) {
      this.initGridData();
    }
  }
  private initGridData() {
    this.builderGridComponent();
    this.builderToolbarComponent();
  }
  private builderToolbarComponent() {
    this.builderToolbar = new VnrToolbarNewBuilder({
      isShowConfig: true,
      gridName: this.gridName,
      isSupperAdmin: true,
      gridRef: this.gridControl,
      permission: '',
      screenName: 'eva-proposal-grid-detail',
      storeName: '',
      options: {
        configButtonChangeColumn: {
          isShow: true,
          isConfigMenuDisplay: true,
          isChangeColumnNew: true,
          isDisabled: false,
          configButtonSetting: {
            isShow: true,
            isDisabled: true,
          },
        },
        configButtonExport: {
          isShowBtnExcelAll: true,
          isShowBtnWord: true,
          isShowBtnExcelByTemplate: true,
        },
        configButtonDelete: {
          isShow: true,
          isDisabled: false,
        },
        configQuickSearch: {
          textPlaceHolder: 'Tìm kiếm theo tên, mã...',
          searchKey: 'ProfileName',
        },
        configFilterAdvanceQuick: {
          isShow: true,
          isUseConfig: true,
          keyConfig: 'eva-proposal-grid-detail_FilterQuickSeting',
          isShowBtnAdvance: false,
          components: gridDefineDataFilterQuick,
        },
        isSetBackground: false,
        buttonMoreFlexDirection: 'inherit',
        isExpanded: false,
      },
    });
  }
  private buildButton() {
    this.builderButtonProposal = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'objEval.EvaProposalList.Proposal',
      options: {
        style: 'outline-primary',
        icon: {
          fontIcon: 'far fa-paper-plane',
        },
      },
    });
    this.builderButtonDropdownMore = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: '',
      options: {
        style: 'default',
        icon: {
          fontIcon: 'more',
        },
      },
    });
  }
  private builderGridComponent() {
    this.builderGrid = new VnrGridNewBuilder({
      options: {
        configHeightGrid: {
          isAllowCalcRowHeight: true,
        },
        configIndexColumn: {
          isShow: true,
          width: 40,
        },
        configSelectable: {
          columnKey: 'Id',
        },
        configShowHide: {
          isPageExpand: false,
          isShowDelete: false,
          isShowEdit: false,
        },
        configCommandColumn: {
          isEnabledMenuAction: true,
        },
        configGroupable: {
          isEnabled: false,
        },
      },
    });
  }

  protected getSelectedID($event: any) {
    this.selectedItem = $event;
  }
  protected getDataItem($event: any) {
    console.log($event);
  }
  protected onOpenDetail($event: any) {}
  protected onGridEdit($event: any) {}
  protected onGridDelete($event: any) {}
  protected onGridViewDetail($event: any) {}
  protected onGridCellClick($event: any) {
    this._modalService.create({
      nzTitle: '',
      nzContent: EvaProposalDetailComponent,
      nzViewContainerRef: this._vc,
      nzClosable: true,
      nzMaskClosable: true,
      nzMask: true,
      nzWidth: '80%',
      nzBodyStyle: { minheight: `512px`, padding: '16px' },
      nzFooter: null,
      nzData: {
        paramEditID: $event.dataItem?.Id,
      },
    });
  }

  public reloadGridData(): void {
    this.gridControl.vnrReadGrid();
  }
  public getSelectedIDs() {
    return this.selectedItem || [];
  }
  protected getColorStatus(value: any): string {
    return this._statusFormat[value];
  }
  protected getColorGrade(value: any): string {
    return this._gradeFormat[value];
  }
  protected getColorGradeText(value: any): string {
    return this._gradeTextFormat[value];
  }
  protected onProposal($event: any) {
    this._commonService.message({ message: 'Tính năng đang phát triển', type: 'success' });
  }
}
