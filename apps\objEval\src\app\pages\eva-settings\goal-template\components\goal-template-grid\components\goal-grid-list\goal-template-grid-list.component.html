<vnr-grid-new
  class="grid-new"
  #vnrGrid
  [builder]="builderGrid"
  [gridName]="gridName"
  [dataLocal]="dataLocal"
  [columns]="columns"
  [dataFormSearch]="dataFormSearch"
  [columnTemplates]="{
    GoalType: templateType,
    TargetScale: templateTargetScale,
  }"
  (getSelectedID)="getSelectedID($event)"
  (vnrEdit)="onGridEdit($event)"
  (vnrDelete)="onGridDelete($event)"
  (vnrCellClick)="onGridCellClick($event)"
>
</vnr-grid-new>
<ng-template #templateType let-dataItem>
  <vnr-tag
    *ngIf="dataItem['GoalType']; else templateEmpty"
    [vnrStatus]="'E_WAITING'"
    [vnrTitle]="dataItem['GoalType']"
  ></vnr-tag>
</ng-template>
<ng-template #templateTargetScale let-dataItem>
  <div class="template-target-scale" *ngIf="dataItem['TargetScale']; else templateEmpty">
    {{ dataItem['TargetScaleName'] }} - {{ dataItem['MinimumValue'] }} {{ dataItem['Unit'] }}
  </div>
</ng-template>
<ng-template #templateEmpty>-</ng-template>

<ng-template #tplContentDelete let-params>
  <div class="template-delete">
    <div class="template-delete__header">
      <img src="assets/icon/vnr-icon/icon-confirm-delete.png" alt="delete" />
    </div>
    <div class="template-delete__content">
      <div class="template-delete__content-title">
        <span>{{ 'common.modal.confirmDeleteData' | translate }}</span>
      </div>
      <div class="template-delete__content-description">
        {{ 'common.modal.deletedDataCannotBeRecovered' | translate }}
      </div>
    </div>
  </div>
</ng-template>
<ng-template #tplFooterDelete let-ref="modalRef">
  <div class="template-view-detail__action">
    <vnr-button-new [builder]="builderbtnDoNo" (vnrClick)="ref.destroy()"></vnr-button-new>
    <vnr-button-new
      [builder]="builderbtnDoYes"
      (vnrClick)="onConfirmDelete($event, ref)"
    ></vnr-button-new>
  </div>
</ng-template>
