<vnr-grid-new
  #vnrGrid
  [builder]="gridBuilder"
  [gridName]="gridName"
  [dataLocal]="dataLocal"
  [columns]="columns"
  [defaultColumnTemplate]="tplCustomTemplateByColumn"
  (vnrCellClick)="onGridCellClick($event)"
></vnr-grid-new>

<ng-template #tplCustomTemplateByColumn let-dataItem let-column="columnItem" let-field="field">
  <ng-container [ngSwitch]="column['Name']">
    <span *ngSwitchCase="'Reporter'">
      <div class="d-flex align-items-center" style="gap: 8px">
        <app-vnr-letters-avatar
          [avatarName]="dataItem['Reporter']['name']"
          [circular]="true"
          [width]="32"
          [src]="dataItem['Reporter']['avatar']"
        ></app-vnr-letters-avatar>
        <div class="d-block">
          <div>{{ dataItem['Reporter']['name'] }}</div>
          <div class="text-muted">{{ dataItem['Reporter']['code'] }}</div>
        </div>
      </div>
    </span>
    <span *ngSwitchCase="'Status'">
      <vnr-tag
        [vnrTitle]="dataItem['Status']"
        [vnrColor]="dataItem['Status'] === 'Lưu nháp' ? 'default' : 'success'"
      ></vnr-tag>
    </span>
    <span *ngSwitchCase="'Target'">
      <span><i class="fa-light fa-bullseye mr-1"></i> {{ dataItem['Target'] }} mục tiêu</span>
    </span>
    <span *ngSwitchCase="'Comment'">
      <span><i class="fa-light fa-comment-dots mr-1"></i>{{ dataItem['Comment'] }} bình luận</span>
    </span>
    <span *ngSwitchDefault>
      {{ dataItem[column['Name']] || '-' }}
    </span>
  </ng-container>
</ng-template>

<ng-template #drawerTitle>
  <span style="display: flex; flex-direction: column; align-items: flex-start; gap: 2px">
    <span style="display: flex; align-items: center; gap: 8px">
      <span style="font-weight: 500">{{ selectedDataItem?.ReportType }}</span>
      <vnr-tag
        [vnrTitle]="selectedDataItem?.Status"
        [vnrColor]="selectedDataItem?.Status === 'Lưu nháp' ? 'default' : 'success'"
      ></vnr-tag>
    </span>
    <span style="font-size: 12px; color: #616161; margin-left: 2px">
      <i class="fa-light fa-clock mr-1"></i> {{ selectedDataItem?.ReportDate }}
    </span>
  </span>
</ng-template>
