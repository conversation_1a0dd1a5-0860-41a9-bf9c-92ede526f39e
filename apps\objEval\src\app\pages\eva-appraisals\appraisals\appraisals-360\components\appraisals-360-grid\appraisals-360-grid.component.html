<div class="form-header" nz-form [formGroup]="formGroup">
  <vnr-toolbar-new [builder]="builderToolbar" (onChangeFitler)="onChangeFitler($event)">
    <vnr-button-new [builder]="builderButtonCustom">
      <vnr-treeview
        [builder]="builderOrg"
        [formGroup]="formGroup"
        formControlName="department"
        (ngModelChange)="onModelChangeOrg($event)"
      ></vnr-treeview>
    </vnr-button-new>
    <vnr-button-new [builder]="builderButtonCustom">
      <vnr-combobox
        [builder]="builderPosition"
        [formGroup]="formGroup"
        formControlName="position"
        (ngModelChange)="onModelChangePosition($event)"
      ></vnr-combobox>
    </vnr-button-new>
    <vnr-button-new
      #btnApprove
      [builder]="builderButtonApprove"
      [isShow]="isShowBtnApprove()"
      (vnrClick)="onApprove($event)"
    ></vnr-button-new>
  </vnr-toolbar-new>
</div>
<vnr-grid-new
  #vnrGrid
  [builder]="builderGrid"
  [gridName]="gridName"
  [dataLocal]="dataLocal"
  [columns]="columns"
  [defaultColumnTemplate]="tplCustomTemplateByColumn"
  (getSelectedID)="getSelectedID($event)"
  (getDataItem)="getDataItem($event)"
  (vnrDoubleClick)="onOpenDetail($event)"
  (vnrEdit)="onGridEdit($event)"
  (vnrDelete)="onGridDelete($event)"
  (vnrViewDetails)="onGridViewDetail($event)"
  (vnrCellClick)="onGridCellClick($event)"
>
</vnr-grid-new>

<ng-template #tplCustomTemplateByColumn let-dataItem let-column="columnItem" let-field="field">
  <ng-container [ngSwitch]="column['Name']">
    <span class="--has-custom-template" *ngSwitchCase="'ProfileName'">
      <span>
        <div class="appraisals-360-grid-profile">
          <app-vnr-letters-avatar
            class="appraisals-360-grid-profile__image"
            [avatarName]="dataItem['ProfileName']"
            [circular]="true"
            [width]="32"
            [src]="dataItem['Avatar']"
          ></app-vnr-letters-avatar>
          <div class="appraisals-360-grid-profile__description">
            <div class="appraisals-360-grid-profile__name">
              {{ dataItem['ProfileName'] }}
            </div>
            <div class="appraisals-kpi-grid-profile__code">
              {{ dataItem['EmpCode'] }}
            </div>
          </div>
        </div>
      </span>
    </span>
    <span class="--has-custom-template" *ngSwitchCase="'Grade'">
      <div class="appraisals-360-grid-grade">
        <div class="appraisals-360-grid-grade__name">
          <vnr-tag
            *ngIf="dataItem['Grade']; else templateEmpty"
            [vnrColor]="getColorGrade(dataItem['Grade'])"
            [vnrTitle]="dataItem['Grade']"
            [isBordered]="false"
            [vnrNoTranslate]="true"
          ></vnr-tag>
        </div>
        <div class="appraisals-360-grid-grade__text">
          <font [color]="getColorGradeText(dataItem['Grade'])">{{ dataItem['GradeText'] }}</font>
        </div>
      </div>
    </span>
    <span class="--has-custom-template" *ngSwitchCase="'Status'">
      <vnr-tag
        *ngIf="dataItem['Status']; else templateEmpty"
        [vnrColor]="getColorStatus(dataItem['Status'])"
        [vnrTitle]="dataItem['StatusView'] || dataItem['Status']"
      ></vnr-tag>
    </span>
    <span class="--has-custom-template" *ngSwitchDefault>
      {{ dataItem[column['Name']] || '-' }}
    </span>
  </ng-container>
</ng-template>
<ng-template #templateEmpty>-</ng-template>
