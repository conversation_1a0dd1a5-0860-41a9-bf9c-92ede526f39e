<div class="form-header" nz-form [formGroup]="formGroup">
  <vnr-toolbar-new [builder]="builderToolbar">
    <vnr-button-new
      *ngIf="selectedItem && selectedItem.length === 1"
      [builder]="builderButtonCopy"
      (vnrClick)="onGridCopy(selectedItem[0])"
    >
    </vnr-button-new>
    <vnr-button-new
      *ngIf="selectedItem && selectedItem.length === 1"
      [builder]="builderButtonEdit"
      (vnrClick)="onGridEdit(selectedItem[0])"
    >
    </vnr-button-new>
    <vnr-button-new
      *ngIf="selectedItem && selectedItem.length > 0"
      [builder]="builderButtonDelete"
      (vnrClick)="onGridDelete(selectedItem)"
    >
    </vnr-button-new>
  </vnr-toolbar-new>
</div>
<vnr-grid-new
  #vnrGrid
  [builder]="builderGrid"
  [gridName]="gridName"
  [dataLocal]="dataLocal"
  [columns]="columns"
  [dataFormSearch]="dataFormSearch"
  [columnTemplates]="listColumnTemplates"
  (getSelectedID)="getSelectedID($event)"
  (getDataItem)="getDataItem($event)"
  (vnrDoubleClick)="onOpenDetail($event)"
  (vnrEdit)="onGridEdit($event)"
  (vnrDelete)="onGridDelete($event)"
  (vnrCellClick)="onGridCellClick($event)"
>
</vnr-grid-new>

<ng-template #templateStatus let-dataItem>
  <vnr-tag
    *ngIf="dataItem['Status']; else templateEmpty"
    [vnrColor]="getColorStatus(dataItem['Status'])"
    [vnrTitle]="dataItem['StatusView']"
  ></vnr-tag>
</ng-template>
<ng-template #templateDepartment let-dataItem>
  <vnr-tag
    *ngIf="dataItem['Department']; else templateEmpty"
    [vnrColor]="getDepartmentColor(dataItem['Department'])"
    [vnrTitle]="dataItem['Department']"
  ></vnr-tag>
</ng-template>

<ng-template #templatePosition let-dataItem>
  <vnr-tag
    *ngIf="dataItem['Position']; else templateEmpty"
    [vnrColor]="getPositionColor(dataItem['Position'])"
    [vnrTitle]="dataItem['Position']"
  ></vnr-tag>
</ng-template>

<ng-template #templateEmployee let-dataItem>
  <vnr-tag
    *ngIf="dataItem['Employee']; else templateEmpty"
    [vnrColor]="getEmployeeColor(dataItem['Employee'])"
    [vnrTitle]="dataItem['Employee']"
  ></vnr-tag>
</ng-template>

<ng-template #templateEmpty>-</ng-template>

<ng-template #tplContentDelete let-params>
  <div class="template-delete">
    <div class="template-delete__header">
      <img src="assets/icon/vnr-icon/icon-confirm-delete.png" alt="delete" />
    </div>
    <div class="template-delete__content">
      <div class="template-delete__content-title">
        <span>{{ 'common.modal.confirmDeleteData' | translate }}</span>
      </div>
      <div class="template-delete__content-description">
        {{ 'common.modal.deletedDataCannotBeRecovered' | translate }}
      </div>
    </div>
  </div>
</ng-template>

<ng-template #tplFooterDelete let-ref="modalRef">
  <div class="template-view-detail__action">
    <vnr-button-new [builder]="builderbtnDoNo" (vnrClick)="ref.destroy()"></vnr-button-new>
    <vnr-button-new
      [builder]="builderbtnDoYes"
      (vnrClick)="onConfirmDelete($event, ref)"
    ></vnr-button-new>
  </div>
</ng-template>
