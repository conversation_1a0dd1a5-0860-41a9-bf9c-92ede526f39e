<vnr-toolbar-new [builder]="builderToolbar">
  <vnr-button rightToolbar [vnrTemplate]="tplBtn"></vnr-button>
</vnr-toolbar-new>
<ng-template #tplBtn>
  <nz-segmented
    [nzOptions]="timeFilterOptions"
    (nzValueChange)="handleTimeFilterChange($event)"
    nzSize="small"
  ></nz-segmented>
  <span class="mx-1">|</span> <vnr-button rightToolbar [vnrTemplate]="tplBtnCore"></vnr-button>
  <ng-template #tplBtnCore>
    <button
      class="ant-btn toolbar-border-radius vnr-button-default ant-btn-default ng-star-inserted mr-1"
      style="border: 1px solid #d9d9d9; border-radius: 6px"
    >
      <i class="fa-light fa-file-export mr-1"></i> Xuất dữ liệu
    </button>
    <button
      class="ant-btn toolbar-border-radius vnr-button-default ant-btn-default ng-star-inserted mr-1"
      style="border: 1px solid #d9d9d9; border-radius: 6px"
    >
      <i class="fa-light fa-chart-pie"></i>
    </button>
    <button
      class="ant-btn toolbar-border-radius vnr-button-default ant-btn-default ng-star-inserted"
      style="border: 1px solid #d9d9d9; border-radius: 6px"
    >
      <i class="fa-light fa-filter"></i>
    </button>
  </ng-template>
</ng-template>
<vnr-treelist-new
  #vnrTreeList
  [builder]="builderTreeList"
  [gridName]="gridName"
  [dataLocal]="dataLocal"
  [columns]="columns"
  [defaultColumnTemplate]="tplCustomTemplateByColumn"
>
  <ng-template #tplCustomTemplateByColumn let-dataItem let-column="columnItem" let-field="field">
    <ng-container [ngSwitch]="column['Name']">
      <!-- Assigned -->
      <span *ngSwitchCase="'Representative'">
        <div class="d-flex align-items-center">
          <app-vnr-letters-avatar
            [avatarName]="dataItem['Representative']"
            [circular]="true"
            [width]="32"
            class="mr-2"
          ></app-vnr-letters-avatar>
          <span>{{ dataItem['Representative'] }}</span>
        </div>
      </span>

      <!-- Weight -->
      <span *ngSwitchCase="'Weight'"> {{ dataItem['Weight'] || '0' }}% </span>

      <!-- Kết quả mới nhất -->
      <span *ngSwitchCase="'LatestResult'">
        {{ dataItem['LatestResult'] || '0' | targetFormat : dataItem['Unit'] }}
      </span>

      <!-- Tổng thực đạt -->
      <span *ngSwitchCase="'TotalActual'">
        {{ dataItem['TotalActual'] || '0' | targetFormat : dataItem['Unit'] }}
      </span>

      <!-- Tổng mục tiêu -->
      <span *ngSwitchCase="'TotalTarget'">
        {{ dataItem['TotalTarget'] || '0' | targetFormat : dataItem['Unit'] }}
      </span>

      <!-- Chênh lệch -->
      <span *ngSwitchCase="'Variance'">
        <!-- Dựa vào Tổng thực đạt - Tổng mục tiêu để tính toán -->
        {{
          dataItem['TotalActual'] - dataItem['TotalTarget'] || '0' | targetFormat : dataItem['Unit']
        }}
      </span>

      <!-- Mức độ hoàn thành -->
      <span *ngSwitchCase="'CompletionRate'">
        <!-- Tính phần trăm: (Tổng thực đạt / Tổng mục tiêu) * 100, đảm bảo trong khoảng 0-100% -->
        {{ calculateCompletionRate(dataItem) | number : '1.0-2' }}%
      </span>

      <!-- Kết quả -->
      <span *ngSwitchCase="'ResultStatus'">
        <div class="wrapper-vnrTag">
          <nz-tag
            [class]="'wrapper__vnrtag ' + getStatusProgressColor(dataItem).backgroundColor"
            [nzColor]="getStatusProgressColor(dataItem).backgroundColor"
            [nzBordered]="true"
          >
            <span [style.color]="getStatusProgressColor(dataItem).textColor">{{
              getStatusProgressColor(dataItem).label
            }}</span>
          </nz-tag>
        </div>
      </span>
      <!-- Trạng thái -->
      <span *ngSwitchCase="'Status'">
        <div class="wrapper-vnrTag">
          <nz-tag
            [class]="'wrapper__vnrtag ' + getStatusTag(dataItem).backgroundColor"
            [nzColor]="getStatusTag(dataItem).backgroundColor"
            [nzBordered]="true"
          >
            <span [style.color]="getStatusTag(dataItem).textColor">{{
              getStatusTag(dataItem).label
            }}</span>
          </nz-tag>
        </div>
      </span>
      <!-- attachment -->
      <span *ngSwitchCase="'Attachment'">
        <span class="btn-link"><i class="fa-light fa-download mr-1"></i>BC-doanh-nghiep.xlsx</span>
      </span>

      <!-- data by 12 month, 4 quarter, 2 half year -->
      <span *ngSwitchCase="'Month1'">{{
        dataItem['Month1'] | targetFormat : dataItem['Unit']
      }}</span>
      <span *ngSwitchCase="'Month2'">{{
        dataItem['Month2'] | targetFormat : dataItem['Unit']
      }}</span>
      <span *ngSwitchCase="'Month3'">{{
        dataItem['Month3'] | targetFormat : dataItem['Unit']
      }}</span>
      <span *ngSwitchCase="'Month4'">{{
        dataItem['Month4'] | targetFormat : dataItem['Unit']
      }}</span>
      <span *ngSwitchCase="'Month5'">{{
        dataItem['Month5'] | targetFormat : dataItem['Unit']
      }}</span>
      <span *ngSwitchCase="'Month6'">{{
        dataItem['Month6'] | targetFormat : dataItem['Unit']
      }}</span>
      <span *ngSwitchCase="'Month7'">{{
        dataItem['Month7'] | targetFormat : dataItem['Unit']
      }}</span>
      <span *ngSwitchCase="'Month8'">{{
        dataItem['Month8'] | targetFormat : dataItem['Unit']
      }}</span>
      <span *ngSwitchCase="'Month9'">{{
        dataItem['Month9'] | targetFormat : dataItem['Unit']
      }}</span>
      <span *ngSwitchCase="'Month10'">{{
        dataItem['Month10'] | targetFormat : dataItem['Unit']
      }}</span>
      <span *ngSwitchCase="'Month11'">{{
        dataItem['Month11'] | targetFormat : dataItem['Unit']
      }}</span>
      <span *ngSwitchCase="'Month12'">{{
        dataItem['Month12'] | targetFormat : dataItem['Unit']
      }}</span>
      <span *ngSwitchCase="'Quarter1'">{{
        dataItem['Quarter1'] | targetFormat : dataItem['Unit']
      }}</span>
      <span *ngSwitchCase="'Quarter2'">{{
        dataItem['Quarter2'] | targetFormat : dataItem['Unit']
      }}</span>
      <span *ngSwitchCase="'Quarter3'">{{
        dataItem['Quarter3'] | targetFormat : dataItem['Unit']
      }}</span>
      <span *ngSwitchCase="'Quarter4'">{{
        dataItem['Quarter4'] | targetFormat : dataItem['Unit']
      }}</span>

      <!-- Default Template -->
      <span *ngSwitchDefault>
        {{ dataItem[column['Name']] || '' }}
      </span>
    </ng-container>
  </ng-template>

  <ng-template #templateEmpty>-</ng-template>
</vnr-treelist-new>
