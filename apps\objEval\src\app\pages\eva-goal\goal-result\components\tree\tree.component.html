<div class="p-3">
  <vnr-toolbar-new [builder]="builderToolbar">
    <vnr-button rightToolbar [vnrTemplate]="tplBtn"></vnr-button>
  </vnr-toolbar-new>
  <ng-template #tplBtn>
    <button
      class="ant-btn toolbar-border-radius vnr-button-default ant-btn-default ng-star-inserted mr-1"
      style="border: 1px solid #d9d9d9; border-radius: 6px"
    >
      <i class="fa-light fa-file-export mr-1"></i> Xuất dữ liệu
    </button>
    <button
      class="ant-btn toolbar-border-radius vnr-button-default ant-btn-default ng-star-inserted mr-1"
      style="border: 1px solid #d9d9d9; border-radius: 6px"
    >
      <i class="fa-light fa-chart-pie"></i>
    </button>
    <button
      class="ant-btn toolbar-border-radius vnr-button-default ant-btn-default ng-star-inserted"
      style="border: 1px solid #d9d9d9; border-radius: 6px"
    >
      <i class="fa-light fa-filter"></i>
    </button>
  </ng-template>
  <div class="goal-tree-diagram-container">
    <div #goalTreeDiagram class="goal-tree-diagram"></div>
    <div class="controls">
      <div class="zoom-controls ml-3">
        <vnr-button [vnrIcon]="'minus'" (vnrClick)="zoomOut()"></vnr-button>
        <div class="zoom-text">{{ zoomPercentage }}%</div>
        <vnr-button [vnrIcon]="'plus'" (vnrClick)="zoomIn()"></vnr-button>
      </div>
      <vnr-button
        class="ml-3"
        [vnrText]="'Mở rộng tất cả'"
        [vnrIcon]="'fullscreen'"
        (vnrClick)="expandAll()"
      ></vnr-button>
      <vnr-button
        class="ml-3"
        [vnrText]="'Thu gọn tất cả'"
        [vnrIcon]="'fullscreen-exit'"
        (vnrClick)="collapseAll()"
      ></vnr-button>
    </div>
  </div>
</div>
