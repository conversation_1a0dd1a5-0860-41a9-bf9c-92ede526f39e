import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  TemplateRef,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';
import {
  VnrButtonFactory,
  VnrButtonNewBuilder,
  VnrButtonNewComponent,
  VnrGridNewBuilder,
  VnrGridNewComponent,
  vnrUtilities,
} from '@hrm-frontend-workspace/vnr-module';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { NgIf } from '@angular/common';
import { CommonService } from '@hrm-frontend-workspace/core';
import { ResponseStatus } from '@hrm-frontend-workspace/models';
import { VnrModuleModule, VnrTagComponent } from '@hrm-frontend-workspace/ui';
import { NzDrawerService } from 'ng-zorro-antd/drawer';
import { NzModalService } from 'ng-zorro-antd/modal';
import { EvaCriteriaFacade } from '../../../eva-criteria/facade/eva-criteria.facade';
import { gridDefineDataSource } from '../../data/datasource.data';
import { gridDefineColumns } from '../../data/column.data';
import { EvaCriteriaTypeCreateComponent } from '../../../eva-criteria-type/components/eva-criteria-type-create/eva-criteria-type-create.component';
import { EvaCriteriaRateCreateComponent } from '../eva-criteria-rate-create/eva-criteria-rate-create.component';

@Component({
  selector: 'eva-criteria-rate-grid',
  templateUrl: './eva-criteria-rate-grid.component.html',
  styleUrls: ['./eva-criteria-rate-grid.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [VnrModuleModule, TranslateModule, VnrTagComponent, NgIf, VnrButtonNewComponent],
})
export class EvaCriteriaRateGridComponent implements OnInit, AfterViewInit {
  @Input() criteriaID: string = null;
  @Input() IsView: boolean = false;
  @Output() onSelectItem = new EventEmitter<any>();

  @ViewChild('vnrGrid', { static: true }) gridControl: VnrGridNewComponent;
  @ViewChild('tplContentDelete', { static: true }) private _tplContentDelete: TemplateRef<any>;
  @ViewChild('tplFooterDelete', { static: true })
  private _tplFooterDelete: TemplateRef<any>;
  private _isSupperAdmin: boolean = true;
  private _permission: string = '';
  private _screenName: string = 'eva-criteria-type-grid';
  private _storeName: string = 'hrm_cat_sp_get_evaCriteriaType';
  private _selectedIDs = [];
  private _columnKey = 'Id';
  private _btnFactory: VnrButtonFactory = VnrButtonFactory.init();
  private _isEditing: boolean = false;
  private _criteriaID: string = null;

  protected screenWidth: number;
  protected screenHeight: number;
  protected compRef: ViewContainerRef;
  protected gridHeight: number;
  protected builderGrid: VnrGridNewBuilder;
  protected builderbtnDoNo: VnrButtonNewBuilder;
  protected builderbtnDoYes: VnrButtonNewBuilder;
  protected gridName = 'eva-criteria-type-grid';
  protected dataLocal = gridDefineDataSource;
  protected columns = gridDefineColumns;
  protected dataFormSearch = {
    CriteriaID: this.criteriaID,
  };

  constructor(
    private _evaCriteriaFacade: EvaCriteriaFacade,
    private _drawerService: NzDrawerService,
    private _modalService: NzModalService,
    private _vc: ViewContainerRef,
    private _commonService: CommonService,
    private _translate: TranslateService,
  ) {}
  ngAfterViewInit() {}
  ngOnInit() {
    this.builderButtonComponent();
    this.builderGridComponent();
  }
  private builderButtonComponent() {
    this.builderbtnDoYes = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'common.modal.buttonOk',
      options: {
        style: 'danger',
      },
    });
    this.builderbtnDoNo = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'common.modal.buttonNo',
      options: {
        style: 'default',
      },
    });
  }
  private builderGridComponent() {
    this.builderGrid = new VnrGridNewBuilder({
      options: {
        queryOption: {
          take: 100,
        },
        isResizable: true,
        configHeightGrid: {
          isAllowCalcRowHeight: true,
          rowHeight: 36,
          isHeightByRow: true,
          rowScrollHorizontal: 8,
          rowThreshold: 0,
        },
        configSelectable: {
          columnKey: this._columnKey,
        },
        configShowHide: {
          isPageExpand: true,
          isShowDelete: !this.IsView,
          isShowEdit: !this.IsView,
          isShowColumnCheck: !this.IsView,
        },
        configCommandColumn: {
          isEnabledMenuAction: true,
        },
        configGroupable: {
          isEnabled: false,
        },
      },
    });
  }
  protected getSelectedID($event) {
    this._selectedIDs = $event;
    this.onSelectItem.emit($event);
  }
  protected onGridEdit($event) {
    this._criteriaID = null;
    this._isEditing = true;
    if (!$event) {
      this._commonService.message({
        message: this._translate.instant('common.grid.noSelectedId'),
        type: 'error',
      });
      return;
    }
    this._selectedIDs = [$event[this._columnKey]];
    if (!this._selectedIDs || this._selectedIDs.length == 0) {
      this._commonService.message({
        message: this._translate.instant('common.grid.noSelectedId'),
        type: 'error',
      });
      return;
    }
    this.onOpenFormCreate(this._selectedIDs[0]);
    setTimeout(() => {
      this._isEditing = false;
    }, 1000);
  }
  protected onGridDelete($event) {
    this._selectedIDs = [$event[this._columnKey]];
    this.onOpenConfirmDelete();
  }

  protected onOpenFormCreate($event: any) {
    let title =
      $event != null
        ? this._translate.instant('objEval.EvaCriteriaRate.EditCriteriaRate')
        : this._translate.instant('objEval.EvaCriteriaRate.AddCriteriaRate');
    const modalRef = this._modalService.create({
      nzTitle: title,
      nzContent: EvaCriteriaRateCreateComponent,
      nzViewContainerRef: this._vc,
      nzClosable: true,
      nzMaskClosable: true,
      nzMask: true,
      nzWidth: '600px',
      nzBodyStyle: { minheight: `512px`, paddingTop: '10px' },
      nzFooter: null,
      nzData: {
        paramEditID: $event,
        criteriaID: this._criteriaID,
      },
    });
    modalRef.afterClose.subscribe((modal) => {
      if (modal?.type === 'isReloadData') {
        this.gridControl.vnrReloadGrid();
      }
    });
  }
  public onAddNew(_criteriaID: string) {
    this._criteriaID = _criteriaID;
    this.onOpenFormCreate(null);
  }
  public onOpenConfirmDelete() {
    const modalRef = this._modalService.create({
      nzIconType: '',
      nzContent: this._tplContentDelete,
      nzFooter: this._tplFooterDelete,
      nzMaskClosable: true,
      nzClosable: true,
    });
    modalRef.afterClose.subscribe((modal) => {
      if (modal?.type === 'Reload') {
        this.gridControl.vnrReloadGrid();
      }
    });
  }
  protected onConfirmDelete($event: any, $modalRef: any) {
    $event?.stopPropagation();
    if (!this._selectedIDs || this._selectedIDs.length <= 0) {
      this._commonService.message({ message: 'common.grid.noSelectedId_Delete', type: 'error' });
      return;
    }
    this._evaCriteriaFacade.delete(this._selectedIDs).subscribe((res) => {
      if (res && res.Status === ResponseStatus.SUCCESS) {
        this._commonService.message({
          message: this._translate.instant('common.message.deletedRowOfData', {
            n: this._selectedIDs.length,
          }),
          type: 'success',
        });
        $modalRef.close({
          type: 'Reload',
        });
      } else {
        this._commonService.message({
          message: res.Message || this._translate.instant('common.notification.descErr500'),
          type: 'error',
        });
      }
    });
  }
  public reloadGridData(): void {
    this.gridControl.vnrReloadGrid();
  }
}
