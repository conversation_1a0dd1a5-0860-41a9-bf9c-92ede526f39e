<div class="add-competency-evaluation">
  <form nz-form [formGroup]="competencyFormGroup" novalidate>
    <div nz-row [nzGutter]="16">
      <div nz-col [nzXs]="24" [nzSm]="24">
        <vnr-multiselect
          formControlName="CompetencyID"
          [builder]="builderCompetencyName"
          (selectDataItem)="onSelectCompetencyName($event)"
          #tplCompetencyName
        ></vnr-multiselect>
      </div>
    </div>
  </form>
</div>
<div *nzModalFooter>
  <nz-space>
    <div *nzSpaceItem>
      <vnr-button
        class="custom--btn"
        [vnrType]="'default'"
        [vnrText]="'common.cancel' | translate"
        (vnrClick)="onSubmit('close')"
      >
      </vnr-button>
    </div>
    <div *nzSpaceItem>
      <vnr-button
        class="custom--btn"
        [vnrType]="'primary'"
        [vnrText]="'lưu' | translate"
        (vnrClick)="onSubmit('confirm')"
        [vnrDisabled]="!competencyFormGroup.valid"
      >
      </vnr-button>
    </div>
  </nz-space>
</div>
