import { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import {
  GroupResult,
  State,
  toDataSourceRequest,
  toDataSourceRequestString,
} from '@progress/kendo-data-query';
import { Observable, of, Subject } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { GridDataObjSchema, GridDataSchema } from '../interfaces/grid-data-schema.interface';
import { GroupItem } from '@progress/kendo-angular-grid';
import { IVnRGridNewConfigGetChangeColumn } from '../interfaces/grid-new-change-column.interface';
import { VnRGridNewDataSource } from '../models/grid-new.model';
import { VNRMODULE_TOKEN } from '../../../base/api-config';
import { IVnrModule_Token } from '../../../common/models/app-api-config.interface';
import { IVnrGridColumns } from '../interfaces/grid-column.interface';

// import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class GridNewConfigService {
  private _vnrGrid$: Subject<any> = new Subject<any>();
  constructor(
    private http: HttpClient,
    @Inject(VNRMODULE_TOKEN) private _vnrModule_Token: IVnrModule_Token,
  ) {}

  public sendVnrGridMessage(message: any) {
    return this._vnrGrid$.next(message);
  }

  public getVnrGridMessage(): Observable<any> {
    return this._vnrGrid$.asObservable();
  }
  public refactorColumnToFlatColumns(columns: IVnrGridColumns[], columnsGrid: IVnrGridColumns[]) {
    if (!columns || Array.isArray(columns) === false || columns.length < 0) {
      return;
    }
    columns.forEach((x) => {
      const newObj: IVnrGridColumns = {
        Name: x.Name,
        HeaderName: x.HeaderName,
        HeaderKey: x.HeaderKey,
        Width: x.Width,
        Sortable: x.Sortable,
        Filter: x.Filter,
        Hidden: x.Hidden,
        Format: x.Format,
        OrderColumn: x.OrderColumn,
        RowOnPage: x.RowOnPage,
        Group: x.Group,
        Sum: x.Sum,
        isNumber: x.isNumber,
        Disable: x.Disable,
        Locked: x.Locked,
      };
      columnsGrid.push(newObj);
      if (x.MultiColumn && Array.isArray(x.MultiColumn) && x.MultiColumn.length > 0) {
        this.refactorColumnToFlatColumns(x.MultiColumn, columnsGrid);
      }
    });
  }

  public loadColumnConfig(
    _config: IVnRGridNewConfigGetChangeColumn,
  ): Observable<Array<IVnrGridColumns>> {
    if (!_config) return of([]);
    const _apiGetColumn =
      _config.apiGetColumn ?? this._vnrModule_Token.gridConfig_Token.apiGetColumn?.url;
    return this.http
      .get<Array<IVnrGridColumns>>(_apiGetColumn + `?tableName=${_config.gridName}`)
      .pipe(
        catchError(() => {
          return of([]);
        }),
        map((res: any) => {
          // res = take(res, 8);
          return res.Data.Columns;
        }),
      );
  }

  public loadCustomDataGrid(config: VnRGridNewDataSource, state: State) {
    // console.log('[LOGGER INFO] Loading datasource...')
    const nullOrErrorData: any = {
      data: [],
      total: 0,
    };

    if (!config.api && !config.storeName) return of(nullOrErrorData);
    if (config.storeName) {
      config.dataFormSearch.storeName = config.storeName;
    }

    let data = {};
    if (config.isLazyloadDatasource) {
      data = {
        ...config.dataFormSearch,
        ...state,
        ...toDataSourceRequest(state),
      };
    } else {
      data = {
        ...config.dataFormSearch,
        ...state,
        pageSize: state.take,
        page: state.skip / state.take + 1,
      };
    }

    if (config.isServerSupport) {
      data = {
        ...config.dataFormSearch,
        ...toDataSourceRequest(state),
      };
    } else {
      if (config.isLazyloadDatasource) {
        data = {
          ...config.dataFormSearch,
          ...state,
          dataSourceRequestString: toDataSourceRequestString(state),
        };
      } else {
        data = {
          ...config.dataFormSearch,
          ...state,
          pageSize: state.take,
          page: (state.skip as number) / (state.take as number) + 1,
          dataSourceRequestString: toDataSourceRequestString(state),
        };
      }
    }
    data['PageIndex'] = data['page'] || 1;
    let method = 'post';
    if (config.api?.method) {
      method = config.api?.method;
    } else if (config.api?.method) {
      method = this._vnrModule_Token.gridConfig_Token.apiGridGetDataSource?.method;
    }
    const apiGetDataSource =
      config.api?.url ?? this._vnrModule_Token.gridConfig_Token.apiGridGetDataSource?.url;
    let payload: any = {};
    payload = config && method === 'get' ? { params: data } : { body: data };
    if (!config.storeName === false && method === 'post') {
      const _formData = new FormData();
      for (const key in data) {
        _formData.append(key, data[key]);
      }
      payload.body = _formData;
      //payload.headers = new HttpHeaders().set('Content-Type', 'multipart/form-data');
    }
    return this.http.request(method.toUpperCase(), apiGetDataSource, payload);
  }

  public loadDataGrid(config: VnRGridNewDataSource, state: State) {
    const nullOrErrorData: any = {
      data: [],
      total: 0,
    };
    if (!config.api && !config.storeName) return of(nullOrErrorData);
    if (!config.storeName === false) {
      config.dataFormSearch.storeName = config.storeName;
    }
    let data = {};
    if (config.isLazyloadDatasource) {
      data = {
        ...config.dataFormSearch,
        ...state,
        ...toDataSourceRequest(state),
      };
    } else {
      data = {
        ...config.dataFormSearch,
        ...state,
        pageSize: state.take,
        page: state.skip / state.take + 1,
      };
    }

    if (config.isServerSupport) {
      data = {
        ...config.dataFormSearch,
        ...state,
        ...toDataSourceRequest(state),
      };
    } else {
      if (config.isLazyloadDatasource) {
        data = {
          ...config.dataFormSearch,
          ...state,
          dataSourceRequestString: toDataSourceRequestString(state),
        };
      } else {
        data = {
          ...config.dataFormSearch,
          ...state,
          pageSize: state.take,
          page: (state.skip as number) / (state.take as number) + 1,
          dataSourceRequestString: toDataSourceRequestString(state),
        };
      }
    }
    data['PageIndex'] = data['page'] || 1;
    let method = 'post';
    if (config.api?.method) {
      method = config.api?.method;
    } else if (config.api?.method) {
      method = this._vnrModule_Token.gridConfig_Token.apiGridGetDataSource?.method;
    }
    const apiGetDataSource =
      config.api?.url ?? this._vnrModule_Token.gridConfig_Token.apiGridGetDataSource?.url;
    let payload: any = {};
    payload = config && method === 'get' ? { params: data } : { body: data };
    if (!config.storeName === false && method === 'post') {
      const _formData = new FormData();
      for (const key in data) {
        _formData.append(key, data[key]);
      }
      payload.body = _formData;
    }
    return this.http.request(method.toUpperCase(), apiGetDataSource, payload).pipe(
      map((x: GridDataSchema | any) => {
        if (Array.isArray(x)) {
          return { data: x, total: x.length };
        } else {
          //API Portal v3.0
          if (x.hasOwnProperty('Data') && !Array.isArray(x.Data) && x.Data) {
            x.data = (x.Data as GridDataObjSchema).Data || [];
            x.total =
              (x.Data as GridDataObjSchema).Total || (x.Data as GridDataObjSchema).TotalRow || 0;
          }
          //API Portal v2.0
          else if (x.items && Array.isArray(x.items)) {
            x.total = x.totalItems || 0;
            x.data = x.items || [];

            delete x.items;
            delete x.pageIndex;
            delete x.hasNextPage;
            delete x.hasPreviousPage;
          }
          //API Portal v1.0
          else {
            if (x.Data && !x.data) {
              x.data = (x.Data || []) as any[];
            }
            if (x.Data && Array.isArray(x.Data) && x.Data.length > 0) {
              x.total = x.Data[0].TotalRow || 0;
            }
            if ((x.Total && !x.total) || (x.Total === 0 && !x.total)) {
              x.total = x.Total || 0;
            }
          }
        }
        return x;
      }),
      catchError(() => {
        return of(nullOrErrorData);
      }),
    );
  }

  public postEditGrid(url: string, data?: any) {
    return this.http.post(url, data || null);
  }

  public postExportExcel(url: string, data?: any) {
    const payload = { body: data || null };
    return this.http.request('POST', url, payload).pipe(
      catchError((err: HttpErrorResponse) => {
        return of(null);
      }),
    );
  }

  public postExportWord(url: string, data?: any) {
    const payload = { body: data || null };
    return this.http.request('POST', url, payload).pipe(
      catchError((err: HttpErrorResponse) => {
        return of(null);
      }),
    );
  }

  public findDeepColumn(listColumn: any[], fieldName: string) {
    let result;
    //Find without group
    const columnsWithoutGroup = listColumn?.find((x) => x.name === fieldName) || null;
    if (columnsWithoutGroup) {
      result = columnsWithoutGroup;
    } else {
      //Find all column in group
      const groupColumns =
        listColumn
          ?.filter((x) => x.Type === 'group' && x.MultiColumn && x.MultiColumn.length > 0)
          ?.flatMap((x) => x.MultiColumn) || [];

      if (groupColumns && groupColumns.length > 0) {
        result = groupColumns.find((x) => x.Name === fieldName);
      }
    }
    return result;
  }

  /**
   * Retrieves the current data by index within a group.
   * @param data The array of GroupItem objects.
   * @param state The State object containing group information.
   * @param index The index string to locate the desired data.
   * @returns The GroupItem object or null if not found.
   */
  public getCurrentDataByIndex(
    data: GroupItem[],
    index: string,
    state: State,
  ): GroupItem | any | null {
    // Split the index string and convert each part to a number
    const indices = index.split('_').map(Number);
    let currentData: any = data;

    // Traverse the data structure based on the index
    for (let i = 0; i < indices.length; i++) {
      const idx = indices[i];
      if (!currentData || !Array.isArray(currentData)) {
        return null;
      }
      currentData = currentData[idx];
      if (i < state.group.length - 1) {
        currentData = currentData?.items;
      }
    }
    return currentData;
  }

  /**
   * Retrieves the parent data by index within a group.
   * @param data The array of GroupItem objects.
   * @param state The State object containing group information.
   * @param index The index string to locate the parent data.
   * @returns The parent GroupItem object or null if not found.
   */
  public getParentDataByIndex(data: GroupItem[], index: string, state?: State): GroupItem[] {
    const indices = index?.split('_').map(Number);
    let currentData: any = data;
    const parents: any[] = [];

    for (let i = 0; i < indices.length; i++) {
      const idx = indices[i];
      if (!currentData || !Array.isArray(currentData)) {
        return parents;
      }
      parents.push(currentData[idx]);
      currentData = currentData[idx]?.items;
    }
    return parents;
  }

  /**
   * Extract all items from a hierarchical data structure
   * @param data The data structure to extract items from
   * @returns An array of all items in the data structure
   */
  public extractItems(data: any[]): any[] {
    const items: any[] = [];
    /**
     * Recursively traverse the data structure and add all items to the items array
     * @param itemsArray The current array of items to traverse
     */
    function recursiveExtract(itemsArray: any[]) {
      for (const item of itemsArray) {
        if (item.items && item.items.length > 0) {
          // If the item has subitems, recursively traverse them
          recursiveExtract(item.items);
        } else {
          // If the item does not have subitems, add it to the items array
          items.push(item);
        }
      }
    }

    recursiveExtract(data);
    return items;
  }

  /**
   * Returns the items from a GroupResult object and its children.
   * If the object is not a GroupResult, returns an empty array.
   * If the GroupResult object has no children, returns an empty array.
   * @param group - A GroupResult object.
   * @returns An array of items from the GroupResult object and its children.
   */
  public getGroupItems(group: GroupResult): any[] {
    if (!group || !this.isGroupResult(group) || group.items.length === 0) {
      return [];
    }
    if (!this.isGroupResult(group.items[0])) {
      return group.items;
    }
    let descendants: any[] = [];
    group.items.forEach(
      (item: GroupResult) => (descendants = descendants.concat(this.getGroupItems(item))),
    );
    return descendants;
  }

  /** Checking Groups with their JSON key values */
  public isGroupResult(item: object): item is GroupResult {
    return (
      item.hasOwnProperty('items') && item.hasOwnProperty('value') && item.hasOwnProperty('field')
    );
  }
}
