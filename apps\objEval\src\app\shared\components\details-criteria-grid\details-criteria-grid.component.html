<div class="details-criteria-grid">
  <div class="details-criteria-grid__header">
    <div class="details-criteria-grid__header-title">
      {{ index | evaNumberToLetter }}. {{ dataItemInput?.criteriaName }}
    </div>
  </div>
  <div class="details-criteria-grid__content">
    <vnr-grid-new
      #vnrGrid
      [builder]="builderGrid"
      [gridName]="gridName"
      [dataLocal]="dataLocal"
      [columns]="columnGridLocal"
      [aggregates]="aggregates"
      [customTemplateColumnFooter]="listCustomFooter"
      [defaultColumnTemplate]="tplCustomTemplateByColumn"
    >
    </vnr-grid-new>
  </div>
  <div class="details-criteria-grid__footer mt-3 d-flex">
    <vnr-button
      class="custom--btn"
      leftToolbar
      [vnrType]="'default'"
      [vnrIcon]="'fal fa-plus'"
      [vnrText]="'evaluation.btnAddCriteria' | translate"
      (vnrClick)="addCriteria()"
    >
    </vnr-button>
  </div>
</div>

<ng-template #tplCustomTemplateByColumn let-dataItem let-column="columnItem" let-field="field">
  <ng-container [ngSwitch]="column['Name']">
    <span class="--has-custom-template" *ngSwitchCase="'weight'">
      {{ dataItem[column['Name']] | number : '1.0-2' }}%
    </span>
    <span class="--has-custom-template" *ngSwitchCase="'score'">
      {{ dataItem[column['Name']] | number : '1.0-2' }}
    </span>
    <span class="--has-custom-template" *ngSwitchDefault>
      {{ dataItem[column['Name']] || '' }}
    </span>
  </ng-container>
</ng-template>

<!-- Footer -->
<ng-template
  #templateFooter
  let-dataItem
  let-aggregates="aggregates"
  let-aggregateItem="aggregateItem"
  let-columnItem="columnItem"
>
  <ng-container *ngIf="columnItem?.Calculate && aggregateItem && aggregateItem[columnItem.Name]">
    @switch (columnItem.Name) { @case ('weight') {
    <div class="header-custom">
      Tổng: {{ aggregateItem[aggregates.field][columnItem.Calculate] | kendoNumber : 'n2' }}%
    </div>
    } }
  </ng-container>
</ng-template>
