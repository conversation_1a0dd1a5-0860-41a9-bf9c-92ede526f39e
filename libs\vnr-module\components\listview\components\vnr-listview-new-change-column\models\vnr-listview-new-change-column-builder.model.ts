import { AbstractListviewNewChangeColumnBuilder } from '../../../models/abstract-listview-new-change-column-builder.model';
import { VnrListviewNewBuilder } from '../../vnr-listview-new/models/vnr-listview-new-builder.model';
import { VnRListviewNewChangeColumnOptionBuilder } from './vnr-listview-new-change-column-option-builder.model';

export class VnRListviewNewChangeColumnBuilder extends AbstractListviewNewChangeColumnBuilder<
  VnRListviewNewChangeColumnBuilder,
  VnRListviewNewChangeColumnOptionBuilder
> {
  constructor(builder?: VnRListviewNewChangeColumnBuilder) {
    super();
    if (!builder === false) {
      builder.data = {
        columnMode: builder?.data?.columnMode,
        gridControlName: builder?.data?.gridControlName ?? builder?.gridName,
        keyChangeCol: builder?.data?.keyChangeCol,
        userInfoID: builder?.data?.userInfoID ?? '',
      };
    }
    Object.assign(this, builder);
    this.options = new VnRListviewNewChangeColumnOptionBuilder(builder?.options);
  }
  builderFromGrid?(builderGrid?: VnrListviewNewBuilder) {
    let builder: VnRListviewNewChangeColumnBuilder = {
      gridPlacement: builderGrid.optionChangeColumn?.gridPlacement,
      data: builderGrid.optionChangeColumn?.data,
      options: {
        apiRestoreChangeColumn: {
          apiUrl: builderGrid.optionChangeColumn?.apiRestoreChangeColumn?.url,
          method: builderGrid.optionChangeColumn?.apiRestoreChangeColumn?.method,
        },
        apiSaveChangeColumn: {
          apiUrl: builderGrid.optionChangeColumn?.apiSaveChangeColumn?.url,
          method: builderGrid.optionChangeColumn?.apiSaveChangeColumn?.method,
        },
        apiSaveTranslate: {
          apiUrl: builderGrid.optionChangeColumn?.apiSaveTranslate?.url,
          method: builderGrid.optionChangeColumn?.apiSaveTranslate?.method,
        },
        assemblyName: builderGrid.optionChangeColumn?.assemblyName,

        isDisabledModeViewRecommended:
          builderGrid.optionChangeColumn?.isDisabledModeViewRecommended,
        isDisabledModeViewSimple: builderGrid.optionChangeColumn?.isDisabledModeViewSimple,
        isRestoreApi: builderGrid.optionChangeColumn?.isRestoreApi,
        modelName: builderGrid.optionChangeColumn?.modelName,
        tableDB: builderGrid.optionChangeColumn?.tableDB,
        position: {
          bottom: builderGrid.optionChangeColumn?.position?.bottom,
          top: builderGrid.optionChangeColumn?.position?.top,
          left: builderGrid.optionChangeColumn?.position?.left,
          right: builderGrid.optionChangeColumn?.position?.right,
        },
      },
    };
    Object.assign(this, builder);
    this.options = new VnRListviewNewChangeColumnOptionBuilder(builder?.options);
    return builder;
  }
  builder?(builder?: VnRListviewNewChangeColumnBuilder) {
    Object.assign(this, builder);
    this.options = new VnRListviewNewChangeColumnOptionBuilder(builder?.options);
  }
}
