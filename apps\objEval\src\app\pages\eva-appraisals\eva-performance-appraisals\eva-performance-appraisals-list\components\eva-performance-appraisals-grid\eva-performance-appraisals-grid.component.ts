import { CommonModule, NgIf } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  Input,
  OnInit,
  SimpleChanges,
  TemplateRef,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';
import {
  FormsModule,
  ReactiveFormsModule,
  UntypedFormBuilder,
  UntypedFormGroup,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonService } from '@hrm-frontend-workspace/core';
import { VnrButtonComponent } from '@hrm-frontend-workspace/ui';
import {
  VnrButtonFactory,
  VnrButtonNewBuilder,
  VnrButtonNewComponent,
  VnrFilterAdvanceQuickBuilder,
  VnrFilterAdvanceQuickComponent,
  VnrListviewNewBuilder,
  VnrListviewNewComponent,
  VnrToolbarNewBuilder,
  VnrToolbarNewComponent,
  vnrUtilities,
} from '@hrm-frontend-workspace/vnr-module';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { NzAlertModule } from 'ng-zorro-antd/alert';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzModalModule, NzModalService } from 'ng-zorro-antd/modal';
import { gridDefineColumns } from '../../data/column.data';
import {
  gridDefineDataFilterQuick,
  gridDefineDataFilterQuickType,
} from '../../data/data-filter-advance';
import { gridperformanceAppraisalsDataSource } from '../../data/datasource.data';
import { performanceAppraisalsStatusPublicFormat } from '../../data/eva-performance-appraisals.data';
import { performanceDropdownStatusPublic } from '../../data/list-tabs-filter.data';
import { EvaPerformanceAppraisalsFacade } from '../../facade/eva-performance-appraisals.facade';
import { PerformanceListTab } from '../../models/tab-performance-list.model';
import { EvaPerformanceAppraisalsFormComponent } from '../eva-performance-appraisals-form/eva-performance-appraisals-form.component';
import { NzDrawerService } from 'ng-zorro-antd/drawer';

@Component({
  selector: 'eva-performance-appraisals-grid',
  templateUrl: './eva-performance-appraisals-grid.component.html',
  styleUrls: ['./eva-performance-appraisals-grid.component.scss'],
  imports: [
    FormsModule,
    ReactiveFormsModule,
    CommonModule,
    NgIf,
    VnrToolbarNewComponent,
    TranslateModule,
    VnrButtonNewComponent,
    NzModalModule,
    NzIconModule,
    NzDropDownModule,
    NzGridModule,
    NzAlertModule,
    VnrButtonComponent,
    VnrListviewNewComponent,
    VnrFilterAdvanceQuickComponent,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EvaPerformanceAppraisalsGridComponent implements OnInit {
  @Input() tabFilter: number = PerformanceListTab.ALL;

  @ViewChild('vnrGrid', { static: true }) gridControl: VnrListviewNewComponent;
  @ViewChild('templateStatus', { static: true }) private _templateStatus: TemplateRef<any>;

  protected _dataFormSearch: any = {};
  private _statusFormat = performanceAppraisalsStatusPublicFormat;
  protected gridName = 'eva-performance-appraisals-grid';
  private _screenName = 'eva-performance-appraisals-grid';

  private routerState: any = {};
  protected dropdownStatusPublic: any[] = [];
  protected selectedItem = [];
  protected formGroup: UntypedFormGroup = this._formBuider.group({});
  protected listColumnTemplates: {};
  private _columnKey = 'Id';
  protected builderGrid: VnrListviewNewBuilder;
  protected builderToolbar: VnrToolbarNewBuilder;
  private _btnFactory: VnrButtonFactory = VnrButtonFactory.init();
  protected builderButtonAddAppraisal: VnrButtonNewBuilder;
  protected builderFilterAdvanceQuick: VnrFilterAdvanceQuickBuilder;
  keyPermissionButtonAction = {
    keyPermissionCanceled: 'EvaPerformanceAppraisals_btnCancel',
    keyPermissionSendmail: 'EvaPerformanceAppraisals_btnSendMail',
    keyPermissionEdit: 'EvaPerformanceAppraisals_New_Index',
    keyPermissionDelete: 'EvaPerformanceAppraisals_New_Index',
  };
  protected dataLocal = gridperformanceAppraisalsDataSource;
  protected columns = gridDefineColumns;
  constructor(
    private _drawerService: NzDrawerService,
    private _formBuider: UntypedFormBuilder,
    private _commonService: CommonService,
    private _evaPerformanceAppraisalsFacade: EvaPerformanceAppraisalsFacade,
    private viewContainerRef: ViewContainerRef,
    private _translate: TranslateService,
    private _router: Router,
    private route: ActivatedRoute,
  ) {}

  ngOnInit() {
    this.dropdownStatusPublic = performanceDropdownStatusPublic[this.tabFilter];
    this.builderButton();
    this.builderFilterAdvanceQuickComponent();
    this.builderGridComponent();
    this.builderToolbarComponent();
  }
  ngOnChanges(changes: SimpleChanges): void {
    if (changes['tabFilter']) {
      this.dropdownStatusPublic = performanceDropdownStatusPublic[this.tabFilter];
    }
  }
  private builderFilterAdvanceQuickComponent() {
    this.builderFilterAdvanceQuick = new VnrFilterAdvanceQuickBuilder({
      screenName: this._screenName,
      gridName: this.gridName,
      storeName: '',
      components: gridDefineDataFilterQuick(),
      options: {
        displayFilterDefault: 'objEval.EvaPerformanceAppraisals.quickFilter.Status.Label',
        isSupperAdmin: true,
        isShowBtnAdvance: false,
        isUseConfig: false,
        keyConfig: 'EvaPerformanceAppraisals',
        fullFilterComponents: [],
      },
    });
  }
  private builderButton() {
    this.builderButtonAddAppraisal = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'objEval.EvaPerformanceAppraisals.btnAddAppraisal',
      options: {
        style: 'primary',
        icon: {
          fontIcon: 'plus',
        },
      },
    });
  }
  private builderToolbarComponent() {
    this.builderToolbar = new VnrToolbarNewBuilder({
      isShowConfig: true,
      gridName: this.gridName,
      isSupperAdmin: true,
      gridRef: this.gridControl,
      permission: '',
      screenName: this._screenName,
      storeName: '',
      options: {
        configButtonChangeColumn: {
          isShow: true,
          isConfigMenuDisplay: true,
          isChangeColumnNew: true,
          isDisabled: false,
          configButtonSetting: {
            isShow: true,
            isDisabled: true,
          },
        },
        configButtonExport: {
          isShowBtnExcelAll: true,
          isShowBtnWord: true,
          isShowBtnExcelByTemplate: true,
        },
        configButtonDelete: {
          isShow: true,
          isDisabled: false,
        },
        configQuickSearch: {
          textPlaceHolder: 'Nhập từ khóa để tìm kiếm...',
          searchKey: 'EmployeeName',
        },
        configFilterAdvanceQuick: {
          isShow: true,
          displayFilterDefault: 'objEval.EvaPerformanceAppraisals.quickFilter.TypeAppraisals',
          components: gridDefineDataFilterQuickType(),
          keyConfig: 'EvaPerformanceAppraisals',
          isShowBtnAdvance: false,
        },
        isSetBackground: false,
        buttonMoreFlexDirection: 'inherit',
        isExpanded: false,
      },
    });
  }
  private builderGridComponent() {
    this.builderGrid = new VnrListviewNewBuilder({
      options: {
        isInitiallyExpanded: true,
        isLockedPanelToggle: true,
        displayField: 'CycleName',
        configHeightGrid: {
          isAllowCalcRowHeight: true,
        },
        configSelectable: {
          columnKey: this._columnKey,
        },
        configShowHide: {
          isShowArrow: false,
          isShowDelete: false,
          isShowEdit: false,
          isShowButtonMenu: false,
          isShowCommandRowOnHover: false,
          isPageExpand: false,
          isShowViewDetail: false,
          isShowColumnCheck: false,
        },
      },
    });
  }
  protected getSelectedID($event: any) {
    this.selectedItem = $event;
  }

  protected onAddAppraisal() {
    const drawerRef = this._drawerService.create<EvaPerformanceAppraisalsFormComponent>({
      nzTitle: this._translate.instant('objEval.EvaPerformanceAppraisals.addNew'),
      nzContent: EvaPerformanceAppraisalsFormComponent,
      nzClosable: true,
      nzMaskClosable: false,
      nzMask: true,
      nzData: {},
      nzWrapClassName: 'crud-modal',
      nzWidth: '30vw',
    });
    drawerRef.afterClose.subscribe((result) => {
      if (result && result.isReloadData) {
        this.gridControl.vnrReadGrid();
      }
    });
  }
  public setDataFilter(data: any): void {
    this._dataFormSearch = data;
    this.gridControl.setDataFilter(data);
  }

  public reloadGridData(): void {
    this.gridControl.vnrReadGrid();
  }

  public getSelectedIDs() {
    return this.selectedItem || [];
  }

  protected onChangesFilterQuick(data: any) {
    this.gridControl.vnrReadGrid();
  }
  protected onChangeStatusPublic($event: any, item: any) {
    this._commonService.message({ message: 'Tính năng đang phát triển', type: 'success' });
  }
  protected onLoadData($event) {
    this.gridControl.onExpandAll();
  }
  protected onAdditional($event: any) {
    this._commonService.message({ message: 'Tính năng đang phát triển', type: 'success' });
  }
  protected onEdit($event: any, dataItem: any) {
    const drawerRef = this._drawerService.create<EvaPerformanceAppraisalsFormComponent>({
      nzTitle: this._translate.instant('objEval.EvaPerformanceAppraisals.edit'),
      nzContent: EvaPerformanceAppraisalsFormComponent,
      nzClosable: true,
      nzMaskClosable: false,
      nzMask: true,
      nzData: {
        paramEditID: dataItem?.ID,
      },
      nzWrapClassName: 'crud-modal',
      nzWidth: '30vw',
    });
    drawerRef.afterClose.subscribe((result) => {
      if (result && result.isReloadData) {
        this.gridControl.vnrReadGrid();
      }
    });
  }
  protected onClone($event: any) {
    this._commonService.message({ message: 'Tính năng đang phát triển', type: 'success' });
  }
  protected onDelete($event: any) {
    this._commonService.message({ message: 'Tính năng đang phát triển', type: 'success' });
  }

  protected getStatusPublic(status: string) {
    return this._statusFormat[status] || 'default';
  }
  protected onViewDetail($event: any, dataItem: any) {
    const queryParams: any = { ID: dataItem.ID };
    this._router.navigate(['/objEval/eva-appraisals/performance-appraisals/detail'], {
      state: this.routerState,
      relativeTo: this.route,
      queryParams: queryParams,
      queryParamsHandling: 'merge',
    });
  }
}
