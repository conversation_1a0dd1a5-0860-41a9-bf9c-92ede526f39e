<div class="drawer-body" [formGroup]="form">
  <!-- Thông tin nhân viên -->
  <div class="section-container">
    <h3 class="section-title">Nhân viên báo cáo</h3>
    <div class="info-grid">
      <div class="d-flex align-items-center" style="gap: 8px">
        <app-vnr-letters-avatar
          [avatarName]="dataItem['Reporter']['name']"
          [circular]="true"
          [width]="32"
          [src]="dataItem['Reporter']['avatar']"
        ></app-vnr-letters-avatar>
        <div>
          <div class="font-weight-bold">
            {{ dataItem['Reporter']['name'] }} - {{ dataItem['Reporter']['code'] }}
          </div>
          <div class="d-flex">
            <div>
              <vnr-tag [vnrTitle]="dataItem['Department']" [vnrColor]="'default'"></vnr-tag>
            </div>
            <div>
              <vnr-tag [vnrTitle]="dataItem['Position']" [vnrColor]="'default'"></vnr-tag>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="section-container">
    <h3 class="section-title">Danh sách mục tiêu</h3>
    <div class="table-responsive">
      <table class="table table-bordered table-striped custom-goal-table">
        <thead>
          <tr>
            <th style="width: 220px">Tên mục tiêu</th>
            <th style="width: 180px">Người thực hiện</th>
            <th style="width: 160px">Phòng ban</th>
            <th style="width: 120px; white-space: nowrap">Kỳ thực hiện</th>
            <th style="width: 120px; white-space: nowrap">Tiến độ</th>
            <th style="width: 140px; white-space: nowrap">Mục tiêu</th>
            <th style="width: 140px; white-space: nowrap">Kết quả mới nhất</th>
            <th style="width: 140px; white-space: nowrap">Mục tiêu năm</th>
            <th style="width: 140px; white-space: nowrap">Lũy kế</th>
            <th style="width: 180px">Nhận xét</th>
            <th style="width: 180px">Tệp đính kèm</th>
            <th style="width: 180px">Chỉ đạo</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let goal of goals">
            <td style="width: 220px; word-break: break-word">
              <a href="javascript:void(0)" (click)="openGoalDetail(goal)" style="color: #1677ff; cursor: pointer;">
                {{ goal.goalName }}
              </a>
            </td>
            <td style="width: 180px">
              <div class="d-flex align-items-center">
                <app-vnr-letters-avatar
                  [avatarName]="goal.representative"
                  [circular]="true"
                  [width]="32"
                  [src]="goal.avatar"
                ></app-vnr-letters-avatar>
                <span class="ml-2">{{ goal.representative }}</span>
              </div>
            </td>
            <td style="width: 160px">{{ goal.department }}</td>
            <td style="width: 120px; white-space: nowrap">{{ goal.period }}</td>
            <td style="width: 120px; white-space: nowrap">
              <nz-progress [nzPercent]="goal.progress"></nz-progress>
            </td>
            <td style="width: 140px; white-space: nowrap">
              {{ goal.target | targetFormat : goal.unit }}
            </td>
            <td style="width: 140px; white-space: nowrap">
              {{ goal.latestResult | targetFormat : goal.unit }}
            </td>
            <td style="width: 140px; white-space: nowrap">
              {{ goal.yearTarget | targetFormat : goal.unit }}
            </td>
            <td style="width: 140px; white-space: nowrap">
              {{ goal.accumulated | targetFormat : goal.unit }}
            </td>
            <td style="width: 180px">
              <span>{{ goal.comment }}</span>
              <!-- <input type="text" class="form-control" placeholder="Nhận xét" /> -->
            </td>
            <td style="width: 180px">
              <a
                *ngIf="goal.attachment"
                [href]="goal.attachment.url"
                target="_blank"
                class="btn-link"
              >
                <i class="fa fa-download"></i> {{ goal.attachment.name }}
              </a>
            </td>
            <td style="width: 180px">
              <!-- {{ goal.directive }} -->
              <input type="text" class="form-control" placeholder="Nhập chỉ đạo" />
            </td>
          </tr>
          <tr *ngIf="goals.length === 0">
            <td colspan="11" class="text-center">Không có dữ liệu</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <div class="section-container">
    <h3 class="section-title">Nội dung báo cáo</h3>
    <div class="info-grid" style="grid-template-columns: auto 1fr">
      <span class="info-label align-self-baseline"><strong>1. Thành tựu trong tuần</strong></span>
      <ul class="mb-3">
        <li>Doanh thu đạt 2.100 tỷ đồng, tăng 12% trong tuần qua</li>
        <li>Hoàn thành đơn hàng 5000 xe sedan cho Đông Nam Á.</li>
        <li>Ra mắt xe điện mới, nhận phản hồi tích cực.</li>
      </ul>
      <span class="info-label align-self-baseline"><strong>2. Khó khăn, rủi ro</strong></span>
      <ul class="mb-3">
        <li>Thiếu chip bán dẫn, chậm sản xuất xe cao cấp.</li>
        <li>Giá nguyên liệu tăng, chi phí sản xuất tăng 10%.</li>
        <li>Cạnh tranh từ các đối thủ giá rẻ.</li>
      </ul>
      <span class="info-label align-self-baseline"><strong>3. Kế hoạch tiếp theo</strong></span>
      <ul>
        <li>Ổn định nguồn cung chip, tìm nhà cung cấp mới.</li>
        <li>Quảng bá xe điện qua truyền thông và triển lãm.</li>
        <li>Đầu tư 20 tỷ đồng phát triển pin xe điện</li>
      </ul>
    </div>
  </div>

  <!-- Chỉ đạo chung -->
  <div class="section-container">
    <h3 class="section-title">Chỉ đạo chung</h3>
    <div class="info-grid">
      <div class="form-item">
        <vnr-textarea
          id="generalDirective"
          formControlName="generalDirective"
          [builder]="generalDirectiveBuilder"
        ></vnr-textarea>

        <button
          type="button"
          class="btn btn-primary"
          style="border-radius: 6px; width: fit-content"
        >
          <i class="fa-light fa-floppy-disk mr-2"></i> Lưu
        </button>
      </div>
    </div>
  </div>

  <div>
    <h3 class="section-title">Bình luận</h3>
    <app-comment
      [feedbacks]="dataItem?.feedbacks || []"
      [getAvatarText]="getAvatarText.bind(this)"
      (sendFeedback)="onSendFeedback($event)"
    ></app-comment>
  </div>
</div>

<!-- <div class="drawer-footer"> -->
<!-- <vnr-button-new (vnrClick)="closeDrawer()" [builder]="cancelBuilder">
  </vnr-button-new>
  <vnr-button-new (vnrClick)="saveDraft()" [builder]="saveDraftBuilder">
  </vnr-button-new>
  <vnr-button-new (vnrClick)="save()" [builder]="saveBuilder">
  </vnr-button-new> -->
<!-- </div> -->
