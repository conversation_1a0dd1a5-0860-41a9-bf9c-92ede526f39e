import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  TemplateRef,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';
import {
  VnrButtonFactory,
  VnrButtonNewBuilder,
  VnrButtonNewComponent,
  VnrListviewNewBuilder,
  VnrListviewNewComponent,
  VnrToolbarNewBuilder,
  VnrToolbarNewComponent,
  vnrUtilities,
} from '@hrm-frontend-workspace/vnr-module';
import { evaCriteriaGroupColumns } from '../../data/column.data';

import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonService } from '@hrm-frontend-workspace/core';
import { ResponseStatus } from '@hrm-frontend-workspace/models';
import { VnrTagComponent } from '@hrm-frontend-workspace/ui';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzDrawerService } from 'ng-zorro-antd/drawer';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzModalService } from 'ng-zorro-antd/modal';
import { gridEvaCriteriaGroupDefineDataSource } from '../../data/datasource.data';
import { EvaCriteriaFacade } from '../../facade/eva-criteria.facade';
import { EvaCriteriaGridListComponent } from './components/eva-criteria-grid/eva-criteria-grid-list.component';
import { EvaCriteriaGroupCreateComponent } from '../../../eva-criteria-group/components/eva-criteria-group-create/eva-criteria-group-create.component';
import { EvaCriteriaGroupFacade } from '../../../eva-criteria-group/facade/eva-criteria-group.facade';
import { EvaCriteriaCreateComponent } from '../eva-criteria-create/eva-criteria-create.component';

@Component({
  selector: 'eva-criteria-grid',
  templateUrl: './eva-criteria-grid.component.html',
  styleUrls: ['./eva-criteria-grid.component.scss'],
  imports: [
    FormsModule,
    ReactiveFormsModule,
    CommonModule,
    VnrToolbarNewComponent,
    VnrListviewNewComponent,
    VnrTagComponent,
    TranslateModule,
    VnrButtonNewComponent,
    NzButtonModule,
    NzGridModule,
    EvaCriteriaGridListComponent,
  ],
})
export class EvaCriteriaGridComponent implements OnInit, OnChanges {
  @Output() onSelectCriteriaGroup = new EventEmitter<any>();
  @Output() onSelectCriteria = new EventEmitter<any>();

  @ViewChild('vnrGrid', { static: true }) gridControl: VnrListviewNewComponent;
  @ViewChild('templateType', { static: true }) private _templateType: TemplateRef<any>;
  @ViewChild('tplContentDelete', { static: true }) private _tplContentDelete: TemplateRef<any>;
  @ViewChild('tplFooterDelete', { static: true })
  private _tplFooterDelete: TemplateRef<any>;
  @ViewChild('tplFooterDeleteCriteria', { static: true })
  private _tplFooterDeleteCriteria: TemplateRef<any>;
  @ViewChild('tplContentViewDetail', { static: true })
  private _tplContentViewDetail: TemplateRef<any>;
  @ViewChild('tplFooterViewDetail', { static: true })
  private _tplFooterViewDetail: TemplateRef<any>;
  private _btnFactory: VnrButtonFactory = VnrButtonFactory.init();
  protected selectedItem = null;
  private _selectedIDs = [];
  private _columnKey = 'Id';
  private _criteriaTypeID: string = null;
  private _criteriaGroupID: string = null;
  protected builderbtnOpenColumnCheck: VnrButtonNewBuilder;
  protected builderbtnCancelOpenColumnCheck: VnrButtonNewBuilder;
  protected builderbtnExpandAll: VnrButtonNewBuilder;
  protected builderbtnCollapseAll: VnrButtonNewBuilder;
  protected builderbtnAddCriteriaItem: VnrButtonNewBuilder;
  protected isShowOpenColumnCheck: boolean = true;
  protected isShowSelectAll: boolean = true;
  protected isShowExpandAll: boolean = true;

  protected builderGrid: VnrListviewNewBuilder;
  protected builderToolbar: VnrToolbarNewBuilder;
  protected builderButtonCustom: VnrButtonNewBuilder;
  protected builderbtnDelete: VnrButtonNewBuilder;
  protected builderbtnEdit: VnrButtonNewBuilder;
  protected builderbtnDoNo: VnrButtonNewBuilder;
  protected builderbtnDoYes: VnrButtonNewBuilder;
  protected gridName = 'criteria-grid';
  protected dataLocal = gridEvaCriteriaGroupDefineDataSource;
  protected columns = evaCriteriaGroupColumns;
  protected dataFormSearch = {};

  constructor(
    private _modalService: NzModalService,
    private _drawerService: NzDrawerService,
    private _commonService: CommonService,
    private _evaCriteriaFacade: EvaCriteriaFacade,
    private _evaCriteriaGroupFacade: EvaCriteriaGroupFacade,
    private _vc: ViewContainerRef,
    private _translate: TranslateService,
  ) {}
  //#region Life cycle
  ngOnInit() {
    this.builderButtonComponent();
    this.builderGridComponent();
    this.builderToolbarComponent();
  }
  ngOnChanges(changes: SimpleChanges): void {}

  //#endregion
  //#region Builder
  private builderButtonComponent() {
    this.builderbtnDelete = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'common.delete',
      options: {
        style: 'outline-danger',
        icon: {
          fontIcon: 'fas fa-trash-alt',
        },
      },
    });
    this.builderbtnEdit = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'common.edit',
      options: {
        style: 'default',
        icon: {
          fontIcon: 'edit',
        },
      },
    });
    this.builderbtnDoYes = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'common.modal.buttonOk',
      options: {
        style: 'danger',
      },
    });
    this.builderbtnDoNo = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'common.modal.buttonNo',
      options: {
        style: 'default',
      },
    });

    this.builderbtnOpenColumnCheck = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'objEval.EvaCriteria.selectCriteriaGroup',
      options: {
        classNames: ['mr-1'],
        style: 'default',
        icon: {
          fontIcon: 'check-square',
        },
      },
    });
    this.builderbtnCancelOpenColumnCheck = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'objEval.EvaCriteria.cancelSelect',
      options: {
        classNames: ['mr-1'],
        style: 'default',
        icon: {
          fontIcon: 'stop',
        },
      },
    });
    this.builderbtnExpandAll = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'common.grid.buttonAction.ExpandAll',
      options: {
        classNames: ['mr-1'],
        style: 'default',
        icon: {
          fontIcon: 'arrows-alt',
        },
      },
    });
    this.builderbtnCollapseAll = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'common.grid.buttonAction.CollapseAll',
      options: {
        classNames: ['mr-1'],
        style: 'default',
        icon: {
          fontIcon: 'shrink',
        },
      },
    });
    this.builderbtnAddCriteriaItem = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'objEval.EvaCriteria.btnAddCriteriaDetail',
      options: {
        classNames: ['mr-1'],
        style: 'outline-primary',
        icon: {
          fontIcon: 'plus',
        },
      },
    });
  }
  private builderToolbarComponent() {
    this.builderToolbar = new VnrToolbarNewBuilder({
      isShowConfig: false,
      gridName: this.gridName,
      isSupperAdmin: false,
      permission: '',
      screenName: 'criteria-grid',
      storeName: '',
      options: {
        configButtonChangeColumn: {
          isShow: false,
        },
        configButtonExport: {
          isShowBtnExcelAll: false,
          isShowBtnWord: false,
          isShowBtnExcelByTemplate: false,
        },
        configButtonDelete: {
          isShow: false,
        },
        configQuickSearch: {
          isShow: false,
        },
        configFilterAdvanceQuick: {
          isShow: false,
        },
        isSetBackground: false,
        buttonMoreFlexDirection: 'inherit',
        isExpanded: false,
      },
    });
  }
  private builderGridComponent() {
    this.builderGrid = new VnrListviewNewBuilder({
      options: {
        configHeightGrid: {
          isAllowCalcRowHeight: true,
        },
        configSelectable: {
          columnKey: this._columnKey,
        },
        configShowHide: {
          isPageExpand: false,
          isShowViewDetail: true,
          isShowColumnCheck: false,
        },
      },
    });
  }
  //#endregion
  //#region Event for grid
  protected onGridSelectedID($event: any) {
    this.onSelectCriteriaGroup.emit($event);
  }
  protected onGridOpenDetail($event: any) {
    this.selectedItem = $event;
    this._selectedIDs = [$event[this._columnKey]];
    const modalRef = this._modalService.create({
      nzTitle: this._translate.instant('objEval.EvaCriteriaGroup.CriteriaGroup'),
      nzContent: this._tplContentViewDetail,
      nzFooter: this._tplFooterViewDetail,
      nzMask: true,
      nzWidth: '600px',
      nzMaskClosable: true,
      nzClosable: true,
    });
    modalRef.afterClose.subscribe((modal) => {
      if (modal?.type === 'Confirm') {
        this.reloadGridData();
      }
    });
  }
  protected onGridEdit($event: any) {
    let paramEditID = $event[this._columnKey];
    this.onAddCriteriaGroup(null, paramEditID);
  }
  protected onGridDelete($event: any) {
    this._selectedIDs = [$event[this._columnKey]];
    this.onOpenConfirmDeleteCriteriaGroup();
  }
  protected onExpandAll($event) {
    this.gridControl.onExpandAll();
    this.isShowExpandAll = !this.isShowExpandAll;
  }
  protected onCollapseAll($event) {
    this.gridControl.onCollapseAll();
    this.isShowExpandAll = !this.isShowExpandAll;
  }
  protected onOpenColumnCheck($event) {
    this.gridControl.onOpenColumnCheck();
    this.isShowOpenColumnCheck = !this.isShowOpenColumnCheck;
  }
  protected onCancelOpenColumnCheck($event) {
    this.gridControl.onCancelOpenColumnCheck();
    this.gridControl.onCancelSelectAll();
    this.isShowOpenColumnCheck = !this.isShowOpenColumnCheck;
    this.onSelectCriteriaGroup.emit([]);
  }
  protected onLoadData($event) {
    this.isShowExpandAll = true;
  }
  //#endregion
  //#region Event Criteria
  public onAddCriteriaDetailFromGroup($event: any, dataItem: any) {
    this._criteriaTypeID = dataItem?.CriteriaTypeId;
    this._criteriaGroupID = dataItem?.Id;
    this.onAddCriteria($event);
  }
  public onAddNewCriteria($event: any) {
    this._criteriaTypeID = null;
    this._criteriaGroupID = null;
    this.onAddCriteria($event);
  }
  public onEditCriteria($event: any, _paramEditID: string) {
    this._criteriaTypeID = null;
    this._criteriaGroupID = null;
    this.onAddCriteria($event, _paramEditID);
  }
  public onDeleteCriteria($event: any, selectedIDs: any) {
    this._selectedIDs = selectedIDs;
    this.onOpenConfirmDeleteCriteria();
  }
  protected onAddCriteria($event: any, _paramEditID?: string) {
    $event?.stopPropagation();
    let title =
      _paramEditID != null
        ? this._translate.instant('objEval.EvaCriteria.EditCriteria')
        : this._translate.instant('objEval.EvaCriteria.AddCriteria');
    let drawerRef = this._drawerService.create({
      nzTitle: title,
      nzContent: EvaCriteriaCreateComponent,
      nzContentParams: {
        paramEditID: _paramEditID,
        criteriaTypeID: this._criteriaTypeID,
        criteriaGroupID: this._criteriaGroupID,
      },
      nzMaskClosable: false,
      nzWidth: '600px',
      nzWrapClassName: 'crud-modal',
      nzClosable: true,
      nzMask: true,
    });
    drawerRef.afterClose.subscribe((res) => {
      if (res && res['isReloadData'] == true) {
        this.gridControl.vnrReloadGrid();
      }
    });
  }
  protected onConfirmEditCriteriaGroup($modalRef: any) {
    $modalRef.close({
      type: 'Edit',
    });
    this.onGridEdit(this.selectedItem);
  }
  protected onOpenConfirmDeleteCriteria() {
    const modalRef = this._modalService.create({
      nzIconType: '',
      nzTitle: '',
      nzContent: this._tplContentDelete,
      nzFooter: this._tplFooterDeleteCriteria,
      nzMaskClosable: true,
      nzClosable: true,
    });
    modalRef.afterClose.subscribe((modal) => {
      if (modal?.type === 'Reload') {
        this.reloadGridData();
      }
    });
  }
  protected onConfirmDeleteCriteria($event: any, $modalRef: any) {
    $event?.stopPropagation();
    if (!this._selectedIDs || this._selectedIDs.length <= 0) {
      this._commonService.message({ message: 'common.grid.noSelectedId_Delete', type: 'error' });
      return;
    }
    this._evaCriteriaFacade.delete(this._selectedIDs).subscribe((res) => {
      if (res && res.Status === ResponseStatus.SUCCESS) {
        this._commonService.message({
          message: this._translate.instant('common.message.deletedRowOfData', {
            n: this._selectedIDs.length,
          }),
          type: 'success',
        });
        this.gridControl.onCancelSelectAll();
        $modalRef.close({
          type: 'Reload',
        });
      } else {
        this._commonService.message({
          message: res.Message || this._translate.instant('common.notification.descErr500'),
          type: 'error',
        });
      }
    });
  }

  //#endregion
  //#region Event Criteria Group
  public onAddNewCriteriaGroup($event: any) {
    this.onAddCriteriaGroup($event);
  }
  protected onAddCriteriaGroup($event: any, _paramEditID?: string) {
    $event?.stopPropagation();
    let title =
      _paramEditID != null
        ? this._translate.instant('objEval.EvaCriteria.EditCriteria')
        : this._translate.instant('objEval.EvaCriteria.AddCriteria');
    const modalRef = this._modalService.create({
      nzTitle: title,
      nzContent: EvaCriteriaGroupCreateComponent,
      nzViewContainerRef: this._vc,
      nzClosable: true,
      nzMaskClosable: true,
      nzMask: true,
      nzWidth: '600px',
      nzBodyStyle: { minheight: `512px`, paddingTop: '10px' },
      nzFooter: null,
      nzData: {
        paramEditID: _paramEditID,
      },
    });
    modalRef.afterClose.subscribe((modal) => {
      if (modal?.type === 'Confirm') {
        this.reloadGridData();
      }
    });
  }
  protected onOpenConfirmDeleteCriteriaGroupFromDetail($modalRef: any) {
    $modalRef.close({
      type: 'ConfirmDelete',
    });
    this.onOpenConfirmDeleteCriteriaGroup();
  }
  protected onOpenConfirmDeleteCriteriaGroup() {
    const modalRef = this._modalService.create({
      nzIconType: '',
      nzTitle: '',
      nzContent: this._tplContentDelete,
      nzFooter: this._tplFooterDelete,
      nzMaskClosable: true,
      nzClosable: true,
    });
    modalRef.afterClose.subscribe((modal) => {
      if (modal?.type === 'Reload') {
        this.reloadGridData();
      }
    });
  }
  public onDeleteCriteriaGroup($event: any, selectedIDs: any) {
    this._selectedIDs = selectedIDs;
    this.onOpenConfirmDeleteCriteriaGroup();
  }
  protected onConfirmDeleteCriteriaGroup($event: any, $modalRef: any) {
    $event?.stopPropagation();
    if (!this._selectedIDs || this._selectedIDs.length <= 0) {
      this._commonService.message({ message: 'common.grid.noSelectedId_Delete', type: 'error' });
      return;
    }
    this._evaCriteriaGroupFacade.delete(this._selectedIDs).subscribe((res) => {
      if (res && res.Status === ResponseStatus.SUCCESS) {
        this.gridControl.onCancelSelectAll();
        this._commonService.message({
          message: this._translate.instant('common.message.deletedRowOfData', {
            n: this._selectedIDs.length,
          }),
          type: 'success',
        });
        $modalRef.close({
          type: 'Reload',
        });
        this.gridControl.onCancelSelectAll();
      } else {
        this._commonService.message({
          message: res.Message || this._translate.instant('common.notification.descErr500'),
          type: 'error',
        });
      }
    });
  }
  //#endregion
  protected onSelectItemCriteria($event: any) {
    this.onSelectCriteria.emit($event);
  }
  public reloadGridData(): void {
    this.gridControl.vnrReadGrid();
  }
}
