<vnr-toolbar-new [builder]="builderToolbar">
  <vnr-button-new
    rightToolbar
    [builder]="builderbtnOpenColumnCheck"
    [isShow]="isShowOpenColumnCheck"
    (vnrClick)="onOpenColumnCheck($event)"
  ></vnr-button-new>
  <vnr-button-new
    rightToolbar
    [builder]="builderbtnCancelOpenColumnCheck"
    [isShow]="!isShowOpenColumnCheck"
    (vnrClick)="onCancelOpenColumnCheck($event)"
  ></vnr-button-new>
  <vnr-button-new
    rightToolbar
    [builder]="builderbtnExpandAll"
    [isShow]="isShowExpandAll"
    (vnrClick)="onExpandAll($event)"
  ></vnr-button-new>
  <vnr-button-new
    rightToolbar
    [builder]="builderbtnCollapseAll"
    [isShow]="!isShowExpandAll"
    (vnrClick)="onCollapseAll($event)"
  ></vnr-button-new>
</vnr-toolbar-new>
<vnr-listview-new
  #vnrGrid
  [builder]="builderGrid"
  [gridName]="gridName"
  [dataLocal]="dataLocal"
  [columns]="columns"
  [dataFormSearch]="dataFormSearch"
  [rowDetailTemplate]="tplMasterDetail"
  [rowActionsTemplate]="rowActionsTemplate"
  [columnHeaderTemplate]="tplHeader"
  (vnrLoadData)="onLoadData($event)"
  (vnrEdit)="onGridEdit($event)"
  (vnrDelete)="onGridDelete($event)"
  (getSelectedID)="onGridSelectedID($event)"
  (vnrViewDetails)="onGridOpenDetail($event)"
>
</vnr-listview-new>

<ng-template #tplMasterDetail let-dataItem>
  <goal-template-grid-list
    [columnMaster]="dataItem"
    (onSelectItem)="onSelectItemGoal($event)"
  ></goal-template-grid-list>
</ng-template>
<ng-template #tplHeader let-dataItem>
  <div class="grid-item-header">
    <span class="grid-item-header__title"
      >{{ dataItem.GroupName }}
      <vnr-tag
        [vnrStatus]="'E_WAITING'"
        [vnrTitle]="
          'objEval.GoalTemplate.tagGoalItemsPerPage' | translate : { n: dataItem.TotalItems }
        "
      ></vnr-tag>
    </span>
    <span class="grid-item-header__code">{{ dataItem.GroupCode }}</span>
  </div>
</ng-template>
<ng-template #rowActionsTemplate let-dataItem>
  <vnr-button-new
    [builder]="builderbtnAddGoalItem"
    (vnrClick)="onAddGoalDetailFromGroup($event, dataItem)"
  ></vnr-button-new>
</ng-template>
<ng-template #tplContentDelete let-params>
  <div class="template-delete">
    <div class="template-delete__header">
      <img src="assets/icon/vnr-icon/icon-confirm-delete.png" alt="delete" />
    </div>
    <div class="template-delete__content">
      <div class="template-delete__content-title">
        <span>{{ 'common.modal.confirmDeleteData' | translate }}</span>
      </div>
      <div class="template-delete__content-description">
        {{ 'common.modal.deletedDataCannotBeRecovered' | translate }}
      </div>
    </div>
  </div>
</ng-template>
<ng-template #tplFooterDelete let-ref="modalRef">
  <div class="template-view-detail__action">
    <vnr-button-new [builder]="builderbtnDoNo" (vnrClick)="ref.destroy()"></vnr-button-new>
    <vnr-button-new
      [builder]="builderbtnDoYes"
      (vnrClick)="onConfirmDeleteGoalGroup($event, ref)"
    ></vnr-button-new>
  </div>
</ng-template>
<ng-template #tplFooterDeleteGoal let-ref="modalRef">
  <div class="template-view-detail__action">
    <vnr-button-new [builder]="builderbtnDoNo" (vnrClick)="ref.destroy()"></vnr-button-new>
    <vnr-button-new
      [builder]="builderbtnDoYes"
      (vnrClick)="onConfirmDeleteGoal($event, ref)"
    ></vnr-button-new>
  </div>
</ng-template>
<ng-template #tplContentViewDetail let-params>
  <div class="template-view-detail">
    <div nz-row [nzGutter]="[20, 24]">
      <div nz-col class="gutter-row" nzSpan="7">
        {{ 'objEval.GoalTemplate.GroupName' | translate }}
      </div>
      <div nz-col class="gutter-row" nzSpan="17">
        {{ selectedItem.GroupName }}
      </div>
    </div>
    <div nz-row [nzGutter]="[20, 24]">
      <div nz-col class="gutter-row" nzSpan="7">
        {{ 'objEval.GoalTemplate.GroupCode' | translate }}
      </div>
      <div nz-col class="gutter-row" nzSpan="17">
        {{ selectedItem.GroupCode }}
      </div>
    </div>
    <div nz-row [nzGutter]="[20, 24]">
      <div nz-col class="gutter-row" nzSpan="7">
        {{ 'objEval.GoalTemplate.Description' | translate }}
      </div>
      <div nz-col class="gutter-row" nzSpan="17">
        {{ selectedItem.Description }}
      </div>
    </div>
  </div>
</ng-template>
<ng-template #tplFooterViewDetail let-ref="modalRef">
  <div class="template-view-detail__action">
    <vnr-button-new
      [builder]="builderbtnDelete"
      (vnrClick)="onConfirmDeleteGoalGroupFromDetail(ref)"
    ></vnr-button-new>
    <vnr-button-new
      [builder]="builderbtnEdit"
      (vnrClick)="onConfirmEditGoalGroup(ref)"
    ></vnr-button-new>
  </div>
</ng-template>
