import { AbstractGridOptionBuilder } from '../../../models/abstract-grid-option-builder.model';

export class VnrGridNewOptionBuilder extends AbstractGridOptionBuilder {
  constructor(options?: VnrGridNewOptionBuilder) {
    super();
    if (!options) {
      options = {};
    }
    options.isResizable ??= true;
    options.isBorderGrid ??= true;
    options.queryOption = {
      skip: options.queryOption?.skip ?? 0,
      take: options.queryOption?.take ?? 25,
      group: options.queryOption?.group ?? [],
      sort: options.queryOption?.sort ?? [],
      filter: options.queryOption?.filter ?? { filters: [], logic: 'and' },
    };
    options.configEvent = {
      rowClass: options.configEvent?.rowClass ?? (() => ''),
    };
    options.configShowHide = {
      isShowEdit: options.configShowHide?.isShowEdit ?? true,
      isShowDelete: options.configShowHide?.isShowDelete ?? true,
      isShowViewDetail: options.configShowHide?.isShowViewDetail ?? false,
      isShowColumnCheck: options.configShowHide?.isShowColumnCheck ?? true,
      isShowColumnGroupCheck: options.configShowHide?.isShowColumnGroupCheck ?? false,
      isPageExpand: options.configShowHide?.isPageExpand ?? false,
      isShowRefresh: options.configShowHide?.isShowRefresh ?? false,
      isShowButtonMenu: options.configShowHide?.isShowButtonMenu ?? false,
      isExpendButtonLoadMore: options.configShowHide?.isExpendButtonLoadMore ?? false,
      isShowTempColumn: options.configShowHide?.isShowTempColumn ?? false,
      isShowMenuColumn: options.configShowHide?.isShowMenuColumn ?? false,
    };
    options.configApiSupport = {
      apiGetColumn: {
        url: options.configApiSupport?.apiGetColumn?.url ?? '',
        method: options.configApiSupport?.apiGetColumn?.method ?? 'get',
      },
      apiDownload: {
        url: options.configApiSupport?.apiDownload?.url ?? '',
        method: options.configApiSupport?.apiDownload?.method ?? 'get',
      },
      apiExport: {
        url: options.configApiSupport?.apiExport?.url ?? '',
        method: options.configApiSupport?.apiExport?.method ?? 'post',
      },
      apiExportAll: {
        url: options.configApiSupport?.apiExportAll?.url ?? '',
        method: options.configApiSupport?.apiExportAll?.method ?? 'post',
      },
    };
    options.configTitle = {
      alignment: options.configTitle?.alignment ?? 'Center',
      prefixTitleColumn: options.configTitle?.prefixTitleColumn ?? 'LevelEva',
    };
    options.configHeightGrid = {
      gridHeight: options.configHeightGrid?.gridHeight ?? 400,
      isAllowCalcRowHeight: options.configHeightGrid?.isAllowCalcRowHeight ?? false,
      rowScrollHorizontal: options.configHeightGrid?.rowScrollHorizontal ?? 8,
      rowThreshold: options.configHeightGrid?.rowThreshold ?? 8,
      isHeightByRow: options.configHeightGrid?.isHeightByRow ?? false,
      rowHeight: options.configHeightGrid?.rowHeight ?? null,
    };
    options.configColumnSchema = {
      fieldBoolean: options.configColumnSchema?.fieldBoolean ?? [],
      fieldDate: options.configColumnSchema?.fieldDate ?? [],
      fieldNumber: options.configColumnSchema?.fieldNumber ?? [],
      fieldNumberMoney: options.configColumnSchema?.fieldNumberMoney ?? [],
    };
    options.configSelectable = {
      columnKey: options.configSelectable?.columnKey ?? 'ID',
      isCheckboxOnly: options.configSelectable?.isCheckboxOnly ?? true,
      isEnabled: options.configSelectable?.isEnabled ?? true,
      mode: options.configSelectable?.mode ?? 'multiple',
    };
    options.configPageable = {
      buttonCount: options.configPageable?.buttonCount ?? 10,
      isPreviousNext: options.configPageable?.isPreviousNext ?? true,
      isShowPageSize: options.configPageable?.isShowPageSize ?? false,
      isShowInfo: options.configPageable?.isShowInfo ?? true,
      isResponsive: options.configPageable?.isResponsive ?? false,
      position: options.configPageable?.position ?? 'bottom',
      pageSizes: options.configPageable?.pageSizes ?? [10, 20, 50, 100],
      type: options.configPageable?.type ?? 'numeric',
    };
    options.configGroupable = {
      emptyText: options.configGroupable?.emptyText ?? 'vnrcontrol.common.dropGroupGrid',
      isEnabled: options.configGroupable?.isEnabled ?? false,
      isShowFooter: options.configGroupable?.isShowFooter ?? false,
    };
    options.configCommandColumn = {
      isEnabledMenuAction: options.configCommandColumn?.isEnabledMenuAction ?? false,
      width: options.configCommandColumn?.width ?? 100,
    };
    options.configIndexColumn = {
      isLocked: options.configIndexColumn?.isLocked ?? false,
      isShow: options.configIndexColumn?.isShow ?? false,
      width: options.configIndexColumn?.width ?? 76,
    };
    options.configExpanded = {
      groupKey: options.configExpanded?.groupKey ?? [],
      isExpanded: options.configExpanded?.isExpanded ?? true,
    };
    options.configExpandDetail = {
      by: options.configExpandDetail?.by,
      keys: options.configExpandDetail?.keys ?? [],
    };
    Object.assign(this, options);
  }
}
