<button nz-button nzType="warning" class="mr-2 ant-btn ant-btn-warning" (click)="reload()">
  IsSupperAdmin = {{ isSupperAdmin }}
</button>
<button nz-button nzType="warning" class="mr-2 ant-btn ant-btn-warning" (click)="reload1()">
  IsSupperAdmin = {{ isSupperAdmin }}
</button>
<div class="d-flex justify-content-end mb-2">
  <button
    nz-button
    nzType="primary"
    class="mr-2 ant-btn ant-btn-primary"
    #anchor
    (click)="toggleChangeColumn(false)"
  >
    Đổi cột v.1
  </button>
  <button
    nz-button
    nzType="primary"
    type="button"
    class="mr-2 ant-btn ant-btn-primary"
    #anchor
    (click)="toggleChangeColumn(true)"
  >
    Đổi cột v.2
  </button>
</div>
<vnr-toolbar-new
  class="border-0 --custom-bg width-100p manage-personnel-records-grids__toolbar"
  [builder]="builderToolbar"
  (vnrChangeColumn)="toggleChangeColumn($event)"
>
</vnr-toolbar-new>
<vnr-grid-new
  class="grid-new"
  #vnrGrid
  [builder]="builderGrid"
  [dataLocal]="dataLocal"
  [columns]="columns"
  [gridName]="gridName"
  [isSupperAdmin]="isSupperAdmin"
  [isChangeColumnNew]="true"
  [columnTemplates]="listColumnTemplates"
  (getSelectedID)="getSelectedID($event)"
  (getDataItem)="getDataItem($event)"
  (vnrDoubleClick)="onOpenDetail($event)"
  (vnrViewModeGrid)="onVnRViewModeGrid($event)"
  (vnrEdit)="onGridEdit($event)"
  (vnrDelete)="onGridDelete($event)"
  (vnrViewDetails)="onGridViewDetail($event)"
  (vnrCellClick)="onGridCellClick($event)"
>
</vnr-grid-new>

<ng-template #tplCustomButtonAction let-dataItem> </ng-template>
<ng-template #tplCustomTemplateByColumn let-dataItem let-column="columnItem" let-field="field">
  <ng-container [ngSwitch]="column['name']">
    <span class="--has-custom-template" *ngSwitchCase="'ProfileName'">
      <span *ngIf="dataItem?.ProfileName">
        <div class="vnrGrid-manage-personnel-records__profiles">
          <div class="d-flex flex-column justify-content-center">
            <div class="vnrGrid-manage-personnel-records__name">
              {{ dataItem?.ProfileName }}
            </div>
          </div>
        </div>
      </span>
    </span>
    <span class="--has-custom-template" *ngSwitchCase="'StatusView'">
      {{ dataItem?.StatusView }} - {{ dataItem?.Status }}
    </span>
    <span class="--has-custom-template" *ngSwitchDefault>
      {{ dataItem[column['name']] }}
    </span>
  </ng-container>
</ng-template>
<ng-template #templateStatusSync let-dataItem>
  <span class="bg-success p-2">{{ dataItem?.StatusSyn | translate }}</span>
</ng-template>
<ng-template #templateCellPhone let-dataItem>
  <span>{{ maskPhone(dataItem?.Cellphone) }}</span>
</ng-template>
