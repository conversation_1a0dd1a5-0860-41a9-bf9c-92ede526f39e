import {
  CommonModule,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Ng<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  NgT<PERSON>plateOutlet,
} from '@angular/common';
import {
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  Inject,
  Input,
  OnDestroy,
  OnInit,
  Output,
  QueryList,
  Renderer2,
  TemplateRef,
  ViewChild,
  ViewChildren,
  ViewContainerRef,
} from '@angular/core';
import {
  FormArray,
  FormControl,
  FormsModule,
  NgForm,
  ReactiveFormsModule,
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { KENDO_BUTTON } from '@progress/kendo-angular-buttons';
import { KENDO_COMMON } from '@progress/kendo-angular-common';
import {
  GridComponent,
  GridDataResult,
  GridItem,
  KENDO_GRID,
  PageChangeEvent,
  RemoveEvent,
} from '@progress/kendo-angular-grid';
import { KENDO_ICON } from '@progress/kendo-angular-icons';
import { KENDO_CHECKBOX, KENDO_INPUTS } from '@progress/kendo-angular-inputs';
import { IntlService, KENDO_DATE, KENDO_INTL } from '@progress/kendo-angular-intl';
import { KENDO_PAGER } from '@progress/kendo-angular-pager';
import { aggregateBy, process } from '@progress/kendo-data-query';
import { cloneDeep } from 'lodash';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { of, Subscription } from 'rxjs';
import { catchError } from 'rxjs/operators';
import {
  VnrColumnWidthPipe,
  VnrConvertFileNameToListPipe,
  VnrGridColumnsValidatorsPipe,
  VnrGridInitBuilderControlsEditPipe,
  VnrIsLastGroupDataPipe,
} from '../..';
import { VNRMODULE_TOKEN } from '../../../../base/api-config';
import { IVnrModule_Token } from '../../../../common/models/app-api-config.interface';
import { VnrInputsModule } from '../../../inputs';
import { VnrPickersModule } from '../../../pickers';
import { VnrComboBoxComponent, VnrSelectsModule } from '../../../selects';
import { IVnrGridColumns } from '../../interfaces/grid-column.interface';
import { VnrGridGroupTitlePipe } from '../../pipes/vnr-grid-group-title.pipe';
import { VnrGridNewEditTypeControlsPipe } from '../../pipes/vnr-grid-new-edit-type-controls.pipe';
import { GridNewChangeColumnConfigGridService } from '../../services/grid-new-change-column-config-grid.service';
import { GridNewChangeColumnService } from '../../services/grid-new-change-column.service';
import { GridNewConfigService } from '../../services/grid-new-config.service';
import { VnrGridNewEditInlineService } from '../../services/grid-new-Edit-Inline.service';
import { VnrGridEditControls } from '../../types/vnr-grid-edit-controls.enum';
import { VnrGridNewChangeColumnComponent } from '../vnr-grid-new/change-new-column/change-new-column.component';
import { VnrGridNewChangeColumnOldComponent } from '../vnr-grid-new/change-old-column/change-old-column.component';
import { VnrGridNewComponent } from '../vnr-grid-new/vnr-grid-new.component';
import { VnrGridNewEditInlineBuilder } from './models/vnr-grid-new-edit-inline-builder.model';

const CREATE_ACTION = 'create';
const SAVE_ACTION = 'save';
const UPDATE_ACTION = 'update';
const REMOVE_ACTION = 'destroy';

@Component({
  selector: 'vnr-grid-new-Edit-Inline',
  templateUrl: './vnr-grid-new-Edit-Inline.component.html',
  styleUrls: ['./vnr-grid-new-Edit-Inline.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    NgClass,
    NgSwitch,
    NgSwitchCase,
    NgIf,
    NgFor,
    NgTemplateOutlet,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    KENDO_GRID,
    KENDO_BUTTON,
    KENDO_ICON,
    KENDO_COMMON,
    KENDO_DATE,
    KENDO_INTL,
    KENDO_INPUTS,
    KENDO_CHECKBOX,
    KENDO_PAGER,
    NzButtonModule,
    NzIconModule,
    NzInputNumberModule,
    NzCheckboxModule,
    VnrConvertFileNameToListPipe,
    VnrColumnWidthPipe,
    VnrInputsModule,
    VnrPickersModule,
    VnrSelectsModule,
    VnrGridColumnsValidatorsPipe,
    VnrGridNewEditTypeControlsPipe,
    VnrGridGroupTitlePipe,
    VnrIsLastGroupDataPipe,
    VnrGridInitBuilderControlsEditPipe,
    VnrGridNewChangeColumnComponent,
    VnrGridNewChangeColumnOldComponent,
  ],
})
export class VnrGridNewEditInlineComponent
  extends VnrGridNewComponent
  implements OnInit, OnDestroy
{
  @Input() override builder: VnrGridNewEditInlineBuilder = new VnrGridNewEditInlineBuilder();
  @Input() columnEditTemplates: { [key: string]: TemplateRef<any> };
  @Input() addGroupButtonTemplate: TemplateRef<any>;
  @Input() customControlTemplate: TemplateRef<any>;
  //#region Output
  @Output() vnrSaveEvent = new EventEmitter<any>();
  @Output() vnrDeleteEvent = new EventEmitter<any>();
  @Output() vnrOutSideClick = new EventEmitter<any>();
  @Output() vnrChangeEditData = new EventEmitter<any>(); //emit data mới chỉnh sửa
  @Output() vnrChangeComboBoxDataItem = new EventEmitter<any>(); //emit data mới chỉnh sửa của comboBox
  @Output() vnrChangeComboBoxValue = new EventEmitter<any>(); //emit data mới chỉnh sửa của comboBox
  @Output() vnrOpenComboBox = new EventEmitter<any>(); //emit data mới chỉnh sửa của comboBox
  //#endregion

  //#region ViewChild
  @ViewChild('vnrGridInlineForm') ngForm: NgForm;
  @ViewChild(GridComponent) grid: GridComponent;
  @ViewChild('kendoGridInline', { read: ElementRef })
  kendoGridInlineRef!: ElementRef;
  @ViewChildren(VnrComboBoxComponent) cbos: QueryList<VnrComboBoxComponent>;
  //#endregion

  private _editedRowIndex: number;
  private _editedProduct: any;
  private _docClickSubscription: Subscription = new Subscription();
  protected formGroups: UntypedFormGroup = new UntypedFormGroup({
    items: new FormArray([]),
  });
  protected formGroup: UntypedFormGroup;

  protected newlyAddedRows: any[] = [];
  protected dataBeforeDeletedRows: any[] = [];
  protected vnrGridEditControls: any = VnrGridEditControls;
  protected vnrGridEditInlineBackData: GridDataResult;
  public isEdited: boolean = false;
  override get getDataSource() {
    return this.gridView?.data || [];
  }

  public get getDataEditInlineSource() {
    return cloneDeep(this.vnrGridEditInlineBackData);
  }
  constructor(
    @Inject(VNRMODULE_TOKEN) protected override _vnrModule_Token: IVnrModule_Token,
    protected override _gridService: GridNewConfigService,
    protected override modal: NzModalService,
    protected intl: IntlService,
    protected notificationService: NzNotificationService,
    protected override translate: TranslateService,
    protected renderer: Renderer2,
    protected override _cdr: ChangeDetectorRef,
    protected override _vc: ViewContainerRef,
    protected formBuilder: UntypedFormBuilder,
    protected _gridEditInlineService: VnrGridNewEditInlineService,
    protected override _gridNewChangeColumnConfigGridService?: GridNewChangeColumnConfigGridService,
    protected override _gridNewChangeColumnService?: GridNewChangeColumnService,
  ) {
    super(
      _vnrModule_Token,
      _gridService,
      modal,
      _cdr,
      translate,
      _vc,
      _gridNewChangeColumnConfigGridService,
      _gridNewChangeColumnService,
    );
  }

  override async ngOnInit() {
    this.isLoading = true;
    await super.ngOnInit();
    this.vnrGridEditInlineBackData = cloneDeep(this.gridView);
    this._docClickSubscription?.add(
      this.renderer.listen('document', 'click', this.onDocumentClick.bind(this)),
    );
  }

  override ngAfterViewInit() {
    if (this.builder.options?.configEdit?.isEditAllRow) {
      this.isLoading = true;
      setTimeout(() => {
        if (!this.builder.options?.configEdit?.isLoadAllData) {
          this.editAllHandler(this.grid);
        }
      }, 700);
    }
  }
  protected override ensureApiAvailable(): void {
    if (!this.builder.options?.configEdit?.apiSaveChange) {
      this.builder.options.configEdit.apiSaveChange =
        this._vnrModule_Token.gridConfig_Token.apiSaveEditIncell?.url;
    }
    super.ensureApiAvailable();
  }
  private onDocumentClick(e: Event): void {
    if (this.ngForm && this.ngForm.valid) {
      // this.saveCurrent();
    }
  }
  //#endregion
  protected override async initColumns() {
    await super.initColumns();
    if (this.columnEditTemplates) {
      this.addEditorCustom(this.gridColumns);
    }
  }

  private addEditorCustom(listColumn: Array<IVnrGridColumns>) {
    listColumn.forEach((column: IVnrGridColumns) => {
      this.columnEditTemplates[column.Name] &&
        (column['editor'] = this.columnEditTemplates[column.Name]);
      if (column.MultiColumn && column.MultiColumn.length > 0) {
        this.addEditorCustom(column.MultiColumn);
      }
    });
  }
  //#region Events
  override showOrderNumber(dataItem: any, rowIndex: number) {
    dataItem.OrderNumber = rowIndex + 1;
    return rowIndex + 1;
  }

  protected checkRequiredEdit(column: any): boolean {
    if (this.builder.options?.configEdit.columnsRequiredEdit.includes(column)) {
      return true;
    }
    return false;
  }

  public addRowHandler(grid: GridComponent, index?: string, dataItemAdd?: any): void {
    let data = dataItemAdd
      ? dataItemAdd
      : { ID: this._gridEditInlineService.newGuid(), isNew: true };
    this.isLoading = true;
    let rows = grid.data;
    index ??= '0';
    let indexCounter: any = { currentIndex: 0 }; // Biến để theo dõi chỉ số liên tục
    let groupLength = this.builder.options.queryOption?.group?.length || 0;
    let objDataGroup: any = {};
    if (!this.builder.options?.configEdit?.isEditAllRow) {
      // formInstance.reset()
      this.closeEditor(grid);
    }
    const parentData = this._gridService.getParentDataByIndex(
      rows && rows['data'] && rows['data'].length > 0 ? rows['data'] : [],
      index,
      this.builder.options.queryOption,
    );
    if (parentData && parentData.length > 0 && groupLength > 0) {
      objDataGroup = parentData?.reduce((acc: any, item: any) => {
        acc[item.field] = item.value;
        return acc;
      }, {});
    }
    let formGroup = this.createFormGroup(
      Object.assign(data, objDataGroup ? objDataGroup : {}),
      true,
    );
    this._gridEditInlineService.update(
      formGroup.value,
      true,
      this.gridView,
      this.builder.options.queryOption,
    );
    this.vnrGridEditInlineBackData = this.gridView; //lưu data để xử lý cho cancel
    if (groupLength > 0) {
      rows['data']?.forEach((_item, index) => {
        this.updateGroupRecursive(
          rows['data'][index]['items'],
          groupLength,
          grid,
          indexCounter,
          CREATE_ACTION,
          formGroup,
        );
      });
      this.newlyAddedRows.push(formGroup.value);
    } else {
      (this.formGroups.get('items') as FormArray).insert(0, formGroup);
      this.newlyAddedRows.push(formGroup.value);
      grid.editRow(this.gridView.total - 1);
      grid.editRow(0);
    }
    setTimeout(() => {
      this.isLoading = false;
      this._cdr.detectChanges();
    }, 300);
  }

  /**
   * Edit the row at the specified index.
   * @param {any} evt - Event emitted when the edit button is clicked.
   * @param {GridComponent} sender - The grid component.
   * @param {number} rowIndex - The index of the row to edit.
   * @param {any} dataItem - The data item of the row to edit.
   */
  public editHandler({ sender, rowIndex, dataItem }: any): void {
    // Update the groups
    // this.updateGroups(sender)
    // Close the editor of the row at the specified index.
    // If the row is currently being edited, this will revert the row back into view mode.
    this.closeEditor(sender);
    // Create a new form group
    const formGroup = this.createFormGroup(dataItem);
    // Insert the form group at the specified index
    (this.formGroups.get('items') as FormArray).insert(rowIndex, formGroup);
    // Store the index of the row that is being edited
    this._editedRowIndex = rowIndex;
    // Store a copy of the data item that is being edited
    this._editedProduct = Object.assign({}, dataItem);
    // Edit the row at the specified index
    sender.editRow(rowIndex, formGroup);
  }

  private closeEditor(
    grid: GridComponent,
    rowIndex = this._editedRowIndex,
    isResetPage: boolean = true,
  ) {
    grid.closeRow(rowIndex);
    if (this._editedProduct && isResetPage) this.resetEditPage();
    this._editedRowIndex = undefined;
    this._editedProduct = {};
  }

  /**
   * Cancel the editing of the row at the specified index.
   * @param {any} evt - Event emitted when the cancel button is clicked.
   * @param {GridComponent} sender - The grid component.
   * @param {number} rowIndex - The index of the row to cancel.
   */
  public cancelHandler(event: any): void {
    this.closeEditor(event.sender, event.rowIndex);
    // Close the editor of the row at the specified index.
    // If the row is currently being edited, this will revert the row back into view mode.
  }
  /**
   * Cancel the editing of all rows in the grid.
   * This will close all editors and revert all rows back into view mode.
   * @param {GridComponent} grid - The grid component.
   */
  public cancelAllHandler(grid: GridComponent): void {
    this.isEdited = false;
    const groupLength = this.builder.options.queryOption?.group?.length || 0;
    let rows: any = grid?.data;
    this.gridView = this.vnrGridEditInlineBackData;
    this._gridEditInlineService.cancel(
      this.dataBeforeDeletedRows,
      this.gridView,
      this.builder.options.queryOption,
    );
    // Close all editors and revert all rows back into view mode.
    if (rows && rows['data'] && rows['data'].length > 0) {
      let indexCounter = { currentIndex: 0 }; // Biến để theo dõi chỉ số liên tục
      if (groupLength > 0) {
        rows['data']?.forEach((_item, index) => {
          this.updateGroupRecursive(
            rows['data'][index]['items'],
            groupLength,
            grid,
            indexCounter,
            REMOVE_ACTION,
          );
        });
      } else {
        rows['data']?.forEach((_item, index) => {
          grid.closeRow(index);
        });
      }
    }

    // Remove all newly added rows from the grid data.
    for (let i = 0; i < this.newlyAddedRows.length; i++) {
      if (this.newlyAddedRows[i]) {
        this._gridEditInlineService.remove(
          this.newlyAddedRows[i],
          this.gridView,
          this.builder.options.queryOption,
        );
      }
    }
    // this.calcAggregateGrid()
    this.newlyAddedRows = [];
    this.dataBeforeDeletedRows = [];
  }

  /**
   * Remove the current dataItem from the current data source, `_gridEditInlineService` in this example.
   * @param {RemoveEvent} args - The event object containing the dataItem to be removed.
   */
  public removeHandler(dataItem: any, grid: GridComponent): void {
    if (this.builder.options?.configEdit?.isEditAllRow) {
      this.dataBeforeDeletedRows.push(dataItem);
      // Persist values in newly added rows, if any
      // Remove the current dataItem from the current data source, `_gridEditInlineService` in this example
      this._gridEditInlineService.remove(dataItem, this.gridView, this.builder.options.queryOption);
      this.vnrGridEditInlineBackData = this.gridView;
      this.calcAggregateGrid();
      // Update the groups after removing a row
      this.updateGroups(grid);
      this.vnrDeleteEvent.emit(this.dataBeforeDeletedRows);
    } else {
      this.vnrDeleteEvent.emit(dataItem);
    }
  }

  public async saveAllHandler(grid: GridComponent) {
    // console.log(this.formGroups)
    if (
      (this.formGroups.get('items') as FormArray).controls.length === 0 ||
      this.formGroups.invalid
    ) {
      // this.ngForm.ngSubmit.emit()

      // Trigger validation for all form controls
      this.triggerValidation();
      return; // Stop execution if the form is invalid
    } else {
      let arrDataItem = [];
      if (this.formGroups.value && this.formGroups.value.items) {
        arrDataItem = this.formGroups.value.items;
      }
      var saveDynamic = await this.saveChanges(arrDataItem);
      if (saveDynamic) {
        this.completeAndCloseAllHandler(grid, arrDataItem);
      }
    }
  }
  private completeAndCloseAllHandler(grid: GridComponent, dataItems: any[]): void {
    this.isEdited = false;
    if (dataItems) {
      dataItems.forEach((_item, index) => {
        grid.closeRow(index);
      });
    }
    this.newlyAddedRows = [];
    this.dataBeforeDeletedRows = [];
  }
  public onSubmit($event) {
    this.ngForm.ngSubmit.emit();
  }

  /**
   * Trigger validation for all controls in the form groups.
   * This will mark all controls as dirty and update their validity.
   */
  private triggerValidation(): void {
    let listInValidID = [];
    const itemsArray = this.formGroups.get('items') as FormArray;
    itemsArray.controls.forEach((group: UntypedFormGroup) => {
      if (group) {
        if (group.invalid) {
          group.value?.ID && listInValidID.push(group.value?.ID);
        }
        Object.values(group.controls).forEach((control) => {
          if (control.invalid) {
            control.markAsDirty();
            control.markAsTouched();
            control.updateValueAndValidity();
          }
        });
      }
    });
    if (this.cbos && this.cbos.length > 0) {
      this.cbos
        .filter((x: any) => listInValidID?.includes(x.index))
        .forEach((cboItem: VnrComboBoxComponent) => {
          cboItem?.checkValidating();
        });
      return;
    }
  }

  /**
   * Close all editors and revert all rows back into view mode.
   * This will cancel all row edits and discard any changes made to the row.
   * @param {GridComponent} grid - The grid component.
   */
  public closeAllEditor(grid: GridComponent) {
    const groupLength = this.builder.options.queryOption?.group?.length || 0;
    let rows: any = grid.data;
    if (rows && rows['data'] && rows['data'].length > 0) {
      let indexCounter = { currentIndex: 0 }; // Biến để theo dõi chỉ số liên tục
      if (groupLength > 0) {
        rows['data'].forEach((_item, index) => {
          this.updateGroupRecursive(
            rows['data'][index]['items'],
            groupLength,
            grid,
            indexCounter,
            REMOVE_ACTION,
          );
        });
      } else {
        rows['data'].forEach((_item, index) => {
          grid.closeRow(index);
        });
      }
    }
    // Reset the state of the inline editor.
    this.isEdited = false;
    this.newlyAddedRows = [];
    this.dataBeforeDeletedRows = [];
    this._editedRowIndex = undefined;
    this._editedProduct = undefined;
    setTimeout(() => {
      this.isLoading = false;
      this._cdr.detectChanges();
    }, 300);
  }

  public async saveHandler(evt: any) {
    const { sender, rowIndex, formGroup, isNew, dataItem } = evt;
    let arrDataItem = [dataItem];
    var saveDynamic = await this.saveChanges(arrDataItem);
    if (saveDynamic) {
      sender.closeRow(rowIndex);
    }
  }
  protected changeDateFormat(evt: any, dataItem?: any, name?: string) {
    if (evt) dataItem[name] = this.intl.formatDate(evt, 'yyyy-MM-dd:HH:mm:ss');
  }

  protected fixedWidth(col: any, lastItem: any, defaultWidth: number = 150) {
    if (lastItem) return;
    return col.Width ? col.Width : defaultWidth;
  }
  //#endregion

  /**
   *  Emit Event Click Format File
   * @param value => string
   */
  override onClickFileName(value: string) {
    this.vnrFormatFileClick.emit(value);
  }
  //#endregion

  /**
   *  Emit Event Click upload File
   * @param value => string
   */
  override onClickUploadFileToColumn(columnName: string, dataItem: any, column: any) {
    this.vnrUploadFileToColumnClick.emit({
      columnName: columnName,
      dataItem: dataItem,
      column: column,
    });
  }
  //#endregion

  /**
   * Get a FormGroup by row index and column name
   * @param rowIndex => number
   * @param column => any
   * @returns FormControl
   */
  protected getFormGroup(rowIndex: number, column: any): FormControl {
    const rowOnPage: UntypedFormGroup = (this.formGroups.get('items') as FormArray)?.controls[
      rowIndex
    ] as UntypedFormGroup;
    // Return the FormControl for the given column name
    return <FormControl>rowOnPage?.controls[column.field];
  }
  /**
   * Edit all the rows in the grid.
   * @param {GridComponent} sender - The grid component.
   */
  public editAllHandler(grid: GridComponent): void {
    this.isLoading = true;
    // Set the grid to edit mode
    this.isEdited = true;
    // Update the groups
    this.updateGroups(grid);

    // Edit the first row => Cause need focus to first row (when edit all row)
    if (this.builder.options?.configEdit?.isEditAllRow) {
      grid.editRow(0);
    }

    setTimeout(() => {
      this.isLoading = false;
      this._cdr.detectChanges();
    }, 300);
  }

  protected trackByIndex(index: number, item: GridItem): any {
    return index;
  }
  /**
   * Update groups
   * @param grid => GridComponent
   */
  protected editAllFormGroupsFromDataSource(): void {
    const formArr = this.formGroups.get('items') as FormArray;
    const dataSource = this.dataLocal || this.getDataSource;
    this.isEdited = true;
    const startIndex = this.builder.options.queryOption.skip;
    const totalRows = this.builder.options.queryOption.skip + this.builder.options.queryOption.take;
    const endIndex = Math.min(totalRows, dataSource.length); // Ensure not exceeding dataSource bounds

    // Pre-fetch form controls to avoid repeated costly lookups
    const formGroups = formArr.controls.slice(startIndex, endIndex) as UntypedFormGroup[];

    for (let i = 0; i < formGroups.length; i++) {
      const index = startIndex + i;
      const dataItem = dataSource[index];
      const formGroup = formGroups[i];

      this.formGroup = formGroup; // Check if necessary to set for every loop iteration or can be set once outside loop

      // Consider collecting rows to edit and call editRow outside of loop if possible
      this.grid.editRow(index, formGroup);
    }

    if (this.builder.options?.configEdit?.isEditAllRow) {
      this.grid.editRow(0);
    }

    setTimeout(() => {
      this.isLoading = false;
      this.grid.loading = false;
      this._cdr.detectChanges();
    }, 300);
  }

  /**
   * Generate all form data
   * @param grid => GridComponent
   */
  protected generateAllFormData(dataSource: any[]): void {
    const formArr = this.formGroups.get('items') as FormArray;
    formArr?.clear();

    let rows: any = dataSource;

    if (rows && rows.length > 0) {
      for (let index = 0; index < rows.length; index++) {
        const dataItem = rows[index];

        let group = this.createFormGroup(dataItem);

        this.formGroup = group;

        formArr.push(group);
      }
    }
  }

  /**
   * Update groups
   * @param grid => GridComponent
   */
  protected updateGroups(grid: GridComponent): void {
    const groupLength = this.builder.options.queryOption?.group?.length || 0;
    let rows: any = grid?.data;
    let isPagingData = !this.builder.options.configShowHide.isPageExpand;
    const formArr = this.formGroups.get('items') as FormArray;
    if (!isPagingData) {
      formArr.clear();
    }

    if (rows && rows['data'] && rows['data'].length > 0) {
      let indexCounter = { currentIndex: 0 }; // Biến để theo dõi chỉ số liên tục
      if (groupLength > 0) {
        rows['data']?.forEach((_item, index) => {
          this.updateGroupRecursive(rows['data'][index]['items'], groupLength, grid, indexCounter);
        });
      } else {
        for (let index = 0; index < rows['data'].length; index++) {
          const dataItem = rows['data'][index];
          let group = this.createFormGroup(dataItem);

          this.formGroup = group;

          formArr.push(group);

          // console.log(formArr)

          if (this.isEdited) {
            const rowIndex = isPagingData ? this.builder.options.queryOption.skip + index : index;
            grid.editRow(rowIndex, group);
          }
        }

        // rows['data']?.forEach((_item, index) => {
        //   // Store the created FormGroup in a FormArray so that the respective form groups
        //   // can be easily reused when necessary
        //   let group = this.createFormGroup(rows['data'][index])
        //   ;(this.formGroups.get('items') as FormArray).push(group)
        //   if (this.isEdited) {
        //     grid.editRow(index, group)
        //   }
        // })
      }
    }
  }
  /**
   * Recursively create form groups based on the items array.
   * @param {any[]} items - The items array to create form groups from.
   * @param {number} groupLength - The number of form groups to create.
   * @param {GridComponent} grid - The grid component.
   * @param {{currentIndex: number}} indexCounter - An object to store the current index.
   * @param {number} level - The level of the recursion.
   * @returns void
   */
  private updateGroupRecursive(
    items: any[],
    groupLength: number,
    grid: GridComponent,
    indexCounter: { currentIndex: number },
    action: string = UPDATE_ACTION,
    formGroupNew?: any,
    level: number = 0,
  ) {
    if (items && items.length > 0 && level < groupLength) {
      items.forEach((item, index) => {
        if (item['items'] && Array.isArray(item['items'])) {
          // Recursively call the function to create form groups for the nested items array.
          this.updateGroupRecursive(
            item['items'],
            groupLength,
            grid,
            indexCounter,
            action,
            formGroupNew,
            level + 1,
          );
        } else {
          if (action === UPDATE_ACTION) {
            // Create a form group for the item.
            let group = this.createFormGroup(item);
            // Push the form group to the form array.
            (this.formGroups.get('items') as FormArray).push(group);
            if (this.isEdited) {
              // Edit the row at the current index.
              grid.editRow(indexCounter.currentIndex++, group);
              if (item.IsDisabled) {
                this.builder.options?.configEdit?.readonlyColumns?.forEach((field) => {
                  group?.controls[field]?.disable();
                });
              }
            }
          } else if (action === REMOVE_ACTION) {
            grid.closeRow(indexCounter.currentIndex++);
          } else if (action === SAVE_ACTION) {
            const controls = (this.formGroups.get('items') as FormArray).controls[
              indexCounter.currentIndex++
            ];
            this._gridEditInlineService.update(
              controls.value,
              false,
              this.gridView,
              this.builder.options.queryOption,
            );
          } else if (action === CREATE_ACTION) {
            if (formGroupNew?.value.ID == item.ID) {
              (this.formGroups.get('items') as FormArray).insert(
                indexCounter.currentIndex++,
                formGroupNew,
              );
            }

            if (this.isEdited) {
              grid.editRow(indexCounter.currentIndex++);
            }
          }
        }
      });
    }
  }

  /**
   * Create a FormGroup based on the dataItem and column names
   * @param dataItem => any
   * @returns UntypedFormGroup
   */
  protected createFormGroup(dataItem: any, isNew: boolean = false): UntypedFormGroup {
    Object.assign(dataItem, {
      IsEditing: isNew ? true : false,
      IsNew: isNew,
      IsDisabled: false,
    });
    /* Create an empty object to store the form controls */
    let form: any = {};
    let _valueFields: any[] = [];
    let _valueFieldsComboBox: any[] = [];
    let column: any[] = cloneDeep(this.columns);
    let arrFieldRequired = [];
    _valueFields = column?.flatMap((col) => {
      if (col?.Type === 'group' && col?.MultiColumn && col.MultiColumn.length > 0) {
        return col.MultiColumn?.map((multiCol) => {
          //Add field required
          if ((multiCol.Validators && multiCol.Validators.Required) || multiCol.Required)
            arrFieldRequired.push(col.Name);

          if (multiCol?.TypeControl === VnrGridEditControls.ComboBox) {
            const textFieldInit = multiCol?.Name?.endsWith('ID')
              ? multiCol?.Name?.slice(0, -2) + 'Name'
              : multiCol?.Name;
            _valueFieldsComboBox?.push(textFieldInit);
          }
          return multiCol.Name;
        });
      } else {
        //Add field required
        if ((col.Validators && col.Validators.Required) || col.Required)
          arrFieldRequired.push(col.Name);

        if (col?.TypeControl === VnrGridEditControls.ComboBox) {
          const textFieldInit = col?.Name?.endsWith('ID')
            ? col?.Name?.slice(0, -2) + 'Name'
            : col?.Name;
          _valueFieldsComboBox.push(textFieldInit);
        }
        return col.Name;
      }
    });

    _valueFields = _valueFields?.concat(['IsEditing', 'IsDisabled', 'IsNew']);
    _valueFields = _valueFields?.concat(_valueFieldsComboBox);

    /* Loop through the column names and create a FormControl for each one */
    _valueFields?.forEach((el) => {
      const textField = this.builder.options?.configEdit?.builderConfigByColumn?.[el]?.textField;
      const valueField = this.builder.options?.configEdit?.builderConfigByColumn?.[el]?.valueField;
      const isRequired = arrFieldRequired.includes(el) ? [Validators.required] : [];

      if (
        this.builder.options?.configEdit?.builderConfigByColumn &&
        this.builder.options?.configEdit?.builderConfigByColumn[el] &&
        this.builder.options?.configEdit?.builderConfigByColumn[el].serverSide &&
        textField &&
        valueField
      ) {
        form[textField] = [dataItem[textField]];
        form[valueField] = [dataItem[valueField], isRequired];

        let addedDataSource = {};
        addedDataSource[textField] = dataItem[textField];
        addedDataSource[valueField] = dataItem[valueField];

        if (dataItem[valueField]) {
          const currentDataSrc =
            this.builder.options?.configEdit?.builderConfigByColumn[el].dataSource || [];
          this.builder.options.configEdit.builderConfigByColumn[el].dataSource = [
            ...currentDataSrc,
            addedDataSource,
          ];
        }
        // Nếu col khác vs value & text => add form
        if (el !== valueField && el !== textField) {
          form[el] = [dataItem[el], isRequired];
          addedDataSource[textField] = dataItem[`${el}View`];
          addedDataSource[valueField] = dataItem[el];
          if (dataItem[el]) {
            const currentDataColSrc =
              this.builder.options?.configEdit?.builderConfigByColumn[el].dataSource || [];
            this.builder.options.configEdit.builderConfigByColumn[el].dataSource = [
              ...currentDataColSrc,
              addedDataSource,
            ];
          }
        }
      } else {
        form[el] = [dataItem[el], isRequired];
      }
    });
    /* Return the FormGroup created from the form controls */
    return this.formBuilder.group(form);
  }
  /**
   * Save changes to the data.
   * @param grid => GridComponent
   * @param saveNew => boolean
   */
  public async saveChanges(dataItems: any[]) {
    this.isLoading = true;
    let result = {};
    if (dataItems && this.builder.options.configEdit?.apiSaveChange) {
      result = await this._gridService
        .postEditGrid(this.builder.options.configEdit?.apiSaveChange, dataItems)
        .pipe(
          catchError((err) => {
            this.notificationService.error(
              this.translate.instant('common.title.error'),
              this.builder.options?.configEdit?.isShowMessenger
                ? err
                : this.translate.instant('common.message.actionSuccess'),
              { nzPlacement: 'bottomRight' },
            );
            this.isLoading = false;
            return of(null);
          }),
        )
        .toPromise();
      if (result) {
        if (this.builder.options?.configEdit?.isShowMessenger)
          this.notificationService.success(
            this.translate.instant('common.title.success'),
            this.translate.instant('common.message.actionSuccess'),
          );
        this.resetEditPage();
        this._editedRowIndex = undefined;
        this._editedProduct = undefined;
        this.isEdited = false;
        this.newlyAddedRows = [];
        this.dataBeforeDeletedRows = [];
      }
    } else {
      this.resetEditPage();
    }
    this.isLoading = false;
    this.vnrSaveEvent.emit(this.gridView);
    return result;
  }
  /**
   * Handle cell click event.
   * @param args => any
   */

  public cellClickHandler({
    sender,
    rowIndex,
    columnIndex,
    dataItem,
    isEdited,
    originalEvent,
    column,
  }: any): any {
    if (originalEvent.target && !originalEvent.target.hasAttribute('kendogridcell')) {
      return;
    }
    if (
      this.builder.options?.configEdit?.isEnableEditRowClick &&
      !this.builder.options?.configEdit?.isEditAllRow &&
      !this.builder.options?.configEdit?.isShowColumnAction
    ) {
      (this.formGroups?.get('items') as FormArray).clear();

      if (originalEvent.which === 3) return;
      // Update the groups
      this.updateGroups(sender);

      const controls = (this.formGroups?.get('items') as FormArray).controls[this._editedRowIndex];
      if (controls) {
        this._gridEditInlineService.update(
          controls.value,
          false,
          this.gridView,
          this.builder.options.queryOption,
        );
      }
      // this.saveChanges(sender, false)

      // Close the editor of the row at the specified index.
      // If the row is currently being edited, this will revert the row back into view mode.
      this.closeEditor(sender, this._editedRowIndex, false);
      // Create a new form group
      const formGroup = this.createFormGroup(dataItem);
      // Insert the form group at the specified index
      (this.formGroups.get('items') as FormArray).insert(rowIndex, formGroup);
      // Store the index of the row that is being edited
      this._editedRowIndex = rowIndex;
      // Store a copy of the data item that is being edited
      this._editedProduct = Object.assign({}, dataItem);
      // Edit the row at the specified index
      sender.editRow(rowIndex, formGroup);
    }
  }
  public cellCloseHandler(args: any) {}

  /**
   * Handle changes to the form data.
   * @param $event => any
   * @param formGroup => FormGroup
   */
  protected onModelChangeInputNumber($event, rowIndex, columnName: string, dataItem: any) {
    const rowOnPage: UntypedFormGroup = (this.formGroups?.get('items') as FormArray)?.controls[
      rowIndex
    ] as UntypedFormGroup;

    if (rowOnPage) {
      if ($event && dataItem && dataItem[columnName] !== $event) {
        rowOnPage.patchValue({ IsEditing: true });
      } else {
        rowOnPage.patchValue({ IsEditing: false });
      }
      if ($event === '') {
        rowOnPage.patchValue({ [columnName]: null });
      }
      this.vnrChangeEditData.emit({
        data: rowOnPage?.value,
        rowIndex: rowIndex,
        columnName: columnName,
      });
    }
  }
  protected onModelChangeMoney($event, rowIndex, columnName: string, dataItem: any) {
    const rowOnPage: UntypedFormGroup = (this.formGroups?.get('items') as FormArray)?.controls[
      rowIndex
    ] as UntypedFormGroup;

    if (rowOnPage) {
      if ($event && dataItem && dataItem[columnName] !== $event) {
        rowOnPage.patchValue({ IsEditing: true });
      } else {
        rowOnPage.patchValue({ IsEditing: false });
      }
      if ($event === '') {
        rowOnPage.patchValue({ [columnName]: null });
      }
      this.vnrChangeEditData.emit({
        data: rowOnPage?.value,
        rowIndex: rowIndex,
        columnName: columnName,
      });
    }
  }
  protected onModelChange($event, rowIndex, columnName: string, dataItem: any) {
    const rowOnPage: UntypedFormGroup = (this.formGroups?.get('items') as FormArray)?.controls[
      rowIndex
    ] as UntypedFormGroup;

    if (rowOnPage) {
      if ($event && dataItem && dataItem[columnName] !== $event) {
        rowOnPage.patchValue({ IsEditing: true });
      } else {
        rowOnPage.patchValue({ IsEditing: false });
      }
      this.vnrChangeEditData.emit({
        data: rowOnPage?.value,
        rowIndex: rowIndex,
        columnName: columnName,
      });
    }
  }
  protected onSelectDataItem($event: any, column: any, rowIndex) {
    const rowOnPage: UntypedFormGroup = (this.formGroups.get('items') as FormArray)?.controls[
      rowIndex
    ] as UntypedFormGroup;

    if (
      this.builder.options?.configEdit?.builderConfigByColumn &&
      this.builder.options?.configEdit?.builderConfigByColumn[column.field]
    ) {
      const textFieldInit = column.field.endsWith('ID')
        ? column.field.slice(0, -2) + 'Name'
        : column.field;
      rowOnPage?.controls[textFieldInit]?.setValue(
        ($event &&
          $event[
            this.builder.options?.configEdit?.builderConfigByColumn[column?.field].textField
          ]) ||
          '',
      );
    }
    this.vnrChangeComboBoxDataItem.emit($event);
  }
  protected onModelChangeComboBox($event, rowIndex, columnName: string, dataItem: any): any {
    const rowOnPage: UntypedFormGroup = (this.formGroups.get('items') as FormArray)?.controls[
      rowIndex
    ] as UntypedFormGroup;

    if (rowOnPage) {
      if ($event && dataItem && dataItem[columnName] !== $event) {
        rowOnPage.patchValue({ IsEditing: true });
      } else {
        rowOnPage.patchValue({ IsEditing: false });
      }
    }
    this.vnrChangeComboBoxValue.emit($event);
  }
  protected onOpenComboBox($event, rowIndex) {
    this.vnrOpenComboBox.emit({ isOpen: $event, rowIndex: rowIndex });
  }

  protected isReadOnly(field: string): boolean {
    return this.builder.options?.configEdit?.readonlyColumns.indexOf(field) > -1;
  }

  protected calcAggregateGrid() {
    let temp = this._gridService.extractItems(this.gridView.data) || [];
    this.vnrAggregateResult = aggregateBy(temp, this.aggregates);
    this.gridView = process(temp, this.builder.options.queryOption);
  }

  /**
   * Page Change
   * @param event
   */
  override onPageChange(event: PageChangeEvent): void {
    this.vnrPageChange.emit(event);
    this._calcGridHeight$.next(true);
  }

  /**
   * Unsubscribe from the document click event when the component is destroyed.
   * This is necessary to prevent memory leaks.
   */
  override ngOnDestroy(): void {
    this.resetEditPage();
    this._docClickSubscription.unsubscribe();
  }
  override loadExpandBtn() {
    super.loadExpandBtn();
    this.vnrGridEditInlineBackData = this.gridView;
  }
}
