export const goalTreeDataSource = [
  {
    key: '1',
    GoalName: 'Tổng doanh thu toàn tập đoàn năm 2025',
    Department: 'Tập đoàn',
    Representative: 'Ho<PERSON>ng <PERSON>u<PERSON>',
    JobTitle: 'Tổng giám đốc',
    progress: 70,
    description: '70%/100%',
    dueDate: '01/01/2025 -> 31/12/2025',
    Status: 'E_CONFIRMED',
    viewStatus: 'Không đạt',
    parent: '',
    type: 'KPI',
  },
  {
    key: '2',
    GoalName: 'Doanh thu ô tô sản xuất & phân phối',
    Department: 'CLD AUTO',
    progress: 100,
    description: '100%/100%',
    dueDate: '01/01/2025 -> 31/12/2025',
    Status: 'E_CONFIRMED',
    viewStatus: 'Đạt chỉ tiêu',
    parent: '1',
    type: 'KPI',
    Representative: '<PERSON><PERSON>',
    JobTitle: 'GĐ CLD AUTO',
  },
  {
    key: '3',
    GoalName: '<PERSON><PERSON>h thu trồng trọ<PERSON> & chế biến',
    Department: 'CLD AGRICO',
    progress: 110,
    description: '140 tỷ VNĐ/ 150 tỷ VNĐ',
    dueDate: '01/01/2025 -> 31/12/2025',
    Status: 'E_CONFIRMED',
    viewStatus: 'Vượt chỉ tiêu',
    parent: '1',
    type: 'KPI',
    Representative: 'Nguyễn Xuân Hoài',
    JobTitle: 'GĐ CLD AGRICO',
  },
  {
    key: '4',
    GoalName: 'Doanh thu logistics & vận tải',
    Department: 'CLD LOGI',
    progress: 100,
    description: '100%/100%',
    dueDate: '01/01/2025 -> 31/12/2025',
    Status: 'E_CONFIRMED',
    viewStatus: 'Đạt chỉ tiêu',
    parent: '1',
    type: 'KPI',
    Representative: 'Vũ Văn Thái',
    JobTitle: 'GĐ CLD LOGI',
  },
  {
    key: '5',
    GoalName: 'Doanh thu tổng thầu và đầu tư xây dựng',
    Department: 'CLD DICO',
    progress: 50,
    description: '50%/100%',
    dueDate: '01/01/2025 -> 31/12/2025',
    Status: 'E_CONFIRMED',
    viewStatus: 'Không đạt',
    parent: '1',
    type: 'KPI',
    Representative: 'Đoàn Trung Hiếu',
    JobTitle: 'GĐ CLD DICO',
  },
  {
    key: '6',
    GoalName: 'Doanh thu phân phối, bán lẻ & dịch vụ',
    Department: 'CLD INDUSTRIES',
    progress: 50,
    description: '50%/100%',
    dueDate: '01/01/2025 -> 31/12/2025',
    Status: 'E_CONFIRMED',
    viewStatus: 'Không đạt',
    parent: '1',
    type: 'KPI',
    Representative: 'Nguyễn Thái Hòa',
    JobTitle: 'GĐ CLD IND',
  },
  {
    key: '7',
    GoalName: 'Sản lượng ô tô sản xuất',
    Department: 'Cty SX Ô tô Chu Lai',
    progress: 70,
    description: '70%/100%',
    dueDate: '01/01/2025 -> 31/12/2025',
    Status: 'E_CONFIRMED',
    viewStatus: 'Không đạt',
    parent: '2',
    type: 'OKR',
    Representative: 'Đào Hải',
    JobTitle: 'GĐ SX Chu Lai',
  },
  {
    key: '8',
    GoalName: 'Doanh thu bán xe nội địa',
    Department: 'Cty SX Ô tô Chu Lai',
    progress: 60,
    description: '60%/100%',
    dueDate: '01/01/2025 -> 31/12/2025',
    Status: 'E_CONFIRMED',
    viewStatus: 'Không đạt',
    parent: '2',
    type: 'KPI',
    Representative: 'Lê Hoàng Hải Yến',
    JobTitle: 'GĐ KD',
  },
  {
    key: '9',
    GoalName: 'Doanh thu xuất khẩu',
    Department: 'Cty SX Ô tô Chu Lai',
    progress: 65,
    description: '65%/100%',
    dueDate: '01/01/2025 -> 31/12/2025',
    Status: 'E_CONFIRMED',
    viewStatus: 'Không đạt',
    parent: '2',
    type: 'KPI',
    Representative: 'Tôn Nữ Bảo Quyên',
    JobTitle: 'GĐ KD QT',
  },
];

export const STATUS_TAG = [
  {
    label: 'Chờ xác nhận',
    backgroundColor: '#FFEFCC',
    textColor: '#A13D0B',
  },
  {
    label: 'Đã huỷ',
    backgroundColor: '#FEF8C3',
    textColor: '#85550E',
  },
  {
    label: 'Tạm dừng',
    backgroundColor: '#EBEBEB',
    textColor: '#303030',
  },
];

export const STATUS_PROGRESS_TAG = [
  {
    label: 'Vượt chỉ tiêu',
    backgroundColor: '#DCECFE',
    textColor: '#1E45AF',
  },
  {
    label: 'Đạt chỉ tiêu',
    backgroundColor: '#D8FDDB',
    textColor: '#13681C',
  },
  {
    label: 'Không đạt',
    backgroundColor: '#FFDBDC',
    textColor: '#AB1316',
  },
  {
    label: 'Đã huỷ',
    backgroundColor: '#FEF8C3',
    textColor: '#85550E',
  },
  {
    label: 'Tạm dừng',
    backgroundColor: '#EBEBEB',
    textColor: '#303030',
  },
];

export function treeFilter(): any[] {
  const listDepartment = Array.from(
    new Set(goalTreeDataSource.filter((item) => item.Department).map((item) => item.Department)),
  ).map((department) => ({
    label: department,
    value: department,
  }));
  return [
    {
      numberOrder: 1,
      isFavorite: false,
      isUsing: false,
      field: 'Department',
      type: 'multipleCheckbox',
      value: '',
      title: 'Phòng ban',
      dataSource: {
        staticSource: listDepartment,
      },
    },
  ];
}

export interface GoalNode {
  key: string;
  GoalName: string;
  Department: string;
  Representative: string;
  JobTitle: string;
  progress?: number;
  parent?: string;
  type?: string;
  minProgress?: number;
  dueDate?: string;
  category?: string;
  color?: string;
}

export const statusColorMap = {
  E_WAITING_CONFIRM: 'warning',
  E_CONFIRMED: 'success',
  E_REJECTED: 'error',
  E_WAITING_APPROVE: 'warning',
  E_IN_PROGRESS: 'success',
  E_PAUSED: 'warning',
  E_CANCELED: 'error',
};

export const statusTextMap = {
  E_WAITING_CONFIRM: 'objEval.GoalPeriod.WaitingConfirm',
  E_CONFIRMED: 'objEval.GoalPeriod.Confirmed',
  E_REJECTED: 'objEval.GoalPeriod.Rejected',
  E_WAITING_APPROVE: 'objEval.GoalPeriod.WaitingApprove',
  E_IN_PROGRESS: 'objEval.GoalPeriod.InProgress',
  E_PAUSED: 'objEval.GoalPeriod.Paused',
  E_CANCELED: 'objEval.GoalPeriod.Canceled',
};

export const goalTypeColorMap = {
  OKR: '#7833F4',
  KPI: '#256AEB',
};
