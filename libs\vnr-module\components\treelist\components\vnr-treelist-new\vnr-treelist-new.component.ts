import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  CommonModule,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Ng<PERSON><PERSON>,
  Ng<PERSON><PERSON><PERSON><PERSON>,
  Ng<PERSON><PERSON>Def<PERSON>,
  NgT<PERSON>plateOutlet,
} from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  HostListener,
  Inject,
  Input,
  OnInit,
  Output,
  TemplateRef,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { GridDataResult, PageChangeEvent } from '@progress/kendo-angular-grid';
import { IconsModule, SVGIconModule } from '@progress/kendo-angular-icons';
import { KENDO_CHECKBOX, KENDO_INPUTS } from '@progress/kendo-angular-inputs';
import { KENDO_DATE, KENDO_INTL } from '@progress/kendo-angular-intl';
import { KENDO_PAGER, PageSizeChangeEvent } from '@progress/kendo-angular-pager';
import {
  CellClickEvent,
  ExpandEvent,
  FlatBindingDirective,
  KENDO_TREELIST,
  RowClassArgs,
  SelectionItem,
  TreeListComponent,
  TreeListModule,
} from '@progress/kendo-angular-treelist';
import { process, toDataSourceRequest } from '@progress/kendo-data-query';
import { cloneDeep, sumBy, union } from 'lodash';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzOutletModule } from 'ng-zorro-antd/core/outlet';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { debounceTime, finalize, map, Observable, of, Subject, takeUntil } from 'rxjs';
import { VNRMODULE_TOKEN } from '../../../../base/api-config';
import { VnRGridBase } from '../../../../base/grid-base';
import { IVnrModule_Token } from '../../../../common/models/app-api-config.interface';
import { ITreelistNewGetChangeColumnConfig } from '../../interfaces/treelist-new-change-column.interface';
import { VnrTreelistNewColumnModel } from '../../interfaces/treelist-new.interface';
import {
  VnRTreeListNewChangeColumnPosition,
  VnRTreeListNewDataSource,
} from '../../models/vnr-treelist.model';
import { TreeListNewService } from '../../services/vnr-treelist-new.service';
import { VnRTreelistNewChangeColumnBuilder } from '../vnr-treelist-new-change-column/models/vnr-treelist-new-change-column-builder.model';
import { TreelistNewChangeColumnConfigService } from '../vnr-treelist-new-change-column/services/treelist-new-change-column-config.service';
import { TreelistNewChangeColumnService } from '../vnr-treelist-new-change-column/services/treelist-new-change-column.service';
import { VnrTreelistNewChangeColumnComponent } from '../vnr-treelist-new-change-column/vnr-treelist-new-change-column.component';
import { VnrTreelistNewBuilder } from './models/vnr-treelist-new-builder.model';
import { IExportExcel, IExportWord } from '../../../grids/interfaces/export-excel.interface';
@Component({
  selector: 'vnr-treelist-new',
  preserveWhitespaces: false,
  templateUrl: './vnr-treelist-new.component.html',
  styleUrls: ['./vnr-treelist-new.component.scss'],
  standalone: true,
  imports: [
    KENDO_TREELIST,
    KENDO_INPUTS,
    KENDO_CHECKBOX,
    KENDO_PAGER,
    KENDO_DATE,
    KENDO_INTL,
    TreeListModule,
    NgIf,
    NgSwitch,
    NgSwitchCase,
    NgSwitchDefault,
    NgTemplateOutlet,
    NgForOf,
    NzOutletModule,
    CommonModule,
    NzDropDownModule,
    NzButtonModule,
    NzIconModule,
    TranslateModule,
    FormsModule,
    ReactiveFormsModule,
    AsyncPipe,
    IconsModule,
    SVGIconModule,
    VnrTreelistNewChangeColumnComponent,
  ],
})
export class VnrTreelistNewComponent extends VnRGridBase<any> implements OnInit, AfterViewInit {
  @Input() override builder: VnrTreelistNewBuilder = new VnrTreelistNewBuilder();
  @Input() gridName: string = 'grid-name';
  @Input() dataSource: VnRTreeListNewDataSource;
  @Input() dataLocal: any[] = [];
  @Input() dataFormSearch?: any;
  @Input() columns: any;
  @Input() pageSize: number = 50;

  @Input() isOpenChangeColumn?: boolean = false;
  @Input() isChangeColumnNew?: boolean = false;
  @Input() isSupperAdmin: boolean = false;
  @Input() isAddColumnDev: boolean = false;
  @Input() columnTemplates: { [key: string]: TemplateRef<any> };
  @Input() defaultColumnTemplate: TemplateRef<any>;
  @Input() columnHeaderTemplate: TemplateRef<any>;
  @Input() rowActionsTemplate: TemplateRef<any>;
  @Input() columnFooterTemplate: TemplateRef<any>;

  @Input() vnrExpandHandler: (event: any) => boolean; // Sự kiện Đóng child
  @Input() vnrCollapseHandler: (event: any) => boolean; // Sự kiện Mở child
  @Input() vnrRowClass: (event: any) => any; // Sự kiện class của row

  @Output() vnrAfterCollapse: EventEmitter<any> = new EventEmitter<any>();
  @Output() vnrAfterExpand: EventEmitter<any> = new EventEmitter<any>();
  @Output() vnrCellGroupClick: EventEmitter<any> = new EventEmitter<any>();
  @Output() vnrCellClick: EventEmitter<any> = new EventEmitter<any>();
  @Output() vnrDoubleClick: EventEmitter<any> = new EventEmitter<any>();
  @Output() protected getSelectedID = new EventEmitter<string[]>();
  @Output() protected getSelectedDataItem = new EventEmitter<any[]>();
  @Output() protected getDataItem = new EventEmitter<any>();
  @Output() protected vnrEdit: EventEmitter<any> = new EventEmitter<any>();
  @Output() protected vnrDelete: EventEmitter<any> = new EventEmitter<any>();
  @Output() protected vnrViewDetails = new EventEmitter<any>();
  @Output() protected vnrFormatFileClick = new EventEmitter<string>();

  @ViewChild(TreeListComponent) _treeList: TreeListComponent;
  @ViewChild(FlatBindingDirective) dataBinding: FlatBindingDirective;

  private _skip = 0;
  private _pageSize = 50;
  private _exportColumns: any[] = [];
  private _defaultState = Object.assign({}, this.builder.options.queryOption);
  private _gridHeight: number = 0;
  private _isLoadScroll = false;
  private _valueFieldsInit: string = '';
  private _expandedIds: string[] = [];
  private _gridDataOriginal: any = [];
  private _gridDataChild: any = [];
  protected _calcGridHeight$: Subject<any> = new Subject<any>();
  private _destroy$: Subject<any> = new Subject<any>();
  private cellClickItem: any;

  protected dateFormat = 'dd/MM/yyyy';
  protected gridColumns: VnrTreelistNewColumnModel[] = [];
  protected gridDataSource: GridDataResult = {
    data: [],
    total: 0,
  };
  protected gridChildDataSource: GridDataResult = {
    data: [],
    total: 0,
  };
  protected groupKey: string = this.builder.options.configSelectable?.groupKey || 'ParentID';
  protected columnKey: string = this.builder.options.configSelectable?.columnKey || 'ID';
  protected isHovered: boolean = false;
  protected hasLockedColumn: boolean = false;
  protected gridSelectedKeys: any[] = [];
  protected isLoading = false;
  protected builderTreelistChangeColumn: VnRTreelistNewChangeColumnBuilder;
  protected selectedItems: SelectionItem[] = [];
  protected dataSource$: Observable<any[]>;

  constructor(
    @Inject(VNRMODULE_TOKEN) protected _vnrModule_Token: IVnrModule_Token,
    protected _treeListNewService: TreeListNewService,
    protected _treelistNewChangeColumnConfigService: TreelistNewChangeColumnConfigService,
    protected _treelistNewChangeColumnService: TreelistNewChangeColumnService,
    private _cdr: ChangeDetectorRef,
    private _vc: ViewContainerRef,
  ) {
    super();
    this.isLoading = true;
  }
  get allChecked(): boolean {
    return (
      this.gridDataSource?.data?.length > 0 &&
      this.gridDataSource?.data.every((item) => item.checked) &&
      (!this.gridChildDataSource?.data ||
        this.gridChildDataSource?.data?.every((item) => item.checked))
    );
  }
  get someChecked(): boolean {
    return (
      this.gridDataSource?.data?.some((item) => item.checked) ||
      this.gridChildDataSource?.data?.some((item) => item.checked)
    );
  }
  async ngOnInit() {
    this.subscribeCalcGridHeightEvent();
    this.ensureApiAvailable();
    this.builderTreelistChangeColumnComponent();
    await this.initColumns();
    this.initData();
    this.initexpandedKeys();
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.reorderColumn();
    }, 500);
  }
  ngOnDestroy(): void {
    this._destroy$.next(true);
    this._destroy$.complete();
  }
  //#region HostListener
  @HostListener('document:mouseover', ['$event'])
  protected onMouseEnter(event: MouseEvent) {
    const isGridRow = (event.target as HTMLElement)?.classList?.contains('k-table-td');
    if (isGridRow) {
      this.isHovered = true;
    } else {
      this.isHovered = false;
    }
  }
  @HostListener('document:mouseleave', ['$event'])
  protected onMouseLeave(event: MouseEvent) {
    this.isHovered = false;
  }
  @HostListener('window:resize', ['$event'])
  onResize(event: UIEvent) {
    this._calcGridHeight$.next(true);
  }
  //#endregion HostListener
  private subscribeCalcGridHeightEvent(): void {
    this._calcGridHeight$.pipe(takeUntil(this._destroy$), debounceTime(300)).subscribe((res) => {
      if (res && this.builder.options.configHeightGrid?.isAllowCalcRowHeight) {
        setTimeout(() => {
          this.reCalcHeightGrid();
        }, 500);
      }
    });
  }
  private initexpandedKeys(): void {
    if (!this.builder.options.expandedKeyIds) {
      return;
    }
    this.builder.options.expandedKeyIds.forEach((id: string) => {
      const itemExpaned = {
        [this.builder.options.configSelectable?.columnKey]: id,
      };
      this.fetchChildren(itemExpaned);
    });
  }
  private initData() {
    this.isLoading = true;
    if (!this.dataLocal === false) {
      this._gridDataOriginal = cloneDeep(this.dataLocal);
    }
    this.dataSource$ = this.loadDataSource();
    this._calcGridHeight$.next(true);
    this.isLoading = false;
    this._cdr.detectChanges();
  }
  protected hasChildren = (item: any): boolean => {
    return item.HasChildren === true;
  };
  protected fetchChildren = (item: any): Observable<any[]> => {
    let groupKeyValue = item[this.columnKey];
    this.setDataFilter({ [this.groupKey]: groupKeyValue });
    if (!this.dataLocal) {
      let listDateFormat = this.getDateField();
      this._defaultState = cloneDeep(this.builder.options.queryOption);
      this._defaultState.take = 20000 - 1;
      this._defaultState.skip = 0;
      Object.assign(this.builder.options.queryOption, { ValueFields: this._valueFieldsInit });
      return this._treeListNewService.loadDataTreelist(this.dataSource, this._defaultState).pipe(
        map((x: any) => {
          if (x.hasOwnProperty('Data')) delete x['Data'];
          if (x.hasOwnProperty('Total')) delete x['Total'];
          if (x.data && x.data.length > 0) {
            x.data.forEach((item: any) => {
              listDateFormat.forEach((field) => {
                item[field] = this.partDateTime(item[field]);
              });
            });
          }
          let backData = [...this.gridChildDataSource.data, ...x.data];
          this.gridChildDataSource = {
            total: this.gridViewTotal(x),
            data: backData,
          };
          return x.data;
        }),
      );
    } else {
      this._gridDataChild = this.dataLocal.filter((x) => {
        return x[this.groupKey] === item[this.columnKey];
      });
      this.gridChildDataSource.data = [...this.gridChildDataSource.data, ...this._gridDataChild];
      this.gridChildDataSource.total = this.gridChildDataSource.data.length;
      return of(this._gridDataChild);
    }
  };
  private loadDataSource(): Observable<any[]> {
    this.removeDataFilter([this.groupKey]);
    if (!this.dataLocal) {
      let listDateFormat = this.getDateField();
      Object.assign(this.builder.options.queryOption, { ValueFields: this._valueFieldsInit });
      return this._treeListNewService
        .loadDataTreelist(this.dataSource, this.builder.options.queryOption)
        .pipe(
          map((x: any) => {
            if (x.hasOwnProperty('Data')) delete x['Data'];
            if (x.hasOwnProperty('Total')) delete x['Total'];
            if (x.data && x.data.length > 0) {
              x.data.forEach((item: any) => {
                listDateFormat.forEach((field) => {
                  item[field] = this.partDateTime(item[field]);
                });
              });
            }
            this._gridDataOriginal = cloneDeep(x.data);
            let backData = x.data;
            if (this._isLoadScroll) {
              backData = [...this.gridDataSource.data, ...x.data];
            }
            this.gridDataSource = {
              total: this.gridViewTotal(x),
              data: backData,
            };
            this.isLoading = false;
            return backData;
          }),
        );
    } else {
      let dataLocal = this._gridDataOriginal.filter((x) => !x[this.groupKey]);
      const searchForm = this.dataSource.dataFormSearch;
      if (searchForm) {
        Object.keys(searchForm).forEach((searchKey) => {
          let searchValue = this.dataSource.dataFormSearch[searchKey];
          dataLocal = this.filterDataLocal(dataLocal, searchKey, searchValue);
        });
      }
      const _gridData = process(dataLocal, this.builder.options.queryOption);
      if (this._isLoadScroll) {
        _gridData.data = [...this.gridDataSource.data, ..._gridData.data];
      }
      this.gridDataSource = {
        total: this.gridViewTotal(_gridData),
        data: _gridData.data,
      };
      return of(_gridData.data);
    }
  }
  protected onPageSizeChange($event: PageSizeChangeEvent): void {
    const { newPageSize } = $event;
    this.builder.options.queryOption.skip = 0;
    this.builder.options.queryOption.take = parseInt(newPageSize as string) || this._pageSize;
    this.dataSource$ = this.loadDataSource();
    this._calcGridHeight$.next(true);
  }

  protected onPageChange($event: PageChangeEvent): void {
    const { skip, take } = $event;
    this.builder.options.queryOption.skip = skip;
    this.builder.options.queryOption.take = take;
    this.dataSource$ = this.loadDataSource();
  }

  private filterDataLocal<T>(dataSource: T[], key: keyof T, query: string): T[] {
    if (!query || typeof query === 'object') {
      return dataSource;
    }
    return dataSource.filter(
      (item) =>
        !item[key] || String(item[key]).toLowerCase().includes(query?.toString()?.toLowerCase()),
    );
  }
  private gridViewTotal(rsp: any) {
    let total = 0;
    if (!rsp) return total;

    if (rsp.total && rsp.total > 0) {
      total = rsp.total;
    } else if (rsp.totalPages && rsp.totalPages > 0) {
      total = rsp.totalPages;
    }
    return total;
  }
  private partDateTime(d: any): any {
    if (d && d instanceof Date && !isNaN(d.getTime())) {
      return d;
    } else if (d && /\/Date\((\d*)\)\//.exec(d)) {
      return new Date(+(+/\/Date\((\d*)\)\//.exec(d)![1]));
    } else if (d) {
      return new Date(d);
    }
  }
  private getDateField() {
    return union(
      this.gridColumns
        .filter((x) => {
          return x.Format?.toLowerCase()?.startsWith('datetime|');
        })
        .map((x) => x.Name),
      this.builder.options.configColumnSchema && this.builder.options.configColumnSchema?.fieldDate
        ? this.builder.options.configColumnSchema?.fieldDate
        : [],
    );
  }
  protected ensureApiAvailable(): void {
    if (!this.dataSource) {
      this.dataSource = {
        api: {
          url: this._vnrModule_Token.gridConfig_Token.apiGridGetDataSource?.url,
          method: this._vnrModule_Token.gridConfig_Token.apiGridGetDataSource?.method,
        },
      };
    }
    if (!this.dataSource?.api?.url) {
      this.dataSource.api.url = this._vnrModule_Token.gridConfig_Token.apiGridGetDataSource?.url;
    }
    if (!this.dataSource?.api?.method) {
      this.dataSource.api.method =
        this._vnrModule_Token.gridConfig_Token.apiGridGetDataSource?.method;
    }
    if (!this.builder.options?.configApiSupport?.apiGetColumn.url) {
      this.builder.options.configApiSupport.apiGetColumn.url =
        this._vnrModule_Token.gridConfig_Token.apiGetColumn?.url;
    }
    if (!this.builder.options?.configApiSupport?.apiDownload.url) {
      this.builder.options.configApiSupport.apiDownload.url =
        this._vnrModule_Token.gridConfig_Token.apiDownloadUrl?.url;
    }
    if (!this.builder.options?.configApiSupport?.apiExport.url) {
      this.builder.options.configApiSupport.apiExport.url =
        this._vnrModule_Token.gridConfig_Token.apiGridExport?.url;
    }
    if (!this.builder.options?.configApiSupport?.apiExportAll.url) {
      this.builder.options.configApiSupport.apiExportAll.url =
        this._vnrModule_Token.gridConfig_Token.apiGridExportAll?.url;
    }
    if (!this.builder.optionChangeColumn?.apiRestoreChangeColumn?.url) {
      this.builder.optionChangeColumn.apiRestoreChangeColumn.url =
        this._vnrModule_Token.gridConfig_Token.apiRestoreChangeColumn?.url;
    }
    if (!this.builder.optionChangeColumn?.apiSaveChangeColumn?.url) {
      this.builder.optionChangeColumn.apiSaveChangeColumn.url =
        this._vnrModule_Token.gridConfig_Token.apiSaveChangeColumn?.url;
    }
    if (!this.builder.optionChangeColumn?.apiSaveTranslate?.url) {
      this.builder.optionChangeColumn.apiSaveTranslate.url =
        this._vnrModule_Token.gridConfig_Token.apiSaveTranslate?.url;
    }
  }
  private builderTreelistChangeColumnComponent() {
    this.builderTreelistChangeColumn = new VnRTreelistNewChangeColumnBuilder().builderFromGrid(
      this.builder,
    );
    this.builderTreelistChangeColumn.gridName = this.gridName;
    this.builderTreelistChangeColumn.columns = this.columns;
    this.builderTreelistChangeColumn.isOpenChangeColumn = this.isOpenChangeColumn;
    this.builderTreelistChangeColumn.isSupperAdmin = this.isSupperAdmin;
    this.builderTreelistChangeColumn.options.isAddColumnDev = this.isAddColumnDev;
    this.builderTreelistChangeColumn.options.isChangeColumnNew = this.isChangeColumnNew;
  }

  protected async initColumns() {
    let data: Array<VnrTreelistNewColumnModel> = [];
    let gridName = this.gridName;
    let _configGetColumn: ITreelistNewGetChangeColumnConfig = {
      apiGetColumn: this.builder.options.configApiSupport?.apiGetColumn?.url,
      method: this.builder.options.configApiSupport?.apiGetColumn?.method,
      isSupperAdmin: this.isSupperAdmin,
      gridName: gridName,
    };
    if (this.isChangeColumnNew) {
      let _changeColumnConfig = await this._treelistNewChangeColumnConfigService
        .getChangeColumnConfigGrid$(_configGetColumn)
        .toPromise();
      data =
        _changeColumnConfig && _changeColumnConfig.Columns && _changeColumnConfig.Columns.length > 0
          ? _changeColumnConfig.Columns
          : this.columns;
    } else if (this.columns) {
      data = this.columns;
    } else if (this.builder.options.configApiSupport.apiGetColumn) {
      data = await this._treeListNewService.getColumnsConfig(_configGetColumn).toPromise();
    }
    this.gridColumns = data;

    let sumWidth = 0;
    this.gridColumns.forEach((x, i) => {
      sumWidth += x.Width;
      if (this.gridColumns.length === i + 1) {
        if (this.builder.options.configShowHide?.isShowColumnCheck) {
          sumWidth += 50;
        }
        if (
          this.builder.options.configShowHide?.isShowEdit &&
          !this.builder.options.configShowHide?.isShowButtonMenu
        ) {
          sumWidth += 50;
        }
        if (this.builder.options.configShowHide?.isShowButtonMenu) {
          sumWidth += 100;
        }
      }
    });

    if (data.length > 0) {
      let lstColumnName = data?.map((x) => x.Name)?.flat();
      if (lstColumnName && lstColumnName.length > 0) {
        this._valueFieldsInit = lstColumnName?.join(',');
        Object.assign(this.builder.options.queryOption, { ValueFields: this._valueFieldsInit });
      }

      if (!this.builder.options.queryOption.take) {
        if (!data[0].RowOnPage) {
          data[0].RowOnPage = this._pageSize;
        }
        this.builder.options.queryOption.take = parseInt(data[0].RowOnPage);
      }
      if (this._pageSize !== this.builder.options.queryOption.take) {
        this._pageSize = this.builder.options.queryOption.take;
      }

      this._exportColumns =
        cloneDeep(data)
          ?.flatMap((col) => col.Name)
          ?.filter((x: any) => x.Name !== 'ID') || [];
    }

    if (this.gridColumns.length > 0 && this.gridColumns[0] && this.gridColumns[0].OrderColumn) {
      let sortConfig: any = this.gridColumns[0]['OrderColumn'];
      typeof sortConfig == 'string' && (sortConfig = JSON.parse(sortConfig));
      this.builder.options.queryOption.sort = [...[sortConfig]];
    }

    this._defaultState = cloneDeep(this.builder.options.queryOption);
    if (this.columnTemplates) {
      this.gridColumns.forEach((column: VnrTreelistNewColumnModel) => {
        if (this.columnTemplates[column.Name]) {
          column['template'] = this.columnTemplates[column.Name];
        }
      });
    }
    this.hasLockedColumn = this.gridColumns.some((x) => x.Locked);
  }

  protected expandedIdsChange($event: any) {
    this._expandedIds = $event;
  }

  protected rowClass = (context: RowClassArgs) => {
    return this.vnrRowClass ? this.vnrRowClass(context) : '';
  };

  protected cellClickHandler(event: CellClickEvent): void {
    const { dataItem, column } = event;
    this.cellClickItem = dataItem;
    this.vnrCellClick.emit({ event: event });
  }

  protected onEdit($event: any) {
    this.vnrEdit.emit($event);
  }

  protected onDelete($event: any) {
    this.vnrDelete.emit($event);
  }
  protected onViewDetails($event: any) {
    this.vnrViewDetails.emit($event);
  }

  protected onCollapse(args: ExpandEvent): void {
    this._expandedIds = this._expandedIds.filter(
      (id) => id !== args.dataItem?.[this.builder?.options?.configSelectable?.columnKey],
    );
    this.vnrAfterCollapse.emit(args);
  }

  protected onExpand(args: ExpandEvent): void {
    this._expandedIds.push(args.dataItem?.[this.builder?.options?.configSelectable?.columnKey]);
    this.vnrAfterExpand.emit(args);
  }

  //#endregion Event xử lý của treelist kendo

  //#region Tiện ích

  protected reorderColumn(): void {
    if (this._treeList && this._treeList.columns && this._treeList.columns.length > 0) {
      this._treeList.columns
        .filter((x) => x.isColumnGroup)
        .forEach((baseColumn) => {
          try {
            const arrCssClass = baseColumn.cssClass as string[];
            const cssClass = arrCssClass?.join(',') || '';
            const destIndex = parseInt(cssClass?.split('_')?.pop());
            if (destIndex >= 0) {
              this._treeList.reorderColumn(baseColumn, destIndex);
            }
          } catch (error) {
            console.error('[LOGGER] Error ReorderColumn', error);
          }
        });
    }
  }

  protected triggerDetechChanges() {
    this._cdr.detectChanges();
  }

  //#endregion Tiện ích

  //#region Sự kiện xử lý

  protected doubleClicked($event: any) {
    $event.stopPropagation();
    $event.preventDefault();
    this.vnrDoubleClick.emit({ event: $event, record: this.cellClickItem });
  }

  protected onGroupColumnClick($event: any, col: any) {
    this.vnrCellGroupClick.emit({ event: $event, column: col });
  }
  protected onClickFileName(value: string) {
    this.vnrFormatFileClick.emit(value);
  }
  //#endregion Sự kiện xử lý
  //#region Tính chiều cao grid
  protected reCalcHeightGrid() {
    let _windowHeight = window.innerHeight;
    if (this._gridHeight == _windowHeight) {
      return;
    }
    const gridEle = this._vc?.element?.nativeElement.querySelector('.k-grid') as HTMLElement;
    if (!gridEle) {
      return;
    }
    const _gridHeaderHeight = this.getHeaderHeight(gridEle);
    const _footerWithoutGridHeight = this.getFooterWithoutGridHeight(gridEle);
    let gridWrapHeight = gridEle.querySelector('.k-grid-table')?.clientHeight;
    const _gridOffsetTop = this.getElementAbsoluteTop(gridEle);
    _windowHeight -= _gridOffsetTop + _footerWithoutGridHeight;
    if (this.builder.options?.configHeightGrid) {
      const _rowScrollHorizontal =
        this.builder.options.configHeightGrid.rowScrollHorizontal +
        this.builder.options.configHeightGrid.gridBottomMargin;
      _windowHeight -= _rowScrollHorizontal;
      gridWrapHeight += _rowScrollHorizontal;
    }
    let _gridNoRecords =
      gridEle.querySelectorAll('.k-grid-table tr.k-grid-norecords')?.length ||
      gridEle.querySelectorAll('.k-grid-table tr.k-table-row')?.length ||
      1;

    if (_windowHeight <= 0 || _windowHeight > gridWrapHeight) {
      const _gridRowHeight = gridWrapHeight / _gridNoRecords;
      if (this.builder.options.configShowHide.isPageExpand) {
        if (this._pageSize > _gridNoRecords) {
          _gridNoRecords = this._pageSize;
        }
      } else if (this._pageSize >= _gridNoRecords) {
        _gridNoRecords = this._pageSize;
      }
      if (_windowHeight > _gridRowHeight * _gridNoRecords + _gridHeaderHeight) {
        _windowHeight = _gridRowHeight * _gridNoRecords + _gridHeaderHeight;
      }
    }

    this.builder.options.configHeightGrid.gridHeight = _windowHeight;
    this._gridHeight = _windowHeight;
    this._cdr.detectChanges();
  }
  private getElementAbsoluteTop(el: HTMLElement): number {
    let top = 0;
    while (el) {
      top += el.offsetTop;
      el = el.offsetParent as HTMLElement;
    }
    return top;
  }
  private getHeaderHeight(grid: HTMLElement) {
    let headerHeight = 0;
    const _groupPanel = grid.querySelector('kendo-grid-group-panel') as HTMLElement;
    const _gridHeader = grid.querySelector('.k-grid-header') as HTMLElement;

    const groupPanelHeight = _groupPanel?.offsetHeight || _groupPanel?.clientHeight || 0;
    const gridHeaderHeight = _gridHeader?.offsetHeight || _gridHeader?.clientHeight || 0;

    headerHeight += groupPanelHeight;
    headerHeight += gridHeaderHeight;
    return headerHeight;
  }
  private getFooterWithoutGridHeight(grid: HTMLElement) {
    let footerHeight = 0;
    const _gridLoadMore = grid.parentElement?.querySelector('.treelist-loadmore') as HTMLElement;
    const _gridPager = grid.parentElement?.querySelector('.k-pager') as HTMLElement;
    const gridLoadMoreHeight = _gridLoadMore?.offsetHeight || _gridLoadMore?.clientHeight || 0;
    const gridPagerHeight = _gridPager?.offsetHeight || _gridPager?.clientHeight || 0;

    footerHeight += gridLoadMoreHeight;
    footerHeight += gridPagerHeight;
    return footerHeight;
  }
  //#endregion
  protected trackByFn(index: number, item: any): any {
    return item?.name || index;
  }
  protected integerParser = (value: string) => value.replace(/\D/g, '');
  protected getColumnClassNames(col: VnrTreelistNewColumnModel): string {
    return (col.Format && col.Format.toLowerCase().startsWith('number|')) ||
      (col.Format && col.Format.toLowerCase().startsWith('datetime|')) ||
      (this.builder.options?.configColumnSchema &&
        this.builder.options?.configColumnSchema.fieldNumber?.indexOf(col.Name) >= 0) ||
      (this.builder.options?.configColumnSchema &&
        this.builder.options?.configColumnSchema.fieldNumberMoney &&
        this.builder.options?.configColumnSchema.fieldNumberMoney.indexOf(col.Name) >= 0)
      ? (col.Class || '') + ' text-right'
      : col.Class || '';
  }

  protected onSelectAllChange($event: any) {
    const isChecked = ($event.target as HTMLInputElement).checked;
    let rawData = this.gridDataSource?.data || [];
    const rawChildData = this.gridChildDataSource?.data || [];
    rawData = [...rawData, ...rawChildData];
    if (!rawData || rawData.length <= 0) {
      return;
    }
    rawData.forEach((item) => {
      item.checked = isChecked;
    });
    if (this.isGroupStateValid(this.builder.options.queryOption, this._treeList)) {
      let temp = this._treelistNewChangeColumnConfigService.extractItems(rawData) || [];
      this.gridSelectedKeys = temp
        ?.filter((x) => x.checked)
        .map((item) => item[this.builder.options.configSelectable?.columnKey]);
    } else {
      this.gridSelectedKeys = rawData
        .filter((x) => x.checked)
        .map((item) => item[this.builder.options.configSelectable?.columnKey]);
    }
    if (isChecked) {
      this.getDataItem.emit(rawData);
      this.getSelectedDataItem.emit(rawData);
    } else {
      this.getDataItem.emit([]);
      this.getSelectedDataItem.emit([]);
    }
    this.getSelectedID.emit(this.gridSelectedKeys);
  }
  protected checkDataItem(dataItem: any) {
    if (!dataItem) {
      return;
    }
    let rawData = this.gridDataSource?.data || [];
    const rawChildData = this.gridChildDataSource?.data || [];
    rawData = [...rawData, ...rawChildData];
    rawData.forEach((item) => {
      if (
        item[this.builder.options.configSelectable?.columnKey] ==
        dataItem[this.builder.options.configSelectable?.columnKey]
      ) {
        item.checked = dataItem.checked;
      }
    });
    this.gridSelectedKeys = rawData
      .filter((x) => x.checked)
      .map((item) => item[this.builder.options.configSelectable?.columnKey]);
    if (dataItem.checked) {
      this.getDataItem.emit([dataItem]);
    } else {
      this.getDataItem.emit([]);
    }
    this.getSelectedID.emit(this.gridSelectedKeys);
    const _selectedDataItems =
      rawData.filter((ele) =>
        this.gridSelectedKeys.includes(ele[this.builder.options.configSelectable?.columnKey]),
      ) || [];
    this.getSelectedDataItem.emit(_selectedDataItems);
  }

  protected isGroupStateValid(state: any, gridView: any): boolean {
    return (
      state?.group &&
      state.group.length > 0 &&
      gridView?.data.length > 0 &&
      ((gridView?.data[0]['Items'] && Array.isArray(gridView?.data[0]['Items'])) ||
        (gridView?.data[0]['items'] && Array.isArray(gridView?.data[0]['items'])))
    );
  }
  //#region tính số total row
  protected funcCalcCountLoadMore(dataGrid: any) {
    let result: number = 0;
    if (dataGrid) {
      if (
        this.builder.options.queryOption?.group &&
        this.builder.options.queryOption.group.length > 0 &&
        dataGrid.length > 0 &&
        ((dataGrid[0]['Items'] && Array.isArray(dataGrid[0]['Items'])) ||
          (dataGrid[0]['items'] && Array.isArray(dataGrid[0]['items'])))
      ) {
        if (dataGrid[0]?.ItemCount) {
          result =
            sumBy(dataGrid, (item: any) => {
              return item?.ItemCount;
            }) || 0;
        } else {
          let data = this._treelistNewChangeColumnConfigService.extractItems(dataGrid);
          return data?.length || 0;
        }
      } else {
        result = dataGrid?.length;
      }
    }
    return result;
  }
  //#endregion
  //#region Public Method
  public setOpenChangeColumn(isOpenChangeColumn: boolean): void {
    if (!this.builder.optionChangeColumn) {
      return;
    }
    this.isOpenChangeColumn = isOpenChangeColumn;
    this.builderTreelistChangeColumn.isOpenChangeColumn = isOpenChangeColumn;
  }
  public setChangeColumnVersion(isNewVersion: boolean): void {
    if (!this.builder.optionChangeColumn) {
      return;
    }
    this.isChangeColumnNew = isNewVersion;
    if (this.builderTreelistChangeColumn.options) {
      this.builderTreelistChangeColumn.options.isChangeColumnNew = isNewVersion;
    }
  }
  public setChangeColumnPosition(position: VnRTreeListNewChangeColumnPosition): void {
    if (!position) {
      return;
    }
    if (this.builder?.optionChangeColumn) {
      this.builder.optionChangeColumn.position = {
        bottom: position.bottom ?? this.builder.optionChangeColumn?.position?.bottom,
        top: position.top ?? this.builder.optionChangeColumn?.position?.top,
        left: position.left ?? this.builder.optionChangeColumn?.position?.left,
        right: position.right ?? this.builder.optionChangeColumn?.position?.right,
        width: position.width ?? this.builder.optionChangeColumn?.position?.width,
        height: position.height ?? this.builder.optionChangeColumn?.position?.height,
      };
    }
    if (this.builderTreelistChangeColumn?.options) {
      this.builderTreelistChangeColumn.options.position = {
        bottom: position.bottom ?? this.builderTreelistChangeColumn.options.position?.bottom,
        top: position.top ?? this.builderTreelistChangeColumn.options.position?.top,
        left: position.left ?? this.builderTreelistChangeColumn.options.position?.left,
        right: position.right ?? this.builderTreelistChangeColumn.options.position?.right,
        width: position.width ?? this.builderTreelistChangeColumn.options.position?.width,
        height: position.height ?? this.builderTreelistChangeColumn.options.position?.height,
      };
    }
  }
  public removeDataFilter(filterKeys: string[]): void {
    if (this.dataSource?.dataFormSearch) {
      filterKeys.forEach((field) => {
        if (field in this.dataSource.dataFormSearch) {
          delete this.dataSource.dataFormSearch[field];
        }
      });
    }
  }
  public setDataFilter(filter: any): void {
    if (!this.dataSource.dataFormSearch) {
      this.dataSource.dataFormSearch = {};
    }
    Object.assign(this.dataSource.dataFormSearch, filter);
  }
  public loadMore(): void {
    this.isLoading = true;
    this._isLoadScroll = true;
    this.builder.options.queryOption.skip += this._pageSize;
    this.dataSource$ = this.loadDataSource();
    this._calcGridHeight$.next(true);
    this.isLoading = false;
    this._cdr.detectChanges();
  }
  public vnrReadGrid() {
    this.isLoading = true;
    this.builder.options.queryOption = this._defaultState;
    this.builder.options.queryOption.take =
      this.gridColumns[0] && this.gridColumns[0].RowOnPage
        ? parseInt(this.gridColumns[0].RowOnPage)
        : this._pageSize;
    this.gridSelectedKeys = [];
    this.getSelectedID.emit([]);
    this.getDataItem.emit([]);
    this.getSelectedDataItem.emit([]);
    this.dataSource$ = this.loadDataSource();
    this.isLoading = false;
    this._cdr.detectChanges();
  }

  public vnrReloadGrid() {
    this.builder.options.queryOption = this._defaultState;
    this.builder.options.queryOption.take =
      this.gridColumns[0] && this.gridColumns[0].RowOnPage
        ? parseInt(this.gridColumns[0].RowOnPage)
        : this._pageSize;
    this.gridSelectedKeys = [];
    this.getSelectedID.emit([]);
    this.getDataItem.emit([]);
    this.getSelectedDataItem.emit([]);
    this.ngOnInit();
  }

  public vnrcloseAndRefreshGird(evt: any) {
    this.setOpenChangeColumn(evt.isShowPopup);
    if (evt.isRefresh) {
      this.vnrReloadGrid();
    }
  }
  //#endregion
  //#region load cấu hình cột mới
  protected vnrReloadGridChangeColumn($event) {
    this.isLoading = true;
    if ($event) {
      this.loadColumnConfig();
    }
  }
  protected loadColumnConfig() {
    let gridName = this.gridName;
    let _configGetColumn: ITreelistNewGetChangeColumnConfig = {
      apiGetColumn: this.builder.options.configApiSupport?.apiGetColumn?.url,
      method: this.builder.options.configApiSupport?.apiGetColumn?.method,
      isSupperAdmin: this.isSupperAdmin,
      gridName: gridName,
    };
    this._treelistNewChangeColumnConfigService
      .getChangeColumnConfigGrid$(_configGetColumn)
      .pipe(
        takeUntil(this._destroy$),
        finalize(() => this._calcGridHeight$.next(true)),
      )
      .subscribe((columnConfig: any) => {
        if (columnConfig) {
          let _changeColumnConfig = columnConfig;
          if (!columnConfig.Columns === false) {
            this.gridColumns = columnConfig.Columns; //cột
          }
          this.hasLockedColumn = this.gridColumns.some((x) => x.Locked);
        }
      });
    setTimeout(() => {
      this.isLoading = false;
    }, 1000);
  }
  //#endregion
  //#region Export
  public onExcelExport($event: any, param?: any): Observable<any> {
    $event.preventDefault();
    let state = cloneDeep(this.builder.options.queryOption && this.builder.options.queryOption);
    state ? (state.take = 20000 - 1) : {};

    const valueFields = this._getExportValueFields();
    const payload = this._buildExportPayload(state, valueFields, param, 'excel');
    let urlApi =
      this.gridSelectedKeys && this.gridSelectedKeys.length === 0
        ? this.builder.options.configApiSupport?.apiExportAll?.url
        : this.builder.options.configApiSupport?.apiExport?.url;
    return this._treeListNewService.postExportExcel(urlApi, payload);
  }

  /**
   * New Event Export Excel
   * @param config Using `IExportExcel`
   * @returns Observable<any>
   */
  public onExportExcel(config: IExportExcel): Observable<any> {
    let state = cloneDeep(this.builder.options.queryOption && this.builder.options.queryOption);
    state ? (state.take = 20000 - 1) : {};
    if (this._exportColumns) {
      this._exportColumns = this._exportColumns.filter((x) => !x.Hidden && !x.IsHiddenConfig);
    }

    const defaultValueFields = this._exportColumns?.length > 0 ? this._exportColumns.join(',') : '';
    const valueFields = config?.valueFields || this._getExportValueFields() || defaultValueFields;
    const payload = this._buildExportPayload(state, valueFields, config.params, 'excel');
    let urlApi =
      this.gridSelectedKeys && this.gridSelectedKeys.length === 0
        ? config.urlExportAll
        : config.urlExportSelected;

    return this._treeListNewService.postExportExcel(urlApi, payload);
  }

  /**
   *
   * @param config Using `IExportWord`
   * @returns Observable<any>
   */
  public onExportWord(config: IExportWord): Observable<any> {
    if (!config.urlExportAll && !config.urlExportSelected) return of(null);
    let state = cloneDeep(this.builder.options.queryOption && this.builder.options.queryOption);
    state ? (state.take = 20000 - 1) : {};

    if (this._exportColumns) {
      this._exportColumns = this._exportColumns.filter((x) => !x.Hidden && !x.IsHiddenConfig);
    }

    const defaultValueFields = this._exportColumns?.length > 0 ? this._exportColumns.join(',') : '';
    const valueFields = config?.valueFields || this._getExportValueFields() || defaultValueFields;
    const payload = this._buildExportPayload(state, valueFields, config.params, 'word');
    let urlApi =
      this.gridSelectedKeys && this.gridSelectedKeys.length === 0
        ? config.urlExportAll
        : config.urlExportSelected;
    return this._treeListNewService.postExportWord(urlApi, payload);
  }
  private _getExportValueFields(): string {
    return (
      this.gridColumns
        ?.filter((x) => !x.Hidden && !x.IsHiddenConfig)
        ?.flatMap((col) =>
          col.Type === 'group' ? col.MultiColumn?.map((multiCol) => multiCol.Name) : col.Name,
        )
        ?.join(',') || ''
    );
  }
  private _buildExportPayload(
    state: any,
    valueFields: string,
    params: any,
    exportBy: 'excel' | 'word',
  ): any {
    let request = toDataSourceRequest(state);
    let data = {
      ...request,
      selectedIds: this.gridSelectedKeys ? this.gridSelectedKeys.join(',') : '',
      IsExport: true,
      IsPortal: true,
      exportBy,
      valueFields,
      ...(params || {}),
    };

    data['PageIndex'] = data['page'] || 1;
    data['PageSize'] = data['pageSize'] || state?.take;

    if (!this.dataSource.dataFormSearch === false) {
      Object.assign(data, this.dataSource.dataFormSearch);
    }

    if (params && !params.params === false) {
      Object.assign(data, params.params);
    }

    if (!this.dataSource.storeName === false) {
      const formData = new FormData();
      for (const key in data) {
        formData.append(key, data[key]);
      }
      return formData;
    }

    return data;
  }

  //#endregion
}
