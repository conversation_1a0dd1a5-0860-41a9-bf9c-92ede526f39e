import {
  Component,
  Input,
  ChangeDetectionStrategy,
  ViewChild,
  EventEmitter,
  Output,
  TemplateRef,
  SimpleChanges,
  OnInit,
  OnChanges,
  Inject,
} from '@angular/core';
import {
  VnrToolbarNewComponent,
  VnrGridNewComponent,
  VnrGridNewBuilder,
  VnrButtonNewBuilder,
  VnrToolbarNewBuilder,
  VnrComboBoxBuilder,
  VnrComboBoxComponent,
  VnrButtonNewComponent,
  VnrTreeViewBuilder,
  IVnrModule_Token,
  VNRMODULE_TOKEN,
  VnrOrgComponent,
  VnrTreeViewComponent,
  VnrButtonFactory,
} from '@hrm-frontend-workspace/vnr-module';
import { gridDefineColumns, gridDefineColumnsSummaryResult } from '../../data/column.data';
import {
  appraisalsKpiDataSource,
  appraisalsKpiSummaryResultDataSource,
} from '../../data/datasource.data';
import { CommonModule, NgIf, NgSwitch, NgSwitchCase, NgSwitchDefault } from '@angular/common';

import { VnrLettersAvatarComponent, VnrTagComponent } from '@hrm-frontend-workspace/ui';
import { TranslateModule } from '@ngx-translate/core';
import { debounceTime, Subject, takeUntil } from 'rxjs';
import { AppraisalsKpiTab } from '../../models/appraisals-kpi.model';
import { cloneDeep } from 'lodash';
import {
  FormsModule,
  ReactiveFormsModule,
  UntypedFormBuilder,
  UntypedFormGroup,
} from '@angular/forms';
import { appraisalsKpiPositionDataSource } from '../../data/datasource-component.data';
import { appraisalsKpiDepartmentDataSource } from '../../data/datasource-component.data';
import {
  appraisalsGradeFormat,
  appraisalsGradeTextFormat,
  appraisalsKpiStatusFormat,
} from '../../data/appraisals-kpi.data';
import { vnrUtilities } from '@hrm-frontend-workspace/vnr-module';
import { ResponseStatus } from '@hrm-frontend-workspace/models';
import { CommonService } from '@hrm-frontend-workspace/core';
import { AppraisalsKpiFacade } from '../../facade/appraisals-kpi.facade';
import { Router, ActivatedRoute } from '@angular/router';

@Component({
  selector: 'appraisals-kpi-grid',
  templateUrl: './appraisals-kpi-grid.component.html',
  styleUrls: ['./appraisals-kpi-grid.component.scss'],
  imports: [
    FormsModule,
    ReactiveFormsModule,
    CommonModule,
    NgSwitch,
    NgSwitchCase,
    NgSwitchDefault,
    NgIf,
    VnrToolbarNewComponent,
    VnrGridNewComponent,
    VnrComboBoxComponent,
    VnrButtonNewComponent,
    VnrLettersAvatarComponent,
    VnrTagComponent,
    VnrTreeViewComponent,
    TranslateModule,
  ],
})
export class AppraisalsKpiGridComponent implements OnInit, OnChanges {
  @Input() tabFilter: number = AppraisalsKpiTab.APPRAISALS_KPI_TAB_ALL;
  @Output() reloadTabCount: EventEmitter<boolean> = new EventEmitter<boolean>();
  @ViewChild('vnrGrid', { static: true }) gridControl: VnrGridNewComponent;

  private _dataFormSearch: any = {};
  private _statusFormat = appraisalsKpiStatusFormat;
  private _gradeFormat = appraisalsGradeFormat;
  private _gradeTextFormat = appraisalsGradeTextFormat;
  private _btnFactory: VnrButtonFactory = VnrButtonFactory.init();
  protected gridName: string = 'appraisals-360-grid';
  protected dataLocal: any = [];
  protected columns: any = [];
  protected selectedItem = [];
  protected builderOrg: VnrTreeViewBuilder = new VnrTreeViewBuilder();
  protected builderPosition: VnrComboBoxBuilder = new VnrComboBoxBuilder();
  protected builderButtonApprove: VnrButtonNewBuilder;
  protected formGroup: UntypedFormGroup = this._formBuider.group({
    department: [''],
    position: [''],
  });

  protected builderGrid: VnrGridNewBuilder;
  protected builderToolbar: VnrToolbarNewBuilder;
  protected builderButtonCustom: VnrButtonNewBuilder;
  constructor(
    private _formBuider: UntypedFormBuilder,
    private _commonService: CommonService,
    private _appraisalsKpiFacade: AppraisalsKpiFacade,
    @Inject(VNRMODULE_TOKEN) private _vnrModule_Token: IVnrModule_Token,
    private _router: Router,
    private _route: ActivatedRoute,
  ) {}
  ngOnInit() {
    this.builderComponent();
    this.initGridData();
  }
  ngOnChanges(changes: SimpleChanges): void {
    const tabFilterChange = changes['tabFilter'];
    if (tabFilterChange && !tabFilterChange.firstChange) {
      this.tabFilter = tabFilterChange.currentValue;
      this.initGridData();
    }
  }
  private initGridData() {
    this.gridName = 'appraisals-kpi-grid';
    this.dataLocal = cloneDeep(appraisalsKpiDataSource);
    this.columns = cloneDeep(gridDefineColumns);
    if (this.tabFilter === AppraisalsKpiTab.APPRAISALS_KPI_TAB_SUMMARYRESULT) {
      this.gridName = 'appraisals-kpi-grid-summary-result';
      this.dataLocal = cloneDeep(appraisalsKpiSummaryResultDataSource);
      this.columns = cloneDeep(gridDefineColumnsSummaryResult);
    }
    this.builderGridComponent();
    this.builderToolbarComponent();
  }
  private builderComponent() {
    this.builderOrg.builder({
      label: '',
      placeholder: 'objEval.Appraisals.selectAllOrg',
      valueField: 'OrderNumber',
      textField: 'Name',
      childKey: 'ListChild',
      options: {
        hasFeedBack: false,
        checkable: true,
        maxTagCount: 1,
        scrollX: true,
      },
      dataSource: appraisalsKpiDepartmentDataSource,
      //serverSide: {
      //  urlApi: `${this._vnrModule_Token.apiConfig_Token?.apiUrl}/api/Cat_GetData/GetOrgTreeView`,
      //  method: 'get',
      //},
    });
    this.builderPosition.builder({
      label: '',
      placeholder: 'objEval.Appraisals.selectAllPosition',
      textField: 'PositionNameAndCode',
      valueField: 'ID',
      //serverSide: {
      //  urlApi: `${this._vnrModule_Token.apiConfig_Token?.apiUrl}/api/Att_GetData/GetMultiPosition`,
      //  method: 'GET',
      //  data: { text: '', TextField: 'PositionName' },
      //},
      dataSource: appraisalsKpiPositionDataSource,
      options: {
        allowValueObject: true,
        hasFeedBack: false,
      },
    });
    this.builderButtonApprove = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'objEval.Appraisals.btnAprove',
      options: {
        classNames: ['btn-approve'],
        style: 'green',
        icon: {
          fontIcon: 'check',
        },
      },
    });
    this.builderButtonCustom = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: 'custom',
    });
  }
  private builderToolbarComponent() {
    this.builderToolbar = new VnrToolbarNewBuilder({
      isShowConfig: true,
      gridName: this.gridName,
      isSupperAdmin: true,
      gridRef: this.gridControl,
      permission: '',
      screenName: 'appraisals-execution-list-grid',
      storeName: '',
      options: {
        configButtonChangeColumn: {
          isShow: true,
          isConfigMenuDisplay: true,
          isChangeColumnNew: true,
          isDisabled: false,
          configButtonSetting: {
            isShow: true,
            isDisabled: true,
          },
        },
        configButtonExport: {
          isShowBtnExcelAll: true,
          isShowBtnWord: true,
          isShowBtnExcelByTemplate: true,
        },
        configButtonDelete: {
          isShow: true,
          isDisabled: false,
        },
        configQuickSearch: {
          textPlaceHolder: 'Tìm kiếm theo tên, mã...',
          searchKey: 'ProfileName',
        },
        isSetBackground: false,
        buttonMoreFlexDirection: 'inherit',
        isExpanded: false,
      },
    });
  }

  private builderGridComponent() {
    this.builderGrid = new VnrGridNewBuilder({
      options: {
        configHeightGrid: {
          isAllowCalcRowHeight: true,
        },
        configIndexColumn: {
          isShow: true,
          width: 40,
        },
        configSelectable: {
          columnKey: 'Id',
        },
        configShowHide: {
          isPageExpand: false,
          isShowDelete: false,
          isShowEdit: false,
        },
        configCommandColumn: {
          isEnabledMenuAction: true,
        },
        configGroupable: {
          isEnabled: false,
        },
      },
    });
  }

  protected getSelectedID($event: any) {
    this.selectedItem = $event;
  }
  protected getDataItem($event: any) {}
  protected onOpenDetail($event: any) {
    const { record } = $event;
    const appraisalId = record?.PerformanceID;
    const queryParams: any = {
      ProfileID: record?.ProfileID1 || '1',
    };
    this._router.navigate(['/objEval/eva-appraisals/appraisals/form-appraisal', appraisalId], {
      relativeTo: this._route,
      queryParamsHandling: 'merge',
      queryParams: queryParams,
    });
  }
  protected onGridEdit($event: any) {}
  protected onGridDelete($event: any) {}
  protected onGridViewDetail($event: any) {}
  protected onGridCellClick($event: any) {}
  public setDataFilter(data: any): void {
    this._dataFormSearch = data;
    this.gridControl.setDataFilter(data);
  }
  public reloadGridData(): void {
    this.gridControl.vnrReadGrid();
  }
  public getSelectedIDs() {
    return this.selectedItem || [];
  }
  protected getColorStatus(value: any): string {
    return this._statusFormat[value];
  }
  protected getColorGrade(value: any): string {
    return this._gradeFormat[value];
  }
  protected getColorGradeText(value: any): string {
    return this._gradeTextFormat[value];
  }
  protected onChangeFitler($event: any) {
    this.reloadTabCount.emit();
  }
  public onChangeTabFilter($event: any): void {
    this.tabFilter = $event;
  }
  protected onModelChangeOrg($event: any) {
    this.setDataFilter({ OrderNumber: $event?.join() });
    this.reloadGridData();
    this.reloadTabCount.emit();
  }
  protected onModelChangePosition($event: any) {
    debugger;
    this.setDataFilter({ PositionId: $event });
    this.reloadGridData();
    this.reloadTabCount.emit();
  }
  protected isShowBtnApprove(): boolean {
    return (
      this.tabFilter === AppraisalsKpiTab.APPRAISALS_KPI_TAB_TODO &&
      this.getSelectedIDs().length > 0
    );
  }
  public onApprove($event: any): void {
    const arrIDCheck: any = cloneDeep(this.getSelectedIDs());
    if (!arrIDCheck || !Array.isArray(arrIDCheck) || arrIDCheck.length === 0) {
      this._commonService.notification({
        type: 'error',
        message: 'common.grid.noSelectedId',
      });
      return;
    }
    this._appraisalsKpiFacade.approveAppraisals(arrIDCheck).subscribe((dataResult: any) => {
      if (dataResult && dataResult['Status'] === ResponseStatus.SUCCESS) {
        this._commonService.notification({
          type: 'success',
          message: dataResult['Message'] || 'common.notification.actionSuccess',
        });
      } else {
        this._commonService.notification({
          type: 'error',
          message: dataResult['Message'] || 'common.notification.descErr404',
        });
      }
    });
  }
}
