{"compilerOptions": {"target": "es2022", "forceConsistentCasingInFileNames": true, "strict": true, "noImplicitOverride": false, "noPropertyAccessFromIndexSignature": false, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "strictNullChecks": false, "strictFunctionTypes": false, "allowSyntheticDefaultImports": true, "skipDefaultLibCheck": true, "noImplicitAny": false, "emitDecoratorMetadata": true, "experimentalDecorators": true, "skipLibCheck": true}, "files": [], "include": [], "references": [{"path": "./tsconfig.editor.json"}, {"path": "./tsconfig.app.json"}], "extends": "../../tsconfig.base.json", "angularCompilerOptions": {"enableI18nLegacyMessageIdFormat": false, "strictInjectionParameters": true, "strictInputAccessModifiers": true, "strictTemplates": true}}