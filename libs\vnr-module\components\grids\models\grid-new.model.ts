import { Grid<PERSON>ataR<PERSON>ult, GroupKey, PagerType } from '@progress/kendo-angular-grid';
import { IVnrApiEndpoint } from '../../../common/models/app-api-config.interface';

export class VnRGridNewDataSource {
  storeName?: string;
  api?: VnRGridNewDataSourceApi;
  dataFormSearch?: any;
  isAutobind?: boolean;
  isServerSupport?: boolean;
  isLazyloadDatasource?: boolean;
}
export class VnRGridNewDataSourceApi {
  url?: string;
  method?: string;
}
export class VnRGridNewConfigApiSupport {
  apiExport?: IVnrApiEndpoint;
  apiExportAll?: IVnrApiEndpoint;
  apiDownload?: IVnrApiEndpoint;
  apiGetColumn?: IVnrApiEndpoint;
}
export class VnRGridNewConfigShowHide {
  isShowEdit?: boolean = true;
  isShowDelete?: boolean = true;
  isShowViewDetail?: boolean = false;
  isShowColumnCheck?: boolean = true;
  isShowColumnGroupCheck?: boolean = false;
  isPageExpand?: boolean = false;
  isShowRefresh?: boolean = false;
  isShowButtonMenu?: boolean = false;
  isExpendButtonLoadMore?: boolean = false;
  isShowTempColumn?: boolean = false;
  isShowMenuColumn?: boolean = false;
}
export class VnRGridNewConfigTitle {
  alignment?: 'Left' | 'Right' | 'Center' | 'Justify' = 'Center';
  prefixTitleColumn?: string = 'LevelEva';
}
export class VnRGridNewConfigHeightGrid {
  gridHeight?: number = 400;
  rowThreshold?: number = 8;
  rowScrollHorizontal?: number = 8;
  isAllowCalcRowHeight?: boolean = false;
  isHeightByRow?: boolean = false;
  rowHeight?: number = null;
}

export class VnRGridNewConfigColumnSchema {
  fieldBoolean?: string[] = [];
  fieldDate?: string[] = [];
  fieldNumber?: string[] = [];
  fieldNumberMoney?: string[] = [];
}
export class VnRGridNewConfigSelectable {
  mode?: 'multiple' | 'single' = 'multiple';
  columnKey?: string = 'ID';
  isEnabled?: boolean = true;
  isCheckboxOnly?: boolean = true;
}
export class VnRGridNewConfigIndexColumn {
  width?: number = 76;
  isShow?: boolean = false;
  isLocked?: boolean = false;
}
export class VnRGridNewConfigExpandDetail {
  by?: any = null;
  keys?: [] = [];
}
export class VnRGridNewConfigEvent {
  rowClass?: () => any;
}
export class VnRGridNewConfigPageable {
  buttonCount?: number = 10;
  type?: PagerType = 'numeric';
  isShowPageSize?: boolean = false;
  pageSizes?: number[] = [10, 20, 50, 100];
  position?: 'top' | 'bottom' | 'both' = 'bottom';
  isShowInfo?: boolean = true;
  isPreviousNext?: boolean = true;
  isResponsive?: boolean = false;
}
export class VnRGridNewConfigGroupable {
  isEnabled?: boolean = false;
  isShowFooter?: boolean = false;
  emptyText?: string = 'vnrcontrol.common.dropGroupGrid';
}
export class VnRGridNewConfigCommandColumn {
  isEnabledMenuAction?: boolean = false;
  width?: number = 100;
}

export class VnRGridNewConfigExpanded {
  groupKey?: GroupKey[] = [];
  isExpanded?: boolean = true;
}

export class VnRGridNewConfigEditInline {
  apiSaveChange?: string;
  readonlyColumns?: string[] = [];
  builderConfigByColumn?: any = {};
  isEnableEditRowClick?: boolean = true;
  isLoadAllData?: boolean = false;
  isShowColumnAction?: boolean = true;
  commandColumnWidth?: number = 90;
  configValidate?: any = {};
  isShowAddRowGroupData?: boolean = true;
  isEditAllRow?: boolean = false;
  isShowSave?: boolean = true;
  isShowIconEdited?: boolean = true;
  isShowMessenger?: boolean = true;
  columnsRequiredEdit?: string[];
}

export class VnRGridNewConfigEditIncell {
  apiSaveChange?: string;
  configValidate?: any = {};
  isShowMessenger?: boolean = true;
  isAutoSave?: boolean = true;
  isShowBtnSave?: boolean = false;
  isInvalidData?: boolean = false;
  isAllowEditOtherCell?: boolean = false;
  isAllowEditTemplateCellNew?: boolean = false;
  readonlyColumns?: string[] = [];
  fieldDisabled?: string = '';
  builderConfigByColumn?: any = {};
  columnsRequiredEdit?: string[];
}
