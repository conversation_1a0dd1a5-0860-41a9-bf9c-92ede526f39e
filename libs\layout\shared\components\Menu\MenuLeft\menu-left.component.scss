@import 'mixins';

:host {
  height: 100%;
  width: 100%;
}

/////////////////////////////////////////////////////////////////////////////////////////
/* MENU COMPONENT */
.menu {
  background: $white !important;
  color: $text;
  transition: none;
  display: block;
  min-height: 100%;
  font-size: rem(15);

  ::ng-deep {
    .ng-scroll-content {
      width: 100% !important;
    }
    .ant-layout-sider-trigger {
      display: none !important;
    }
    .ant-menu-title-content:has(.menu-line-icon) {
      display: flex !important;
    }
  }

  &__collapse {
    position: fixed;
    bottom: 5px;
    left: 15px;

    &__active {
      button {
        height: 32px;
      }
    }

    &__inactive {
      button {
        height: 32px;
      }
    }

    button {
      transition: width 0s;
      background: rgb(0, 0, 0, 0.12);
      color: $white;
      border: 0;
    }
  }

  .menuOuter {
    position: fixed;
    height: 100%;
    width: inherit;
    max-width: inherit;
  }

  /////////////////////////////////////////////////////////////////////////////////////////
  /* LOGO */
  .logoContainer {
    height: 64px;
    display: flex;
    align-items: center;
    padding: 0 rem(15) 0 rem(28);

    .logo {
      display: flex;
      flex-direction: row;
      align-items: center;
      height: rem(24);
      white-space: nowrap;
      width: 100%;
    }
    .name {
      font-size: rem(20);
      font-weight: 700;
      color: $black;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      max-width: 90%;
      display: block;
    }
    .descr {
      margin-left: rem(11);
      padding-left: rem(11);
      height: rem(38);
      line-height: rem(38);
      border-left: 1px solid $gray-2;
      font-size: rem(15);
    }
  }

  /////////////////////////////////////////////////////////////////////////////////////////
  /* ICON */
  .icon {
    font-size: rem(15);
    // text-align: center;
    // position: absolute;
    // right: 14px;
    // width: 24px;
    // top: 11px;
    // margin: 0 !important;
    // line-height: 1 !important;
    // color: $gray-5;
    // font-size: rem(24);
    // transition: color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  }

  /////////////////////////////////////////////////////////////////////////////////////////
  /* COLLAPSE TRIGGER BUTTON */
  ::ng-deep {
    .ant-layout-sider-trigger {
      background: $gray-1;
      color: $text;
      transition: none;
    }

    .ant-menu-inline-collapsed + .banner {
      display: none;
    }
  }

  /////////////////////////////////////////////////////////////////////////////////////////
  /* ANTD MENU CUSTOMIZATION */
  .navigation {
    padding: 0 8px;
    transition: background 0s, width 0.0001s !important; // collapsed state ant-menu fix
    background: transparent;
    border-right: none;
    color: $text;

    .menu-left {
      &__item {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: rem(40);
        // max-height: rem(54);
        // margin: 0 !important;
        margin: 0 0 4px 0 !important;
        width: 100%;

        &.active-parent {
          background-color: rgb(0 0 0 / 30%) !important;
        }

        &.ant-menu-submenu-inline {
          flex-wrap: wrap;

          ::ng-deep .ant-menu-submenu-title {
            min-height: rem(40) !important;
            max-height: rem(40) !important;
            padding-right: 15px;

            .title {
              display: flex;
              align-items: center;
              width: 100%;
            }
          }
        }
      }
    }

    .title {
      font-size: rem(15);
    }

    ::ng-deep {
      .ant-menu-submenu > .ant-menu {
        background: transparent;
      }
      .ant-menu-submenu-arrow {
        opacity: 0.55 !important;
        right: 6px;
      }

      .ant-menu-inline .ant-menu-item,
      .ant-menu-inline .ant-menu-submenu-title {
        width: 100%;
      }

      .ant-menu-item-group-title {
        color: $gray-4 !important;
        font-size: rem(12);
        text-transform: uppercase;
        letter-spacing: 2px;
        transition: none;
      }

      .ant-menu-submenu-title {
        margin: 0 !important;
        transition: none !important;
        background: transparent !important;
        color: $text;
        width: 100%;
        height: 100%;

        &:hover {
          color: $primary;
        }
      }

      .ant-menu-submenu {
        border-radius: 8px;
        will-change: transform;
      }

      .ant-menu-item {
        margin-bottom: 4px;
        border-radius: 8px;
        outline: none;
        transition: background 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) !important;
        > span {
          color: $text;
        }

        > a {
          color: $text;

          &:hover {
            color: $primary;

            .icon {
              color: $primary;
            }
          }
        }
      }

      .ant-menu-submenu-selected {
        :global(.ant-menu-submenu-title),
        .icon {
          color: $primary;
        }
      }

      .ant-menu-submenu-active {
        .icon {
          color: $primary;
        }
      }

      .ant-menu-submenu-open,
      .ant-menu-submenu-active,
      .ant-menu-item-active {
        background: $gray-2 !important;
      }

      .ant-menu-item-selected {
        background: $primary !important;
        border-radius: 8px;

        > a,
        > span {
          color: $white !important;

          .icon {
            color: $white !important;
          }
        }

        &:after {
          display: none;
        }
      }

      .ant-menu-item-active {
        > span {
          color: $primary;
        }
      }

      .ant-menu-item-disabled {
        > span {
          color: $gray-4;
        }
      }

      // Đường kẻ dọc cho submenu
      .menu-left__item.ant-menu-submenu {
        ul {
          position: relative;

          &:after {
            content: '';
            position: absolute;
            top: 0;
            width: 2px;
            height: calc(100% - 20px);
            background-color: $white;
            left: 15px;
            opacity: 0.55;
          }

          // Đảm bảo tất cả items con đều có đường kẻ ngang
          .ant-menu-item {
            position: relative;

            &:after {
              content: '';
              position: absolute;
              top: 50%;
              transform: translateY(-50%);
              height: 2px;
              width: 15px;
              background: $white;
              left: 15px;
              z-index: 1;
              opacity: 0.55;
            }
          }
        }
      }

      // Đường kẻ cho menu items
      li.menu-left__item.ant-menu-item {
        position: relative;

        &:before {
          content: '';
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          left: 15px;
          width: 15px;
          height: 2px;
          background: $white;
          z-index: 1;
          opacity: 0.55;
        }
      }

      // Điều chỉnh padding cho menu items
      .ant-menu-submenu-title {
        padding-left: 11px !important;
        &:has(.menu-left__item--collapsed) {
          padding-left: 0 !important;
        }
      }

      .ant-menu-item {
        padding-left: 24px !important;
        padding-right: 0 !important;
      }
      .ant-menu-item:has(.menu-left__item--hidden-icon) {
        margin-left: 0 !important;
        .ant-menu-title-content {
          .menu-left__item__content {
            padding-left: 10px !important;
          }
          margin-left: 7px !important;
          border-radius: 8px !important;
          &:hover {
            background: $primary;
            color: $white;
          }
        }
      }

      .menu-left__item--child {
        padding-left: 11px !important;
        &:before {
          opacity: 0 !important;
        }
        &:has(.menu-left__item--collapsed) {
          padding-left: 0 !important;
        }
      }
    }
  }

  /////////////////////////////////////////////////////////////////////////////////////////
  /* COLLAPSED STYLES */
  &.ant-layout-sider-collapsed {
    ::ng-deep {
      @at-root {
        .ant-menu-inline-collapsed-tooltip .icon {
          display: none;
        }
      }

      .badge {
        position: absolute;
        z-index: 1;
        top: 3px;
        right: 3px;
      }

      .title,
      .name,
      .line,
      .descr {
        display: none !important;
      }

      .ant-menu-item-group {
        position: relative;
        &:after {
          content: '';
          display: block;
          width: 100%;
          height: 1px;
          position: absolute;
          left: 0;
          top: 50%;
          background: $border;
        }
      }

      .ant-menu-item-group-title {
        visibility: hidden;
      }

      .ant-menu-inline-collapsed-tooltip .icon {
        display: none;
      }

      .ant-menu-item,
      .ant-menu-submenu-title {
        padding-left: 0 !important;
        padding-right: 0 !important;

        .icon {
          right: rem(15);
        }
      }
    }
  }

  /////////////////////////////////////////////////////////////////////////////////////////
  /* BANNER COMPONENT */
  .banner {
    padding: rem(20);
    background: $gray-1;
    border-radius: 3px;
    margin: rem(20);
    overflow: hidden;
    border: 1px solid $border;
  }
}

/////////////////////////////////////////////////////////////////////////////////////////
/* SETTINGS */
.unfixed {
  .menuOuter {
    position: relative;
  }
}

.shadow {
  box-shadow: 0 0 100px -30px rgba(57, 55, 73, 0.3);
  z-index: 1;
}

/////////////////////////////////////////////////////////////////////////////////////////
/* GRAY THEME */
.gray {
  background: $gray-1 !important;
}

/////////////////////////////////////////////////////////////////////////////////////////
/* MENU THEME BY MODULE */
.moduleName {
  &__example {
    background-color: $example-menuBgColor !important;

    ::ng-deep {
      .ant-menu-submenu {
        .ant-menu-submenu-title {
          .ant-menu-submenu-arrow {
            color: $example-menuColor;
          }
          .ant-menu-submenu-expand-icon {
            color: $example-menuColor;
          }
        }
      }
    }

    .navigation {
      ::ng-deep {
        .ant-menu-submenu-active,
        .ant-menu-item-selected {
          background-color: $example-menuHoverBgColor !important;
        }

        .ant-menu-submenu-open {
          background-color: rgb(0 0 0 / 10%) !important;
        }

        .menu-left__item.ant-menu-submenu ul:after {
          background-color: var(--menu-line-color, $example-menuColor);
        }

        li.menu-left__item.ant-menu-item:before {
          border-left-color: var(--menu-line-color, $example-menuColor);
          border-bottom-color: var(--menu-line-color, $example-menuColor);
        }
      }

      .title,
      .icon {
        color: $example-menuColor;
      }
    }
  }

  &__attendance {
    background-color: $attendance-menuBgColor !important;

    ::ng-deep {
      .ant-menu-submenu {
        .ant-menu-submenu-title {
          .ant-menu-submenu-arrow {
            color: $attendance-menuColor;
          }
          .ant-menu-submenu-expand-icon {
            color: $attendance-menuColor;
          }
        }
      }
    }

    .navigation {
      ::ng-deep {
        .ant-menu-submenu-open,
        .ant-menu-submenu-active,
        .ant-menu-item-selected {
          background-color: $attendance-menuHoverBgColor !important;
        }

        .menu-left__item.ant-menu-submenu ul:after {
          background-color: var(--menu-line-color, $attendance-menuColor);
        }

        li.menu-left__item.ant-menu-item:before {
          border-left-color: var(--menu-line-color, $attendance-menuColor);
          border-bottom-color: var(--menu-line-color, $attendance-menuColor);
        }
      }

      .title,
      .icon {
        color: $attendance-menuColor;
      }
    }
  }

  &__objEval {
    background-color: $objEval-menuBgColor !important;

    ::ng-deep {
      .ant-menu-submenu {
        .ant-menu-submenu-title {
          border-radius: 8px !important;
          margin-bottom: 4px !important;

          &:hover {
            background: darken($objEval-menuHoverBgColor, 10%) !important;
            transition: 0.5s;
          }
          .ant-menu-submenu-arrow {
            color: $objEval-menuColor;
          }
          .ant-menu-submenu-expand-icon {
            color: $objEval-menuColor;
          }
        }
      }
    }

    .navigation {
      ::ng-deep {
        .ant-menu-submenu-open,
        .ant-menu-submenu-active {
          background-color: $objEval-menuBgColor !important;
        }
        .ant-menu-item {
          > span {
            color: $objEval-menuColor;
          }
          > a,
          > span {
            color: $objEval-menuColor;

            &:hover {
              color: $objEval-menuColor;

              .icon {
                color: $objEval-menuColor !important;
              }
            }
          }
          &:hover {
            &:has(.menu-left__item--hidden-icon) {
              background: $objEval-menuBgColor !important;
            }
            &:has(.menu-left__item--child-link) {
              background: $objEval-menuHoverBgColor !important;
            }
            transition: 0.5s;
          }
        }
        .ant-menu-item:has(.menu-left__item--hidden-icon) {
          .ant-menu-title-content {
            &:hover {
              background: $objEval-menuHoverBgColor !important;
              color: $objEval-menuColor !important;
            }
          }
        }

        .ant-menu-item-selected {
          &:has(.menu-left__item--child-link) {
            background: $objEval-menuHoverBgColor !important;
          }
          &:has(.menu-left__item--hidden-icon) {
            background: $objEval-menuBgColor !important;
          }
          .ant-menu-title-content {
            background: $objEval-menuHoverBgColor !important;
          }
        }
        .ant-menu-item-active {
          > span {
            color: $objEval-menuColor;
          }
          .ant-menu-item-selected {
            &:has(.menu-left__item--child-link) {
              background: $objEval-menuHoverBgColor !important;
            }
            &:has(.menu-left__item--hidden-icon) {
              background: $objEval-menuBgColor !important;
            }
            .ant-menu-title-content {
              background: $objEval-menuHoverBgColor !important;
            }
          }
        }

        // active submenu
        .ant-menu-submenu-active:has(.ant-menu-item-selected),
        .ant-menu-submenu-open:has(.ant-menu-item-selected) {
          .ant-menu-submenu-title {
            background: darken($objEval-menuHoverBgColor, 10%) !important;
            margin-bottom: 4px !important;
            &:hover {
              background: darken($objEval-menuHoverBgColor, 10%) !important;
              transition: 0.5s;
            }
          }
        }
      }

      .title,
      .icon {
        color: $objEval-menuColor;
      }
    }
  }
  &__humanResources {
    background-color: $humanResources-menuBgColor !important;

    ::ng-deep {
      .ant-menu-submenu {
        .ant-menu-submenu-title {
          border-radius: 8px !important;
          margin-bottom: 4px !important;

          &:hover {
            background: darken($humanResources-menuHoverBgColor, 10%) !important;
            transition: 0.5s;
          }
          .ant-menu-submenu-arrow {
            color: $humanResources-menuColor;
          }
          .ant-menu-submenu-expand-icon {
            color: $humanResources-menuColor;
          }
        }
      }
    }

    .navigation {
      ::ng-deep {
        .ant-menu-submenu-open,
        .ant-menu-submenu-active {
          background-color: $humanResources-menuBgColor !important;
        }
        .ant-menu-item {
          > span {
            color: $humanResources-menuColor;
          }
          > a,
          > span {
            color: $humanResources-menuColor;

            &:hover {
              color: $humanResources-menuColor;

              .icon {
                color: $humanResources-menuColor !important;
              }
            }
          }
          &:hover {
            &:has(.menu-left__item--hidden-icon) {
              background: $humanResources-menuBgColor !important;
            }
            &:has(.menu-left__item--child-link) {
              background: $humanResources-menuHoverBgColor !important;
            }
            transition: 0.5s;
          }
        }
        .ant-menu-item:has(.menu-left__item--hidden-icon) {
          .ant-menu-title-content {
            &:hover {
              background: $humanResources-menuHoverBgColor !important;
              color: $humanResources-menuColor !important;
            }
          }
        }

        .ant-menu-item-selected {
          &:has(.menu-left__item--child-link) {
            background: $humanResources-menuHoverBgColor !important;
          }
          &:has(.menu-left__item--hidden-icon) {
            background: $humanResources-menuBgColor !important;
          }
          .ant-menu-title-content {
            background: $humanResources-menuHoverBgColor !important;
          }
        }
        .ant-menu-item-active {
          > span {
            color: $humanResources-menuColor;
          }
          .ant-menu-item-selected {
            &:has(.menu-left__item--child-link) {
              background: $humanResources-menuHoverBgColor !important;
            }
            &:has(.menu-left__item--hidden-icon) {
              background: $humanResources-menuBgColor !important;
            }
            .ant-menu-title-content {
              background: $humanResources-menuHoverBgColor !important;
            }
          }
        }

        // active submenu
        .ant-menu-submenu-active:has(.ant-menu-item-selected),
        .ant-menu-submenu-open:has(.ant-menu-item-selected) {
          .ant-menu-submenu-title {
            background: darken($humanResources-menuHoverBgColor, 10%) !important;
            margin-bottom: 4px !important;
            &:hover {
              background: darken($humanResources-menuHoverBgColor, 10%) !important;
              transition: 0.5s;
            }
          }
        }
      }

      .title,
      .icon {
        color: $humanResources-menuColor;
      }
    }
  }
}

/////////////////////////////////////////////////////////////////////////////////////////
/* DARK THEME */
.dark {
  color: $dark-gray-2;
  background: $dark-gray-6 !important;

  .icon {
    color: $dark-gray-2;
  }

  .logoContainer {
    .name {
      color: $white;
    }
    .descr {
      color: $dark-gray-2;
      border-left: 1px solid $dark-gray-4;
    }
  }

  .banner {
    background: $dark-gray-4;
    border: 1px solid $dark-gray-4;
  }

  ::ng-deep {
    .ant-layout-sider-trigger {
      background: lighten($dark-gray-6, 5%);
    }
  }

  .navigation {
    ::ng-deep {
      .ant-menu-item-group-title {
        color: darken($dark-gray-3, 10%) !important;
      }

      .ant-menu-submenu-title {
        color: $dark-gray-2;

        &:hover {
          color: $primary;
        }
      }

      .ant-menu-item {
        > span {
          color: $dark-gray-2;
        }
        > a,
        > span {
          color: $dark-gray-2;

          &:hover {
            color: $primary;

            .icon {
              color: $primary;
            }
          }
        }
      }

      .ant-menu-submenu-open,
      .ant-menu-submenu-active,
      .ant-menu-item-active {
        background: $dark-gray-4 !important;
      }

      .ant-menu-item-active {
        > span {
          color: $primary;
        }
        .ant-menu-item-selected {
          background: $primary !important;
        }
      }

      .ant-menu-item-disabled {
        > span {
          color: $dark-gray-3;
        }
      }
    }
  }

  &.ant-layout-sider-collapsed {
    ::ng-deep {
      .ant-menu-item-group {
        &:after {
          background: $dark-gray-4;
        }
      }
    }
  }
}
