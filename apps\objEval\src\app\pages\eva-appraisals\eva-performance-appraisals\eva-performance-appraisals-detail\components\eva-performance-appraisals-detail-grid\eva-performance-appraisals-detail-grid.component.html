<div class="form-header">
  <div class="form-header__warning">
    <nz-alert
      *ngIf="dataDetailInput.NumberEmployeesNoTemplate > 0"
      nzCloseable
      nzShowIcon
      nzType="warning"
      [nzMessage]="
        'objEval.EvaPerformanceAppraisals.warningEmployeeNoTemplate'
          | translate : { n: dataDetailInput.NumberEmployeesNoTemplate }
      "
      [nzAction]="tplBtnAdditional"
    ></nz-alert>
    <ng-template #tplBtnAdditional>
      <a nzType="link" class="text-primary mx-3" (click)="onAdditional($event)">{{
        'objEval.EvaPerformanceAppraisals.additional' | translate
      }}</a>
    </ng-template>
  </div>
  <div class="form-header__content">
    <div class="form-header__toolbar" nz-form [formGroup]="formGroup">
      <vnr-toolbar-new [builder]="builderToolbar" (onChangeFitler)="onChangeFitler($event)">
        <!-- TH chưa gửi thì không hiển thị phòng ban và vị trí -->

        <vnr-button-new leftToolbar [builder]="builderButtonCustom">
          <vnr-treeview
            class="treeview-custom"
            [builder]="builderOrg"
            [formGroup]="formGroup"
            formControlName="department"
            (ngModelChange)="onModelChangeOrg($event)"
          ></vnr-treeview>
        </vnr-button-new>
        <vnr-button-new leftToolbar [builder]="builderButtonCustom">
          <vnr-combobox
            [builder]="builderPosition"
            [formGroup]="formGroup"
            formControlName="position"
            (ngModelChange)="onModelChangePosition($event)"
          ></vnr-combobox>
        </vnr-button-new>
        <vnr-filter-advance-quick
          leftToolbar
          [builder]="builderFilterAdvanceQuick"
          (onChange)="onChangesFilterQuick($event)"
        ></vnr-filter-advance-quick>
        <vnr-button-new rightToolbar [builder]="builderButtonCustom">
          <grid-view-mode
            [selectedViewMode]="selectedViewMode"
            (viewModeChange)="onViewModeChange($event)"
          ></grid-view-mode>
        </vnr-button-new>

        <vnr-button-new
          rightToolbar
          class="--custom-bg"
          [builder]="builderBtnAddEmployee"
          (vnrClick)="onAddEmployee()"
        ></vnr-button-new>
      </vnr-toolbar-new>
    </div>
    <vnr-grid-new
      #vnrGrid
      [builder]="builderGrid"
      [gridName]="gridName"
      [dataLocal]="dataLocal"
      [columns]="columns"
      (getSelectedID)="getSelectedID($event)"
      [defaultColumnTemplate]="tplCustomTemplateByColumn"
    >
    </vnr-grid-new>
  </div>
</div>
<ng-template #tplCustomTemplateByColumn let-dataItem let-column="columnItem" let-field="field">
  <ng-container [ngSwitch]="column['Name']">
    <!-- nhân viên -->
    <span *ngSwitchCase="'ProfileName'">
      <span
        *ngIf="dataItem['ProfileInfo']"
        (click)="onSelectEmployee(dataItem)"
        (keydown.enter)="$event.stopPropagation()"
        (keydown.space)="$event.stopPropagation()"
        tabindex="0"
        style="cursor: pointer"
      >
        <div class="eva-performance-appraisals-detail-grid__custom">
          <app-vnr-letters-avatar
            class="eva-performance-appraisals-detail-grid__image"
            [avatarName]="dataItem['ProfileInfo'].ProfileName"
            [circular]="true"
            [width]="32"
            [src]="dataItem['ProfileInfo'].ImagePath"
          ></app-vnr-letters-avatar>
          <div class="d-flex flex-column justify-content-center">
            <div class="eva-performance-appraisals-detail-grid__name">
              {{ dataItem['ProfileInfo'].ProfileName }}
            </div>
            <ng-container>
              <div class="eva-performance-appraisals-detail-grid__code-emp">
                {{ dataItem['ProfileInfo'].CodeEmp ? dataItem['ProfileInfo'].CodeEmp : '' }}
              </div>
            </ng-container>
          </div>
        </div>
      </span>
    </span>
    <!-- Trạng thái -->
    <span class="--has-custom-template" *ngSwitchCase="'Status'">
      <ng-container *ngIf="dataItem['Status']; else templateEmpty">
        <vnr-tag
          [vnrStatus]="statusFormat[dataItem['Status']]"
          [vnrTitle]="dataItem['StatusView']"
        ></vnr-tag>
      </ng-container>
    </span>
    <span class="--has-custom-template" *ngSwitchCase="'EvaluationForm'">
      <ng-container *ngIf="dataItem['EvaluationForm']; else tplbtnAddEvaluationForm">
        <a
          class="text-primary"
          (click)="onClickFileName(dataItem['EvaluationForm']); $event.preventDefault()"
          [title]="dataItem['EvaluationForm']"
        >
          <div class="vnr-grids-upload__nameFile">
            {{ dataItem['EvaluationForm'] }}
          </div>
        </a>
      </ng-container>
    </span>
    <span class="--has-custom-template" *ngSwitchCase="'AppraisalsTime'">
      <ng-container *ngIf="dataItem['PeriodDate']; else templateEmpty">
        {{ dataItem['PeriodDate'] ? (dataItem['PeriodDate'] | date : 'dd/MM/yyyy') : '' }} -
        {{ dataItem['DueDate'] ? (dataItem['DueDate'] | date : 'dd/MM/yyyy') : '' }}
      </ng-container>
    </span>
    <span class="--has-custom-template" *ngSwitchDefault>
      {{ dataItem[column['Name']] || '' }}
    </span>
  </ng-container>
</ng-template>
<ng-template #templateEmpty>-</ng-template>
<ng-template #tplbtnAddEvaluationForm>
  <vnr-button
    [vnrType]="'default'"
    [vnrSize]="'small'"
    [vnrText]="'Chọn mẫu'"
    [vnrIcon]="'plus'"
    (vnrClick)="onAddEvaluationForm()"
  ></vnr-button>
</ng-template>
