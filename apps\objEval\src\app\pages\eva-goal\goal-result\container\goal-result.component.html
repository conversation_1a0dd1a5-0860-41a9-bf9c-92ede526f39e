<!-- Header -->
<div class="page-header-container">
    <div class="d-flex justify-content-between align-items-center p-1 bg-white">
        <div class="page-title">
            <PERSON><PERSON><PERSON> quả
        </div>
        <form [formGroup]="form" class="d-flex">
            <vnr-combobox formControlName="quarter" [builder]="quarterBuilder" class="ml-2"></vnr-combobox>
            <vnr-datepicker formControlName="time" [builder]="datePickerBuilder" class="ml-2"></vnr-datepicker>
        </form>
    </div>
    <div class="tab-container">
        <div class="tab-text">
            <nz-tabset [nzSelectedIndex]="selectedTab" (nzSelectChange)="onTabChange($event)">
                <nz-tab *ngFor="let tab of tabsFiltered" [nzTitle]="tab.title"></nz-tab>
            </nz-tabset>
        </div>
    </div>
</div>

<!-- Body -->
<div [ngSwitch]="tabs[selectedTab]?.key">
    <app-overview *ngSwitchCase="'overview'"></app-overview>
    <app-tree *ngSwitchCase="'tree'"></app-tree>
    <app-department *ngSwitchCase="'department'"></app-department>
    <app-member *ngSwitchCase="'member'"></app-member>
</div>
