import { ChangeDetectionStrategy, Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { NzTabsModule, NzTabChangeEvent } from 'ng-zorro-antd/tabs';
import { OverviewComponent } from '../components/overview/overview.component';
import { TreeComponent } from '../components/tree/tree.component';
import { DepartmentComponent } from '../components/department/department.component';
import { MemberComponent } from '../components/member/member.component';
import {
  VNR_DATE_MODE,
  VnrComboBoxBuilder,
  VnrDatePickerBuilder,
} from '@hrm-frontend-workspace/vnr-module';
import { TranslateService } from '@ngx-translate/core';
import { Subject, takeUntil } from 'rxjs';
import { VnrDatePickerComponent, VnrComboBoxComponent } from '@hrm-frontend-workspace/vnr-module';

@Component({
  selector: 'app-goal-result',
  templateUrl: './goal-result.component.html',
  styleUrls: ['./goal-result.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NzTabsModule,
    OverviewComponent,
    TreeComponent,
    DepartmentComponent,
    MemberComponent,
    VnrComboBoxComponent,
    VnrDatePickerComponent,
  ],
})
export class GoalResultComponent implements OnInit, OnDestroy {
  tabs = [
    { id: 0, key: 'overview', title: 'Tổng quan', show: true },
    { id: 1, key: 'tree', title: 'Sơ đồ cây', show: true },
    { id: 2, key: 'department', title: 'Bộ phận', show: true },
    { id: 3, key: 'member', title: 'Thành viên', show: true },
  ];
  selectedTab = 0;

  form: FormGroup;
  datePickerBuilder = new VnrDatePickerBuilder();
  quarterBuilder = new VnrComboBoxBuilder();
  private destroy$ = new Subject<void>();

  constructor(private fb: FormBuilder, private translate: TranslateService) {
    this.form = this.fb.group({
      time: [new Date()],
      quarter: ['month'],
    });
  }

  ngOnInit(): void {
    this.initBuilders();
    this.setupFormListeners();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  get tabsFiltered() {
    return this.tabs.filter((tab) => tab.show);
  }

  onTabChange(event: NzTabChangeEvent): void {
    if (typeof event.index === 'number') {
      this.selectedTab = event.index;
    }
  }

  private initBuilders(): void {
    this.updateDatePickerFormat('month');
    this.quarterBuilder.builder({
      textField: 'label',
      valueField: 'value',
      dataSource: [
        { value: 'month', label: this.translate.instant('objEval.GoalPeriod.Month') },
        { value: 'quarter', label: this.translate.instant('objEval.GoalPeriod.Quarter') },
        { value: 'year', label: this.translate.instant('objEval.GoalPeriod.Year') },
      ],
    });
    this.datePickerBuilder.builder({
      options: {
        mode: VNR_DATE_MODE.E_MONTH,
      },
    });
  }

  private setupFormListeners(): void {
    this.form.valueChanges.pipe(takeUntil(this.destroy$)).subscribe((value) => {
      // Handle filter changes
      // console.log(value);
    });
    this.form
      .get('quarter')
      .valueChanges.pipe(takeUntil(this.destroy$))
      .subscribe((value) => {
        this.updateDatePickerFormat(value);
      });
  }

  private updateDatePickerFormat(timeType: string): void {
    switch (timeType) {
      case 'month':
        this.datePickerBuilder.options.mode = VNR_DATE_MODE.E_MONTH;
        break;
      case 'quarter':
        this.datePickerBuilder.options.mode = VNR_DATE_MODE.E_QUARTER;
        break;
      case 'year':
        this.datePickerBuilder.options.mode = VNR_DATE_MODE.E_YEAR;
        break;
      default:
        this.datePickerBuilder.options.mode = VNR_DATE_MODE.E_MONTH;
    }
  }
}
