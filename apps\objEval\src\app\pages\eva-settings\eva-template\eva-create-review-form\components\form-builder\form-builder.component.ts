import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  TemplateRef,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';
import { FormBuilder, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { vnrUtilities } from '@hrm-frontend-workspace/common';
import { CommonService } from '@hrm-frontend-workspace/core';
import {
  VnrButtonNewBuilder,
  VnrButtonNewComponent,
  VnrInputsModule,
} from '@hrm-frontend-workspace/vnr-module';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzDrawerModule, NzDrawerService } from 'ng-zorro-antd/drawer';
import { NzEmptyModule } from 'ng-zorro-antd/empty';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzModalModule, NzModalService } from 'ng-zorro-antd/modal';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { EvaTemplateFacade } from '../../../eva-evaluation-form-list/facade/eva-template.facade';
import { FormBuilderFacade } from '../../facade/form-builder.facade';
import { EnumFormType, FormType } from '../../models/enums/form-canvas.enum';
import { IFormSection, IFormTemplate, SectionTemplate } from '../../models/form-builder.model';
import { ControlPaletteComponent } from '../control-palette/control-palette.component';
import { EvaTemplateGeneralInfoComponent } from '../eva-template-general-info/eva-template-general-info.component';
import { FormCanvasComponent } from '../form-canvas/form-canvas.component';

@Component({
  selector: 'app-form-builder',
  templateUrl: './form-builder.component.html',
  styleUrls: ['./form-builder.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    ControlPaletteComponent,
    FormCanvasComponent,
    NzModalModule,
    NzButtonModule,
    NzIconModule,
    NzInputModule,
    NzSelectModule,
    NzTabsModule,
    NzInputNumberModule,
    TranslateModule,
    VnrButtonNewComponent,
    VnrInputsModule,
    NzFormModule,
    NzEmptyModule,
    EvaTemplateGeneralInfoComponent,
    NzDrawerModule,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FormBuilderComponent implements OnInit, OnChanges {
  @ViewChild('addSectionModal', { static: false }) addSectionModal!: TemplateRef<any>;
  @ViewChild(ControlPaletteComponent, { static: false }) controlPalette: ControlPaletteComponent;
  @ViewChild(EvaTemplateGeneralInfoComponent, { static: false })
  generalInfoComponent: EvaTemplateGeneralInfoComponent;

  @Input() formTemplate: IFormTemplate;
  @Input() selectedSection: IFormSection | null = null;
  @Input() formType: FormType = EnumFormType.CREATE;
  @Input() isPreviewVisible = false;
  @Input() isPreviewMode: boolean;
  @Input() isEdit = false;
  @Output() addSection = new EventEmitter<{ title: string; description?: string; type?: string }>();
  @Output() updateSection = new EventEmitter<{
    sectionId: string;
    updates: Partial<IFormSection>;
  }>();
  @Output() removeSection = new EventEmitter<string>();
  @Output() selectSection = new EventEmitter<string>();
  @Output() reorderSections = new EventEmitter<string[]>();
  @Output() saveTemplate = new EventEmitter<IFormTemplate>();
  @Output() saveDraft = new EventEmitter<IFormTemplate>();
  @Output() previewModeChange = new EventEmitter<boolean>();
  @Output() dragStart = new EventEmitter<void>();
  @Output() editSection = new EventEmitter<IFormSection>();
  @Output() back = new EventEmitter<void>();

  newSectionTitle = '';
  newSectionDescription = '';
  isAddSectionModalVisible = false;

  // Danh sách các loại section đã được sử dụng
  usedSectionTypes: string[] = [];

  // VNR Button Builders
  builderButtonPreview: VnrButtonNewBuilder;
  builderButtonSave: VnrButtonNewBuilder;
  builderButtonSaveDraft: VnrButtonNewBuilder;
  builderButtonCancel: VnrButtonNewBuilder;
  builderButtonConfirm: VnrButtonNewBuilder;
  builderButtonClosePreview: VnrButtonNewBuilder;

  // Thuộc tính cho vùng thông tin chung
  isGeneralInfoCollapsed = false;
  isTemplateInfoCollapsed = false;

  constructor(
    private modalService: NzModalService,
    private facade: FormBuilderFacade,
    private cdr: ChangeDetectorRef,
    private router: Router,
    private fb: FormBuilder,
    private translateService: TranslateService,
    private viewContainerRef: ViewContainerRef,
    private drawerService: NzDrawerService,
    private _commonService: CommonService,
    private evaTemplateFacade: EvaTemplateFacade,
  ) {
    if (!this.formTemplate) {
      this.formTemplate = {
        ID: vnrUtilities.newGuid(),
        EvaName: 'Đánh giá hiệu suất cuối năm 2024',
        Description: 'Mẫu đánh giá hiệu suất nhân viên',
        sections: [],
        CreatedDate: new Date(),
        ModifiedDate: new Date(),
      };
    }
  }

  /**
   * Khởi tạo component
   */
  ngOnInit(): void {
    this.initBuilders();
    this.updateUsedSections();
  }

  /**
   * Xử lý khi input properties thay đổi
   * @param changes - Các thay đổi của input properties
   */
  ngOnChanges(changes: SimpleChanges): void {
    // Xử lý khi formTemplate thay đổi
    if (changes['formTemplate'] && !changes['formTemplate'].firstChange) {
      // Cập nhật UI khi formTemplate thay đổi
      this.updateUsedSections();
    }

    // Xử lý khi isPreviewMode thay đổi
    if (changes['isPreviewMode'] && !changes['isPreviewMode'].firstChange) {
      // Cập nhật UI khi isPreviewMode thay đổi
      this.isPreviewVisible = this.isPreviewMode;
    }
  }

  /**
   * Khởi tạo các builder cho VNR controls
   */
  private initBuilders(): void {
    // Button builders
    this.builderButtonPreview = new VnrButtonNewBuilder({
      text: this.isPreviewVisible ? 'Thoát xem trước' : 'Xem trước',
      action: '',
      options: {
        style: 'info',
      },
    });

    this.builderButtonSave = new VnrButtonNewBuilder({
      text: 'Lưu',
      action: '',
      options: {
        style: 'primary',
      },
    });

    this.builderButtonSaveDraft = new VnrButtonNewBuilder({
      text: 'Lưu nháp',
      action: '',
      options: {
        style: 'default',
      },
    });

    this.builderButtonCancel = new VnrButtonNewBuilder({
      text: 'Hủy',
      action: '',
      options: {
        style: 'default',
      },
    });

    this.builderButtonConfirm = new VnrButtonNewBuilder({
      text: 'Xác nhận',
      action: '',
      options: {
        style: 'primary',
      },
    });

    this.builderButtonClosePreview = new VnrButtonNewBuilder({
      text: 'Đóng',
      action: '',
      options: {
        style: 'default',
      },
    });
  }

  /**
   * Cập nhật danh sách các loại section đã được sử dụng
   */
  updateUsedSections(): void {
    if (this.formTemplate && this.formTemplate.sections) {
      this.usedSectionTypes = this.formTemplate.sections.map((section) => section.type);
    } else {
      this.usedSectionTypes = [];
    }

    // Cập nhật trạng thái trong control palette nếu đã được khởi tạo
    if (this.controlPalette) {
      this.controlPalette.usedSectionTypes = this.usedSectionTypes;
    }

    this.cdr.detectChanges();
  }

  /**
   * Xử lý khi danh sách các loại section thay đổi
   * @param types - Danh sách các loại section
   */
  onSectionTypesChanged(types: string[]): void {
    this.usedSectionTypes = types;
    this.cdr.detectChanges();
  }

  /**
   * Xử lý khi thả section vào canvas
   * @param sectionTemplate - Template của section được thả
   */
  onDropSection(sectionTemplate: SectionTemplate): void {
    // Tạo section mới từ template
    const newSection: IFormSection = {
      id: vnrUtilities.newGuid(),
      title: sectionTemplate.name,
      description: sectionTemplate.description || '',
      type: sectionTemplate.type,
      layout: 'vertical',
      columns: 2,
      controls: [],
      properties: sectionTemplate.config,
    };

    // Thêm section mới vào template
    if (!this.formTemplate?.sections) {
      this.formTemplate.sections = [];
    }

    this.formTemplate.sections.push(newSection);
    this.updateUsedSections();
    this.onSectionClick(newSection);

    // Chọn section mới thêm
    this.selectedSection = newSection;
    this.selectSection.emit(newSection.id);
    this.cdr.detectChanges();
  }

  /**
   * Xử lý khi click vào section
   * @param section - Section được click
   */
  onSectionClick(section: IFormSection): void {
    this.selectedSection = section;
    this.selectSection.emit(section.id);

    // Cập nhật control-palette để đồng bộ hóa section được chọn
    if (this.controlPalette) {
      // Tìm section template tương ứng trong control-palette
      const sectionTemplate = this.controlPalette.sectionTemplates.find(
        (s) => s.isUsed && s.type === section.type,
      );

      if (sectionTemplate) {
        this.controlPalette.selectedSection = sectionTemplate;
        // Không cần gọi markForCheck vì selectedSection là @Input và sẽ tự động được phát hiện
      }
    }

    this.cdr.detectChanges();
  }

  /**
   * Xử lý khi thay đổi thứ tự các section
   * @param sections - Danh sách các section sau khi thay đổi thứ tự
   */
  onSectionOrderChanged(sections: IFormSection[]): void {
    if (this.formTemplate) {
      this.formTemplate.sections = sections;
      const sectionIds = sections.map((section) => section.id);
      this.reorderSections.emit(sectionIds);
    }
  }

  /**
   * Xử lý khi thêm section mới
   */
  onAddSection(): void {
    // Hiển thị modal để nhập thông tin section mới
    this.modalService.create({
      nzTitle: 'Thêm vùng mới',
      nzContent: this.addSectionModal,
      nzFooter: null,
      nzWidth: 500,
    });
  }

  /**
   * Hủy thêm section mới
   */
  cancelAddSection(): void {
    this.modalService.closeAll();
  }

  /**
   * Xác nhận thêm section mới
   */
  confirmAddSection(): void {
    // Kiểm tra dữ liệu đầu vào
    if (!this.newSectionTitle.trim()) {
      return;
    }

    // Emit sự kiện để thêm section mới
    this.addSection.emit({
      title: this.newSectionTitle,
      description: this.newSectionDescription,
      type: 'custom',
    });

    // Tạo section mới
    const newSection: IFormSection = {
      id: vnrUtilities.newGuid(),
      title: this.newSectionTitle,
      description: this.newSectionDescription,
      type: 'custom',
      layout: 'vertical',
      columns: 2,
      controls: [],
    };

    // Thêm section mới vào template
    if (!this.formTemplate?.sections) {
      this.formTemplate.sections = [];
    }

    this.formTemplate.sections.push(newSection);
    this.updateUsedSections();

    // Đóng modal
    this.modalService.closeAll();
  }

  /**
   * Xử lý khi xóa section
   * @param sectionId - ID của section cần xóa
   */
  onRemoveSection(sectionId: string): void {
    if (this.formTemplate && this.formTemplate.sections) {
      // Tìm và xóa section
      const index = this.formTemplate.sections.findIndex((s) => s.id === sectionId);
      if (index !== -1) {
        // Lưu lại type của section trước khi xóa để cập nhật control-palette
        const sectionType = this.formTemplate.sections[index].type;

        // Xóa section
        this.formTemplate.sections.splice(index, 1);
        this.removeSection.emit(sectionId);
        this.updateUsedSections();

        // Nếu đang chọn section bị xóa, bỏ chọn
        if (this.selectedSection && this.selectedSection.id === sectionId) {
          this.selectedSection = null;
        }

        // Cập nhật control-palette
        if (this.controlPalette) {
          // Kiểm tra xem có còn section nào cùng type không
          const hasSimilarTypeSection = this.formTemplate.sections.some(
            (s) => s.type === sectionType,
          );
          if (!hasSimilarTypeSection) {
            // Nếu không còn section nào cùng type, cập nhật selectedSection trong control-palette
            this.controlPalette.selectedSection = null;
          }
        }

        this.cdr.detectChanges();
      }
    }
  }

  /**
   * Đóng/mở vùng thông tin chung
   */
  toggleGeneralInfo(): void {
    this.isGeneralInfoCollapsed = !this.isGeneralInfoCollapsed;
    this.cdr.detectChanges();
  }

  /**
   * Đóng/mở vùng thông tin mẫu
   */
  toggleTemplateInfo(): void {
    this.isTemplateInfoCollapsed = !this.isTemplateInfoCollapsed;
    this.cdr.detectChanges();
  }

  /**
   * Xử lý khi thay đổi thông tin chung
   */
  onGeneralFormChange(event: any): void {
    // Không cần thực hiện gì ở đây vì chúng ta sẽ lấy dữ liệu khi lưu
    this.cdr.detectChanges();
  }

  /**
   * Xử lý khi nhấn nút lưu
   */
  onSaveClick(): void {
    // Lấy dữ liệu từ component thông tin chung
    if (!this.generalInfoComponent) {
      console.error('General info component not found');
      return;
    }
    // Kiểm tra tính hợp lệ của form
    if (!this.generalInfoComponent.isFormValid()) {
      // Đánh dấu tất cả các trường là đã chạm vào để hiển thị lỗi
      this.generalInfoComponent.markAllAsTouched();

      // Hiển thị thông báo lỗi
      this._commonService.notification({
        message: 'Vui lòng nhập đầy đủ thông tin cơ bản (Tên mẫu đánh giá và Mô tả)',
        type: 'warning',
      });

      // Mở vùng thông tin chung nếu đang đóng
      if (this.isGeneralInfoCollapsed) {
        this.isGeneralInfoCollapsed = false;
        this.cdr.detectChanges();
      }

      return;
    }

    // Lấy dữ liệu từ form
    const generalFormData = this.generalInfoComponent.getFormData();
    if (!generalFormData) {
      console.error('General form data is invalid');
      return;
    }

    // Cập nhật formTemplate với dữ liệu từ form
    this.formTemplate = {
      ...this.formTemplate,
      ...generalFormData,
      ModifiedDate: new Date(),
    };

    // Lưu template vào state thông qua facade
    this.evaTemplateFacade.createTemplate(this.formTemplate);

    return;
    this.saveTemplate.emit(this.formTemplate);
    // // Gọi API để lưu template
    // this.facade.saveTemplate(this.formTemplate).subscribe({
    //   next: () => {
    //     // Hiển thị thông báo thành công
    //     this.modalService.success({
    //       nzTitle: 'Thành công',
    //       nzContent: 'Đã lưu biểu mẫu thành công',
    //       nzOnOk: () => {
    //         // Điều hướng về trang gốc
    //         this.router.navigate(['objEval/setting/eva-template/list']);
    //       },
    //     });
    //   },
    //   error: (error) => {
    //     // Hiển thị thông báo lỗi
    //     this._commonService.notification({
    //       message: 'Có lỗi xảy ra khi lưu biểu mẫu. Vui lòng thử lại sau.',
    //       type: 'error',
    //     });
    //     console.error('Error saving template:', error);
    //   },
    // });
  }

  /**
   * Xử lý khi nhấn nút lưu tạm
   */
  onSaveDraftClick(): void {
    // Lấy dữ liệu từ component thông tin chung
    if (this.generalInfoComponent) {
      const generalFormData = this.generalInfoComponent.getFormData();
      if (generalFormData) {
        // Cập nhật formTemplate với dữ liệu từ form
        this.formTemplate = {
          ...this.formTemplate,
          ...generalFormData,
          ModifiedDate: new Date(),
          Status: 'draft', // Đặt trạng thái là nháp
        };
      }
    }

    // Emit sự kiện để lưu tạm template
    this.saveDraft.emit(this.formTemplate);

    // Hiển thị thông báo thành công
    this._commonService.message({
      message: 'Đã lưu tạm biểu mẫu thành công',
      type: 'success',
    });
  }

  /**
   * Xử lý khi nhấn nút quay lại
   */
  onBackClick(): void {
    // Hiển thị xác nhận trước khi quay lại
    this.modalService.confirm({
      nzTitle: 'Xác nhận quay lại',
      nzContent: 'Các thay đổi chưa lưu sẽ bị mất. Bạn có chắc chắn muốn quay lại không?',
      nzOkText: 'Đồng ý',
      nzCancelText: 'Hủy',
      nzOnOk: () => {
        this.router.navigate(['objEval/setting/eva-template/list']);
      },
    });
  }

  /**
   * Chuyển đổi chế độ xem trước
   */
  togglePreviewMode(): void {
    const isSection =
      this.formTemplate && this.formTemplate.sections && this.formTemplate.sections.length === 0;
    if (isSection || !this.formTemplate) {
      this._commonService.message({
        message: 'Vui lòng thêm ít nhất một vùng đánh giá trước khi xem trước mẫu đánh giá',
        type: 'warning',
      });
      return;
    }
    const drawerRef = this.drawerService.create({
      nzWidth: '95vw',
      nzBodyStyle: { height: '98vh', maxHeight: '98vh', top: '20px' },
      nzTitle: 'Xem trước mẫu đánh giá',
      nzContent: FormCanvasComponent,
      nzContentParams: {
        formTemplate: this.formTemplate,
        selectedSection: this.selectedSection,
        formType: EnumFormType.PREVIEW,
      },
      nzFooter: null,
      nzMaskClosable: false,
      nzMask: true,
      nzClosable: true,
    });
    // modalRef.afterClose.subscribe((modal) => {
    //   if (modal?.type === 'Save') {
    //     this.form.patchValue({
    //       formula: modal.formula,
    //     });
    //     this.formulaConfigBuilder.formula = modal.formula;
    //     this._cdr.detectChanges();
    //   }
    // });
    // this.isPreviewVisible = !this.isPreviewVisible;
    // this.previewModeChange.emit(this.isPreviewVisible);
  }

  /**
   * Đóng chế độ xem trước
   */
  closePreview(): void {
    this.isPreviewVisible = false;
    this.previewModeChange.emit(false);
  }

  /**
   * Xử lý khi người dùng click vào một section đã được sử dụng trong control palette
   * @param section - Section template được click
   */
  handleGoToSection(section: SectionTemplate): void {
    // Tìm section trong danh sách sections
    const targetSection = this.formTemplate.sections.find(
      (s) => s.title === section.name || s.type === section.type,
    );

    if (targetSection) {
      // Chọn section
      this.onSectionClick(targetSection);

      // Scroll đến section
      setTimeout(() => {
        const sectionElement = document.getElementById(`section-${targetSection.id}`);
        if (sectionElement) {
          sectionElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }, 100);
    }
  }
  onFormValueChange(event: any): void {
    this.formTemplate = { ...this.formTemplate, ...event };
  }
}
