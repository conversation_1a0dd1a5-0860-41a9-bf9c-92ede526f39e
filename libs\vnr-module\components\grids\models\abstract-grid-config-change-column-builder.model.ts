import { IApiOptionsChangeColumnModel } from '../interfaces/grid-new-change-column.interface';

export abstract class AbstractGridConfigChangeColumnBuilder<TBuilder, TOptions> {
  gridName?: string;
  columns?: any;
  isSupperAdmin?: boolean;
  isOpenChangeColumn?: boolean;
  gridPlacement?: 'leftTop' | 'rightTop' | '';
  data?: IApiOptionsChangeColumnModel;
  options?: TOptions;
  abstract builder?(builder?: TBuilder): void;
}
