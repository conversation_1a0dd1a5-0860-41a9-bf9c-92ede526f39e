<div class="p-3">
  <vnr-toolbar-new [builder]="builderToolbar">
    <vnr-button rightToolbar [vnrTemplate]="tplBtn"></vnr-button>
  </vnr-toolbar-new>
  <ng-template #tplBtn>
    <button
      class="ant-btn toolbar-border-radius vnr-button-default ant-btn-default ng-star-inserted mr-1"
      style="border: 1px solid #d9d9d9; border-radius: 6px"
    >
      <i class="fa-light fa-file-export mr-1"></i> Xuất dữ liệu
    </button>
    <button
      class="ant-btn toolbar-border-radius vnr-button-default ant-btn-default ng-star-inserted mr-1"
      style="border: 1px solid #d9d9d9; border-radius: 6px"
    >
      <i class="fa-light fa-chart-pie"></i>
    </button>
    <button
      class="ant-btn toolbar-border-radius vnr-button-default ant-btn-default ng-star-inserted"
      style="border: 1px solid #d9d9d9; border-radius: 6px"
    >
      <i class="fa-light fa-filter"></i>
    </button>
  </ng-template>
  <vnr-treelist-new
    #vnrTreeList
    [builder]="builderTreeList"
    [gridName]="gridName"
    [dataLocal]="dataLocal"
    [columns]="columns"
    [isSupperAdmin]="isSupperAdmin"
    [defaultColumnTemplate]="tplCustomTemplateByColumn"
    (vnrDoubleClick)="handleDoubleClick($event)"
  >
    <ng-template #tplCustomTemplateByColumn let-dataItem let-column="columnItem" let-field="field">
      <ng-container [ngSwitch]="column['Name']">
        <!-- Nhân viên -->
        <span *ngSwitchCase="'Representative'">
          <div class="d-flex align-items-center">
            <app-vnr-letters-avatar
              [avatarName]="dataItem['Representative']"
              [circular]="true"
              [width]="32"
              class="mr-2"
            ></app-vnr-letters-avatar>
            <span>{{ dataItem['Representative'] }}</span>
          </div>
        </span>

        <!-- CompletionRate -->
        <span *ngSwitchCase="'CompletionRate'">
          <span *ngIf="dataItem['CompletionRate'] >= 100" class="text-success">
            {{ dataItem['CompletionRate'] }}%
          </span>
          <span *ngIf="dataItem['CompletionRate'] < 100"> {{ dataItem['CompletionRate'] }}% </span>
        </span>

        <!-- Kết quả -->
        <span *ngSwitchCase="'Result'">
          <span class="text-muted"> {{ dataItem['Result'] }} </span>
        </span>

        <!-- Xếp loại -->
        <span *ngSwitchCase="'Rank'">
          <span class="text-muted"> {{ dataItem['Rank'] }} </span>
        </span>

        <!-- Default Template -->
        <span *ngSwitchDefault>
          {{ dataItem[column['Name']] || '' }}
        </span>
      </ng-container>
    </ng-template>

    <ng-template #templateEmpty>-</ng-template>
  </vnr-treelist-new>
</div>
