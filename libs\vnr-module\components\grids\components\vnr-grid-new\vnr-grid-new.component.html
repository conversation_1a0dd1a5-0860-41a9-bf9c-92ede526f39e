<form novalidate #dynamicForm="ngForm">
  <!-- #region Change Columns New -->
  <change-new-column
    *ngIf="isOpenChangeColumn && isChangeColumnNew"
    [builder]="builderGridChangeColumn"
    (vnrClosePopup)="vnrcloseAndRefreshGird($event)"
    (vnrReloadGridChangeColumn)="vnrReloadGridChangeColumn($event)"
  ></change-new-column>
  <!-- #endregion Change Columns -->

  <!--
    [listColumnGrid]="gridColumns"

    [vnrPlacement]="vnrPlacement"
   -->
  <kendo-grid
    #kendoGrid
    [ngClass]="{
      'vnr-pageEP-custom': builder.options.configShowHide.isPageExpand,
      'vnr-pager-custom-kendo': !builder.options.configShowHide.isPageExpand,
      'vnr-grid__hide-vertical-scroll': builder.options.configHeightGrid.isAllowCalcRowHeight,
      'vnr-border-grid': builder.options.isBorderGrid
    }"
    [class]="'vnr-grid-density__' + builder.options.gridDensityClassName"
    [reorderable]="true"
    [data]="gridView"
    [loading]="isLoading"
    [kendoGridSelectBy]="builder.options.configSelectable.columnKey"
    [sortable]="{
      allowUnsort: true,
      mode: false ? 'multiple' : 'single'
    }"
    [height]="builder.options.configHeightGrid.gridHeight"
    [sort]="builder.options.queryOption.sort"
    [group]="builder.options.queryOption.group"
    [pageSize]="builder.options.queryOption.take"
    [skip]="builder.options.queryOption.skip"
    [filter]="builder.options.queryOption.filter"
    [pageable]="{
      buttonCount: builder.options.configPageable.buttonCount,
      info: builder.options.configPageable.isShowInfo,
      type: builder.options.configPageable.type,
      pageSizes: builder.options.configPageable.isShowPageSize,
      previousNext: builder.options.configPageable.isPreviousNext,
      responsive: builder.options.configPageable.isResponsive,
      position: builder.options.configPageable.position,
    }"
    [selectedKeys]="gridSelectedKeys"
    [selectable]="{
      enabled: builder.options.configSelectable.isEnabled,
      mode: builder.options.configSelectable.mode,
      checkboxOnly: builder.options.configSelectable.isCheckboxOnly,
      cell: false
    }"
    [columnMenu]="builder.options.configShowHide.isShowMenuColumn"
    [kendoGridExpandDetailsBy]="builder.options.configExpandDetail.by"
    [(expandedDetailKeys)]="builder.options.configExpandDetail.keys"
    filterable="menu"
    (selectedKeysChange)="vnrKeyChange($event)"
    (pageChange)="onPageChange($event)"
    (filterChange)="vnrFilterChange($event)"
    (dataStateChange)="vnrDataStateChange($event)"
    (selectionChange)="vnrSelectedRowChange($event)"
    (excelExport)="onExcelExport($event)"
    (columnResize)="onColumnResize($event)"
    (detailExpand)="onDetailExpand($event)"
    (dblclick)="onGridDoubleClicked($event)"
    (cellClick)="onCellClick($event)"
    (sortChange)="sortChange($event)"
    [rowClass]="builder.options.configEvent.rowClass"
    [resizable]="builder.options.isResizable"
    [groupable]="{
      emptyText: builder.options.configGroupable.emptyText | translate,
      enabled: builder.options.configGroupable.isEnabled,
      showFooter: builder.options.configGroupable.isShowFooter
    }"
    (groupChange)="vnrGroupChange($event)"
    [virtualColumns]="builder.options.isVirtualColumn"
    (scrollBottom)="vnrScrollBottom()"
    kendoGridExpandGroupBy
    [groupsInitiallyExpanded]="builder.options.configExpanded.isExpanded"
    [(expandedGroupKeys)]="builder.options.configExpanded.groupKey"
  >
    <!-- [isGroupExpanded]="isGroupExpanded"
    (groupCollapse)="toggleGroup($event)"
    (groupExpand)="toggleGroup($event)" -->
    <!-- #region Checkbox Select Row -->
    <kendo-grid-checkbox-column
      [width]="50"
      [minResizableWidth]="50"
      [maxResizableWidth]="50"
      class="text-center"
      [columnMenu]="false"
      [locked]="hasLockedColumn || builder.options.isLockedColumnCheckBox"
      *ngIf="
        gridColumns && gridColumns.length > 0 && builder.options.configShowHide.isShowColumnCheck
      "
    >
      <ng-template kendoGridHeaderTemplate let-column let-columnIndex="rowIndex">
        <div class="text-center">
          <!-- <input type="checkbox" kendoCheckBox class="k-checkbox" kendoGridSelectAllCheckbox /> -->
          <input
            type="checkbox"
            id="selectAllCheckboxId"
            kendoCheckBox
            class="k-checkbox"
            kendoGridSelectAllCheckbox
            [state]="selectAllState"
            (selectAllChange)="onSelectAllChange($event)"
          />
        </div>
      </ng-template>
      <ng-template
        kendoGridCellTemplate
        let-dataItem
        let-rowIndex="rowIndex"
        let-field="field"
        let-value="value"
        *ngIf="builder.options.configShowHide.isShowColumnCheck"
      >
        <input
          type="checkbox"
          kendoCheckBox
          class="k-checkbox"
          [checked]="dataItem.checked"
          [(ngModel)]="dataItem.checked"
          class="customCheckBtn"
          id="id_{{ field }}_{{ value }}"
          [name]="dataItem?.ID"
          [ngModelOptions]="{ standalone: true }"
          (change)="checkDataItem(dataItem)"
          [kendoGridSelectionCheckbox]="rowIndex"
        />
      </ng-template>
    </kendo-grid-checkbox-column>
    <!-- #endregion -->

    <!-- #region Edit command -->
    <kendo-grid-command-column
      title=""
      [width]="
        builder.options.configShowHide.isShowEdit && builder.options.configShowHide.isShowViewDetail
          ? 80
          : 50
      "
      [minResizableWidth]="
        builder.options.configShowHide.isShowEdit && builder.options.configShowHide.isShowViewDetail
          ? 80
          : 50
      "
      [maxResizableWidth]="80"
      [columnMenu]="false"
      *ngIf="
        gridColumns &&
        gridColumns.length > 0 &&
        (builder.options.configShowHide.isShowEdit ||
          builder.options.configShowHide.isShowViewDetail) &&
        !builder.options.configShowHide.isShowButtonMenu &&
        !builder.options.configCommandColumn.isEnabledMenuAction
      "
    >
      <ng-template kendoGridCellTemplate let-dataItem let-isNew="isNew">
        <div
          class="d-flex"
          *ngIf="
            builder.options.configShowHide.isShowEdit ||
            builder.options.configShowHide.isShowViewDetail
          "
        >
          <div class="mr-1" *ngIf="builder.options.configShowHide.isShowEdit">
            <button
              kendoGridEditCommand
              (click)="onEdit(dataItem)"
              class="btn-kendoGrid-customize btn-kendoGrid-customize__edit ant-btn ant-btn-primary"
            >
              <span nz-icon nzType="edit" nzTheme="outline"></span>
            </button>
          </div>
          <div *ngIf="builder.options.configShowHide.isShowViewDetail">
            <button
              kendoGridEditCommand
              (click)="onViewDetails(dataItem)"
              class="btn-kendoGrid-customize ant-btn btn-kendoGrid-customize__ViewDetails"
            >
              <i nz-icon nzType="eye" nzTheme="outline"></i>
            </button>
          </div>
        </div>
      </ng-template>
    </kendo-grid-command-column>
    <!-- #endregion -->

    <!-- #region Order Number Column -->
    <kendo-grid-command-column
      [title]="'common.grid.orderNumber' | translate"
      [width]="builder.options.configIndexColumn.width"
      [locked]="builder.options.configIndexColumn.isLocked"
      *ngIf="builder.options.configIndexColumn.isShow"
    >
      <ng-template
        kendoGridCellTemplate
        let-rowIndex="rowIndex"
        let-column="column"
        let-dataItem="dataItem"
        let-field="field"
      >
        {{ rowIndex + 1 }}
      </ng-template>
    </kendo-grid-command-column>
    <!-- #endregion -->

    <!-- #region List column -->
    <ng-container *ngFor="let col of gridColumns; trackBy: trackByFn">
      <!-- #region Column with Group -->
      <ng-container *ngIf="col.Type === 'group'; else columnWithoutGroup">
        <kendo-grid-column-group
          [title]="col.HasChangeDisplayName ? col.DisplayName : (col.HeaderKey | translate)"
          [width]="columnWidth(col)"
          [minResizableWidth]="col?.MinResizableWidth || 50"
          [maxResizableWidth]="col?.MaxResizableWidth"
          [locked]="col.Locked"
          [headerClass]="col.Class"
        >
          <ng-template kendoGridHeaderTemplate let-columnIndex="columnIndex" let-column="column">
            <span
              [ngClass]="{
                'vnr-grid-multiColumn':
                  col.Type === 'group' && col.MultiColumn && col.MultiColumn.length > 0
              }"
            >
              <ng-container
                *ngIf="col[builder.options.configTitle.prefixTitleColumn]; else tplNormalHeader"
              >
                <div
                  [ngClass]="{ 'text-center': builder.options.configTitle.alignment == 'Center' }"
                  [innerText]="
                    ((col.DisplayName ? col.DisplayName : col.HeaderKey) | translate) +
                    (col[builder.options.configTitle.prefixTitleColumn]
                      ? ' ' + col[builder.options.configTitle.prefixTitleColumn]
                      : '')
                  "
                ></div>
              </ng-container>

              <ng-template #tplNormalHeader>
                <div
                  [ngClass]="{ 'text-center': builder.options.configTitle.alignment == 'Center' }"
                  [innerText]="(col.DisplayName ? col.DisplayName : col.HeaderKey) | translate"
                ></div>
              </ng-template>
            </span>
          </ng-template>
          <ng-container *ngFor="let subCol of col.MultiColumn">
            <ng-container
              *ngIf="subCol?.Type === 'group' && subCol?.MultiColumn?.length > 0; else innerColumn"
            >
              <kendo-grid-column-group
                [title]="
                  subCol.HasChangeDisplayName ? subCol.DisplayName : (subCol.HeaderKey | translate)
                "
                [width]="columnWidth(subCol)"
                [minResizableWidth]="subCol?.MinResizableWidth || 50"
                [maxResizableWidth]="subCol?.MaxResizableWidth"
                [locked]="subCol.Locked"
                [headerClass]="col.Class"
              >
                <ng-template
                  kendoGridHeaderTemplate
                  let-columnIndex="columnIndex"
                  let-column="column"
                >
                  <span
                    [ngClass]="{
                      'vnr-grid-multiColumn':
                        subCol.Type === 'group' &&
                        subCol.MultiColumn &&
                        subCol.MultiColumn.length > 0
                    }"
                  >
                    <ng-container
                      *ngIf="
                        subCol[builder.options.configTitle.prefixTitleColumn];
                        else tplNormalHeader
                      "
                    >
                      <div
                        [ngClass]="{ 'text-center': builder.options.configTitle.prefixTitleColumn }"
                        [innerText]="
                          ((subCol.DisplayName ? subCol.DisplayName : subCol.HeaderKey)
                            | translate) +
                          (subCol[builder.options.configTitle.prefixTitleColumn]
                            ? ' ' + subCol[builder.options.configTitle.prefixTitleColumn]
                            : '')
                        "
                      ></div>
                    </ng-container>

                    <ng-template #tplNormalHeader>
                      <div
                        [ngClass]="{ 'text-center': builder.options.configTitle.prefixTitleColumn }"
                        [innerText]="
                          (subCol.DisplayName ? subCol.DisplayName : subCol.HeaderKey) | translate
                        "
                      ></div>
                    </ng-template>
                  </span>
                </ng-template>

                <kendo-grid-column
                  [field]="col.Name"
                  title="{{
                    col.HasChangeDisplayName ? col.DisplayName : (col.HeaderKey | translate)
                  }}"
                  [filterable]="col.Filter"
                  [width]="columnWidth(col)"
                  [maxResizableWidth]="col?.MaxResizableWidth"
                  [minResizableWidth]="col?.MinResizableWidth || 50"
                  [locked]="col.Locked === null || col.Locked === undefined ? false : col.Locked"
                  [sortable]="col.Sortable"
                  [hidden]="col.Hidden"
                  [sticky]="col?.Sticky"
                  [columnMenu]="!col.Hidden"
                  [headerClass]="col.Class"
                  class="{{
                    (col.Format && col.Format.toLowerCase().startsWith('number|')) ||
                    (col.Format && col.Format.toLowerCase().startsWith('datetime|')) ||
                    (builder.options?.configColumnSchema &&
                      builder.options?.configColumnSchema.fieldNumber?.indexOf(col.Name) >= 0) ||
                    (builder.options?.configColumnSchema &&
                      builder.options?.configColumnSchema.fieldNumberMoney &&
                      builder.options?.configColumnSchema.fieldNumberMoney.indexOf(col.Name) >= 0)
                      ? col.Class + ' text-right'
                      : col.Class
                  }}"
                  *ngFor="let col of subCol.MultiColumn"
                >
                  <ng-template
                    kendoGridHeaderTemplate
                    let-columnIndex="columnIndex"
                    let-column="column"
                  >
                    <span>
                      <ng-container
                        *ngIf="
                          col[builder.options.configTitle.prefixTitleColumn];
                          else tplNormalHeader
                        "
                      >
                        {{
                          (col.HasChangeDisplayName
                            ? col.DisplayName
                            : (col.HeaderKey | translate)) +
                            (col[builder.options.configTitle.prefixTitleColumn]
                              ? ' ' + col[builder.options.configTitle.prefixTitleColumn]
                              : '')
                        }}
                      </ng-container>

                      <ng-template #tplNormalHeader>
                        {{
                          col.HasChangeDisplayName ? col.DisplayName : (col.HeaderKey | translate)
                        }}
                      </ng-template>
                    </span>
                  </ng-template>

                  <!-- #region Group -->
                  <ng-template
                    kendoGridGroupHeaderTemplate
                    let-group="group"
                    let-field="field"
                    let-value="value"
                    let-aggregates="aggregates"
                    *ngIf="col.Group"
                    let-index="index"
                  >
                    <span
                      class="mr-1 ml-1"
                      *ngIf="
                        builder.options.configShowHide.isShowColumnCheck &&
                        builder.options.configShowHide.isShowColumnGroupCheck
                      "
                    >
                      <input
                        type="checkbox"
                        kendoCheckBox
                        class="k-checkbox"
                        (change)="checkGroup(group, index)"
                        [checked]="isGroupChecked(group)"
                        class="customCheckBtn"
                        id="id_{{ field }}_{{ value }}"
                      />
                    </span>
                    <ng-container
                      *ngIf="
                        col[builder.options.configTitle.prefixTitleColumn];
                        else tplNormalHeader
                      "
                    >
                      <span
                        class="vnr-grid-grouping-title"
                        [ngClass]="{
                          'vnr-grid-grouping-title__checked':
                            builder.options.configShowHide.isShowColumnCheck &&
                            customTemplateGroupColumnFooter
                        }"
                        title="{{ col | vnrGridGroupTitle : value : group }}"
                      >
                        <strong class="mr-1" [class]="col?.Class + '__title-group'">
                          {{
                            (col.HasChangeDisplayName
                              ? col.DisplayName
                              : (col.HeaderKey | translate)) +
                              (col[builder.options.configTitle.prefixTitleColumn]
                                ? ' ' + col[builder.options.configTitle.prefixTitleColumn]
                                : '')
                          }}:
                        </strong>
                        <ng-container *ngIf="col && col.Format; else tplNormalHeaderFormat">
                          <span [ngSwitch]="col.Format.split('|')[0].toLowerCase()">
                            <span *ngSwitchCase="'datetime'">
                              {{ value | date : col.Format.split('|')[1] }} ({{
                                group.items.length | json
                              }})
                            </span>
                            <span *ngSwitchDefault>
                              {{ value }} ({{ group.items.length | json }})
                            </span>
                          </span>
                        </ng-container>
                        <ng-template #tplNormalHeaderFormat>
                          {{ value }} ({{ group.items.length | json }})
                        </ng-template>
                      </span>
                    </ng-container>

                    <ng-template #tplNormalHeader>
                      <span
                        class="vnr-grid-grouping-title"
                        [ngClass]="{
                          'vnr-grid-grouping-title__checked':
                            builder.options.configShowHide.isShowColumnCheck &&
                            customTemplateGroupColumnFooter
                        }"
                        title="{{ col | vnrGridGroupTitle : value : group }}"
                      >
                        <strong class="mr-1" [class]="col?.Class + '__title-group'">
                          {{
                            col.HasChangeDisplayName
                              ? col.DisplayName
                              : (col.HeaderKey | translate)
                          }}:
                        </strong>
                        <ng-container *ngIf="col && col.Format; else tplNormalHeaderFormat">
                          <span [ngSwitch]="col.Format.split('|')[0].toLowerCase()">
                            <span *ngSwitchCase="'datetime'">
                              {{ value | date : col.Format.split('|')[1] }} ({{
                                group.items.length | json
                              }})
                            </span>
                            <span *ngSwitchDefault>
                              {{ value }} ({{ group.items.length | json }})
                            </span>
                          </span>
                        </ng-container>
                        <ng-template #tplNormalHeaderFormat>
                          {{ value }} ({{ group.items.length | json }})
                        </ng-template>
                      </span>
                    </ng-template>
                  </ng-template>
                  <!-- #endregion -->

                  <!-- #region Template outside -->
                  <ng-template
                    #tplColumnByColumnName
                    *ngIf="col.template || defaultColumnTemplate"
                    kendoGridCellTemplate
                    let-dataItem
                    let-rowIndex="rowIndex"
                    let-field="field"
                    let-column="column"
                  >
                    <ng-container
                      *ngIf="
                        builder.options.isEnabledFormat && isColumnHasFormatSpecial(col);
                        else defaultDataGroup
                      "
                    >
                      <ng-container
                        *ngTemplateOutlet="
                          tplFormatTemplate;
                          context: { $implicit: col, dataItem: dataItem, rowIndex: rowIndex }
                        "
                      ></ng-container>
                    </ng-container>
                    <ng-template #defaultDataGroup>
                      <ng-container
                        [ngTemplateOutlet]="col.template || defaultColumnTemplate"
                        [ngTemplateOutletContext]="{
                          $implicit: dataItem,
                          rowIndex: rowIndex,
                          field: field,
                          column: column,
                          columnItem: col
                        }"
                      >
                      </ng-container>
                    </ng-template>
                  </ng-template>
                  <!-- #endregion -->

                  <!-- #region Format -->
                  <ng-template
                    kendoGridCellTemplate
                    let-dataItem
                    let-rowIndex="rowIndex"
                    let-column="column"
                    let-field="field"
                    *ngIf="isColumnHasFormatSpecial(col) && !col.template"
                  >
                    <ng-template *ngIf="col.Format; then gridFormat; else elseSchema"></ng-template>
                    <ng-template #gridFormat>
                      <span *ngIf="col.Format?.toLowerCase()?.startsWith('datetime|')">
                        <ng-container
                          *ngIf="
                            col.template || defaultColumnTemplate;
                            else tplColumnDatetimeNoTemplate
                          "
                          [ngTemplateOutlet]="col.template || defaultColumnTemplate"
                          [ngTemplateOutletContext]="{
                            $implicit: dataItem,
                            rowIndex: rowIndex,
                            field: field,
                            column: column,
                            columnItem: col
                          }"
                        >
                        </ng-container>
                        <ng-template #tplColumnDatetimeNoTemplate>
                          {{ dataItem[column.field] | date : col.Format.split('|')[1] }}
                        </ng-template>
                      </span>
                      <span *ngIf="col.Format?.toLowerCase()?.startsWith('number|')">
                        {{ dataItem[column.field] | kendoNumber : col.Format.split('|')[1] }}
                      </span>

                      <span *ngIf="col.Format?.toLowerCase()?.startsWith('bool')">
                        <label
                          nz-checkbox
                          [(ngModel)]="dataItem[column.field]"
                          nzDisabled
                          [name]="column.field + '__' + rowIndex"
                        >
                        </label>
                      </span>

                      <span *ngIf="col.Format?.toLowerCase()?.startsWith('link')">
                        <a [name]="column.field + '__' + rowIndex" [href]="dataItem[column.field]">
                          {{ dataItem[column.field] }}
                        </a>
                      </span>

                      <span *ngIf="col.Format?.toLowerCase()?.startsWith('file')">
                        <ng-container
                          *ngIf="
                            col.template || defaultColumnTemplate;
                            else tplColumnFileNoTemplate
                          "
                          [ngTemplateOutlet]="col.template || defaultColumnTemplate"
                          [ngTemplateOutletContext]="{
                            $implicit: dataItem,
                            rowIndex: rowIndex,
                            field: field,
                            column: column,
                            columnItem: col
                          }"
                        >
                        </ng-container>
                        <ng-template #tplColumnFileNoTemplate>
                          <a
                            [name]="column.field + '__' + rowIndex"
                            (click)="onClickFileName(dataItem[column.field])"
                          >
                            {{ dataItem[column.field] }}
                          </a>
                        </ng-template>
                      </span>

                      <div
                        *ngIf="col.Format && col.Format?.toLowerCase()?.startsWith('html')"
                        [innerHTML]="dataItem[column.field]"
                      ></div>
                    </ng-template>
                    <ng-template #elseSchema>
                      <span
                        *ngIf="
                          builder.options?.configColumnSchema &&
                          builder.options?.configColumnSchema.fieldBoolean &&
                          builder.options?.configColumnSchema.fieldBoolean.indexOf(column.field) >=
                            0
                        "
                      >
                        <label
                          nz-checkbox
                          [(ngModel)]="dataItem[column.field]"
                          nzDisabled
                          [name]="column.field + '__' + rowIndex"
                        >
                        </label>
                      </span>

                      <!-- Edit incell -->
                      <ng-template
                        kendoGridEditTemplate
                        let-dataItem="dataItem"
                        *ngIf="!col.Disable"
                        let-rowIndex="rowIndex"
                        let-field="field"
                        let-column="column"
                      >
                        <ng-container
                          [ngTemplateOutlet]="col.editor"
                          *ngIf="column.editor"
                          [ngTemplateOutletContext]="{
                            $implicit: dataItem,
                            rowIndex: rowIndex,
                            field: field,
                            column: column,
                            columnItem: col
                          }"
                        >
                        </ng-container>
                      </ng-template>

                      <span
                        *ngIf="
                          builder.options?.configColumnSchema &&
                          builder.options?.configColumnSchema.fieldDate &&
                          builder.options?.configColumnSchema.fieldDate.indexOf(column.field) >= 0
                        "
                      >
                        {{ dataItem[column.field] | date : dateFormat }}
                      </span>

                      <span
                        *ngIf="
                          builder.options?.configColumnSchema &&
                          builder.options?.configColumnSchema.fieldNumber &&
                          builder.options?.configColumnSchema.fieldNumber.indexOf(column.field) >= 0
                        "
                      >
                        {{ dataItem[column.field] }}
                      </span>

                      <span
                        *ngIf="
                          builder.options?.configColumnSchema &&
                          builder.options?.configColumnSchema.fieldNumberMoney &&
                          builder.options?.configColumnSchema.fieldNumberMoney.indexOf(
                            column.field
                          ) >= 0
                        "
                      >
                        {{ dataItem[column.field] }}
                      </span>
                    </ng-template>
                  </ng-template>
                  <!-- #endregion -->

                  <!-- #region Fillter template menu-->
                  <ng-template
                    *ngIf="
                      (col.Format && col.Format.toLowerCase().startsWith('datetime|')) ||
                      (col.Format && col.Format.toLowerCase().startsWith('number|'))
                    "
                    kendoGridFilterMenuTemplate
                    let-filter
                    let-column="column"
                    let-filterService="filterService"
                  >
                    <kendo-grid-date-filter-menu
                      [column]="column"
                      [filter]="filter"
                      [filterService]="filterService"
                      *ngIf="col.Format && col.Format.toLowerCase().startsWith('datetime|')"
                      [format]="col.Format.split('|')[1]"
                    >
                    </kendo-grid-date-filter-menu>

                    <kendo-grid-numeric-filter-menu
                      [format]="col.Format.split('|')[1]"
                      [column]="column"
                      [filter]="filter"
                      [filterService]="filterService"
                      *ngIf="col.Format && col.Format.toLowerCase().startsWith('number|')"
                    >
                    </kendo-grid-numeric-filter-menu>
                  </ng-template>
                  <!-- #endregion -->

                  <!-- #region Group Footer Template -->
                  <ng-template
                    kendoGridGroupFooterTemplate
                    let-column
                    let-group="group"
                    let-columnIndex="columnIndex"
                    let-aggregates
                    let-field="field"
                    let-dataItem
                    let-rowIndex="rowIndex"
                    *ngIf="
                      customTemplateGroupColumnFooter && customTemplateGroupColumnFooter[col.Name]
                    "
                  >
                    <ng-container
                      [ngTemplateOutlet]="customTemplateGroupColumnFooter[col.Name]"
                      [ngTemplateOutletContext]="{
                        $implicit: dataItem,
                        rowIndex: rowIndex,
                        columnIndex: columnIndex,
                        field: field,
                        column: column,
                        columnItem: col,
                        aggregates: aggregates,
                        aggregateItem: vnrAggregateResult,
                        group: group
                      }"
                    >
                    </ng-container>
                  </ng-template>
                  <!-- #endregion -->

                  <!-- #region Footer -->
                  <ng-template
                    kendoGridFooterTemplate
                    let-column
                    let-columnIndex="columnIndex"
                    let-aggregates
                    let-field="field"
                    let-dataItem
                    let-rowIndex="rowIndex"
                    *ngIf="customTemplateColumnFooter && customTemplateColumnFooter[col.Name]"
                  >
                    <ng-container
                      [ngTemplateOutlet]="customTemplateColumnFooter[col.Name]"
                      [ngTemplateOutletContext]="{
                        $implicit: dataItem,
                        rowIndex: rowIndex,
                        columnIndex: columnIndex,
                        field: field,
                        column: column,
                        columnItem: col,
                        aggregates: aggregates,
                        aggregateItem: vnrAggregateResult
                      }"
                    >
                    </ng-container>
                  </ng-template>
                  <!-- #endregion -->

                  <ng-template
                    kendoGridGroupHeaderColumnTemplate
                    let-group="group"
                    let-column
                    let-columnIndex="columnIndex"
                    let-aggregates
                    let-field="field"
                    let-dataItem
                    let-rowIndex="rowIndex"
                    *ngIf="
                      customTemplateGroupColumnFooter && customTemplateGroupColumnFooter[col.Name]
                    "
                  >
                    <ng-container
                      [ngTemplateOutlet]="customTemplateGroupColumnFooter[col.Name]"
                      [ngTemplateOutletContext]="{
                        $implicit: dataItem,
                        rowIndex: rowIndex,
                        columnIndex: columnIndex,
                        field: field,
                        column: column,
                        columnItem: col,
                        aggregates: aggregates,
                        aggregateItem: vnrAggregateResult,
                        group: group
                      }"
                    >
                    </ng-container>
                  </ng-template>
                </kendo-grid-column>
              </kendo-grid-column-group>
            </ng-container>
            <ng-template #innerColumn let-depth="depth">
              <ng-container
                *ngTemplateOutlet="singleColumns; context: { col: subCol, depth: depth }"
              ></ng-container>
            </ng-template>
          </ng-container>

          <ng-template #singleColumns let-col="col" let-depth="depth">
            <kendo-grid-column
              [field]="col.Name"
              title="{{ col.HasChangeDisplayName ? col.DisplayName : (col.HeaderKey | translate) }}"
              [filterable]="col.Filter"
              [width]="columnWidth(col)"
              [maxResizableWidth]="col?.MaxResizableWidth"
              [minResizableWidth]="col?.MinResizableWidth || 50"
              [locked]="col.Locked === null || col.Locked === undefined ? false : col.Locked"
              [sortable]="col.Sortable"
              [hidden]="col.Hidden"
              [sticky]="col?.Sticky"
              [columnMenu]="!col.Hidden"
              [headerClass]="col.Class"
              class="{{
                (col.Format && col.Format.toLowerCase().startsWith('number|')) ||
                (col.Format && col.Format.toLowerCase().startsWith('datetime|')) ||
                (builder.options?.configColumnSchema &&
                  builder.options?.configColumnSchema.fieldNumber?.indexOf(col.Name) >= 0) ||
                (builder.options?.configColumnSchema &&
                  builder.options?.configColumnSchema.fieldNumberMoney &&
                  builder.options?.configColumnSchema.fieldNumberMoney.indexOf(col.Name) >= 0)
                  ? col.Class + ' text-right'
                  : col.Class
              }}"
            >
              <!-- *ngFor="let col of col.MultiColumn" -->
              <ng-template
                kendoGridHeaderTemplate
                let-columnIndex="columnIndex"
                let-column="column"
              >
                <span>
                  <ng-container
                    *ngIf="col[builder.options.configTitle.prefixTitleColumn]; else tplNormalHeader"
                  >
                    {{
                      (col.HasChangeDisplayName ? col.DisplayName : (col.HeaderKey | translate)) +
                        (col[builder.options.configTitle.prefixTitleColumn]
                          ? ' ' + col[builder.options.configTitle.prefixTitleColumn]
                          : '')
                    }}
                  </ng-container>

                  <ng-template #tplNormalHeader>
                    {{ col.HasChangeDisplayName ? col.DisplayName : (col.HeaderKey | translate) }}
                  </ng-template>
                </span>
              </ng-template>

              <!-- #region Group -->
              <ng-template
                kendoGridGroupHeaderTemplate
                let-group="group"
                let-field="field"
                let-value="value"
                let-aggregates="aggregates"
                *ngIf="col.Group"
                let-index="index"
              >
                <span
                  class="mr-1 ml-1"
                  *ngIf="
                    builder.options.configShowHide.isShowColumnCheck &&
                    builder.options.configShowHide.isShowColumnGroupCheck
                  "
                >
                  <input
                    type="checkbox"
                    kendoCheckBox
                    class="k-checkbox"
                    (change)="checkGroup(group, index)"
                    [checked]="isGroupChecked(group)"
                    class="customCheckBtn"
                    id="id_{{ field }}_{{ value }}"
                  />
                </span>
                <ng-container
                  *ngIf="col[builder.options.configTitle.prefixTitleColumn]; else tplNormalHeader"
                >
                  <span
                    class="vnr-grid-grouping-title"
                    [ngClass]="{
                      'vnr-grid-grouping-title__checked':
                        builder.options.configShowHide.isShowColumnCheck &&
                        customTemplateGroupColumnFooter
                    }"
                    title="{{ col | vnrGridGroupTitle : value : group }}"
                  >
                    <strong class="mr-1" [class]="col?.Class + '__title-group'">
                      {{
                        (col.HasChangeDisplayName ? col.DisplayName : (col.HeaderKey | translate)) +
                          (col[builder.options.configTitle.prefixTitleColumn]
                            ? ' ' + col[builder.options.configTitle.prefixTitleColumn]
                            : '')
                      }}:
                    </strong>
                    <ng-container *ngIf="col && col.Format; else tplNormalHeaderFormat">
                      <span [ngSwitch]="col.Format.split('|')[0].toLowerCase()">
                        <span *ngSwitchCase="'datetime'">
                          {{ value | date : col.Format.split('|')[1] }} ({{
                            group.items.length | json
                          }})
                        </span>
                        <span *ngSwitchDefault>
                          {{ value }} ({{ group.items.length | json }})
                        </span>
                      </span>
                    </ng-container>
                    <ng-template #tplNormalHeaderFormat>
                      {{ value }} ({{ group.items.length | json }})
                    </ng-template>
                  </span>
                </ng-container>

                <ng-template #tplNormalHeader>
                  <span
                    class="vnr-grid-grouping-title"
                    [ngClass]="{
                      'vnr-grid-grouping-title__checked':
                        builder.options.configShowHide.isShowColumnCheck &&
                        customTemplateGroupColumnFooter
                    }"
                    title="{{ col | vnrGridGroupTitle : value : group }}"
                  >
                    <strong class="mr-1" [class]="col?.Class + '__title-group'">
                      {{
                        col.HasChangeDisplayName ? col.DisplayName : (col.HeaderKey | translate)
                      }}:
                    </strong>
                    <ng-container *ngIf="col && col.Format; else tplNormalHeaderFormat">
                      <span [ngSwitch]="col.Format.split('|')[0].toLowerCase()">
                        <span *ngSwitchCase="'datetime'">
                          {{ value | date : col.Format.split('|')[1] }} ({{
                            group.items.length | json
                          }})
                        </span>
                        <span *ngSwitchDefault>
                          {{ value }} ({{ group.items.length | json }})
                        </span>
                      </span>
                    </ng-container>
                    <ng-template #tplNormalHeaderFormat>
                      {{ value }} ({{ group.items.length | json }})
                    </ng-template>
                  </span>
                </ng-template>
              </ng-template>
              <!-- #endregion -->

              <!-- #region Template outside -->
              <ng-template
                #tplColumnByColumnName
                *ngIf="col.template || defaultColumnTemplate"
                kendoGridCellTemplate
                let-dataItem
                let-rowIndex="rowIndex"
                let-field="field"
                let-column="column"
              >
                <ng-container
                  *ngIf="
                    builder.options.isEnabledFormat && isColumnHasFormatSpecial(col);
                    else defaultDataGroup
                  "
                >
                  <ng-container
                    *ngTemplateOutlet="
                      tplFormatTemplate;
                      context: { $implicit: col, dataItem: dataItem, rowIndex: rowIndex }
                    "
                  ></ng-container>
                </ng-container>
                <ng-template #defaultDataGroup>
                  <ng-container
                    [ngTemplateOutlet]="col.template || defaultColumnTemplate"
                    [ngTemplateOutletContext]="{
                      $implicit: dataItem,
                      rowIndex: rowIndex,
                      field: field,
                      column: column,
                      columnItem: col
                    }"
                  >
                  </ng-container>
                </ng-template>
              </ng-template>
              <!-- #endregion -->

              <!-- #region Format -->
              <ng-template
                kendoGridCellTemplate
                let-dataItem
                let-rowIndex="rowIndex"
                let-column="column"
                let-field="field"
                *ngIf="isColumnHasFormatSpecial(col) && !col.template"
              >
                <ng-template *ngIf="col.Format; then gridFormat; else elseSchema"></ng-template>
                <ng-template #gridFormat>
                  <span *ngIf="col.Format?.toLowerCase()?.startsWith('datetime|')">
                    <ng-container
                      *ngIf="
                        col.template || defaultColumnTemplate;
                        else tplColumnDatetimeNoTemplate
                      "
                      [ngTemplateOutlet]="col.template || defaultColumnTemplate"
                      [ngTemplateOutletContext]="{
                        $implicit: dataItem,
                        rowIndex: rowIndex,
                        field: field,
                        column: column,
                        columnItem: col
                      }"
                    >
                    </ng-container>
                    <ng-template #tplColumnDatetimeNoTemplate>
                      {{ dataItem[column.field] | date : col.Format.split('|')[1] }}
                    </ng-template>
                  </span>
                  <span *ngIf="col.Format?.toLowerCase()?.startsWith('number|')">
                    {{ dataItem[column.field] | kendoNumber : col.Format.split('|')[1] }}
                  </span>

                  <span *ngIf="col.Format?.toLowerCase()?.startsWith('bool')">
                    <label
                      nz-checkbox
                      [(ngModel)]="dataItem[column.field]"
                      nzDisabled
                      [name]="column.field + '__' + rowIndex"
                    >
                    </label>
                  </span>

                  <span *ngIf="col.Format?.toLowerCase()?.startsWith('link')">
                    <a [name]="column.field + '__' + rowIndex" [href]="dataItem[column.field]">
                      {{ dataItem[column.field] }}
                    </a>
                  </span>

                  <span *ngIf="col.Format?.toLowerCase()?.startsWith('file')">
                    <ng-container
                      *ngIf="col.template || defaultColumnTemplate; else tplColumnFileNoTemplate"
                      [ngTemplateOutlet]="col.template || defaultColumnTemplate"
                      [ngTemplateOutletContext]="{
                        $implicit: dataItem,
                        rowIndex: rowIndex,
                        field: field,
                        column: column,
                        columnItem: col
                      }"
                    >
                    </ng-container>
                    <ng-template #tplColumnFileNoTemplate>
                      <a
                        [name]="column.field + '__' + rowIndex"
                        (click)="onClickFileName(dataItem[column.field])"
                      >
                        {{ dataItem[column.field] }}
                      </a>
                    </ng-template>
                  </span>

                  <div
                    *ngIf="col.Format && col.Format?.toLowerCase()?.startsWith('html')"
                    [innerHTML]="dataItem[column.field]"
                  ></div>
                </ng-template>
                <ng-template #elseSchema>
                  <span
                    *ngIf="
                      builder.options?.configColumnSchema &&
                      builder.options?.configColumnSchema.fieldBoolean &&
                      builder.options?.configColumnSchema.fieldBoolean.indexOf(column.field) >= 0
                    "
                  >
                    <label
                      nz-checkbox
                      [(ngModel)]="dataItem[column.field]"
                      nzDisabled
                      [name]="column.field + '__' + rowIndex"
                    >
                    </label>
                  </span>

                  <!-- Edit incell -->
                  <ng-template
                    kendoGridEditTemplate
                    let-dataItem="dataItem"
                    *ngIf="!col.Disable"
                    let-rowIndex="rowIndex"
                    let-field="field"
                    let-column="column"
                  >
                    <ng-container
                      [ngTemplateOutlet]="col.editor"
                      *ngIf="column.editor"
                      [ngTemplateOutletContext]="{
                        $implicit: dataItem,
                        rowIndex: rowIndex,
                        field: field,
                        column: column,
                        columnItem: col
                      }"
                    >
                    </ng-container>
                  </ng-template>

                  <span
                    *ngIf="
                      builder.options?.configColumnSchema &&
                      builder.options?.configColumnSchema.fieldDate &&
                      builder.options?.configColumnSchema.fieldDate.indexOf(column.field) >= 0
                    "
                  >
                    {{ dataItem[column.field] | date : dateFormat }}
                  </span>

                  <span
                    *ngIf="
                      builder.options?.configColumnSchema &&
                      builder.options?.configColumnSchema.fieldNumber &&
                      builder.options?.configColumnSchema.fieldNumber.indexOf(column.field) >= 0
                    "
                  >
                    {{ dataItem[column.field] }}
                  </span>

                  <span
                    *ngIf="
                      builder.options?.configColumnSchema &&
                      builder.options?.configColumnSchema.fieldNumberMoney &&
                      builder.options?.configColumnSchema.fieldNumberMoney.indexOf(column.field) >=
                        0
                    "
                  >
                    {{ dataItem[column.field] }}
                  </span>
                </ng-template>
              </ng-template>
              <!-- #endregion -->

              <!-- #region Fillter template menu-->
              <ng-template
                *ngIf="
                  (col.Format && col.Format.toLowerCase().startsWith('datetime|')) ||
                  (col.Format && col.Format.toLowerCase().startsWith('number|'))
                "
                kendoGridFilterMenuTemplate
                let-filter
                let-column="column"
                let-filterService="filterService"
              >
                <kendo-grid-date-filter-menu
                  [column]="column"
                  [filter]="filter"
                  [filterService]="filterService"
                  *ngIf="col.Format && col.Format.toLowerCase().startsWith('datetime|')"
                  [format]="col.Format.split('|')[1]"
                >
                </kendo-grid-date-filter-menu>

                <kendo-grid-numeric-filter-menu
                  [format]="col.Format.split('|')[1]"
                  [column]="column"
                  [filter]="filter"
                  [filterService]="filterService"
                  *ngIf="col.Format && col.Format.toLowerCase().startsWith('number|')"
                >
                </kendo-grid-numeric-filter-menu>
              </ng-template>
              <!-- #endregion -->

              <!-- #region Group Footer Template -->
              <ng-template
                kendoGridGroupFooterTemplate
                let-column
                let-group="group"
                let-columnIndex="columnIndex"
                let-aggregates
                let-field="field"
                let-rowIndex="rowIndex"
                let-dataItem
                *ngIf="customTemplateGroupColumnFooter && customTemplateGroupColumnFooter[col.Name]"
              >
                <ng-container
                  [ngTemplateOutlet]="customTemplateGroupColumnFooter[col.Name]"
                  [ngTemplateOutletContext]="{
                    $implicit: dataItem,
                    rowIndex: rowIndex,
                    columnIndex: columnIndex,
                    field: field,
                    column: column,
                    columnItem: col,
                    aggregates: aggregates,
                    aggregateItem: vnrAggregateResult,
                    group: group
                  }"
                >
                </ng-container>
              </ng-template>
              <!-- #endregion -->

              <!-- #region Footer -->
              <ng-template
                kendoGridFooterTemplate
                let-column
                let-columnIndex="columnIndex"
                let-aggregates
                let-field="field"
                let-rowIndex="rowIndex"
                let-dataItem
                *ngIf="customTemplateColumnFooter && customTemplateColumnFooter[col.Name]"
              >
                <ng-container
                  [ngTemplateOutlet]="customTemplateColumnFooter[col.Name]"
                  [ngTemplateOutletContext]="{
                    $implicit: dataItem,
                    rowIndex: rowIndex,
                    columnIndex: columnIndex,
                    field: field,
                    column: column,
                    columnItem: col,
                    aggregates: aggregates,
                    aggregateItem: vnrAggregateResult
                  }"
                >
                </ng-container>
              </ng-template>
              <!-- #endregion -->

              <ng-template
                kendoGridGroupHeaderColumnTemplate
                let-group="group"
                let-column
                let-columnIndex="columnIndex"
                let-aggregates
                let-field="field"
                let-rowIndex="rowIndex"
                let-dataItem
                *ngIf="customTemplateGroupColumnFooter && customTemplateGroupColumnFooter[col.Name]"
              >
                <ng-container
                  [ngTemplateOutlet]="customTemplateGroupColumnFooter[col.Name]"
                  [ngTemplateOutletContext]="{
                    $implicit: dataItem,
                    rowIndex: rowIndex,
                    columnIndex: columnIndex,
                    field: field,
                    column: column,
                    columnItem: col,
                    aggregates: aggregates,
                    aggregateItem: vnrAggregateResult,
                    group: group
                  }"
                >
                </ng-container>
              </ng-template>
            </kendo-grid-column>
          </ng-template>
        </kendo-grid-column-group>
      </ng-container>
      <!-- #endregion -->

      <!-- #region Column without Group -->
      <ng-template #columnWithoutGroup>
        <kendo-grid-column
          [field]="col.Name"
          title="{{ col.HasChangeDisplayName ? col.DisplayName : (col.HeaderKey | translate) }}"
          [filterable]="col.Filter"
          [width]="columnWidth(col)"
          [minResizableWidth]="col?.MinResizableWidth || 50"
          [maxResizableWidth]="col?.MaxResizableWidth"
          [locked]="col.Locked === null || col.Locked === undefined ? false : col.Locked"
          [sortable]="col.Sortable"
          [hidden]="col.Hidden"
          [sticky]="col?.Sticky"
          [columnMenu]="!col.Hidden"
          [headerClass]="col.Class"
          class="{{
            (col.Format && col.Format.toLowerCase().startsWith('number|')) ||
            (col.Format && col.Format.toLowerCase().startsWith('datetime|')) ||
            (builder.options?.configColumnSchema &&
              builder.options?.configColumnSchema.fieldNumber.indexOf(col.Name) >= 0) ||
            (builder.options?.configColumnSchema &&
              builder.options?.configColumnSchema.fieldNumberMoney &&
              builder.options?.configColumnSchema.fieldNumberMoney.indexOf(col.Name) >= 0)
              ? col.Class + ' text-right'
              : col.Class
          }}"
        >
          <ng-template
            kendoGridHeaderTemplate
            let-dataItem
            let-columnIndex="columnIndex"
            let-column="column"
          >
            <ng-container
              *ngIf="columnHeaderTemplate; else tplDefaultHeaderTemplate"
              [ngTemplateOutlet]="columnHeaderTemplate"
              [ngTemplateOutletContext]="{
                $implicit: dataItem,
                column: column,
                columnIndex: columnIndex,
                col: col,
                fieldIndex: builder.options.configTitle.prefixTitleColumn
              }"
            ></ng-container>
            <ng-template #tplDefaultHeaderTemplate>
              <span>
                <ng-container
                  *ngIf="col[builder.options.configTitle.prefixTitleColumn]; else tplNormalHeader"
                >
                  {{
                    (col.HasChangeDisplayName ? col.DisplayName : (col.HeaderKey | translate)) +
                      (col[builder.options.configTitle.prefixTitleColumn]
                        ? ' ' + col[builder.options.configTitle.prefixTitleColumn]
                        : '')
                  }}
                </ng-container>

                <ng-template #tplNormalHeader>
                  {{ col.HasChangeDisplayName ? col.DisplayName : (col.HeaderKey | translate) }}
                </ng-template>
              </span>
            </ng-template>
          </ng-template>

          <!-- #region Group -->
          <ng-template
            kendoGridGroupHeaderTemplate
            let-group="group"
            let-field="field"
            let-value="value"
            let-aggregates="aggregates"
            let-index="index"
            let-column
            let-columnIndex="columnIndex"
            let-dataItem
            *ngIf="col.Group"
          >
            <span
              class="mr-1 ml-1"
              *ngIf="
                builder.options.configShowHide.isShowColumnCheck &&
                builder.options.configShowHide.isShowColumnCheck
              "
            >
              <input
                type="checkbox"
                kendoCheckBox
                class="k-checkbox"
                (change)="checkGroup(group, index)"
                [checked]="isGroupChecked(group)"
                class="customCheckBtn"
                id="id_{{ field }}_{{ value }}"
              />
            </span>
            <ng-container
              [ngTemplateOutlet]="tplDefaultHeaderGroupTemplate"
              [ngTemplateOutletContext]="{
                $implicit: dataItem,
                column: column,
                columnIndex: columnIndex,
                col: col,
                fieldIndex: builder.options.configTitle.prefixTitleColumn
              }"
            ></ng-container>
            <ng-template #tplDefaultHeaderGroupTemplate>
              <ng-container
                *ngIf="col[builder.options.configTitle.prefixTitleColumn]; else tplNormalHeader"
              >
                <span
                  class="vnr-grid-grouping-title"
                  [ngClass]="{
                    'vnr-grid-grouping-title__checked':
                      builder.options.configShowHide.isShowColumnCheck &&
                      customTemplateGroupColumnFooter
                  }"
                  title="{{ col | vnrGridGroupTitle : value : group }}"
                >
                  <strong class="mr-1 q" [class]="col?.Class + '__title-group'">
                    {{
                      (col.HasChangeDisplayName ? col.DisplayName : (col.HeaderKey | translate)) +
                        (col[builder.options.configTitle.prefixTitleColumn]
                          ? ' ' + col[builder.options.configTitle.prefixTitleColumn]
                          : '')
                    }}:
                  </strong>
                  <ng-container *ngIf="col && col.Format; else tplNormalHeaderFormat">
                    <span [ngSwitch]="col.Format.split('|')[0].toLowerCase()">
                      <span *ngSwitchCase="'datetime'">
                        {{ value | date : col.Format.split('|')[1] }} ({{
                          group.items.length | json
                        }})
                      </span>
                      <span *ngSwitchDefault> {{ value }} ({{ group.items.length | json }}) </span>
                    </span>
                  </ng-container>
                  <ng-template #tplNormalHeaderFormat>
                    {{ value }} ({{ group.items.length | json }})
                  </ng-template>
                </span>
              </ng-container>

              <ng-template #tplNormalHeader>
                <span
                  class="vnr-grid-grouping-title"
                  [ngClass]="{
                    'vnr-grid-grouping-title__checked':
                      builder.options.configShowHide.isShowColumnCheck &&
                      customTemplateGroupColumnFooter
                  }"
                  title="{{ col | vnrGridGroupTitle : value : group }}"
                >
                  <strong class="mr-1" [class]="col?.Class + '__title-group'">
                    {{ col.HasChangeDisplayName ? col.DisplayName : (col.HeaderKey | translate) }}:
                  </strong>

                  <ng-container *ngIf="col && col.Format; else tplNormalHeaderFormat">
                    <span [ngSwitch]="col.Format.split('|')[0].toLowerCase()">
                      <span *ngSwitchCase="'datetime'">
                        {{ value | date : col.Format.split('|')[1] }} ({{
                          group.items.length | json
                        }})
                      </span>
                      <span *ngSwitchDefault> {{ value }} ({{ group.items.length | json }}) </span>
                    </span>
                  </ng-container>
                  <ng-template #tplNormalHeaderFormat>
                    {{ value }} ({{ group.items.length | json }})
                  </ng-template>
                </span>
              </ng-template>
            </ng-template>
          </ng-template>
          <!-- #endregion -->

          <!-- #region Custom by Column Name -->
          <ng-template
            *ngIf="col.template || defaultColumnTemplate"
            kendoGridCellTemplate
            let-dataItem
            let-rowIndex="rowIndex"
            let-field="field"
            let-column="column"
          >
            <ng-container
              *ngIf="
                builder.options.isEnabledFormat && isColumnHasFormatSpecial(col);
                else defaultDataWithoutGroup
              "
            >
              <ng-container
                *ngTemplateOutlet="
                  tplFormatTemplate;
                  context: { $implicit: col, dataItem: dataItem, rowIndex: rowIndex }
                "
              ></ng-container>
            </ng-container>
            <ng-template #defaultDataWithoutGroup>
              <ng-container
                [ngTemplateOutlet]="col.template || defaultColumnTemplate"
                [ngTemplateOutletContext]="{
                  $implicit: dataItem,
                  rowIndex: rowIndex,
                  field: field,
                  column: column,
                  columnItem: col
                }"
              >
              </ng-container>
            </ng-template>
          </ng-template>
          <!-- #endregion Custom by Column Name -->

          <!-- #region Format -->
          <ng-template
            kendoGridCellTemplate
            let-dataItem
            let-rowIndex="rowIndex"
            let-column="column"
            let-field="field"
            *ngIf="isColumnHasFormatSpecial(col) && !col.template"
          >
            <ng-template *ngIf="col.Format; then gridFormat; else elseSchema"></ng-template>

            <!-- #region Column with Format  -->
            <ng-template #gridFormat>
              <span *ngIf="col.Format.toLowerCase()?.startsWith('datetime|')">
                <ng-container
                  *ngIf="col.template || defaultColumnTemplate; else tplColumnDatetimeNoTemplate"
                  [ngTemplateOutlet]="col.template || defaultColumnTemplate"
                  [ngTemplateOutletContext]="{
                    $implicit: dataItem,
                    rowIndex: rowIndex,
                    field: field,
                    column: column,
                    columnItem: col
                  }"
                >
                </ng-container>
                <ng-template #tplColumnDatetimeNoTemplate>
                  {{ dataItem[column.field] | date : col.Format.split('|')[1] }}
                </ng-template>
              </span>
              <span *ngIf="col.Format.toLowerCase().startsWith('number|')">
                {{ dataItem[column.field] | kendoNumber : col.Format.split('|')[1] }}
              </span>
              <span *ngIf="col.Format.toLowerCase()?.startsWith('bool')">
                <label
                  nz-checkbox
                  [(ngModel)]="dataItem[column.field]"
                  nzDisabled
                  [name]="column.field + '__' + rowIndex"
                >
                </label>
              </span>

              <span *ngIf="col.Format.toLowerCase()?.startsWith('link')">
                <a [name]="column.field + '__' + rowIndex" [href]="dataItem[column.field]">
                  {{ dataItem[column.field] }}
                </a>
              </span>

              <span *ngIf="col.Format.toLowerCase()?.startsWith('file')">
                <ng-container
                  *ngIf="col.template || defaultColumnTemplate; else tplColumnFileNoTemplate"
                  [ngTemplateOutlet]="col.template || defaultColumnTemplate"
                  [ngTemplateOutletContext]="{
                    $implicit: dataItem,
                    rowIndex: rowIndex,
                    field: field,
                    column: column,
                    columnItem: col
                  }"
                >
                </ng-container>
                <ng-template #tplColumnFileNoTemplate>
                  <a
                    [name]="column.field + '__' + rowIndex"
                    (click)="onClickFileName(dataItem[column.field])"
                  >
                    {{ dataItem[column.field] }}
                  </a>
                </ng-template>
              </span>

              <div
                *ngIf="col.Format && col.Format.toLowerCase().startsWith('html')"
                [innerHTML]="dataItem[column.field]"
              ></div>
            </ng-template>
            <!-- #endregion Column with Format -->

            <!-- #region Column without Format  -->
            <ng-template #elseSchema>
              <span
                *ngIf="
                  builder.options?.configColumnSchema &&
                  builder.options?.configColumnSchema.fieldBoolean &&
                  builder.options?.configColumnSchema.fieldBoolean.indexOf(column.field) >= 0
                "
              >
                <label
                  nz-checkbox
                  [(ngModel)]="dataItem[column.field]"
                  nzDisabled
                  [name]="column.field + '__' + rowIndex"
                >
                </label>
              </span>

              <!-- Edit incell -->
              <ng-template
                kendoGridEditTemplate
                let-dataItem="dataItem"
                *ngIf="!col.Disable"
                let-rowIndex="rowIndex"
                let-field="field"
                let-column="column"
              >
                <ng-container
                  [ngTemplateOutlet]="col.editor"
                  *ngIf="column.editor"
                  [ngTemplateOutletContext]="{
                    $implicit: dataItem,
                    rowIndex: rowIndex,
                    field: field,
                    column: column,
                    columnItem: col
                  }"
                >
                </ng-container>
              </ng-template>

              <span
                *ngIf="
                  builder.options?.configColumnSchema &&
                  builder.options?.configColumnSchema.fieldDate &&
                  builder.options?.configColumnSchema.fieldDate.indexOf(column.field) >= 0
                "
              >
                {{ dataItem[column.field] | date : dateFormat }}
              </span>

              <span
                *ngIf="
                  builder.options?.configColumnSchema &&
                  builder.options?.configColumnSchema.fieldNumber &&
                  builder.options?.configColumnSchema.fieldNumber.indexOf(column.field) >= 0
                "
              >
                {{ dataItem[column.field] }}
              </span>

              <span
                *ngIf="
                  builder.options?.configColumnSchema &&
                  builder.options?.configColumnSchema.fieldNumberMoney &&
                  builder.options?.configColumnSchema.fieldNumberMoney.indexOf(column.field) >= 0
                "
              >
                {{ dataItem[column.field] }}
              </span>
            </ng-template>
            <!-- #endregion Column without Format -->
          </ng-template>
          <!-- #endregion -->

          <!-- #region Fillter template menu-->
          <ng-template
            *ngIf="
              (col.Format && col.Format.toLowerCase().startsWith('datetime|')) ||
              (col.Format && col.Format.toLowerCase().startsWith('number|'))
            "
            kendoGridFilterMenuTemplate
            let-filter
            let-column="column"
            let-filterService="filterService"
          >
            <kendo-grid-date-filter-menu
              [column]="column"
              [filter]="filter"
              [filterService]="filterService"
              *ngIf="col.Format && col.Format.toLowerCase().startsWith('datetime|')"
              [format]="col.Format.split('|')[1]"
            >
            </kendo-grid-date-filter-menu>

            <kendo-grid-numeric-filter-menu
              [format]="col.Format.split('|')[1]"
              [column]="column"
              [filter]="filter"
              [filterService]="filterService"
              *ngIf="col.Format && col.Format.toLowerCase().startsWith('number|')"
            >
            </kendo-grid-numeric-filter-menu>
          </ng-template>
          <!-- #endregion -->

          <!-- #region Group Footer Template -->
          <ng-template
            kendoGridGroupFooterTemplate
            let-column
            let-group="group"
            let-columnIndex="columnIndex"
            let-aggregates
            let-field="field"
            let-dataItem="dataItem"
            let-rowIndex="rowIndex"
            *ngIf="customTemplateGroupColumnFooter && customTemplateGroupColumnFooter[col.Name]"
          >
            <ng-container
              [ngTemplateOutlet]="customTemplateGroupColumnFooter[col.Name]"
              [ngTemplateOutletContext]="{
                $implicit: dataItem,
                rowIndex: rowIndex,
                columnIndex: columnIndex,
                field: field,
                column: column,
                columnItem: col,
                aggregates: aggregates,
                aggregateItem: vnrAggregateResult,
                group: group
              }"
            >
            </ng-container>
          </ng-template>
          <!-- #endregion -->

          <!--- Group Header Template -->
          <ng-template
            kendoGridGroupHeaderColumnTemplate
            let-group="group"
            let-column
            let-columnIndex="columnIndex"
            let-aggregates
            let-field="field"
            let-dataItem="dataItem"
            let-rowIndex="rowIndex"
            *ngIf="customTemplateGroupColumnFooter && customTemplateGroupColumnFooter[col.Name]"
          >
            <ng-container
              [ngTemplateOutlet]="customTemplateGroupColumnFooter[col.Name]"
              [ngTemplateOutletContext]="{
                $implicit: dataItem,
                rowIndex: rowIndex,
                columnIndex: columnIndex,
                field: field,
                column: column,
                columnItem: col,
                aggregates: aggregates,
                aggregateItem: vnrAggregateResult,
                group: group
              }"
            >
            </ng-container>
          </ng-template>

          <!-- #region Footer -->
          <ng-template
            kendoGridFooterTemplate
            let-column
            let-columnIndex="columnIndex"
            let-aggregates
            let-field="field"
            let-dataItem="dataItem"
            let-rowIndex="rowIndex"
            *ngIf="customTemplateColumnFooter && customTemplateColumnFooter[col.Name]"
          >
            <ng-container
              [ngTemplateOutlet]="customTemplateColumnFooter[col.Name]"
              [ngTemplateOutletContext]="{
                $implicit: dataItem,
                rowIndex: rowIndex,
                columnIndex: columnIndex,
                field: field,
                column: column,
                columnItem: col,
                aggregates: aggregates,
                aggregateItem: vnrAggregateResult
              }"
            >
            </ng-container>
          </ng-template>
          <!-- #endregion -->
        </kendo-grid-column>
      </ng-template>
      <!-- #endregion -->
    </ng-container>
    <!-- #endregion -->

    <!-- #region Action Column -->
    <kendo-grid-command-column
      *ngIf="
        gridColumns &&
        gridColumns.length > 0 &&
        builder.options.configCommandColumn.isEnabledMenuAction &&
        (builder.options.configShowHide.isShowEdit ||
          builder.options.configShowHide.isShowDelete ||
          rowActionsTemplate)
      "
      class="vnr-grid-action-column"
      [class]="isLastChild ? 'vnr-grid-action-column-last' : ''"
      [headerClass]="['vnr-grid-action-column-header']"
      [footerClass]="['vnr-grid-action-column-footer']"
      [style]="{ 'background-color': 'transparent' }"
      [sticky]="rowDetailTemplate ? false : true"
      title=""
      [width]="builder.options.configCommandColumn.width"
      [minResizableWidth]="builder.options.configCommandColumn.width || 100"
      [maxResizableWidth]="builder.options.configCommandColumn.width || 100"
      [columnMenu]="false"
    >
      <ng-template kendoGridCellTemplate let-dataItem let-isNew="isNew">
        <div
          class="vnr-grid-action-column__group-btn d-flex justify-content-end align-items-center"
          [ngClass]="{
            'vnr-grid-action-column__group-btn-hidden': !isHovered,
            'vnr-grid-action-column__group-btn-show': isHovered
          }"
        >
          <ng-template [ngTemplateOutlet]="tplCustomBtn"></ng-template>
          <div class="mr-1" *ngIf="builder.options.configShowHide.isShowEdit">
            <button
              nz-button
              nzType="primary"
              (click)="onEdit(dataItem)"
              class="btn-kendoGrid-customize btn-kendoGrid-customize__edit ant-btn ant-btn-primary"
            >
              <span nz-icon nzType="edit" nzTheme="outline"></span>
            </button>
          </div>
          <div *ngIf="builder.options.configShowHide.isShowDelete">
            <button
              nz-button
              nzType="primary"
              (click)="onDelete(dataItem)"
              class="btn-kendoGrid-customize btn-kendoGrid-customize__delete ant-btn ant-btn-primary"
            >
              <i class="far fa-trash-alt"></i>
            </button>
          </div>
        </div>
        <ng-template #tplCustomBtn>
          <ng-container
            *ngIf="rowActionsTemplate"
            [ngTemplateOutletContext]="{ $implicit: dataItem }"
            [ngTemplateOutlet]="rowActionsTemplate"
          ></ng-container>
        </ng-template>
      </ng-template>
    </kendo-grid-command-column>
    <!-- #endregion -->

    <kendo-grid-column
      *ngIf="
        !hasLockedColumn && !hasStickyColumn && builder.options.configShowHide.isShowTempColumn
      "
    ></kendo-grid-column>

    <!-- #region Collapse Template -->
    <ng-template
      *ngIf="rowDetailTemplate && !hasStickyColumn"
      kendoGridDetailTemplate
      let-dataItem
      let-column="column"
    >
      <ng-container
        [ngTemplateOutlet]="rowDetailTemplate"
        [ngTemplateOutletContext]="{
          $implicit: dataItem,
          column: column
        }"
      ></ng-container>
    </ng-template>
    <!-- #endregion Collapse Template -->

    <!-- #region Custom Messages -->
    <kendo-grid-messages
      noRecords="{{ 'common.grid.noData' | translate }}"
      pagerPage="{{ 'common.grid.pagerPage' | translate }}"
      pagerOf="{{ 'common.grid.pagerOf' | translate }}"
      pagerItems="{{ 'common.grid.pagerItems' | translate }}"
      pagerItemsPerPage="{{ 'common.grid.pagerItemsPerPage' | translate }}"
      sortAscending="{{ 'common.grid.sortAscending' | translate }}"
      sortDescending="{{ 'common.grid.sortDescending' | translate }}"
      columns="{{ 'common.grid.columns' | translate }}"
      columnMenu="{{ 'common.grid.columnMenu' | translate }}"
      columnsReset="{{ 'common.grid.columnsReset' | translate }}"
      columnsApply="{{ 'common.grid.columnsApply' | translate }}"
      filterContainsOperator="{{ 'grid.filter.contains' | translate }}"
      filterNotContainsOperator="{{ 'grid.filter.doesNotContain' | translate }}"
      filterAfterOperator="{{ 'grid.filter.isAfter' | translate }}"
      filterAfterOrEqualOperator="{{ 'grid.filter.isAfterOrEqualTo' | translate }}"
      filterBeforeOperator="{{ 'grid.filter.isBefore' | translate }}"
      filterBeforeOrEqualOperator="{{ 'grid.filter.isBeforeOrEqualTo' | translate }}"
      filterEqOperator="{{ 'grid.filter.isEqualTo' | translate }}"
      filterNotEqOperator="{{ 'grid.filter.isNotEqualTo' | translate }}"
      filterStartsWithOperator="{{ 'grid.filter.startsWith' | translate }}"
      filterEndsWithOperator="{{ 'grid.filter.endsWith' | translate }}"
      filterIsNullOperator="{{ 'grid.filter.isNull' | translate }}"
      filterIsNotNullOperator="{{ 'grid.filter.isNotNull' | translate }}"
      filterIsEmptyOperator="{{ 'grid.filter.isEmpty' | translate }}"
      filterIsNotEmptyOperator="{{ 'grid.filter.isNotEmpty' | translate }}"
      filterAndLogic="{{ 'grid.filter.andLogic' | translate }}"
      filterOrLogic="{{ 'grid.filter.orLogic' | translate }}"
      filterFilterButton="{{ 'grid.filter.filterButton' | translate }}"
      pagerPreviousPage="{{ 'common.modal.PreviousPage' | translate }}"
      pagerNextPage="{{ 'common.modal.NextPage' | translate }}"
      pagerFirstPage="{{ 'common.modal.FirstPage' | translate }}"
      pagerLastPage="{{ 'common.modal.LastPage' | translate }}"
      pagerPage="{{ 'common.modal.PagerOf' | translate }}"
    >
    </kendo-grid-messages>
    <!-- #endregion Custom Messages -->

    <!-- #region Pager -->
    <ng-template
      kendoPagerTemplate
      let-totalPages="totalPages"
      let-currentPage="currentPage"
      let-total="total"
      let-pageSize="pageSize"
      let-skip="skip"
    >
      <ng-container *ngIf="builder.options.configShowHide.isPageExpand; else tplKendoPager">
        <div
          class="treelist-loadmore d-flex justify-content-center align-items-center"
          style="text-align: center; width: 100%; min-height: 41px"
          [ngClass]="{
            'vnrPageExpand-none': funcCalcCountLoadMore(gridView?.data) === gridView?.total
          }"
          *ngIf="
            gridView &&
            gridView.data &&
            gridView.data.length > 0 &&
            funcCalcCountLoadMore(gridView?.data) < gridView?.total
          "
        >
          <button
            class="kendo-loadMore-customs"
            nz-button
            nzType="default"
            (click)="loadExpandBtn()"
            nzShape="round"
            [disabled]="isLoading"
          >
            <span class="d-flex justify-content-center align-items-center" nz-icon>
              <span class="mr-1 d-flex" nz-icon *ngIf="!isLoading">
                <i class="fas fa-angle-down"></i>
              </span>
              <span class="mr-1" nz-icon [nzType]="'loading'" *ngIf="isLoading"></span>
              {{ 'loadMore' | translate }}
              <span *ngIf="gridView && gridView.data?.length > 0">
                ({{ funcCalcCountLoadMore(gridView.data) }}/{{ gridView.total }})
              </span>
            </span>
          </button>
        </div>
      </ng-container>
      <ng-template #tplKendoPager>
        <kendo-pager-prev-buttons></kendo-pager-prev-buttons>
        <kendo-pager-numeric-buttons [buttonCount]="buttonCount"></kendo-pager-numeric-buttons>
        <kendo-pager-next-buttons></kendo-pager-next-buttons>
        <!-- <kendo-pager-input></kendo-pager-input> -->
        <kendo-pager-page-sizes
          [pageSizes]="builder.options.configPageable.pageSizes"
        ></kendo-pager-page-sizes>
        <!-- <kendo-pager-info></kendo-pager-info> -->
        <div class="pager-total-custom">
          <p>
            <span>{{ total }}</span> {{ 'common.modal.reasonContentRowSelected' | translate }}
          </p>
        </div>
      </ng-template>
    </ng-template>
    <!-- #endregion -->
  </kendo-grid>

  <!-- #region Change Columns -->
  <change-column-old
    *ngIf="isOpenChangeColumn && !isChangeColumnNew"
    style="width: 100%"
    [builder]="builderGridChangeColumn"
    (vnrClosePopup)="vnrcloseAndRefreshGird($event)"
  ></change-column-old>
  <!-- #endregion Change Columns -->
</form>

<!-- #region tpl format with custom tpl column -->
<ng-template
  #tplFormatTemplate
  let-column
  let-dataItem="dataItem"
  let-field="field"
  let-rowIndex="rowIndex"
>
  <div [ngSwitch]="column?.Format?.split('|')[0]?.toLowerCase()">
    <span *ngSwitchCase="'datetime'">
      <ng-container>
        {{ dataItem[column.Name] | date : column.Format.split('|')[1] }}
      </ng-container>
    </span>
    <span *ngSwitchCase="'number'">
      <ng-container *ngIf="column?.Format?.split('|')[1] === 'cp'; else tplColumnNoCustom">
        {{
          dataItem[column.Name] !== null
            ? (dataItem[column.Name] | kendoNumber : '#,##0.00 \\%')
            : '%'
        }}
      </ng-container>
      <ng-template #tplColumnNoCustom>
        {{ dataItem[column.Name] | kendoNumber : column.Format.split('|')[1] }}
      </ng-template>
    </span>
    <span *ngSwitchCase="'bool'">
      <label
        nz-checkbox
        [(ngModel)]="dataItem[column.Name]"
        nzDisabled
        [name]="column.Name + '__' + rowIndex"
      >
      </label>
    </span>
    <span *ngSwitchCase="'link'">
      <a [name]="column.Name + '__' + rowIndex" [href]="dataItem[column.Name]">
        {{ dataItem[column.Name] }}
      </a>
    </span>
    <span *ngSwitchCase="'file'">
      <ng-container
        *ngIf="column.template; else tplColumnFileNoTemplate1"
        [ngTemplateOutlet]="column.template"
        [ngTemplateOutletContext]="{
          $implicit: dataItem,
          rowIndex: rowIndex,
          field: column.Name,
          column: column,
          columnItem: column
        }"
      >
      </ng-container>
      <ng-template #tplColumnFileNoTemplate1>
        <span class="d-flex">
          <ng-container
            *ngFor="
              let file of dataItem[column.Name]
                | vnrConvertFileNameToList : builder.options.configApiSupport.apiDownload?.url : 25;
              let last = last
            "
          >
            <a
              class="vnr-grids-upload__box"
              (click)="onClickFileName(file.name); $event.preventDefault()"
              [title]="file && file?.showTooltip && file?.name ? file.name : file?.nameFormat"
              [name]="column.Name + '__' + rowIndex"
            >
              <div class="vnr-grids-upload__nameFile">
                <span nz-icon [nzType]="'paper-clip'" style="color: #262626"></span>
                {{ file && file.nameFormat }}
              </div>
            </a>
            <span class="mr-1" *ngIf="!last">,</span>
          </ng-container>
        </span>
      </ng-template>
    </span>
    <span *ngSwitchCase="'html'">
      <div [innerHTML]="dataItem[column.Name]"></div>
    </span>
    <span *ngSwitchCase="'money'">
      {{ dataItem[column.Name] | currency : 'VND' : '' : '1.0-3' : '' }}
    </span>
    <span *ngSwitchCase="'uploadfile'" class="--has-custom-template vnr-format-type-uploadfile">
      <ng-container *ngIf="dataItem[column.Name]; else tplUploadIcon">
        <div class="d-flex">
          <span
            (click)="onClickUploadFileToColumn(column.Name, dataItem, column)"
            class="mr-2"
            style="cursor: pointer"
          >
            <i nz-icon nzType="edit" nzTheme="twotone"></i>
          </span>
          <ng-container
            *ngFor="
              let file of dataItem[column.Name]
                | vnrConvertFileNameToList : builder.options.configApiSupport.apiDownload?.url : 25;
              let last = last
            "
          >
            <a
              class="vnr-grids-upload__box"
              (click)="onClickFileName(file.name); $event.preventDefault()"
              [title]="file && file?.showTooltip && file?.name ? file.name : file?.nameFormat"
              [name]="column.Name + '__' + rowIndex"
            >
              <div class="vnr-grids-upload__nameFile">
                <span nz-icon [nzType]="'paper-clip'" style="color: #262626"></span>
                {{ file && file.nameFormat }}
              </div>
            </a>
            <span class="mr-1" *ngIf="!last">,</span>
          </ng-container>
        </div>
      </ng-container>
      <ng-template #tplUploadIcon>
        <button
          nz-button
          [nzType]="'default'"
          [nzSize]="'small'"
          (click)="onClickUploadFileToColumn(column.Name, dataItem, column)"
          class="d-flex justify-content-center align-items-center m-auto"
        >
          <i nz-icon nzType="upload" nzTheme="outline"></i>
        </button>
      </ng-template>
    </span>
    <span *ngSwitchDefault>
      <ng-container
        *ngIf="column.template || defaultColumnTemplate"
        [ngTemplateOutlet]="column.template || defaultColumnTemplate"
        [ngTemplateOutletContext]="{
          $implicit: dataItem,
          rowIndex: rowIndex,
          field: field,
          column: column,
          columnItem: column
        }"
      >
      </ng-container>
      <ng-template #tplColumnNoFormatTemplate>
        {{ dataItem && dataItem[column['Name']] }}
      </ng-template>
    </span>
  </div>
</ng-template>
<!-- #endregion tpl format with custom tpl column -->
