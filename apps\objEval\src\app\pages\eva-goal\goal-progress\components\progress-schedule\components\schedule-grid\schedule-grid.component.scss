.schedule-grid {
  display: block;
  height: 100%;
  width: 100%;

  vnr-grid-new {
    margin-top: 1rem;
  }

  .report-name {
    .title {
      font-weight: 500;
      margin-bottom: 4px;
    }
    .subtitle {
      color: rgba(0, 0, 0, 0.45);
      font-size: 12px;
      i {
        margin-right: 4px;
      }
    }
  }

  .frequency {
    .time {
      margin-bottom: 4px;
      i {
        margin-right: 4px;
        color: rgba(0, 0, 0, 0.45);
      }
    }
    .detail {
      color: rgba(0, 0, 0, 0.45);
      font-size: 12px;
    }
  }

  .next-report {
    .date {
      margin-bottom: 4px;
    }
    .reminder {
      color: rgba(0, 0, 0, 0.45);
      font-size: 12px;
    }
  }

  .assignee {
    display: flex;
    align-items: center;
    gap: 8px;

    nz-avatar {
      width: 24px;
      height: 24px;
    }
  }

  .status {
    .last-update {
      margin-top: 4px;
      color: rgba(0, 0, 0, 0.45);
      font-size: 12px;
    }
  }
}

:host ::ng-deep {
  .report-name {
    .title {
      font-weight: 500;
      margin-bottom: 4px;
    }
    .subtitle {
      color: rgba(0, 0, 0, 0.65);
      i {
        margin-right: 4px;
      }
    }
  }

  .frequency {
    .type, .time {
      i {
        margin-right: 4px;
        color: rgba(0, 0, 0, 0.45);
      }
    }
    .time {
      margin-top: 4px;
      color: rgba(0, 0, 0, 0.65);
    }
  }

  .next-report {
    .date, .reminder {
      i {
        margin-right: 4px;
        color: rgba(0, 0, 0, 0.45);
      }
    }
    .reminder {
      margin-top: 4px;
      color: rgba(0, 0, 0, 0.65);
    }
  }

  .assignee {
    i {
      margin-right: 4px;
      color: rgba(0, 0, 0, 0.45);
    }
  }

  .status {
    .last-update {
      margin-top: 4px;
      color: rgba(0, 0, 0, 0.65);
      font-size: 12px;
    }
  }

  .toolbar {
    background-color: #fff !important;
  }

  .ant-segmented {
    height: fit-content;
    margin: auto !important;
    border-radius: 6px !important;
    padding: 6px !important;
  }
  .ant-segmented-group {
    align-items: center !important;
  }
  label.ant-segmented-item {
    margin-bottom: 0 !important;
  }

  .btn-add-schedule {
    border-radius: 6px !important;
  }
} 