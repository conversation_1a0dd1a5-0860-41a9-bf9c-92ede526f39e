import { AbstractTreeListNewChangeColumnBuilder } from '../../../models/abstract-treelist-new-change-column-builder.model';
import { VnrTreelistNewBuilder } from '../../vnr-treelist-new/models/vnr-treelist-new-builder.model';
import { VnRTreelistNewChangeColumnOptionBuilder } from './vnr-treelist-new-change-column-option-builder.model';

export class VnRTreelistNewChangeColumnBuilder extends AbstractTreeListNewChangeColumnBuilder<
  VnRTreelistNewChangeColumnBuilder,
  VnRTreelistNewChangeColumnOptionBuilder
> {
  constructor(builder?: VnRTreelistNewChangeColumnBuilder) {
    super();
    if (!builder === false) {
      builder.data = {
        columnMode: builder?.data?.columnMode,
        gridControlName: builder?.data?.gridControlName ?? builder?.gridName,
        keyChangeCol: builder?.data?.keyChangeCol,
        userInfoID: builder?.data?.userInfoID ?? '',
      };
    }
    Object.assign(this, builder);
    this.options = new VnRTreelistNewChangeColumnOptionBuilder(builder?.options);
  }
  builderFromGrid?(builderGrid?: VnrTreelistNewBuilder) {
    let builder: VnRTreelistNewChangeColumnBuilder = {
      gridPlacement: builderGrid.optionChangeColumn?.gridPlacement,
      data: builderGrid.optionChangeColumn?.data,
      options: {
        apiGetColumn: {
          apiUrl: builderGrid.options?.configApiSupport?.apiGetColumn?.url,
          method: builderGrid.options?.configApiSupport?.apiGetColumn?.method,
        },
        apiRestoreChangeColumn: {
          apiUrl: builderGrid.optionChangeColumn?.apiRestoreChangeColumn?.url,
          method: builderGrid.optionChangeColumn?.apiRestoreChangeColumn?.method,
        },
        apiSaveChangeColumn: {
          apiUrl: builderGrid.optionChangeColumn?.apiSaveChangeColumn?.url,
          method: builderGrid.optionChangeColumn?.apiSaveChangeColumn?.method,
        },
        apiSaveTranslate: {
          apiUrl: builderGrid.optionChangeColumn?.apiSaveTranslate?.url,
          method: builderGrid.optionChangeColumn?.apiSaveTranslate?.method,
        },
        assemblyName: builderGrid.optionChangeColumn?.assemblyName,

        isDisabledModeViewRecommended:
          builderGrid.optionChangeColumn?.isDisabledModeViewRecommended,
        isDisabledModeViewSimple: builderGrid.optionChangeColumn?.isDisabledModeViewSimple,
        isRestoreApi: builderGrid.optionChangeColumn?.isRestoreApi,
        modelName: builderGrid.optionChangeColumn?.modelName,
        tableDB: builderGrid.optionChangeColumn?.tableDB,
        position: {
          bottom: builderGrid.optionChangeColumn?.position?.bottom,
          top: builderGrid.optionChangeColumn?.position?.top,
          left: builderGrid.optionChangeColumn?.position?.left,
          right: builderGrid.optionChangeColumn?.position?.right,
        },
      },
    };
    Object.assign(this, builder);
    this.options = new VnRTreelistNewChangeColumnOptionBuilder(builder?.options);
    return builder;
  }
  builder?(builder?: VnRTreelistNewChangeColumnBuilder) {
    Object.assign(this, builder);
    this.options = new VnRTreelistNewChangeColumnOptionBuilder(builder?.options);
  }
}
