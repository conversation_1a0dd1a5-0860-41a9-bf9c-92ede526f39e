{"default": {"moduleName": "__dashboardModule", "moduleTitle": "Trang chủ", "descriptions": "Default descriptions", "moduleIcon": "fe fe-home", "url": "/dashboard", "isShortcut": true}, "example": {"moduleName": "__exampleModule", "moduleTitle": "Example", "descriptions": "Example descriptions", "moduleIcon": "fa-solid fa-code-pull-request-draft", "menuItems": [{"title": "Example Dashboard", "key": "__exampleDashboard", "icon": "fe fe-home", "url": "/example", "isShortcut": true}, {"title": "Example Category", "key": "__exampleCategory", "icon": "fe fe-clipboard", "url": "/example/category", "isShortcut": true}, {"title": "Example Sub Category 1", "key": "__exampleSubCategory1", "icon": "fe fe-clipboard", "children": [{"title": "Example Sub Category 2", "key": "__exampleSubCategory2", "icon": "fe fe-clipboard", "url": "/example/category/category-sub", "isShortcut": true}]}, {"title": "Example Grid", "key": "__exampleGrid", "icon": "fe fe-clipboard", "url": "/example/grid"}, {"title": "Example Grid Edit Inline", "key": "__exampleGrid", "icon": "fe fe-clipboard", "url": "/example/grid/edit-inline"}, {"title": "Example <PERSON><PERSON> <PERSON>", "key": "__exampleGrid", "icon": "fe fe-clipboard", "url": "/example/grid/edit-incell"}]}, "objEval": {"moduleName": "__objEvalModule", "moduleTitle": "<PERSON><PERSON><PERSON> ti<PERSON> & đ<PERSON><PERSON> giá", "descriptions": "<PERSON><PERSON><PERSON>", "moduleIcon": "fa-solid fa-star", "menuItems": [{"title": "Dashboard", "key": "__objEvalDashboard", "icon": "fa-regular fa-house-chimney", "url": "/objEval", "isShortcut": true}, {"title": "<PERSON><PERSON><PERSON> ti<PERSON>", "key": "__eva_goal", "icon": "fa-regular fa-bullseye-arrow", "resource": "New_Hre_EmployeeFile_View_NewPortal_V2", "children": [{"title": "<PERSON><PERSON><PERSON> ti<PERSON>u", "key": "__eva_goal_period", "url": "/objEval/eva-goal", "resource": "New_Hre_EmployeeFile_View_NewPortal_V2", "isShortcut": true, "isHiddenIcon": true}, {"title": "<PERSON><PERSON><PERSON><PERSON> độ", "key": "__eva_goal_progress", "url": "/objEval/eva-goal/progress", "resource": "New_Hre_EmployeeFile_View_NewPortal_V2", "isShortcut": true, "isHiddenIcon": true}, {"title": "<PERSON><PERSON><PERSON> qu<PERSON>", "key": "__eva_goal_summary", "url": "/objEval/eva-goal/result", "resource": "New_Hre_EmployeeFile_View_NewPortal_V2", "isShortcut": true, "isHiddenIcon": true}]}, {"title": "Đánh giá", "key": "__eva_appraisals", "icon": "fa-regular fa-ballot-check", "children": [{"title": "<PERSON><PERSON><PERSON> đ<PERSON> giá", "key": "__eva_appraisals_period", "url": "/objEval/eva-appraisals/performance-appraisals/list", "isShortcut": true, "isHiddenIcon": true}, {"title": "Đánh giá", "key": "__eva_appraisals_result", "url": "/objEval/eva-appraisals/appraisals/list", "isShortcut": true, "isHiddenIcon": true}, {"title": "<PERSON><PERSON> xuất", "key": "__eva_appraisals_proposal", "url": "/objEval/eva-appraisals/proposal/list", "isShortcut": true, "isHiddenIcon": true}]}, {"title": "Báo cáo", "key": "__eva_report", "icon": "fa-regular fa-file-chart-column", "url": "/objEval/report", "isShortcut": true}, {"title": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "key": "__eva_setting", "icon": "fa-solid fa-gear", "children": [{"title": "Mẫu mục tiêu", "key": "__eva_setting_goal_template", "url": "/objEval/setting/goal-template", "isShortcut": true, "isHiddenIcon": true}, {"title": "<PERSON>", "key": "__eva_setting_goal_cycle", "url": "/objEval/setting/goal-cycle", "isShortcut": true, "isHiddenIcon": true}, {"title": "<PERSON>ho tiêu chí", "key": "__eva_setting_criteria", "url": "/objEval/setting/criteria", "isShortcut": true, "isHiddenIcon": true}, {"title": "<PERSON><PERSON> n<PERSON>ng lực", "key": "__eva_setting_competency", "url": "/objEval/setting/competency", "isShortcut": true, "isHiddenIcon": true}, {"title": "objEval.EvaTemplate.template", "key": "__eva_setting_template", "url": "/objEval/setting/eva-template/list", "isShortcut": true, "isHiddenIcon": true}, {"title": "<PERSON><PERSON>", "key": "__eva_setting_category", "url": "/objEval/setting/category", "isShortcut": true, "isHiddenIcon": true}, {"title": "<PERSON><PERSON><PERSON> chỉnh", "key": "__eva_setting_customize", "url": "/objEval/setting/customize", "isShortcut": true, "isHiddenIcon": true}, {"title": "<PERSON><PERSON><PERSON>", "key": "__eva_setting_connect", "url": "/objEval/setting/connect", "isShortcut": true, "isHiddenIcon": true}]}]}, "humanResources": {"moduleName": "__humanResourcesModule", "moduleTitle": "<PERSON><PERSON><PERSON> sự", "descriptions": "Human Resources descriptions", "moduleIcon": "fa-solid fa-user-group", "menuItems": [{"title": "Trang chủ", "key": "__humanResourcesDashboard", "icon": "fa-regular fa-house-chimney", "url": "/humanResources", "isShortcut": true}]}}