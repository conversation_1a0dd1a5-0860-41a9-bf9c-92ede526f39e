import {
  CommonModule,
  DatePipe,
  NgIf,
  Ng<PERSON><PERSON>,
  NgS<PERSON><PERSON><PERSON>,
  NgSwitchDefault,
} from '@angular/common';
import {
  AfterViewInit,
  Component,
  EventEmitter,
  inject,
  Input,
  OnChanges,
  Output,
  SimpleChanges,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import {
  FormsModule,
  ReactiveFormsModule,
  UntypedFormBuilder,
  UntypedFormGroup,
} from '@angular/forms';
import { vnrUtilities } from '@hrm-frontend-workspace/common';
import { CommonService } from '@hrm-frontend-workspace/core';
import {
  SharedEditBuilderChangePipe,
  SharedEditCustomByTypeControlsPipe,
  SharedEditCustomInitBuilderControlsPipe,
  SharedEditGridColumnsValidatorsPipe,
} from '@hrm-frontend-workspace/domain';
import { VnrFieldControls } from '@hrm-frontend-workspace/models';
import { VnrButtonModule } from '@hrm-frontend-workspace/ui';
import {
  VnrColumnsValidatorsTypes,
  VnrComboBoxComponent,
  VnrDatePickerComponent,
  VnrGridColumnsValidatorsPipe,
  VnrGridInitBuilderControlsEditPipe,
  VnrGridNewEditIncellBuilder,
  VnrGridNewEditIncellComponent,
  VnrGridsModule,
  VnrInputNumberComponent,
  VnrMaskedTextboxComponent,
  VnrMultiSelectComponent,
  VnrSwitchComponent,
  VnrTextAreaComponent,
  VnrTextBoxComponent,
  VnrTreeviewV2Component,
} from '@hrm-frontend-workspace/vnr-module';
import { TranslateModule } from '@ngx-translate/core';
import { IntlModule } from '@progress/kendo-angular-intl';
import { aggregateBy, AggregateDescriptor, process } from '@progress/kendo-data-query';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { EvaNumberToLetterPipe } from '../../pipes/number-to-letter.pipe';
import { AddCompetencyEvaluationComponent } from './components/add-competency-evaluation/add-competency-evaluation.component';
import { AddCriteriaEvaluationComponent } from './components/add-criteria-evaluation/add-criteria-evaluation.component';
import { NzModalModule, NzModalService } from 'ng-zorro-antd/modal';
import { ADD_CRITERIA_EVALUATION_DATA } from './data/add-criteria-evaluation.data';
import { ADD_COMPETENCY_EVALUATION_DATA } from './data/add-competency-evaluation.data';

@Component({
  selector: 'app-evaluation-grid',
  templateUrl: './evaluation-grid.component.html',
  styleUrls: ['./evaluation-grid.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    IntlModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    EvaNumberToLetterPipe,
    NgSwitch,
    NgSwitchCase,
    NgIf,
    NgSwitchDefault,
    SharedEditBuilderChangePipe,
    SharedEditCustomByTypeControlsPipe,
    SharedEditCustomInitBuilderControlsPipe,
    SharedEditGridColumnsValidatorsPipe,
    VnrGridsModule,
    VnrGridNewEditIncellComponent,
    VnrGridInitBuilderControlsEditPipe,
    VnrGridColumnsValidatorsPipe,
    NzCheckboxModule,
    VnrButtonModule,
    VnrComboBoxComponent,
    VnrDatePickerComponent,
    VnrTreeviewV2Component,
    VnrMultiSelectComponent,
    VnrMaskedTextboxComponent,
    VnrInputNumberComponent,
    VnrSwitchComponent,
    VnrTextAreaComponent,
    VnrTextBoxComponent,
    AddCriteriaEvaluationComponent,
    AddCompetencyEvaluationComponent,
    NzModalModule,
  ],
  providers: [DatePipe],
})
export class EvaluationGridComponent implements OnChanges, AfterViewInit {
  @Input() vnrDataGridLocal: any[] = [];
  @Input() vnrColumGridLocal: any[] = [];
  @Input() index = 0;
  @Input() id = vnrUtilities.newGuid();
  @Input() isEdit = false; //được phép chỉnh sửa hay không
  @Input() isShowColumnCheck = false; //hiển thị cột check
  @Input() columnNameTotal = 'CriteriaName'; //tên cột tổng\
  @Input() isShowButton = false;
  @Input() gridHeight = 500;
  @Input() titleButtonAddRow = 'evaluation.btnAddCriteria'; //tên button thêm dòng
  @Input() titleButtonAddRowGroup = 'evaluation.btnAddCriteriaGroup'; //tên button thêm dòng nhóm
  @Input() isLockedIndex = true; //cột index có bị khóa không
  @Input() type: 'criteria' | 'competency' = 'competency'; //loại grid

  @Output() changeAddRow = new EventEmitter<any>();
  @Output() changeAddRowGroup = new EventEmitter<any>();

  private _formBuider: UntypedFormBuilder = inject(UntypedFormBuilder);
  protected formGroup: UntypedFormGroup = this._formBuider.group({});

  @ViewChild('vnrGridEditIncell') vnrGridEditIncell!: VnrGridNewEditIncellComponent;
  @ViewChild('templateFooter', { static: true }) public templateFooter!: TemplateRef<any>;
  @ViewChild('templateFooterGroup', { static: true }) public templateFooterGroup!: TemplateRef<any>;

  protected _Required = VnrColumnsValidatorsTypes.Required;
  protected _MinLength = VnrColumnsValidatorsTypes.MinLength;
  protected _MaxLength = VnrColumnsValidatorsTypes.MaxLength;
  protected _Min = VnrColumnsValidatorsTypes.Min;
  protected _Max = VnrColumnsValidatorsTypes.Max;
  protected _Email = VnrColumnsValidatorsTypes.Email;

  protected builderGrid!: VnrGridNewEditIncellBuilder;

  protected gridName = 'evaluation-grid';

  protected columnGridLocal: any[] = [];
  protected dataItemInput: any;
  protected listCustomFooter: any;
  protected listCustomFooterGroup: any;
  protected aggregates: AggregateDescriptor[] = [];
  protected vnrBuilderConfigByColumn: any;
  public dataLocal: any[] = [];
  constructor(private _commonService: CommonService, private modalService: NzModalService) {
    this.initGrid();
  }
  ngAfterViewInit(): void {
    this.initGrid();
  }

  ngOnChanges(changes: SimpleChanges): void {
    const { vnrColumGridLocal, vnrDataGridLocal } = changes;
    if (vnrDataGridLocal) {
      this.dataLocal = vnrDataGridLocal.currentValue;
    }
    if (vnrColumGridLocal?.currentValue && this.vnrColumGridLocal.length > 0) {
      this.columnGridLocal = this.vnrColumGridLocal;
      this.checkEdit(this.isEdit);
    }
    setTimeout(() => {
      this.handleCustomColumnTemplate();
      this.aggregatesCalculate();
      this.initGrid();
      this.readDataAggregate();
      // this.vnrGridEditIncell.vnrReloadGrid();
    });
  }

  private initGrid(): void {
    this.gridName = `evaluation-grid_${this.index}`;
    this.builderGrid = new VnrGridNewEditIncellBuilder({
      options: {
        configIndexColumn: {
          isShow: true,
          isLocked: this.isLockedIndex,
          width: 50,
        },
        configShowHide: {
          isShowEdit: false,
          isShowDelete: false,
          isShowColumnCheck: this.isShowColumnCheck,
          isShowColumnGroupCheck: false,
          isShowRefresh: false,
          isShowButtonMenu: false,
        },
        configCommandColumn: {
          isEnabledMenuAction: false,
        },
        configGroupable: {
          isEnabled: false,
        },
        configHeightGrid: {
          isAllowCalcRowHeight: false,
          isHeightByRow: false,
          rowHeight: 50,
          gridHeight: this.dataLocal?.length > 0 ? this.gridHeight : 500,
        },
        configEdit: {
          builderConfigByColumn: {},
          isAllowEditOtherCell: true,
          isAllowEditTemplateCellNew: true,
          isAutoSave: false,
          isShowBtnSave: false,
        },
      },
    });
  }

  private handleCustomColumnTemplate(): void {
    const templateCustomFooter: any = {};
    const templateCustomFooterGroup: any = {};
    this.columnGridLocal.forEach((data) => {
      templateCustomFooter[data.Name] = this.templateFooter;
      templateCustomFooterGroup[data.Name] = this.templateFooterGroup;
      if (data && data.MultiColumn && data.MultiColumn.length > 0) {
        data.MultiColumn.forEach((item: any) => {
          templateCustomFooter[item.Name] = this.templateFooter;
          templateCustomFooterGroup[item.Name] = this.templateFooterGroup;
        });
      }
    });
    this.listCustomFooter = templateCustomFooter;
    this.listCustomFooterGroup = templateCustomFooterGroup;
  }
  private aggregatesCalculate() {
    //Calculate
    let listCalculateColumn = this.columnGridLocal?.filter((i) => i.Calculate);
    let result = [];
    this.columnGridLocal?.forEach((i: any) => {
      if (i && i.MultiColumn && i.MultiColumn.length > 0) {
        result = result.concat(i.MultiColumn.filter((ele: any) => ele.Calculate));
      }
    });
    if ((listCalculateColumn && listCalculateColumn.length > 0) || (result && result.length > 0)) {
      listCalculateColumn = listCalculateColumn.concat(result);
      const aggregates: AggregateDescriptor[] = [];
      listCalculateColumn.map((item) => {
        aggregates.push({ field: item.Name, aggregate: item.Calculate });
        return item;
      });
      this.aggregates = aggregates;
    }
  }
  private readDataAggregate() {
    if (this.vnrGridEditIncell) {
      const data = this.dataLocal;
      this.vnrGridEditIncell.vnrAggregateResult = aggregateBy(data, this.aggregates);
      this.vnrGridEditIncell.gridView = process(
        data,
        this.vnrGridEditIncell.builder.options.queryOption,
      );
    }
  }

  onModelChange($event) {}

  preventKeyEnter($event) {
    if ($event['key'] == 'Enter') $event.preventDefault();
  }

  isEmailValid(email: string | null): boolean {
    if (!email) return false;
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email);
  }

  onSelectDataItemMultiselect($event) {
    const { event, dataItem, column, index, isNew, group } = $event;
    if (column) {
      const typeControl = column?.TypeControl?.includes('CustomControl|')
        ? column.TypeControl.split('|')[1]
        : column.TypeControl || 'TextBox';

      const controls = [
        VnrFieldControls.SelectEmp,
        VnrFieldControls.TreeViewV2,
        VnrFieldControls.MultiSelect,
        VnrFieldControls.Org,
      ];
      const eventArray =
        event &&
        event.length > 0 &&
        event?.map((item) => {
          return {
            [column.serverSide?.valueField]: item[column.serverSide?.valueField],
            [column.serverSide?.textField]: item[column.serverSide?.textField],
          };
        });
      // dataItem.ValueDefaultList =
      //   dataItem.builderValueDefault && eventArray && eventArray.length > 0 ? eventArray : [];
      let textFieldStr = '';
      if (eventArray && eventArray.length > 0) {
        textFieldStr = eventArray
          ?.map((item) => {
            return item[column.serverSide?.textField];
          })
          ?.join(',');
      }
      if (controls.includes(typeControl)) {
        dataItem[column.Name] = textFieldStr ? textFieldStr : '';
        dataItem[column.ReferenceDataSource] =
          eventArray && eventArray.length > 0 ? eventArray : [];
      }
    }
  }

  onSelectDataItemTreeViewV2orOrg($event) {
    const { event, dataItem, column, index, isNew, group } = $event;
    if (column) {
      const typeControl = column?.TypeControl?.includes('CustomControl|')
        ? column.TypeControl.split('|')[1]
        : column.TypeControl || 'TextBox';

      const controls = [
        VnrFieldControls.TreeViewV2,
        VnrFieldControls.MultiSelect,
        VnrFieldControls.Org,
      ];
      const eventArray =
        event &&
        event.length > 0 &&
        event?.map((item) => {
          return {
            [column.serverSide?.valueField]: item[column.serverSide?.valueField],
            [column.serverSide?.textField]: item[column.serverSide?.textField],
          };
        });
      // dataItem.ValueDefaultList =
      //   dataItem.builderValueDefault && eventArray && eventArray.length > 0 ? eventArray : [];
      let OrgStr = '';
      if (eventArray && eventArray.length > 0) {
        OrgStr = eventArray
          ?.map((item) => {
            return item[column.serverSide?.textField];
          })
          ?.join(',');
      }
      if (controls.includes(typeControl)) {
        if (column?.serverSide?.isMulti) {
          dataItem[column?.ReferenceDataSource] =
            eventArray && eventArray.length > 0 ? eventArray : [];
        }
        dataItem[column.Name] = OrgStr ? OrgStr : '';
      }
    }
  }

  onSelectDataItem($event) {
    const { event, dataItem, column, index, isNew, group } = $event;
    if (column) {
      const typeControl = column?.TypeControl?.includes('CustomControl|')
        ? column.TypeControl.split('|')[1]
        : column.TypeControl || 'TextBox';

      const controls = [
        VnrFieldControls.ComboBox,
        VnrFieldControls.SelectEmp,
        VnrFieldControls.TreeViewV2,
        VnrFieldControls.MultiSelect,
      ];

      if (controls.includes(typeControl)) {
        dataItem[column.Name] = event[column.serverSide?.textField];
      }
    }
    // if (column && column.Name === 'FullField') {
    //   dataItem[column.Name] = event[column.serverSide?.textField];
    // }
  }
  onOutSideClick({ dataItem, column }) {
    const findSelectedID = this.vnrGridEditIncell.getDataSource.find((x) => {
      return x.ID === dataItem['ID'];
    });

    if (findSelectedID && dataItem[column.field] !== findSelectedID[column.field]) {
      dataItem['IsEditing'] = true;
    }
  }
  checkEdit(isEdit: boolean) {
    this.columnGridLocal = this.columnGridLocal.map((x) => {
      // x.Disable = !isEdit; //không cho phép chỉnh sửa
      // x.TypeControl = 'CustomControl|TextBox';
      // x.Validators = {
      //   Required: true,
      //   MinLength: 5,
      //   MaxLength: 50,
      //   Email: true,
      // };
      // // if (!x.Hidden) {
      // //   x.Disable = isEdit;
      // // }
      // if (x.Type === 'group') {
      //   x.MultiColumn.map((column) => {
      //     column.Disable = !isEdit; //không cho phép chỉnh sửa
      //     column.TypeControl = 'CustomControl|TextBox';
      //     column.Validators = {
      //       Required: true,
      //       MinLength: 5,
      //       MaxLength: 50,
      //       Email: true,
      //     };
      //     return column;
      //   });
      // }
      return x;
    });
  }
  addRow() {
    if (!this.isEdit) {
      this._commonService.message({
        message: 'Thiết lập mẫu đánh giá không cho phép thêm dữ liệu',
        type: 'warning',
      });
      return;
    }
    // this.openAddDialog(this.type, 'addRow', 'NewRow');

    // this.dataLocal.push({
    //   ID: vnrUtilities.newGuid(),
    //   Criteria: 'Hiệu quả công việc Tháng 1',
    //   CriteriaName: 'Chất lượng công việc Tháng 1',
    //   Weight: 10,
    // });
  }
  addRowGroup(data: any) {
    const { group, column, value } = data;
    if (!this.isEdit) {
      this._commonService.message({
        message: 'Thiết lập mẫu đánh giá không cho phép thêm dữ liệu',
        type: 'warning',
      });
      return;
    }
    // this.openAddDialog(this.type, 'addRowGroup', value);
    // this.dataLocal.push({
    //   ID: vnrUtilities.newGuid(),
    //   Criteria: value,
    //   CriteriaName: `Chất lượng công việc cho ${value}`,
    //   Weight: 10,
    // });
    // const newRow = {
    //   ID: vnrUtilities.newGuid(),
    //   Criteria: value,
    //   CriteriaName: value,
    //   Weight: 10,
    // };
    // group.items.push(newRow);
  }

  openAddDialog(_data: any, type: 'criteria' | 'competency', typeAdd: 'addRow' | 'addRowGroup') {
    if (!this.isEdit) {
      this._commonService.message({
        message: 'Thiết lập mẫu đánh giá không cho phép thêm dữ liệu',
        type: 'warning',
      });
      return;
    }
    let component;
    let title = typeAdd === 'addRow' ? 'Thêm dữ liệu' : 'Thêm nhóm dữ liệu';
    switch (type) {
      case 'criteria':
        component = AddCriteriaEvaluationComponent;
        title = typeAdd === 'addRow' ? 'Thêm tiêu chí đánh giá' : 'Thêm nhóm tiêu chí đánh giá';
        break;
      case 'competency':
        component = AddCompetencyEvaluationComponent;
        title = typeAdd === 'addRow' ? 'Thêm năng lực đánh giá' : 'Thêm năng lực đánh giá';
        break;
    }
    const modal = this.modalService.create({
      nzTitle: title,
      nzMaskClosable: false,
      nzClosable: false,
      nzContent: component,
      nzFooter: null,
      nzBodyStyle: { padding: '10px' },
    });
    modal.afterClose.subscribe((result: any) => {
      if (result && !result.status) {
        const data =
          type === 'criteria' ? ADD_CRITERIA_EVALUATION_DATA : ADD_COMPETENCY_EVALUATION_DATA;
        const dataInput = data.filter((item) => {
          if (type === 'criteria') {
            return result.data.CriteriaID.includes(item.ID);
          } else {
            return result.data.CompetencyID.includes(item.ID);
          }
        });

        // Thêm thuộc tính Criteria cho mỗi item
        dataInput.forEach((item) => {
          item.Criteria = _data && _data.value ? _data.value : 'NewRow';
        });

        // Xử lý thêm dữ liệu vào grid
        if (_data && typeAdd === 'addRowGroup' && _data.group && _data.group.items) {
          _data.group.items = _data.group.items.concat(dataInput);
        } else {
          this.dataLocal = this.dataLocal.concat(dataInput);
        }

        this.handleCustomColumnTemplate();
        this.aggregatesCalculate();
        this.readDataAggregate();

        if (typeAdd === 'addRowGroup') {
          this.changeAddRowGroup.emit({ data: this.dataLocal });
        } else {
          this.changeAddRow.emit({ data: this.dataLocal });
        }
      }
    });
  }
}
