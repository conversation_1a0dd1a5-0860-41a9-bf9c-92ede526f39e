export const listEmpData = [
  {
    ID: '1',
    CodeEmp: '234646',
    ProfileName: 'Hoàng <PERSON>ù<PERSON>',
    JoinProfileNameCode: '00456 - Hoàng Thù<PERSON> Du<PERSON>',
    OrgStructureName: '<PERSON> ty <PERSON>n <PERSON> & <PERSON><PERSON><PERSON> In Đô Trần - Lào',
    PositionName: 'Chức Vụ IT1',
    DateHire: '2025-06-26T17:00:00Z',
    FormatProfileCodeLogin: 'Hoàng Thùy Dung - 00456',
    Cellphone: '*********',
    DateUpdate: '2025-06-28T04:30:12.19Z',
    TotalRow: 188,
    Seniority: '10 Năm',
    Email: '<EMAIL>',
    JoinDate: '01/07/2021',
    CompanyName: 'Công ty Cổ <PERSON>ần <PERSON> & <PERSON><PERSON><PERSON> In Đô Trần - Lào',
  },
  {
    ID: '2',
    CodeEmp: '234641',
    ProfileName: '<PERSON><PERSON><PERSON><PERSON>',
    JoinProfileNameCode: '234641 - <PERSON><PERSON><PERSON><PERSON>',
    OrgStructureName: '<PERSON>ánh <PERSON>ông ty <PERSON>ổ Phần Giao Nhận & Vận Chuyển In Đô Trần - Lào',
    PositionName: 'Phó Bộ Phận',
    DateHire: '2025-06-25T17:00:00Z',
    FormatProfileCodeLogin: 'Nguyễn Ngọc Hân - 234641',
    DateUpdate: '2025-06-26T04:10:39.66Z',
    CompanyName: 'Công ty Cổ Phần Giao Nhận & Vận Chuyển In Đô Trần - Lào',
    TotalRow: 188,
    Seniority: '10 Năm',
    Email: '<EMAIL>',
    JoinDate: '01/07/2021',
  },
  {
    ID: '3',
    CodeEmp: '0908068',
    ProfileName: 'Đặng Nhật Minh',
    JoinProfileNameCode: '0908068 - Đặng Nhật Minh',
    OrgStructureName: 'Nhóm Hành chính nhân sự',
    EmployeeTypeName: 'Nhân viên',
    PositionName: 'Chuyên trách',
    JobTitleName: 'Hệ Chuyên trách',
    AbilityTitleVNI: 'D2',
    DateHire: '2009-08-05T17:00:00Z',
    LaborType: 'E_OTHER',
    ImagePath: 'https://pedn.vnresource.net:1116/Resources/ProfileImage/no_avatar.jpg',
    FormatProfileCodeLogin: 'Đặng Nhật Minh - 0908068',
    Cellphone: '0905523228',
    DateUpdate: '2025-05-14T04:36:17.61Z',
    TotalRow: 188,
    CompanyName: 'Công ty Cổ Phần Giao Nhận & Vận Chuyển In Đô Trần - Lào',
    Seniority: '10 Năm',
    Email: '<EMAIL>',
    JoinDate: '01/07/2021',
  },
  {
    ID: '3',
    CodeEmp: '19070014',
    ProfileName: 'Lê Phương Linh',
    JoinProfileNameCode: '19070014 - Lê Phương Linh',
    OrgStructureName: 'Chi Nhánh Công ty Cổ Phần Giao Nhận & Vận Chuyển In Đô Trần - Lào',
    EmployeeTypeName: 'Công nhân',
    PositionName: 'Công Nhân',
    JobTitleName: 'Hệ Chế tạo',
    AbilityTitleVNI: 'C1',
    DateHire: '2019-06-30T17:00:00Z',
    LaborType: 'E_INDIRECT',
    ImagePath: 'https://pedn.vnresource.net:1116/Resources/ProfileImage/no_avatar.jpg',
    FormatProfileCodeLogin: 'Lê Phương Linh - 19070014',
    UserLogin: 'TVH00422',
    Cellphone: '0767416517',
    DateUpdate: '2025-05-14T04:34:22.61Z',
    TotalRow: 188,
    CompanyName: 'Công ty Cổ Phần Giao Nhận & Vận Chuyển In Đô Trần - Lào',
    Seniority: '10 Năm',
    Email: '<EMAIL>',
    JoinDate: '01/07/2021',
  },
  {
    ID: '5',
    CodeEmp: '21110180',
    ProfileName: 'Đặng Bích Thảo',
    JoinProfileNameCode: '21110180 - Đặng Bích Thảo',
    OrgStructureName: 'Chi Nhánh Công ty Cổ Phần Giao Nhận & Vận Chuyển In Đô Trần - Lào',
    EmployeeTypeName: 'Công nhân',
    PositionName: 'Công Nhân',
    JobTitleName: 'Hệ Chế tạo',
    AbilityTitleVNI: 'C1',
    DateHire: '2021-11-16T17:00:00Z',
    LaborType: 'E_INDIRECT',
    ImagePath: 'https://pedn.vnresource.net:1116/Resources/ProfileImage/no_avatar.jpg',
    FormatProfileCodeLogin: 'Đặng Bích Thảo - 21110180',
    UserLogin: 'TVH00903',
    Cellphone: '0947553911',
    DateUpdate: '2025-05-14T04:30:02.97Z',
    TotalRow: 188,
    CompanyName: 'Công ty Cổ Phần Giao Nhận & Vận Chuyển In Đô Trần - Lào',
    Seniority: '10 Năm',
    Email: '<EMAIL>',
    JoinDate: '01/07/2021',
  },
  {
    ID: '6',
    CodeEmp: '19060080',
    ProfileName: 'Bùi Minh Khang',
    JoinProfileNameCode: '19060080 - Hoàng Minh Khang',
    OrgStructureName: 'Chi Nhánh Công ty Cổ Phần Giao Nhận & Vận Chuyển In Đô Trần - Lào',
    EmployeeTypeName: 'Công nhân',
    PositionName: 'Công Nhân',
    JobTitleName: 'Hệ Chế tạo',
    AbilityTitleVNI: 'C1',
    DateHire: '2019-06-16T17:00:00Z',
    LaborType: 'E_INDIRECT',
    ImagePath: 'https://pedn.vnresource.net:1116/Resources/ProfileImage/no_avatar.jpg',
    FormatProfileCodeLogin: 'Hoàng Minh Khang - 19060080',
    UserLogin: 'TVH00416',
    Cellphone: '0931979808',
    DateUpdate: '2025-05-14T04:28:16.063Z',
    TotalRow: 188,
    CompanyName: 'Công ty Cổ Phần Giao Nhận & Vận Chuyển In Đô Trần - Lào',
  },
  {
    ID: '053fd550-eaf9-4b68-870d-dd22585d69ac',
    CodeEmp: '19060079',
    ProfileName: 'Phạm Tuấn Kiệt',
    JoinProfileNameCode: '19060079 - Phạm Tuấn Kiệt',
    OrgStructureName: 'Nhóm Logistic Đà Nẵng',
    EmployeeTypeName: 'Nhân viên',
    PositionName: 'Nhân Viên Cao Cấp',
    JobTitleName: 'Phó phòng',
    AbilityTitleVNI: 'D1',
    DateHire: '2019-06-16T17:00:00Z',
    LaborType: 'E_OTHER',
    ImagePath: 'https://pedn.vnresource.net:1116/Resources/ProfileImage/no_avatar.jpg',
    FormatProfileCodeLogin: 'Phạm Tuấn Kiệt - 19060079',
    UserLogin: 'TVH00429',
    Cellphone: '0934855550',
    DateUpdate: '2025-05-13T09:41:21.84Z',
    TotalRow: 188,
    CompanyName: 'Công ty Cổ Phần Giao Nhận & Vận Chuyển In Đô Trần - Lào',
    Seniority: '10 Năm',
    Email: '<EMAIL>',
    JoinDate: '01/07/2021',
  },
  {
    ID: 'b86452ee-07cf-4cf4-a8d6-0f1d37b3e87a',
    CodeEmp: '0405001',
    ProfileName: 'Hoàng Vũ Anh Quân',
    JoinProfileNameCode: '0405001 - Hoàng Vũ Anh Quân',
    OrgStructureName: 'Khoa Nghiệp vụ',
    EmployeeTypeName: 'Nhân viên',
    PositionName: 'Phó Khoa Trưởng',
    JobTitleName: 'Hệ QL',
    AbilityTitleVNI: 'E1',
    ContractTypeName: 'Vô thời hạn - nhân viên',
    DateHire: '2004-05-19T17:00:00Z',
    LaborType: 'E_OTHER',
    ImagePath: 'https://pedn.vnresource.net:1116/Resources/ProfileImage/no_avatar.jpg',
    FormatProfileCodeLogin: 'Hoàng Vũ Anh Quân - 0405001',
    UserLogin: 'long.test',
    Cellphone: '0394804878',
    DateUpdate: '2025-05-13T02:38:05.143Z',
    TotalRow: 188,
    CompanyName: 'Công ty Cổ Phần Giao Nhận & Vận Chuyển In Đô Trần - Lào',
    Seniority: '10 Năm',
    Email: '<EMAIL>',
    JoinDate: '01/07/2021',
  },
  {
    ID: '240b9945-9c3a-4e10-a961-900e337b37a2',
    CodeEmp: '234619',
    ProfileName: 'Cao Hải Hoàng',
    JoinProfileNameCode: '234619 - Cao Hải Hoàng',
    OrgStructureName: 'Bộ phận Quản lý Hành chính Tổng hợp',
    PositionName: 'Kỹ sư cao cấp',
    DateHire: '2025-01-31T17:00:00Z',
    ImagePath:
      'https://pedn.vnresource.net:1116/Resources/ProfileImage/Profile_9875be82-0198-43bd-a1f4-5ced848f2c20.png',
    FormatProfileCodeLogin: 'Cao Hải Hoàng - 234619',
    Cellphone: '09082232348',
    DateUpdate: '2025-05-05T08:59:59.473Z',
    TotalRow: 188,
    Email: '<EMAIL>',
    CompanyName: 'Công ty Cổ Phần Giao Nhận & Vận Chuyển In Đô Trần - Lào',
    Seniority: '10 Năm',
    JoinDate: '01/07/2021',
  },
  {
    ID: 'b9ac7666-4e3a-4a6a-915e-1e55f1690ce0',
    CodeEmp: '234633',
    ProfileName: 'Nguyễn Văn Võ',
    JoinProfileNameCode: '234633 - Nguyễn Văn Võ',
    OrgStructureName: 'Chi Nhánh Công ty Cổ Phần Giao Nhận & Vận Chuyển In Đô Trần - Lào',
    PositionName: 'Chuyên trách',
    DateHire: '2025-04-24T17:00:00Z',
    FormatProfileCodeLogin: 'Nguyễn Văn Võ - 234633',
    Cellphone: '0987565432',
    DateUpdate: '2025-05-05T02:27:56.997Z',
    TotalRow: 188,
    CompanyName: 'Công ty Cổ Phần Giao Nhận & Vận Chuyển In Đô Trần - Lào',
    Email: '<EMAIL>',
    Seniority: '10 Năm',
    JoinDate: '01/07/2021',
  },
  {
    ID: '7d275f22-675e-4185-a9ce-02269027005a',
    CodeEmp: '234623',
    ProfileName: 'Nguyễn Thị Kiều Loan',
    JoinProfileNameCode: '234623 - Nguyễn Thị Kiều Loan',
    OrgStructureName: 'Khoa An toàn sức khỏe & Môi trường',
    PositionName: 'Trưởng bộ phận Trang trí',
    DateHire: '2025-02-27T17:00:00Z',
    FormatProfileCodeLogin: 'Nguyễn Thị Kiều Loan - 234623',
    DateUpdate: '2025-04-28T02:33:53.91Z',
    TotalRow: 188,
    CompanyName: 'Công ty Cổ Phần Giao Nhận & Vận Chuyển In Đô Trần - Lào',
    Email: '<EMAIL>',
    JoinDate: '01/07/2021',
    Seniority: '10 Năm',
  },
  {
    ID: 'cbf5fec5-01d6-4f4c-949c-07c63960985e',
    CodeEmp: '234632',
    ProfileName: 'Lê Huyền Cao Nhi',
    JoinProfileNameCode: '234632 - Lê Huyền Cao Nhi',
    OrgStructureName: 'Nhóm Tài vụ',
    PositionName: 'Tổ Trưởng',
    DateHire: '2025-03-26T17:00:00Z',
    FormatProfileCodeLogin: 'Lê Huyền Cao Nhi - 234632',
    Cellphone: '0398337860',
    DateUpdate: '2025-04-28T01:40:16.647Z',
    TotalRow: 188,
    CompanyName: 'Công ty Cổ Phần Giao Nhận & Vận Chuyển In Đô Trần - Lào',
    Email: '<EMAIL>',
    JoinDate: '01/07/2021',
  },
  {
    ID: '81c0dfe7-b352-41a9-8782-74bf1a116bdc',
    CodeEmp: '234622',
    ProfileName: 'Lê Thị Kim Oanh',
    JoinProfileNameCode: '234622 - Lê Thị Kim Oanh',
    OrgStructureName: 'Nhóm cơ sở hạ tầng.',
    PositionName: 'Trưởng bộ phận Trang trí',
    DateHire: '2025-02-17T17:00:00Z',
    FormatProfileCodeLogin: 'Lê Thị Kim Oanh - 234622',
    DateUpdate: '2025-03-26T01:38:44.71Z',
    TotalRow: 188,
    CompanyName: 'Công ty Cổ Phần Giao Nhận & Vận Chuyển In Đô Trần - Lào',
    Email: '<EMAIL>',
    JoinDate: '01/07/2021',
    Seniority: '10 Năm',
  },
  {
    ID: 'f0eeaaad-a8bb-4e9e-9d4b-b20c2bd9a1c1',
    CodeEmp: '0512015',
    ProfileName: 'NGUYỄN THỊ HƯNG',
    JoinProfileNameCode: '0512015 - NGUYỄN THỊ HƯNG',
    OrgStructureName: 'NHÓM PHẨM QUẢN 5',
    EmployeeTypeName: 'Công nhân',
    PositionName: 'Phẩm Quản Thanh tra',
    JobTitleName: 'Hệ Chế tạo',
    AbilityTitleVNI: 'C1',
    DateHire: '2005-12-11T17:00:00Z',
    LaborType: 'E_DIRECT',
    ImagePath: 'https://pedn.vnresource.net:1116/Resources/ProfileImage/no_avatar.jpg',
    FormatProfileCodeLogin: 'NGUYỄN THỊ HƯNG - 0512015',
    UserLogin: 'ND2',
    Cellphone: '0389247402',
    DateUpdate: '2025-01-22T04:06:36.663Z',
    TotalRow: 188,
    CompanyName: 'Công ty Cổ Phần Giao Nhận & Vận Chuyển In Đô Trần - Lào',
    Email: '<EMAIL>',
    JoinDate: '01/07/2021',
    Seniority: '10 Năm',
  },
  {
    ID: 'ba1b0eb7-8bc6-4195-95c5-42c9485d1d93',
    CodeEmp: '0506028',
    ProfileName: 'NGUYỄN THỊ GẤM',
    JoinProfileNameCode: '0506028 - NGUYỄN THỊ GẤM',
    OrgStructureName: 'Nhóm Môi trường & thúc đẩy CSR',
    EmployeeTypeName: 'Công nhân',
    PositionName: 'Công Nhân',
    JobTitleName: 'Hệ Chế tạo',
    AbilityTitleVNI: 'C1',
    DateHire: '2005-06-05T17:00:00Z',
    LaborType: 'E_INDIRECT',
    ImagePath: 'https://pedn.vnresource.net:1116/Resources/ProfileImage/no_avatar.jpg',
    FormatProfileCodeLogin: 'NGUYỄN THỊ GẤM - 0506028',
    UserLogin: 'ND1',
    Cellphone: '0777468094',
    DateUpdate: '2025-01-22T04:04:29.21Z',
    TotalRow: 188,
    CompanyName: 'Công ty Cổ Phần Giao Nhận & Vận Chuyển In Đô Trần - Lào',
    Email: '<EMAIL>',
    JoinDate: '01/07/2021',
    Seniority: '10 Năm',
  },
  {
    ID: 'a737563e-b804-4db7-9dbf-ec2db42c32c4',
    CodeEmp: 'G2309052',
    ProfileName: 'THẢO HỒ THỊ',
    JoinProfileNameCode: 'G2309052 - THẢO HỒ THỊ',
    OrgStructureName: 'Bộ phận Quản lý Hành chính Tổng hợp',
    PositionName: 'Công Nhân',
    JobTitleName: 'Hệ Chế tạo',
    AbilityTitleVNI: 'C3',
    DateHire: '2023-09-26T17:00:00Z',
    LaborType: 'E_DIRECT',
    FormatProfileCodeLogin: 'THẢO HỒ THỊ - G2309052',
    Cellphone: '0866184034',
    DateUpdate: '2024-10-23T09:34:12.883Z',
    TotalRow: 188,
    CompanyName: 'Công ty Cổ Phần Giao Nhận & Vận Chuyển In Đô Trần - Lào',
    Email: '<EMAIL>',
    JoinDate: '01/07/2021',
    Seniority: '10 Năm',
  },
  {
    ID: '1c03ccaf-dcbc-4129-a90e-6039b559487a',
    CodeEmp: '234583',
    ProfileName: 'Nguyễn Văn Nam (Test đề xuất)',
    JoinProfileNameCode: '234583 - Nguyễn Văn Nam (Test đề xuất)',
    OrgStructureName: 'Bp Quản lý Hành chính',
    PositionName: 'Quản Lý Viên',
    DateHire: '2024-10-20T17:00:00Z',
    FormatProfileCodeLogin: 'Nguyễn Văn Nam (Test đề xuất) - 234583',
    Cellphone: '0329189267',
    DateUpdate: '2024-10-16T04:44:21.813Z',
    TotalRow: 188,
    CompanyName: 'Công ty Cổ Phần Giao Nhận & Vận Chuyển In Đô Trần - Lào',
    Email: '<EMAIL>',
    JoinDate: '01/07/2021',
    Seniority: '10 Năm',
  },
  {
    ID: 'bbcb8643-e115-4f64-a72a-8aece4a5337e',
    ProfileName: 'Test ứng tuyển Performance2 TestPM15 ',
    JoinProfileNameCode: ' - Test ứng tuyển Performance2 TestPM15 ',
    OrgStructureName: 'Bộ phận Quản lý Hành chính Tổng hợp',
    PositionName: 'Nhân Viên Trung cấp',
    JobTitleName: 'Hệ Chuyên trách',
    AbilityTitleVNI: 'C3',
    DateHire: '2024-09-29T17:00:00Z',
    ImagePath: 'https://pedn.vnresource.net:1116/Resources/ProfileImage/no_avatar.jpg',
    Cellphone: '0202052000',
    DateUpdate: '2024-10-04T06:08:42.027Z',
    TotalRow: 188,
    CompanyName: 'Công ty Cổ Phần Giao Nhận & Vận Chuyển In Đô Trần - Lào',
    Email: '<EMAIL>',
    JoinDate: '01/07/2021',
    Seniority: '10 Năm',
  },
  {
    ID: '4049184e-2ab9-44af-8a56-c2b76098176f',
    CodeEmp: '234580',
    ProfileName: 'Nguyễn Văn Long',
    JoinProfileNameCode: '234580 - Nguyễn Văn Long',
    OrgStructureName: 'Khoa Nghiệp vụ',
    PositionName: 'Khoa Trưởng',
    DateHire: '2024-10-23T17:00:00Z',
    FormatProfileCodeLogin: 'Nguyễn Văn Long - 234580',
    UserLogin: 'nhansu123',
    Cellphone: '7867546534',
    DateUpdate: '2024-10-02T16:50:35.793Z',
    TotalRow: 188,
    CompanyName: 'Công ty Cổ Phần Giao Nhận & Vận Chuyển In Đô Trần - Lào',
    Email: '<EMAIL>',
    JoinDate: '01/07/2021',
    Seniority: '10 Năm',
  },
  {
    ID: 'bce216cd-038b-4d09-8f4f-bbe63fd73be4',
    CodeEmp: '234546',
    OrgStructureName: 'Bộ phận Quản lý Hành chính Tổng hợp',
    PositionName: 'Quản Lý Viên',
    DateHire: '2024-08-10T17:00:00Z',
    DateUpdate: '2024-08-15T02:25:32.4Z',
    TotalRow: 188,
    CompanyName: 'Công ty Cổ Phần Giao Nhận & Vận Chuyển In Đô Trần - Lào',
    Company: 'Công ty Cổ Phần Giao Nhận & Vận Chuyển In Đô Trần - Lào',
    Email: '<EMAIL>',
    JoinDate: '01/07/2021',
    Seniority: '1 Năm',
  },
];
