export function memberFilter(): any[] {
  const listDepartment = Array.from(
    new Set(memberDataSource.filter((item) => item.Department).map((item) => item.Department)),
  ).map((department) => ({
    label: department,
    value: department,
  }));
  return [
    {
      numberOrder: 1,
      isFavorite: false,
      isUsing: false,
      field: 'Department',
      type: 'multipleCheckbox',
      value: '',
      title: 'Phòng ban',
      dataSource: {
        staticSource: listDepartment,
      },
    },
  ];
}

export const memberColumns = [
  {
    Name: 'UserName',
    HeaderName: 'Nhân viên',
    HeaderKey: 'Nhân viên',
    DisplayName: 'Nhân viên',
    Width: 150,
    Sortable: true,
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: 20,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: true,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    IsLock: false,
    Template: null,
    AggregateFunc: null,
  },
  {
    Name: 'Department',
    HeaderName: 'Phòng ban',
    HeaderKey: 'Phòng ban',
    DisplayName: 'Phòng ban',
    Width: 100,
    Sortable: true,
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: 20,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: true,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    IsLock: false,
    Template: null,
    AggregateFunc: null,
  },
  {
    Name: 'Position',
    HeaderName: 'Chức vụ',
    HeaderKey: 'Chức vụ',
    DisplayName: 'Chức vụ',
    Width: 100,
    Sortable: true,
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: 20,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: true,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    IsLock: false,
    Template: null,
    AggregateFunc: null,
  },
  {
    Name: 'Position',
    HeaderName: 'Vị trí công việc',
    HeaderKey: 'Vị trí công việc',
    DisplayName: 'Vị trí công việc',
    Width: 100,
    Sortable: true,
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: 20,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: true,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    IsLock: false,
    Template: null,
    AggregateFunc: null,
  },
  {
    Name: 'CompletionRate',
    HeaderName: 'Mức độ hoàn thành',
    HeaderKey: 'Mức độ hoàn thành',
    DisplayName: 'Mức độ hoàn thành',
    Width: 100,
    Sortable: true,
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: 20,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: true,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    IsLock: false,
    Template: null,
    AggregateFunc: null,
  },
  {
    Name: 'SuccessRate',
    HeaderName: 'Tỉ lệ đạt',
    HeaderKey: 'Tỉ lệ đạt',
    DisplayName: 'Tỉ lệ đạt',
    Width: 100,
    Sortable: true,
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: 20,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: true,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    IsLock: false,
    Template: null,
    AggregateFunc: null,
  },
  {
    Name: 'FailRate',
    HeaderName: 'Tỉ lệ không đạt',
    HeaderKey: 'Tỉ lệ không đạt',
    DisplayName: 'Tỉ lệ không đạt',
    Width: 100,
    Sortable: true,
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: 20,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: true,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    IsLock: false,
    Template: null,
    AggregateFunc: null,
  },
  {
    Name: 'TargetNumber',
    HeaderName: 'SL mục tiêu trong kỳ',
    HeaderKey: 'SL mục tiêu trong kỳ',
    DisplayName: 'SL mục tiêu trong kỳ',
    Width: 100,
    Sortable: true,
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: 20,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: true,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    IsLock: false,
    Template: null,
    AggregateFunc: null,
  },
  {
    Name: 'CurentTargetNumber',
    HeaderName: 'SL mục tiêu hiện hữu',
    HeaderKey: 'SL mục tiêu hiện hữu',
    DisplayName: 'SL mục tiêu hiện hữu',
    Width: 100,
    Sortable: true,
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: 20,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: true,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    IsLock: false,
    Template: null,
    AggregateFunc: null,
  },
  {
    Name: 'CanceledTargetNumber',
    HeaderName: 'SL mục tiêu đã hủy',
    HeaderKey: 'SL mục tiêu đã hủy',
    DisplayName: 'SL mục tiêu đã hủy',
    Width: 100,
    Sortable: true,
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: 20,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: true,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    IsLock: false,
    Template: null,
    AggregateFunc: null,
  },
  {
    Name: 'PausedTargetNumber',
    HeaderName: 'SL mục tiêu tạm dừng',
    HeaderKey: 'SL mục tiêu tạm dừng',
    DisplayName: 'SL mục tiêu tạm dừng',
    Width: 100,
    Sortable: true,
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: 20,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: true,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    IsLock: false,
    Template: null,
    AggregateFunc: null,
  },
  {
    Name: 'LastResult',
    HeaderName: 'Cập nhật lần cuối',
    HeaderKey: 'Cập nhật lần cuối',
    DisplayName: 'Cập nhật lần cuối',
    Width: 100,
    Sortable: true,
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: 20,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: true,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    IsLock: false,
    Template: null,
    AggregateFunc: null,
  },
  {
    Name: 'Resut',
    HeaderName: 'Kết quả',
    HeaderKey: 'Kết quả',
    DisplayName: 'Kết quả',
    Width: 100,
    Sortable: true,
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: 20,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: true,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    IsLock: false,
    Template: null,
    AggregateFunc: null,
  },
  {
    Name: 'Ranking',
    HeaderName: 'Xếp loại',
    HeaderKey: 'Xếp loại',
    DisplayName: 'Xếp loại',
    Width: 100,
    Sortable: true,
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: 20,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: true,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    IsLock: false,
    Template: null,
    AggregateFunc: null,
  },
];

export const memberDataSource = [
  {
    UserName: 'Phạm Thái Hòa',
    Department: 'Phòng kinh doanh',
    Position: 'Trưởng phòng kinh doanh',
    CompletionRate: 100,
    SuccessRate: '100% (5/5)',
    FailRate: '0% (0/5)',
    TargetNumber: 4,
    CurentTargetNumber: 3,
    CanceledTargetNumber: 1,
    PausedTargetNumber: 1,
    LastResult: '25/12/2025',
    Resut: 'Chưa đánh giá',
    Ranking: 'Chưa đánh giá',
  },
  {
    UserName: 'Nguyễn Trung Kiên',
    Department: 'Phòng kinh doanh',
    Position: 'Trưởng nhóm kinh doanh',
    CompletionRate: 95,
    SuccessRate: '60% (3/5)',
    FailRate: '40% (2/5)',
    TargetNumber: 7,
    CurentTargetNumber: 5,
    CanceledTargetNumber: 1,
    PausedTargetNumber: 1,
    LastResult: '25/12/2025',
    Resut: 'Chưa đánh giá',
    Ranking: 'Chưa đánh giá',
  },
  {
    UserName: 'Phạm Hồng Quân',
    Department: 'Phòng kinh doanh',
    Position: 'Nhân viên kinh doanh',
    CompletionRate: 100,
    SuccessRate: '100% (5/5)',
    FailRate: '0% (0/5)',
    TargetNumber: 7,
    CurentTargetNumber: 5,
    CanceledTargetNumber: 1,
    PausedTargetNumber: 1,
    LastResult: '25/12/2025',
    Resut: 'Chưa đánh giá',
    Ranking: 'Chưa đánh giá',
  },
  {
    UserName: 'Nguyễn Hải Yến',
    Department: 'Phòng kinh doanh',
    Position: 'Nhân viên kinh doanh',
    CompletionRate: 100,
    SuccessRate: '100% (5/5)',
    FailRate: '0% (0/5)',
    TargetNumber: 7,
    CurentTargetNumber: 5,
    CanceledTargetNumber: 1,
    PausedTargetNumber: 1,
    LastResult: '25/12/2025',
    Resut: 'Chưa đánh giá',
    Ranking: 'Chưa đánh giá',
  },
  {
    UserName: 'Bùi Hồng Nhung',
    Department: 'Phòng kinh doanh',
    Position: 'Nhân viên kinh doanh',
    CompletionRate: 80,
    SuccessRate: '60% (3/5)',
    FailRate: '40% (2/5)',
    TargetNumber: 7,
    CurentTargetNumber: 5,
    CanceledTargetNumber: 1,
    PausedTargetNumber: 1,
    LastResult: '25/12/2025',
    Resut: 'Chưa đánh giá',
    Ranking: 'Chưa đánh giá',
  },
  {
    UserName: 'Nguyễn Minh Anh',
    Department: 'Phòng kinh doanh',
    Position: 'Nhân viên kinh doanh',
    CompletionRate: 100,
    SuccessRate: '100% (5/5)',
    FailRate: '0% (0/5)',
    TargetNumber: 7,
    CurentTargetNumber: 5,
    CanceledTargetNumber: 1,
    PausedTargetNumber: 1,
    LastResult: '25/12/2025',
    Resut: 'Chưa đánh giá',
    Ranking: 'Chưa đánh giá',
  },
  {
    UserName: 'Lê Phương Linh',
    Department: 'Phòng kinh doanh',
    Position: 'Nhân viên kinh doanh',
    CompletionRate: 80,
    SuccessRate: '60% (3/5)',
    FailRate: '40% (2/5)',
    TargetNumber: 7,
    CurentTargetNumber: 5,
    CanceledTargetNumber: 1,
    PausedTargetNumber: 1,
    LastResult: '25/12/2025',
    Resut: 'Chưa đánh giá',
    Ranking: 'Chưa đánh giá',
  },
];

export const memberFormColumns = [
  {
    Name: 'GoalName',
    HeaderName: 'Tên mục tiêu',
    HeaderKey: 'Tên mục tiêu',
    DisplayName: 'Tên mục tiêu',
    Width: 100,
    Sortable: true,
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: 20,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: true,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    IsLock: false,
    Template: null,
    AggregateFunc: null,
  },
  {
    Name: 'Weight',
    HeaderName: 'Trọng số',
    HeaderKey: 'Trọng số',
    DisplayName: 'Trọng số',
    Width: 100,
    Sortable: true,
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: 20,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: true,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    IsLock: false,
    Template: null,
    AggregateFunc: null,
  },
  {
    Name: 'LastResult',
    HeaderName: 'Cập nhật lần cuối',
    HeaderKey: 'Cập nhật lần cuối',
    DisplayName: 'Cập nhật lần cuối',
    Width: 100,
    Sortable: true,
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: 20,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: true,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    IsLock: false,
    Template: null,
    AggregateFunc: null,
  },
  {
    Name: 'Attachment',
    HeaderName: 'Tệp đính kèm',
    HeaderKey: 'Tệp đính kèm',
    DisplayName: 'Tệp đính kèm',
    Width: 100,
    Sortable: true,
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: 20,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: true,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    IsLock: false,
    Template: null,
    AggregateFunc: null,
  },
  {
    Name: 'TotalTarget',
    HeaderName: 'Tổng mục tiêu',
    HeaderKey: 'Tổng mục tiêu',
    DisplayName: 'Tổng mục tiêu',
    Width: 100,
    Sortable: true,
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: 20,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: true,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    IsLock: false,
    Template: null,
    AggregateFunc: null,
  },
  {
    Name: 'TotalActual',
    HeaderName: 'Tổng thực đạt',
    HeaderKey: 'Tổng thực đạt',
    DisplayName: 'Tổng thực đạt',
    Width: 100,
    Sortable: true,
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: 20,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: true,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    IsLock: false,
    Template: null,
    AggregateFunc: null,
  },
  {
    Name: 'Variance',
    HeaderName: 'Chênh lệch',
    HeaderKey: 'Chênh lệch',
    DisplayName: 'Chênh lệch',
    Width: 100,
    Sortable: true,
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: 20,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: true,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    IsLock: false,
    Template: null,
    AggregateFunc: null,
  },
  {
    Name: 'CompletionRate',
    HeaderName: 'Mức độ hoàn thành',
    HeaderKey: 'Mức độ hoàn thành',
    DisplayName: 'Mức độ hoàn thành',
    Width: 100,
    Sortable: true,
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: 20,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: true,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    IsLock: false,
    Template: null,
    AggregateFunc: null,
  },
  {
    Name: 'ResultStatus',
    HeaderName: 'Kết quả',
    HeaderKey: 'Kết quả',
    DisplayName: 'Kết quả',
    Width: 100,
    Sortable: true,
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: 20,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: true,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    IsLock: false,
    Template: null,
    AggregateFunc: null,
  },
  {
    Name: 'Status',
    HeaderName: 'Trạng thái',
    HeaderKey: 'Trạng thái',
    DisplayName: 'Trạng thái',
    Width: 100,
    Sortable: true,
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: 20,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: true,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    IsLock: false,
    Template: null,
    AggregateFunc: null,
  },
];

export const memberFormDataSource = [
  {
    GoalName: 'Doanh thu phân phối bán lẻ',
    Weight: '75%',
    LastResult: '25/12/2025',
    Attachment: 'BC-doanh-thu.xlsx',
    TotalTarget: '100 tỷ VND',
    TotalActual: '120 tỷ VND',
    Variance: '+ 10 tỷ VND',
    CompletionRate: '110%',
    ResultStatus: 'Vượt chỉ tiêu',
    Status: 'Chờ xác nhận',
  },
  {
    GoalName: 'Doanh thu xuất khẩu',
    Weight: '20%',
    LastResult: '25/12/2025',
    Attachment: 'BC-san-luong.xlsx',
    TotalTarget: '200.000 xe',
    TotalActual: '190.000 xe',
    Variance: '-10.000 xe',
    CompletionRate: '95%',
    ResultStatus: 'Không đạt',
    Status: 'Chờ xác nhận',
  },
  {
    GoalName: 'Doanh thu bán xe nội địa',
    Weight: '20%',
    LastResult: '25/12/2025',
    Attachment: 'BC-doanh-thu.xlsx',
    TotalTarget: '50 tỷ VND',
    TotalActual: '290 tỷ VNĐ',
    Variance: '0',
    CompletionRate: '100%',
    ResultStatus: 'Đạt chỉ tiêu',
    Status: 'Chờ xác nhận',
  },
  {
    GoalName: 'Doanh thu sản phẩm mới',
    Weight: '20%',
    LastResult: '25/12/2025',
    Attachment: 'BC-doanh-thu.xlsx',
    TotalTarget: '30 tỷ VND',
    TotalActual: '15 tỷ VND',
    Variance: '-15 tỷ VND',
    CompletionRate: '50%',
    ResultStatus: 'Đã huỷ',
    Status: 'Đã huỷ',
  },
];
