<div class="form-builder-container">
  <!-- Header -->
  <div class="form-builder-header">
    <div class="form-builder-header-left">
      <button class="back-button" (click)="onBackClick()">
        <i nz-icon nzType="arrow-left" nzTheme="outline"></i>
      </button>
      <h2 class="form-builder-title">Tạo mẫu đánh giá</h2>
    </div>
    <div class="form-builder-actions">
      <vnr-button-new [builder]="builderButtonSaveDraft" (vnrClick)="onSaveDraftClick()">
      </vnr-button-new>
      <vnr-button-new [builder]="builderButtonPreview" (vnrClick)="togglePreviewMode()">
      </vnr-button-new>
      <vnr-button-new [builder]="builderButtonSave" (vnrClick)="onSaveClick()"> </vnr-button-new>
    </div>
  </div>

  <div class="form-builder-content">
    <!-- Vùng thông tin chung -->
    <div class="form-builder-general-info" [class.collapsed]="isGeneralInfoCollapsed">
      <div
        class="general-info-header"
        role="button"
        tabindex="0"
        (click)="toggleGeneralInfo()"
        (keydown.enter)="toggleGeneralInfo()"
        (keydown.space)="toggleGeneralInfo()"
      >
        <h3 class="general-info-title">Thông tin chung</h3>
        <button class="toggle-button" aria-label="Đóng/mở thông tin chung">
          <i nz-icon [nzType]="isGeneralInfoCollapsed ? 'down' : 'up'" nzTheme="outline"></i>
        </button>
      </div>
      <div class="general-info-content" *ngIf="!isGeneralInfoCollapsed">
        <app-eva-template-general-info
          [formTemplate]="formTemplate"
          [isEdit]="isEdit"
        ></app-eva-template-general-info>
      </div>
    </div>

    <!-- Vùng thông tin mẫu -->
    <div class="form-builder-template-info" [class.collapsed]="isTemplateInfoCollapsed">
      <div
        class="template-info-header"
        role="button"
        tabindex="0"
        (click)="toggleTemplateInfo()"
        (keydown.enter)="toggleTemplateInfo()"
        (keydown.space)="toggleTemplateInfo()"
      >
        <h3 class="template-info-title">Thông tin mẫu</h3>
        <button class="toggle-button" aria-label="Đóng/mở thông tin mẫu">
          <i nz-icon [nzType]="isTemplateInfoCollapsed ? 'down' : 'up'" nzTheme="outline"></i>
        </button>
      </div>
      <div class="template-info-content" *ngIf="!isTemplateInfoCollapsed">
        <!-- Sidebar trái - Control Palette -->
        <div class="form-builder-sidebar" *ngIf="!isPreviewVisible">
          <div class="sidebar-content">
            <app-control-palette
              [usedSectionTypes]="usedSectionTypes"
              [selectedSectionId]="selectedSection?.id"
              (dragStart)="dragStart.emit()"
              (goToSection)="handleGoToSection($event)"
            ></app-control-palette>
          </div>
        </div>

        <!-- Canvas chính -->
        <div class="form-builder-canvas">
          <app-form-canvas
            [formTemplate]="formTemplate"
            [selectedSection]="selectedSection"
            [formType]="formType"
            (sectionClick)="onSectionClick($event)"
            (dropSection)="onDropSection($event)"
            (removeSection)="onRemoveSection($event)"
            (sectionOrderChanged)="onSectionOrderChanged($event)"
            (sectionTypesChanged)="onSectionTypesChanged($event)"
            (editSection)="editSection.emit($event)"
            (formValueChange)="onFormValueChange($event)"
          ></app-form-canvas>
        </div>
      </div>
    </div>
  </div>
</div>
