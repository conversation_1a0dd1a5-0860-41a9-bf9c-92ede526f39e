import { ChangeDetectionStrategy, Component, OnInit, ViewChild } from '@angular/core';
import { TableDefineDataSource } from '../../../data/datasource.data';
import { tableDefineColumns } from '../../../data/column.data';
import { VnrGridNewComponent, VnrGridNewBuilder } from '@hrm-frontend-workspace/vnr-module';
import { ObjSharedModule } from '../../../../../../shared/obj-shared.module';

@Component({
  selector: 'goal-personal-table',
  templateUrl: './goal-personal-table.component.html',
  styleUrls: ['./goal-personal-table.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ObjSharedModule],
})
export class GoalPersonalTableComponent implements OnInit {
  @ViewChild('vnrGrid', { static: true }) gridControl: VnrGridNewComponent;

  protected builderGrid: VnrGridNewBuilder;
  protected gridName = 'ObjEval_TableGoalPeriod';
  protected isSupperAdmin = true;
  protected dataLocal = TableDefineDataSource;
  protected columns = tableDefineColumns;

  ngOnInit() {
    this.builderGridComponent();
  }

  private builderGridComponent() {
    this.builderGrid = new VnrGridNewBuilder({
      options: {
        configSelectable: {
          isEnabled: false,
        },
        configHeightGrid: {
          gridHeight: 600,
        },
        configShowHide: {
          isShowColumnCheck: false,
          isShowButtonMenu: false,
          isShowEdit: false,
          isShowDelete: false,
        },
      },
    });
  }
}
