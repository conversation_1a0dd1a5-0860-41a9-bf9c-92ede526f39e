/* Styles for progress container */ 
:host {
  display: block;
  width: 100%;
}

.page-title {
  padding: 0 12px;
  background-color: #ffffff;
  font-size: 18px;
  font-weight: 700;
  line-height: 1.22;
  color: #303030;
  margin: 0;
}

.tab-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  width: 100%;
  background-color: #ffffff;
}

.tab-text {
  flex: 1;
}

.tab-action {
  display: flex;
  padding: 0 8px 4px;

  form {
    display: flex;
    align-items: center;
  }

  ::ng-deep {
    label {
      margin-bottom: 0px !important;
    }
    .ant-segmented {
      height: 32px;
    }
  }
}
::ng-deep {
  .ant-tabs-nav {
    margin-bottom: 0 !important;
  }
} 