<button nz-button nzType="warning" class="mr-2 ant-btn ant-btn-warning" (click)="reload()">
  IsSupperAdmin = {{ isSupperAdmin }}
</button>
<button nz-button nzType="warning" class="mr-2 ant-btn ant-btn-warning" (click)="reload1()">
  IsSupperAdmin = {{ isSupperAdmin }}
</button>

<div id="grid" class="mt-2">
  <div class="grid-2">
    <button
      nz-button
      class="mr-2 ant-btn ant-btn-primary"
      #anchor
      vnr-Popover-ChangeColumn
      (click)="toggleChangeColumn(false)"
    >
      Đ<PERSON>i cột v.1
    </button>
    <vnr-grid-new-Edit-Incell
      #vnrEditIncell
      [builder]="builderGrid"
      [gridName]="'Eva_PerformanceTemplateV2Module_Index_Grid'"
      [columns]="columns"
      [dataLocal]="dataLocal"
      [isSupperAdmin]="isSupperAdmin"
      [isChangeColumnNew]="true"
      [defaultColumnTemplate]="tplCustomTemplateByColumn"
      [customTemplateColumnFooter]="[{ KPIName: this.tplFooter }]"
      (getSelectedID)="getSelectedID($event)"
      (getDataItem)="getDataItem($event)"
      (vnrCustomCellClick)="onCustomCellClick($event)"
      (vnrOutSideClick)="onOutSideClick($event)"
      (vnrViewModeGrid)="onVnRViewModeGrid($event)"
      (vnrEdit)="onGridEdit($event)"
      (vnrDelete)="onGridDelete($event)"
      (vnrViewDetails)="onGridViewDetail($event)"
      (vnrCellClick)="onGridCellClick($event)"
      (vnrChangeComboBoxDataItem)="vnrChangeComboBoxDataItem($event)"
      (vnrOpenComboBox)="vnrOpenComboBox($event)"
    ></vnr-grid-new-Edit-Incell>

    <ng-template
      #templateHeader
      let-columnIndex="columnIndex"
      let-field="field"
      let-column="column"
    >
      dadadada đa d
    </ng-template>
    <ng-template
      #tplCustomTemplateByColumn
      let-field="field"
      let-column="column"
      let-dataItem
      let-columnItem="columnItem"
    >
      <ng-container *ngIf="columnItem.IsView; else tplDefault">
        {{
          dataItem[columnItem.Name]
            ? dataItem[columnItem.Name] +
              '/' +
              (dataItem[columnItem.Name + '_Real'] ? dataItem[columnItem.Name + '_Real'] : 0)
            : '' +
              '/' +
              (dataItem[columnItem.Name + '_Real'] ? dataItem[columnItem.Name + '_Real'] : 0)
        }}
      </ng-container>
      <ng-template #tplDefault>
        <ng-container [ngSwitch]="columnItem.Name">
          <span *ngSwitchCase="'KPIID'">
            <span *ngIf="dataItem?.KPIID">
              {{ dataItem?.KPIName }}
            </span>
          </span>
          <span *ngSwitchDefault>
            {{ dataItem[columnItem.Name] }}
          </span>
        </ng-container>
      </ng-template>
      <!-- <span *ngIf="field === 'KPIName'; else tplField" (click)="onClick(dataItem, columnItem)">
        {{ dataItem[field] }}
      </span>
      <ng-template #tplField>
        <span (click)="onClick(dataItem, columnItem)">
          {{ dataItem[field] }}
        </span>
      </ng-template> -->
    </ng-template>
    <ng-template #tplFooter let-columnItem="columnItem" let-columnIndex="columnIndex">
      {{ columnItem.Name }}({{ columnIndex }})
    </ng-template>

    <nz-divider></nz-divider>
  </div>
</div>
<ng-template
  #templateCustomControl
  let-dataItem
  let-rowIndex="rowIndex"
  let-columnItem="columnItem"
  let-formGroup="formGroup"
  let-isNew="isNew"
  let-required="required"
  let-maxlength="maxlength"
  let-minlength="minlength"
  let-min="min"
  let-max="max"
>
  {{ columnItem?.TypeControl?.split('|')[1] }}
  <ng-container [ngSwitch]="columnItem.Name">
    <span *ngSwitchCase="'Rate'"> </span>
  </ng-container>
</ng-template>
