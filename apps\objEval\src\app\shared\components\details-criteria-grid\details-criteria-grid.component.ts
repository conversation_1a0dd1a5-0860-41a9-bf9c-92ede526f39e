import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { VnrButtonModule } from '@hrm-frontend-workspace/ui';
import {
  VnrGridNewBuilder,
  VnrGridNewComponent,
  VnrGridsModule,
} from '@hrm-frontend-workspace/vnr-module';
import { TranslateModule } from '@ngx-translate/core';
import { EvaNumberToLetterPipe } from '../../pipes/number-to-letter.pipe';
import { gridDefineColumns } from './data/column.data';
import { IntlModule } from '@progress/kendo-angular-intl';
import { AggregateDescriptor } from '@progress/kendo-data-query';

@Component({
  selector: 'app-details-criteria-grid',
  templateUrl: './details-criteria-grid.component.html',
  styleUrls: ['./details-criteria-grid.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    VnrGridsModule,
    VnrButtonModule,
    EvaNumberToLetterPipe,
    IntlModule,
  ],
})
export class DetailsCriteriaGridComponent implements OnInit, OnChanges {
  @ViewChild('vnrGrid') vnrGrid!: VnrGridNewComponent;
  @Input() index = 0;
  @Input() vnrdataLocalLocal: any[] = [];
  @Input() vnrDataItem: any;
  @ViewChild('templateFooter', { static: true }) public templateFooter!: TemplateRef<any>;

  builderGrid!: VnrGridNewBuilder;
  protected gridName = 'details-criteria-grid';

  protected dataLocal: any[] = [];
  protected columnGridLocal: any[] = [];
  protected dataItemInput: any;
  protected listCustomFooter: any;
  protected aggregates: AggregateDescriptor[] = [];
  ngOnChanges(changes: SimpleChanges): void {
    const { vnrdataLocalLocal, vnrDataItem } = changes;
    if (vnrdataLocalLocal) {
      this.dataLocal = vnrdataLocalLocal.currentValue;
    }
    if (vnrDataItem) {
      this.dataItemInput = vnrDataItem.currentValue;
    }
    this.columnGridLocal = gridDefineColumns;
    this.handleCustomColumnTemplate();
    this.aggregatesCalculate();
    this.readDataAggregate();
    this.initGrid();
  }
  ngOnInit(): void {}

  private initGrid(): void {
    this.builderGrid = new VnrGridNewBuilder({
      options: {
        configIndexColumn: {
          isShow: true,
          isLocked: true,
        },
        configShowHide: {
          isShowEdit: false,
          isShowDelete: false,
          isShowColumnCheck: false,
          isShowColumnGroupCheck: false,
          isShowRefresh: false,
          isShowButtonMenu: false,
        },
        configCommandColumn: {
          isEnabledMenuAction: false,
        },
        configGroupable: {
          isEnabled: false,
        },
        configHeightGrid: {
          isAllowCalcRowHeight: false,
          isHeightByRow: true,
          rowHeight: 50,
          gridHeight: this.dataLocal?.length > 0 ? 50 * this.dataLocal?.length + 37 : 100,
        },
      },
    });
  }
  addCriteria() {}

  handleCustomColumnTemplate(): void {
    const templateCustomFooter: any = {};
    this.columnGridLocal.forEach((data) => {
      templateCustomFooter[data.Name] = this.templateFooter;
      if (data && data.MultiColumn && data.MultiColumn.length > 0) {
        data.MultiColumn.forEach((item: any) => {
          templateCustomFooter[item.Name] = this.templateFooter;
        });
      }
    });
    this.listCustomFooter = templateCustomFooter;
  }
  aggregatesCalculate() {
    //Calculate
    let listCalculateColumn = this.columnGridLocal?.filter((i) => i.Calculate);
    let result = [];
    this.columnGridLocal?.forEach((i: any) => {
      if (i && i.MultiColumn && i.MultiColumn.length > 0) {
        result = result.concat(i.MultiColumn.filter((ele: any) => ele.Calculate));
      }
    });
    if ((listCalculateColumn && listCalculateColumn.length > 0) || (result && result.length > 0)) {
      listCalculateColumn = listCalculateColumn.concat(result);
      const aggregates: AggregateDescriptor[] = [];
      listCalculateColumn.map((item) => {
        aggregates.push({ field: item.Name, aggregate: item.Calculate });
        return item;
      });
      this.aggregates = aggregates;
    }
  }
  readDataAggregate() {
    // if (this.vnrGridEditInline) {
    //   let data = this.vnrGridEditInline.vnrdataLocalLocal;
    //   this.vnrGridEditInline.vnrAggregateResult = aggregateBy(data, this.aggregates);
    //   this.vnrGridEditInline.gridView = process(data, this.vnrGridEditInline.state);
    // }
  }
}
