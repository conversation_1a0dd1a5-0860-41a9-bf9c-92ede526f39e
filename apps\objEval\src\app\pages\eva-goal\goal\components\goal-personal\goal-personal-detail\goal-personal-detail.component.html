<div class="goal-personal-detail">
  <div class="header-section">
    <!-- Avatar và thông tin nhân viên -->
    <div class="employee-info">
      <div class="avatar-container">
        <app-vnr-letters-avatar
          [width]="48"
          [height]="48"
          [circular]="true"
          [avatarName]="dataItem.ExecutionObject"
        ></app-vnr-letters-avatar>
      </div>
      <div class="info-container">
        <h2 class="employee-name">{{ dataItem.ExecutionObject }}</h2>
        <div class="employee-details">
          <span class="employee-id">Mã nhân viên: NV1</span>
          <span class="employee-department">{{ dataItem.Department }}</span>
        </div>
      </div>
    </div>

    <!-- Nút thêm mục tiêu -->
    <div class="d-flex gap-2">
      <vnr-button
        class="mr-2"
        [vnrType]="'primary'"
        [vnrIcon]="'plus'"
        [vnrText]="'Thêm mục tiêu'"
        (vnrClick)="onAddGoal()"
      ></vnr-button>
      <vnr-button
        class="mr-2"
        [vnrType]="'default'"
        [vnrIcon]="'share-alt'"
        [vnrText]="'Phân bổ'"
        (vnrClick)="onAllocation()"
      ></vnr-button>
    </div>
  </div>

  <!-- Tiêu đề và thông tin kế hoạch -->
  <div class="title-section">
    <h2 class="main-title">Kế hoạch mục tiêu</h2>
    <div class="period-info">
      <span nz-icon nzType="calendar" nzTheme="outline"></span>
      <span class="period-text"
        >Chu kỳ thực hiện: Năm 2025 (01/01/2025 - 31/12/2025) {{ dataItem.GoalCycle }}</span
      >
      <vnr-tag
        [vnrColor]="statusColorMap[dataItem.Status]"
        [vnrTitle]="statusTextMap[dataItem.Status]"
      ></vnr-tag>
    </div>
  </div>
  <ng-template #tplCustomBtn let-dataItem let-column="columnItem" let-field="field">
    <vnr-button
      class="mr-1"
      [vnrType]="'success'"
      [vnrIcon]="'check'"
      [vnrSize]="'small'"
      (vnrClick)="onApprove(dataItem)"
    ></vnr-button>
    <vnr-button
      class="mr-1"
      [vnrType]="'warning'"
      [vnrIcon]="'edit'"
      [vnrSize]="'small'"
      (vnrClick)="onEdit(dataItem)"
    ></vnr-button>
    <vnr-button
      class="mr-1"
      [vnrType]="'danger'"
      [vnrIcon]="'close'"
      [vnrSize]="'small'"
      (vnrClick)="onReject(dataItem)"
    ></vnr-button>
  </ng-template>
</div>
<div class="goal-personal-detail-content">
  <div class="statistics-container">
    <div class="statistics-card total">
      <div class="card-title">Tổng số mục tiêu</div>
      <div class="card-value">{{ statistics.totalGoals }}</div>
    </div>
    <div class="statistics-card allocated">
      <div class="card-title">Mục tiêu chờ xác nhận</div>
      <div class="card-value">{{ statistics.waitingConfirm }}</div>
    </div>
    <div class="statistics-card registered">
      <div class="card-title">Mục tiêu đã xác nhận</div>
      <div class="card-value">{{ statistics.confirmed }}</div>
    </div>
    <div class="statistics-card confirmed">
      <div class="card-title">Mục tiêu từ chối</div>
      <div class="card-value">{{ statistics.rejected }}</div>
    </div>
  </div>

  <div class="total-effectiveness">
    <span class="effectiveness-icon">
      <span nz-icon nzType="pie-chart" nzTheme="fill"></span>
    </span>
    <span class="effectiveness-text">Tổng trọng số mục tiêu hiệu suất:</span>
    <span class="effectiveness-value">100%</span>
  </div>

  <div class="title-section justify-content-between align-items-center">
    <div class="main-title">Danh sách mục tiêu</div>
    <nz-segmented
      [(ngModel)]="selectedViewMode"
      [ngModelOptions]="{ standalone: true }"
      [nzOptions]="viewModeOptions"
      (nzValueChange)="onViewModeChange($event)"
      class="ml-2"
    ></nz-segmented>
  </div>
  <vnr-grid-new
    *ngIf="selectedViewMode === 'table'"
    #vnrGrid
    [builder]="builderGrid"
    [gridName]="gridName"
    [dataLocal]="dataLocal"
    [columns]="columns"
    [isSupperAdmin]="isSupperAdmin"
    [rowActionsTemplate]="tplCustomBtn"
    [defaultColumnTemplate]="tplCustomTemplateByColumn"
  >
  </vnr-grid-new>
  <div *ngIf="selectedViewMode === 'card'">
    <div class="card-grid">
      <div class="goal-card" *ngFor="let item of goalData">
        <div class="goal-card-header">
          <div class="goal-card-title">{{ item.GoalName }}</div>
          <vnr-tag
            [vnrColor]="statusColorMap[item.Status]"
            [vnrTitle]="statusTextMap[item.Status]"
          ></vnr-tag>
          <ng-container *ngIf="item.Status === 'E_WAITING_APPROVE'">
            <vnr-button
              [vnrType]="'success'"
              [vnrIcon]="'check'"
              [vnrSize]="'small'"
              (vnrClick)="onApprove(item)"
            ></vnr-button>
            <vnr-button
              [vnrType]="'warning'"
              [vnrIcon]="'edit'"
              [vnrSize]="'small'"
              (vnrClick)="onEdit(item)"
            ></vnr-button>
            <vnr-button
              [vnrType]="'danger'"
              [vnrIcon]="'close'"
              [vnrSize]="'small'"
              (vnrClick)="onReject(item)"
            ></vnr-button>
          </ng-container>
        </div>
        <div class="goal-card-body">
          <div class="goal-card-info">
            <div class="info-row">
              <span class="info-label">Chu kỳ:</span>
              <span class="info-value">{{ item.Cycle }}</span>
            </div>
            <div class="info-row">
              <span class="info-label">Chỉ tiêu giao:</span>
              <span class="info-value">{{ item.TargetAssigned }}</span>
            </div>
            <div class="info-row">
              <span class="info-label">Chỉ tiêu đăng ký:</span>
              <span class="info-value">{{ item.TargetRegistered }}</span>
            </div>
            <div class="info-row">
              <span class="info-label">Chỉ tiêu duyệt:</span>
              <span class="info-value">{{ item.TargetApproved }}</span>
            </div>
            <div class="info-row">
              <span class="info-label">Trọng số:</span>
              <span class="info-value"
                ><vnr-tag [vnrColor]="'warning'" [vnrTitle]="item.WeightView"></vnr-tag
              ></span>
            </div>
          </div>
          <div class="goal-card-description">
            <div class="description-title">Mô tả mục tiêu:</div>
            <div class="description-content">{{ item.Description }}</div>
          </div>
          <div class="goal-attachments" *ngIf="item.Attachments && item.Attachments.length > 0">
            <div class="attachments-title">Tệp đính kèm:</div>
            <div class="attachments-list">
              <div class="attachment-item" *ngFor="let file of item.Attachments">
                <span nz-icon nzType="paper-clip" nzTheme="outline"></span>
                <a [href]="file.url" target="_blank" class="attachment-name">{{ file.name }}</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<ng-template #tplCustomTemplateByColumn let-dataItem let-column="columnItem" let-field="field">
  <ng-container [ngSwitch]="column['Name']">
    <span *ngSwitchCase="'Group'">
      <vnr-tag
        [vnrColor]="goalTypeColorMap[dataItem[column['Name']]]"
        [vnrTitle]="dataItem[column['Name']]"
      ></vnr-tag>
    </span>
    <span *ngSwitchCase="'Status'">
      <vnr-tag
        [vnrColor]="statusColorMap[dataItem['Status']] || 'default'"
        [vnrTitle]="statusTextMap[dataItem['Status']] | translate"
      ></vnr-tag>
    </span>
    <span *ngSwitchDefault>
      {{ dataItem[column['Name']] }}
    </span>
  </ng-container>
</ng-template>
