import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  TemplateRef,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';
import {
  VnrButtonFactory,
  VnrButtonNewBuilder,
  VnrButtonNewComponent,
  VnrGridNewBuilder,
  VnrGridNewComponent,
  vnrUtilities,
} from '@hrm-frontend-workspace/vnr-module';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { Ng<PERSON><PERSON>, NgIf, NgSwitch, NgSwitchCase, NgSwitchDefault } from '@angular/common';
import { CommonService } from '@hrm-frontend-workspace/core';
import { ResponseStatus } from '@hrm-frontend-workspace/models';
import { VnrModuleModule, VnrTagComponent } from '@hrm-frontend-workspace/ui';
import { NzDrawerService } from 'ng-zorro-antd/drawer';
import { NzModalService } from 'ng-zorro-antd/modal';
import { gridDefineColumns } from '../../../../data/column.data';
import { EvaCriteriaFacade } from '../../../../facade/eva-criteria.facade';
import { evaCriteriaDataSource } from '../../../../data/datasource.data';
import { EvaCriteriaCreateComponent } from '../../../eva-criteria-create/eva-criteria-create.component';
import { EvaCriteriaDetailComponent } from '../../../eva-criteria-detail/eva-criteria-detail.component';

@Component({
  selector: 'eva-criteria-grid-list',
  templateUrl: './eva-criteria-grid-list.component.html',
  styleUrls: ['./eva-criteria-grid-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    VnrModuleModule,
    TranslateModule,
    NgFor,
    NgSwitch,
    NgSwitchCase,
    NgSwitchDefault,
    VnrButtonNewComponent,
  ],
})
export class EvaCriteriaGridListComponent implements OnInit, AfterViewInit {
  @Input() public columnMaster: any;
  @Output() onSelectItem = new EventEmitter<any>();

  @ViewChild('vnrGrid', { static: true }) gridControl: VnrGridNewComponent;
  @ViewChild('tplContentDelete', { static: true }) private _tplContentDelete: TemplateRef<any>;
  @ViewChild('tplFooterDelete', { static: true })
  private _tplFooterDelete: TemplateRef<any>;
  private _isSupperAdmin: boolean = true;
  private _permission: string = '';
  private _screenName: string = 'criteria-grid';
  private _storeName: string = 'hrm_cat_sp_get_criteria';
  private _gridName = 'criteria-grid';
  private _selectedIDs = [];
  private _columnKey = 'Id';
  private _btnFactory: VnrButtonFactory = VnrButtonFactory.init();
  private _isEditing: boolean = false;

  protected screenWidth: number;
  protected screenHeight: number;
  protected compRef: ViewContainerRef;
  protected gridHeight: number;
  protected builderGrid: VnrGridNewBuilder;
  protected builderbtnDoNo: VnrButtonNewBuilder;
  protected builderbtnDoYes: VnrButtonNewBuilder;
  protected gridName = 'criteria-grid';
  protected dataLocal = evaCriteriaDataSource;
  protected columns = gridDefineColumns;
  protected dataFormSearch = {};

  constructor(
    private _evaCriteriaFacade: EvaCriteriaFacade,
    private _drawerService: NzDrawerService,
    private _modalService: NzModalService,
    private _vc: ViewContainerRef,
    private _commonService: CommonService,
    private _translate: TranslateService,
  ) {}
  ngAfterViewInit() {}
  ngOnInit() {
    this.dataFormSearch = {
      CriteriaGroupId: this.columnMaster.Id,
    };
    this.builderButtonComponent();
    this.builderGridComponent();
  }
  private builderButtonComponent() {
    this.builderbtnDoYes = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'common.modal.buttonOk',
      options: {
        style: 'danger',
      },
    });
    this.builderbtnDoNo = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'common.modal.buttonNo',
      options: {
        style: 'default',
      },
    });
  }
  private builderGridComponent() {
    this.builderGrid = new VnrGridNewBuilder({
      options: {
        queryOption: {
          take: 10,
        },
        isResizable: true,
        configHeightGrid: {
          isAllowCalcRowHeight: true,
          rowHeight: 36,
          isHeightByRow: true,
          rowScrollHorizontal: 8,
          rowThreshold: 0,
        },
        configSelectable: {
          columnKey: this._columnKey,
        },
        configShowHide: {
          isPageExpand: true,
        },
        configCommandColumn: {
          isEnabledMenuAction: true,
        },
        configGroupable: {
          isEnabled: false,
        },
      },
    });
  }
  protected getSelectedID($event) {
    this._selectedIDs = $event;
    this.onSelectItem.emit($event);
  }
  protected onGridEdit($event) {
    this._isEditing = true;
    if (!$event) {
      this._commonService.message({
        message: this._translate.instant('common.grid.noSelectedId'),
        type: 'error',
      });
      return;
    }
    this._selectedIDs = [$event[this._columnKey]];
    if (!this._selectedIDs || this._selectedIDs.length == 0) {
      this._commonService.message({
        message: this._translate.instant('common.grid.noSelectedId'),
        type: 'error',
      });
      return;
    }
    this.onAddCriteriaDetail(this._selectedIDs[0]);
    setTimeout(() => {
      this._isEditing = false;
    }, 1000);
  }
  protected onGridDelete($event) {
    this._selectedIDs = [$event[this._columnKey]];
    this.onOpenConfirmDelete();
  }
  protected onGridCellClick($event) {
    if (this._isEditing) {
      return;
    }
    const paramEditID = $event?.dataItem[this._columnKey];
    let drawerRef = this._drawerService.create({
      nzTitle: this._translate.instant('objEval.EvaCriteria.CriteriaDetail'),
      nzContent: EvaCriteriaDetailComponent,
      nzContentParams: {
        paramEditID: paramEditID,
      },
      nzMaskClosable: false,
      nzWidth: '600px',
      nzWrapClassName: 'crud-modal',
      nzClosable: true,
      nzMask: true,
    });
    drawerRef.afterClose.subscribe((res) => {
      if (res && res['isReloadData'] == true) {
        this.gridControl.vnrReloadGrid();
      }
    });
  }
  protected onAddCriteriaDetail($event: any) {
    let title =
      $event != null
        ? this._translate.instant('objEval.EvaCriteria.EditCriteria')
        : this._translate.instant('objEval.EvaCriteria.AddCriteria');
    let drawerRef = this._drawerService.create({
      nzTitle: title,
      nzContent: EvaCriteriaCreateComponent,
      nzContentParams: {
        paramEditID: $event,
      },
      nzMaskClosable: false,
      nzWidth: '600px',
      nzWrapClassName: 'crud-modal',
      nzClosable: true,
      nzMask: true,
    });
    drawerRef.afterClose.subscribe((res) => {
      if (res && res['isReloadData'] == true) {
        this.gridControl.vnrReloadGrid();
      }
    });
  }
  public onOpenConfirmDelete() {
    const modalRef = this._modalService.create({
      nzIconType: '',
      nzContent: this._tplContentDelete,
      nzFooter: this._tplFooterDelete,
      nzMaskClosable: true,
      nzClosable: true,
    });
    modalRef.afterClose.subscribe((modal) => {
      if (modal?.type === 'Reload') {
        this.gridControl.vnrReloadGrid();
      }
    });
  }
  protected onConfirmDelete($event: any, $modalRef: any) {
    $event?.stopPropagation();
    if (!this._selectedIDs || this._selectedIDs.length <= 0) {
      this._commonService.message({ message: 'common.grid.noSelectedId_Delete', type: 'error' });
      return;
    }
    this._evaCriteriaFacade.delete(this._selectedIDs).subscribe((res) => {
      if (res && res.Status === ResponseStatus.SUCCESS) {
        this._commonService.message({
          message: this._translate.instant('common.message.deletedRowOfData', {
            n: this._selectedIDs.length,
          }),
          type: 'success',
        });
        $modalRef.close({
          type: 'Reload',
        });
      } else {
        this._commonService.message({
          message: res.Message || this._translate.instant('common.notification.descErr500'),
          type: 'error',
        });
      }
    });
  }
}
