import { RouterModule, Routes } from '@angular/router';
import { NgModule } from '@angular/core';
import { GoalPeriodComponent } from './goal/container/goal-period.component';
import { AllocationContainerComponent } from './goal-allocation/container/allocation.container/allocation.container.component';
import { GoalResultComponent } from './goal-result/container/goal-result.component';
import { GoalProgressComponent } from './goal-progress/container/goal-progress.component';
import { FormConfigContainerComponent } from './form-config/form-config/container/form-config.component';

const routes: Routes = [
  {
    path: '',
    component: GoalPeriodComponent,
  },
  {
    path: 'allocation',
    component: AllocationContainerComponent,
  },
  {
    path: 'result',
    component: GoalResultComponent,
  },
  {
    path: 'progress',
    component: GoalProgressComponent,
  },
  {
    path: 'form-config',
    component: FormConfigContainerComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class EvaGoalRoutesModule {}
