import { CommonModule } from '@angular/common';
import { Component, OnInit, ViewChildren, QueryList, AfterViewInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import {
  VnrGridNewBuilder,
  VnrGridNewComponent,
  VnrToolbarNewBuilder,
  VnrToolbarNewComponent,
} from '@hrm-frontend-workspace/vnr-module';
import {
  VnrTagComponent,
  VnrButtonModule,
  VnrLettersAvatarComponent,
} from '@hrm-frontend-workspace/ui';
import { TranslateModule } from '@ngx-translate/core';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzIconModule } from 'ng-zorro-antd/icon';
import {
  gridDefineDataFilterQuick,
  mockScheduleData,
  scheduleStatusList,
} from '../../data/schedule.data';
import { scheduleColumns } from '../../data/schedule-columns.data';
import { AddScheduleButtonComponent } from '../add-schedule-button/add-schedule-button.component';
import { NzSegmentedModule } from 'ng-zorro-antd/segmented';

@Component({
  selector: 'app-schedule-grid',
  standalone: true,
  imports: [
    CommonModule,
    VnrGridNewComponent,
    VnrToolbarNewComponent,
    TranslateModule,
    NzTagModule,
    NzIconModule,
    VnrTagComponent,
    VnrButtonModule,
    AddScheduleButtonComponent,
    NzSegmentedModule,
    FormsModule,
    VnrLettersAvatarComponent,
  ],
  templateUrl: './schedule-grid.component.html',
  styleUrls: ['./schedule-grid.component.scss'],
})
export class ScheduleGridComponent implements OnInit, AfterViewInit {
  @ViewChildren('vnrGrid') vnrGrids: QueryList<VnrGridNewComponent>;

  protected builderGrid: VnrGridNewBuilder;
  protected builderToolbar: VnrToolbarNewBuilder;

  private readonly _gridName = 'scheduleGrid';
  protected gridName = 'scheduleGrid';
  protected columns = scheduleColumns;
  private _filteredData = [...mockScheduleData];
  protected dataLocal = this._filteredData;
  protected viewMode: 'list' | 'calendar' = 'list';

  protected viewModeViewOptions = [
    {
      value: 'list',
      icon: 'unordered-list',
    },
    {
      value: 'calendar',
      icon: 'calendar',
    },
  ];

  ngOnInit(): void {
    this.initBuilders();
    this.viewMode = 'list';
  }

  ngAfterViewInit(): void {
    // Update toolbar builder sau khi view được init
    this.updateToolbarWithGridReference();
  }

  // Getter để lấy grid đang active
  get activeGrid(): VnrGridNewComponent | undefined {
    // Sử dụng viewMode để xác định grid nào đang active
    // Vì chỉ có 1 grid được render tại một thời điểm trong switch case
    return this.vnrGrids?.first;
  }

  handleSwitchModeView(value: any) {
    this.viewMode = value;
    // Update grid reference sau khi switch
    setTimeout(() => {
      this.updateToolbarWithGridReference();
    }, 100);
  }

  private updateToolbarWithGridReference(): void {
    if (this.builderToolbar && this.activeGrid) {
      this.builderToolbar.gridRef = this.activeGrid;
    }
  }

  private initBuilders(): void {
    this.initGridBuilder();
    this.initToolbarBuilder();
  }

  private initGridBuilder(): void {
    this.builderGrid = new VnrGridNewBuilder({
      options: {
        configHeightGrid: {
          isAllowCalcRowHeight: true,
        },
        configIndexColumn: {
          isShow: true,
          width: 40,
        },
        configSelectable: {
          columnKey: 'Id',
        },
        configShowHide: {
          isPageExpand: false,
          isShowDelete: false,
          isShowEdit: false,
        },
        configCommandColumn: {
          isEnabledMenuAction: true,
        },
        configGroupable: {
          isEnabled: false,
        },
      },
    });
  }

  private initToolbarBuilder(): void {
    this.builderToolbar = new VnrToolbarNewBuilder({
      isShowConfig: false,
      gridName: this._gridName,
      isSupperAdmin: false,
      screenName: 'schedule-grid',
      storeName: 'schedule-store',
      // gridRef sẽ được set trong updateToolbarWithGridReference()
      options: {
        configButtonChangeColumn: { isShow: true },
        configButtonExport: { isShowBtnExcelAll: true },
        configQuickSearch: {
          isShow: true,
        },
        configFilterAdvanceQuick: {
          isShow: true,
          components: gridDefineDataFilterQuick(),
          keyConfig: 'GridExam_FilterAdvanceSeting',
          isShowBtnAdvance: false,
        },
      },
    });
  }

  protected onGridEdit(event: any): void {
    // Handle edit action
    // TODO: Implement edit functionality
  }

  protected onGridDelete(event: any): void {
    // Handle delete action
    // TODO: Implement delete functionality
  }

  protected onGridViewDetail(event: any): void {
    // Handle view detail action
    // TODO: Implement view detail functionality
  }

  public reloadGridData(): void {
    if (this.activeGrid) {
      this.activeGrid.vnrReadGrid();
    }
  }

  protected getStatusColor(type: string): string {
    const status = scheduleStatusList.find((s) => s.key === type);
    return status?.color || '#000000';
  }

  protected getStatusLabel(type: string): string {
    const status = scheduleStatusList.find((s) => s.key === type);
    return status?.label || type;
  }

  protected onChangeStatusFilter(event: any): void {
    const selectedStatus = event?.value;

    // Filter data based on selected status
    this._filteredData = selectedStatus
      ? mockScheduleData.filter((item) => item.status.type === selectedStatus)
      : [...mockScheduleData];

    // Update grid data source
    if (this.builderGrid) {
      this.dataLocal = this._filteredData;
    }
  }
}
