import { CommonModule } from '@angular/common';
import { Component, inject, Input, OnInit, ViewChild } from '@angular/core';
import {
  FormsModule,
  NgForm,
  ReactiveFormsModule,
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { CommonService } from '@hrm-frontend-workspace/core';
import { ResponseStatus } from '@hrm-frontend-workspace/models';
import { ValidatorDirectiveModule, VnrButtonModule } from '@hrm-frontend-workspace/ui';
import {
  VnrComboBoxBuilder,
  VnrDateRangePickerBuilder,
  VnrFiltersModule,
  VnrInputFactory,
  VnrInputsModule,
  VnrPickerFactory,
  VnrPickersModule,
  VnrSelectFactory,
  VnrSelectsModule,
  VnrSwitchBuilder,
  VnrTextAreaBuilder,
  VnrTextBoxBuilder,
  VnrTreeViewBuilder,
  VnrTreeViewComponent,
} from '@hrm-frontend-workspace/vnr-module';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzDrawerRef } from 'ng-zorro-antd/drawer';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzModalModule } from 'ng-zorro-antd/modal';
import {
  performanceAppraisalsDepartmentDataSource,
  performanceAppraisalsEmployeeDataSource,
  performanceAppraisalsPositionDataSource,
} from '../../data/datasource-component.data';
import { EvaPerformanceAppraisalsFacade } from '../../facade/eva-performance-appraisals.facade';
import {
  IPerformanceAppraisalCreate,
  PerformanceAppraisalStatusPublic,
} from '../../models/eva-performance-appraisals.model';

@Component({
  selector: 'app-eva-performance-appraisals-form',
  templateUrl: './eva-performance-appraisals-form.component.html',
  styleUrls: ['./eva-performance-appraisals-form.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NzFormModule,
    NzGridModule,
    NzButtonModule,
    VnrSelectsModule,
    VnrPickersModule,
    VnrInputsModule,
    ValidatorDirectiveModule,
    TranslateModule,
    VnrFiltersModule,
    VnrButtonModule,
    VnrTreeViewComponent,
    NzModalModule,
  ],
})
export class EvaPerformanceAppraisalsFormComponent implements OnInit {
  @Input() paramEditID: string = null;
  @ViewChild('evaPerformanceAppraisalsFormRef') evaPerformanceAppraisalsFormRef: NgForm;

  private pickerFactory: VnrPickerFactory = VnrPickerFactory.init();
  private selectFactory: VnrSelectFactory = VnrSelectFactory.init();
  private inputFactory: VnrInputFactory = VnrInputFactory.init();
  private fb: UntypedFormBuilder = inject(UntypedFormBuilder);
  private _translate: TranslateService = inject(TranslateService);
  protected isDisabledBtn = false;
  protected evaPerformanceAppraisalsForm: UntypedFormGroup = this.fb.group({
    EvaluationTypeID: [null, [Validators.required]],
    CycleName: [null, [Validators.required]],
    PeriodTypeID: [null, [Validators.required]],
    PeriodDateRange: [null, [Validators.required]],
    PeriodDate: [''],
    DueDate: [''],
    StatusPublic: [PerformanceAppraisalStatusPublic.Plan, [Validators.required]],
    IsAnonymousAppraisals: [false],
    IsRepeat: [false],
    ApplyDepartmentIDs: [null, [Validators.required]],
    ApplyPositionIDs: [null, [Validators.required]],
    ApplyEmployeeIDs: [null, [Validators.required]],
    Description: [null],
  });

  protected builderEvaluationType: VnrComboBoxBuilder = new VnrComboBoxBuilder();
  protected builderPeriodType: VnrComboBoxBuilder = new VnrComboBoxBuilder();
  protected builderPeriodDate: VnrDateRangePickerBuilder = new VnrDateRangePickerBuilder();
  protected builderStatusPublic: VnrComboBoxBuilder = new VnrComboBoxBuilder();
  protected builderApplyObject: VnrComboBoxBuilder = new VnrComboBoxBuilder();
  protected builderOrg: VnrTreeViewBuilder = new VnrTreeViewBuilder();
  protected builderApplyPosition: VnrTreeViewBuilder = new VnrTreeViewBuilder();
  protected builderApplyEmployee: VnrTreeViewBuilder = new VnrTreeViewBuilder();
  protected builderCycleName: VnrTextBoxBuilder = new VnrTextBoxBuilder();
  protected builderAppraisalTemplate: VnrComboBoxBuilder = new VnrComboBoxBuilder();
  protected builderAppraisalProcess: VnrComboBoxBuilder = new VnrComboBoxBuilder();
  protected builderDescription: VnrTextAreaBuilder = new VnrTextAreaBuilder();
  protected builderAnonymousAppraisals: VnrSwitchBuilder = new VnrSwitchBuilder();
  protected builderIsRepeatAppraisals: VnrSwitchBuilder = new VnrSwitchBuilder();
  protected keyConfig = 'EvaPerformanceAppraisals_Register_Form';
  protected _paramEdit: any = null;
  protected isEdit = false;
  constructor(
    private _drawerRef: NzDrawerRef,
    private _evaPerformanceAppraisalsFacade: EvaPerformanceAppraisalsFacade,
    private _commonService: CommonService,
  ) {
    this.builderForm();
  }

  ngOnInit(): void {
    this.getParamEdit();
    this.patchValueDefaults(this._paramEdit);
    this.evaPerformanceAppraisalsForm.markAsDirty();
    this.evaPerformanceAppraisalsForm.updateValueAndValidity({ emitEvent: true });
  }
  private getParamEdit() {
    if (!this.paramEditID) {
      return;
    }
    this._evaPerformanceAppraisalsFacade.getAppraisalById(this.paramEditID).subscribe((res) => {
      this._paramEdit = res?.Data;
    });
  }
  private patchValueDefaults(dataEdit: any) {
    if (dataEdit) {
      this.evaPerformanceAppraisalsForm.patchValue(dataEdit);
      if (dataEdit.StartDate && dataEdit.EndDate) {
        const _periodDate: any[] = [dataEdit.PeriodDate, dataEdit.DueDate];
        this.evaPerformanceAppraisalsForm.controls.PeriodDateRange.setValue(_periodDate);
      }
    }
  }

  private builderForm() {
    // Loại Đánh giá
    this.builderEvaluationType = this.selectFactory.builderComboBox({
      label: this._translate.instant('objEval.EvaPerformanceAppraisals.EvaluationType.label'),
      placeholder: this._translate.instant(
        'objEval.EvaPerformanceAppraisals.EvaluationType.placeholder',
      ),
      textField: 'Name',
      valueField: 'ID',
      autoBind: false,
      disabled: false,
      options: { hasFeedBack: true },
      dataSource: [
        { ID: 1, Name: 'Đánh giá tính lương tháng' },
        { ID: 2, Name: 'Đánh giá xét tăng lương định kỳ' },
        { ID: 3, Name: 'Đánh giá hiệu suất công việc' },
        { ID: 4, Name: 'Đánh giá xét khen thưởng' },
        { ID: 5, Name: 'Đánh giá thử việc' },
        { ID: 6, Name: 'Đánh giá quy hoạch kế nhiệm' },
        { ID: 7, Name: 'Đánh giá bổ nhiệm' },
      ],
    });
    // Loại kỳ
    this.builderPeriodType = this.selectFactory.builderComboBox({
      label: this._translate.instant('objEval.EvaPerformanceAppraisals.PeriodType.label'),
      placeholder: this._translate.instant(
        'objEval.EvaPerformanceAppraisals.PeriodType.placeholder',
      ),
      textField: 'PeriodTypeName',
      valueField: 'ID',
      autoBind: false,
      disabled: false,
      options: { hasFeedBack: true },
      dataSource: [
        { ID: 1, PeriodTypeName: 'Kỳ 1' },
        { ID: 2, PeriodTypeName: 'Kỳ 2' },
      ],
    });
    // Trạng thái
    this.builderStatusPublic = this.selectFactory.builderComboBox({
      label: this._translate.instant('objEval.EvaPerformanceAppraisals.Status.label'),
      placeholder: this._translate.instant('objEval.EvaPerformanceAppraisals.Status.placeholder'),
      textField: 'Name',
      valueField: 'ID',
      autoBind: false,
      disabled: false,
      options: { hasFeedBack: true },
      dataSource: [
        { ID: PerformanceAppraisalStatusPublic.Plan, Name: 'Kế hoạch' },
        { ID: PerformanceAppraisalStatusPublic.Public, Name: 'Công khai' },
        { ID: PerformanceAppraisalStatusPublic.InProgress, Name: 'Đang thực hiện' },
        { ID: PerformanceAppraisalStatusPublic.Pending, Name: 'Tạm dừng' },
        { ID: PerformanceAppraisalStatusPublic.Canceled, Name: 'Hủy' },
        { ID: PerformanceAppraisalStatusPublic.Completed, Name: 'Hoàn thành' },
        { ID: PerformanceAppraisalStatusPublic.Published, Name: 'Đã công bố' },
      ],
    });
    // Mô tả
    this.builderDescription = this.inputFactory.builderTextArea({
      label: this._translate.instant('objEval.EvaPerformanceAppraisals.Description.label'),
      rows: 3,
      placeholder: this._translate.instant(
        'objEval.EvaPerformanceAppraisals.Description.placeholder',
      ),
    });

    // Thời gian
    this.builderPeriodDate = this.pickerFactory.builderDateRangePicker({
      label: this._translate.instant('objEval.EvaPerformanceAppraisals.PeriodDate.label'),
      placeholder: [
        this._translate.instant('objEval.EvaPerformanceAppraisals.PeriodDate.placeholder.from'),
        this._translate.instant('objEval.EvaPerformanceAppraisals.PeriodDate.placeholder.to'),
      ],
      disabled: false,
    });
    // Ẩn danh
    this.builderAnonymousAppraisals.builder({
      required: false,
      size: 'small',
    });
    // Lặp lại
    this.builderIsRepeatAppraisals.builder({
      required: false,
      size: 'small',
    });
    // phòng ban áp dụng
    this.builderOrg = this.selectFactory.builderTreeView({
      label: this._translate.instant('objEval.EvaPerformanceAppraisals.ApplyDepartment.label'),
      placeholder: this._translate.instant(
        'objEval.EvaPerformanceAppraisals.ApplyDepartment.placeholder',
      ),
      valueField: 'OrderNumber',
      textField: 'Name',
      childKey: 'ListChild',
      options: {
        hasFeedBack: false,
        checkable: true,
        maxTagCount: 3,
        scrollX: true,
      },
      dataSource: performanceAppraisalsDepartmentDataSource,
      //serverSide: {
      //  urlApi: `${this._vnrModule_Token.apiConfig_Token?.apiUrl}/api/Cat_GetData/GetOrgTreeView`,
      //  method: 'get',
      //},
    });

    // Vị trí áp dụng
    this.builderApplyPosition = this.selectFactory.builderTreeView({
      label: this._translate.instant('objEval.EvaPerformanceAppraisals.ApplyPosition.label'),
      placeholder: this._translate.instant(
        'objEval.EvaPerformanceAppraisals.ApplyPosition.placeholder',
      ),
      valueField: 'ID',
      textField: 'PositionName',
      childKey: 'ListChild',
      options: {
        hasFeedBack: false,
        checkable: true,
        maxTagCount: 3,
        scrollX: true,
      },
      dataSource: performanceAppraisalsPositionDataSource,
      //serverSide: {
      //  urlApi: `${this._vnrModule_Token.apiConfig_Token?.apiUrl}/api/Cat_GetData/GetOrgTreeView`,
      //  method: 'get',
      //},
    });

    // Nhân viên áp dụng
    this.builderApplyEmployee = this.selectFactory.builderTreeView({
      label: this._translate.instant('objEval.EvaPerformanceAppraisals.ApplyEmployee.label'),
      placeholder: this._translate.instant(
        'objEval.EvaPerformanceAppraisals.ApplyEmployee.placeholder',
      ),
      valueField: 'ID',
      textField: 'JoinProfileNameCode',
      childKey: 'ListChild',
      options: {
        hasFeedBack: false,
        checkable: true,
        maxTagCount: 2,
        scrollX: true,
      },
      dataSource: performanceAppraisalsEmployeeDataSource,
    });
    // Tên đợt đánh giá
    this.builderCycleName = this.inputFactory.builderTextBox({
      label: this._translate.instant('objEval.EvaPerformanceAppraisals.AppraisalName.label'),
      placeholder: this._translate.instant(
        'objEval.EvaPerformanceAppraisals.AppraisalName.placeholder',
      ),
      disabled: false,
    });

    // Mẫu phiếu đánh giá
    this.builderAppraisalTemplate = this.selectFactory.builderComboBox({
      label: this._translate.instant('objEval.EvaPerformanceAppraisals.AppraisalTemplate.label'),
      placeholder: this._translate.instant(
        'objEval.EvaPerformanceAppraisals.AppraisalTemplate.placeholder',
      ),
      textField: 'Name',
      valueField: 'ID',
      autoBind: false,
      disabled: false,
      options: { hasFeedBack: true },
      dataSource: [
        { ID: 1, Name: 'Nhân viên' },
        { ID: 2, Name: 'Quản lý' },
        { ID: 3, Name: 'Trưởng phòng' },
        { ID: 4, Name: 'Giám đốc' },
        { ID: 5, Name: 'Tổng giám đốc' },
        { ID: 6, Name: 'Phó giám đốc' },
        { ID: 7, Name: 'Giám đốc' },
      ],
    });

    // Quy trình đánh giá
    this.builderAppraisalProcess = this.selectFactory.builderComboBox({
      label: this._translate.instant('objEval.EvaPerformanceAppraisals.AppraisalProcess.label'),
      placeholder: this._translate.instant(
        'objEval.EvaPerformanceAppraisals.AppraisalProcess.placeholder',
      ),
      textField: 'Name',
      valueField: 'ID',
      autoBind: false,
      disabled: false,
      options: { hasFeedBack: true },
      dataSource: [
        { ID: 1, Name: 'Nhân viên' },
        { ID: 2, Name: 'Quản lý' },
        { ID: 3, Name: 'Trưởng phòng' },
        { ID: 4, Name: 'Giám đốc' },
        { ID: 5, Name: 'Tổng giám đốc' },
        { ID: 6, Name: 'Phó giám đốc' },
        { ID: 7, Name: 'Giám đốc' },
      ],
    });
  }
  protected onModelChangeTime(e) {
    this.evaPerformanceAppraisalsForm.controls.PeriodDateRange.setValue(e[0]);
    this.evaPerformanceAppraisalsForm.controls.PeriodDateRange.setValue(e[1]);
  }
  protected onSubmit() {
    if (this.evaPerformanceAppraisalsForm.invalid) {
      Object.values(this.evaPerformanceAppraisalsForm.controls).forEach((control) => {
        if (control.invalid) {
          control.markAsDirty();
          control.markAsTouched();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
      return;
    }
    if (this.evaPerformanceAppraisalsForm.valid) {
      this.isDisabledBtn = true;
      const param: IPerformanceAppraisalCreate = {
        ID: this._paramEdit ? this._paramEdit.ID : null,
        ...this.evaPerformanceAppraisalsForm.value,
      };
      this._evaPerformanceAppraisalsFacade.createOrUpdateAppraisal(param).subscribe((res) => {
        const message =
          res?.Status === ResponseStatus.SUCCESS
            ? this._translate.instant('common.message.actionSuccess')
            : res?.Message;
        const type = res?.Status === ResponseStatus.SUCCESS ? 'success' : 'error';
        this._commonService.message({ message, type });
        this.isDisabledBtn = false;
        if (res?.Status === ResponseStatus.SUCCESS) {
          this._drawerRef?.close({
            isReloadData: true,
          });
        }
      });
    }
  }

  protected onCancel() {
    this._drawerRef?.close({
      isReloadData: false,
    });
  }

  protected onselectDataItem(item: any, nameControl?: string) {
    // Handle selection changes if needed
  }
}
