import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { VnrGridNewComponent } from '@hrm-frontend-workspace/vnr-module';
import { TranslateModule } from '@ngx-translate/core';
import { EvaluationGridComponent } from '../../../../../../../shared';
import { dataGoalEvaluationSection } from '../../../data/test/goal-evaluation-section.data';
import { IFormSection } from '../../../models/form-builder.model';
import { FormType, EnumFormType } from '../../../models/enums/form-canvas.enum';

@Component({
  selector: 'app-goal-evaluation-section',
  templateUrl: './goal-evaluation-section.component.html',
  styleUrls: ['./goal-evaluation-section.component.scss'],
  standalone: true,
  imports: [CommonModule, TranslateModule, EvaluationGridComponent],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GoalEvaluationSectionComponent implements OnInit, OnChanges {
  @Input() section: IFormSection | any;
  @Input() data: any;
  @Input() formType: FormType = EnumFormType.CREATE;

  @ViewChild('vnrGrid') vnrGrid: VnrGridNewComponent;
  @ViewChild('evaluationGrid') evaluationGrid: EvaluationGridComponent;

  @Output() formValueChange = new EventEmitter<any>();
  protected _edit = EnumFormType.EDIT;
  protected _preview = EnumFormType.PREVIEW;
  protected _create = EnumFormType.CREATE;
  vnrColumGridLocal: any[] = [];
  vnrDataGridLocal: any[] = []; //dataGoalEvaluationSection;
  ngOnChanges(changes: SimpleChanges): void {
    const { section, data } = changes;
    if (data && data.currentValue) {
      this.vnrDataGridLocal = data.currentValue;
    }
    if (section && section.currentValue) {
      this.vnrColumGridLocal = this.section?.properties?.gridConfig?.columns;
      console.log(section);
    }
  }
  ngOnInit() {
    this.initBuilder();
  }
  private initBuilder() {
    // TODO: init builder
  }
  onChangeAddRow(event: any) {
    const newData = this.evaluationGrid?.dataLocal;
    this.formValueChange.emit({
      sectionId: this.section.id,
      data: newData,
      type: 'addRow',
    });
  }

  onChangeAddRowGroup(event: any) {
    const newData = this.evaluationGrid?.dataLocal;
    this.formValueChange.emit({
      sectionId: this.section.id,
      data: newData,
      type: 'addRowGroup',
    });
  }
}
