import { IApiOptionsChangeColumnModel } from '../interfaces/grid-new-change-column.interface';
import { IVnrApiEndpoint } from '../../../common/models/app-api-config.interface';
import { VnRGridNewOptionChangeColumnPosition } from './grid-new-option-change-column.model';

export abstract class AbstractGridOptionChangeColumnBuilder {
  data?: IApiOptionsChangeColumnModel;
  gridPlacement?: 'leftTop' | 'rightTop' | '' = 'rightTop';
  apiRestoreChangeColumn?: IVnrApiEndpoint;
  apiSaveChangeColumn?: IVnrApiEndpoint;
  apiSaveTranslate?: IVnrApiEndpoint;
  modelName?: string = '';
  assemblyName?: string = '';
  tableDB?: string = '';
  isRestoreApi?: boolean = true;
  isDisabledModeViewRecommended?: boolean = false;
  isDisabledModeViewSimple?: boolean = false;
  position?: VnRGridNewOptionChangeColumnPosition = new VnRGridNewOptionChangeColumnPosition();
}
