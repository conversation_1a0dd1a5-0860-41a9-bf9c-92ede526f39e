import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  Inject,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import {
  FormsModule,
  ReactiveFormsModule,
  UntypedFormBuilder,
  UntypedFormGroup,
} from '@angular/forms';
import { VnrLettersAvatarComponent, VnrTagComponent } from '@hrm-frontend-workspace/ui';
import {
  IVnrModule_Token,
  VnrButtonFactory,
  VnrButtonNewBuilder,
  VnrButtonNewComponent,
  VnrComboBoxBuilder,
  VnrComboBoxComponent,
  VnrGridNewBuilder,
  VnrGridNewComponent,
  VNRMODULE_TOKEN,
  VnrToolbarNewBuilder,
  VnrToolbarNewComponent,
  VnrTreeViewBuilder,
  VnrTreeViewComponent,
  vnrUtilities,
} from '@hrm-frontend-workspace/vnr-module';
import { TranslateModule } from '@ngx-translate/core';
import { AIAnalysisComponent } from '../../../../../../shared/components/ai-analysis/ai-analysis.component';
import { gridProposalDefineColumns } from '../../data/column.data';
import {
  proposalDepartmentDataSource,
  proposalPositionDataSource,
} from '../../data/datasource-component.data';
import { gridProposalDataSource } from '../../data/datasource.data';
import {
  proposalGradeFormat,
  proposalGradeTextFormat,
  proposalStatusFormat,
} from '../../data/proposal.data';

@Component({
  selector: 'eva-proposal-grid',
  templateUrl: './eva-proposal-grid.component.html',
  styleUrls: ['./eva-proposal-grid.component.scss'],
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    CommonModule,
    VnrGridNewComponent,
    VnrToolbarNewComponent,
    VnrButtonNewComponent,
    VnrLettersAvatarComponent,
    VnrTagComponent,
    VnrTreeViewComponent,
    VnrComboBoxComponent,
    TranslateModule,
    AIAnalysisComponent,
  ],
})
export class EvaProposalGridComponent implements OnInit, OnChanges {
  @Output() reloadTabCount: EventEmitter<boolean> = new EventEmitter<boolean>();
  @ViewChild('vnrGrid', { static: true }) gridControl: VnrGridNewComponent;

  private _dataFormSearch: any = {};
  private _statusFormat = proposalStatusFormat;
  private _gradeFormat = proposalGradeFormat;
  private _gradeTextFormat = proposalGradeTextFormat;
  private _btnFactory: VnrButtonFactory = VnrButtonFactory.init();
  protected gridName = 'eva-proposal-grid';

  protected selectedItem = [];
  protected builderOrg: VnrTreeViewBuilder = new VnrTreeViewBuilder();
  protected builderPosition: VnrComboBoxBuilder = new VnrComboBoxBuilder();
  protected builderButtonApprove: VnrButtonNewBuilder;
  protected formGroup: UntypedFormGroup = this._formBuider.group({
    department: [''],
    position: [''],
  });

  protected builderGrid: VnrGridNewBuilder;
  protected builderToolbar: VnrToolbarNewBuilder;
  protected builderButtonCustom: VnrButtonNewBuilder;
  protected builderButtonAskAI: VnrButtonNewBuilder;
  protected dataLocal = gridProposalDataSource;
  protected columns = gridProposalDefineColumns;
  constructor(
    private _formBuider: UntypedFormBuilder,
    @Inject(VNRMODULE_TOKEN) private _vnrModule_Token: IVnrModule_Token,
  ) {}
  ngOnInit() {
    this.buildButton();
    this.builderComponent();
    this.initGridData();
  }
  ngOnChanges(changes: SimpleChanges): void {
    const tabFilterChange = changes['tabFilter'];
    if (tabFilterChange && !tabFilterChange.firstChange) {
      this.initGridData();
    }
  }
  private initGridData() {
    this.builderGridComponent();
    this.builderToolbarComponent();
  }
  private builderComponent() {
    this.builderOrg.builder({
      label: '',
      placeholder: 'objEval.Appraisals.selectAllOrg',
      valueField: 'OrderNumber',
      textField: 'Name',
      childKey: 'ListChild',
      options: {
        hasFeedBack: false,
        checkable: true,
        maxTagCount: 1,
        scrollX: true,
      },
      dataSource: proposalDepartmentDataSource,
      //serverSide: {
      //  urlApi: `${this._vnrModule_Token.apiConfig_Token?.apiUrl}/api/Cat_GetData/GetOrgTreeView`,
      //  method: 'get',
      //},
    });
    this.builderPosition.builder({
      label: '',
      placeholder: 'objEval.Appraisals.selectAllPosition',
      textField: 'PositionNameAndCode',
      valueField: 'ID',
      //serverSide: {
      //  urlApi: `${this._vnrModule_Token.apiConfig_Token?.apiUrl}/api/Att_GetData/GetMultiPosition`,
      //  method: 'GET',
      //  data: { text: '', TextField: 'PositionName' },
      //},
      dataSource: proposalPositionDataSource,
      options: {
        allowValueObject: true,
        hasFeedBack: false,
      },
    });
    this.builderButtonApprove = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'objEval.Appraisals.btnAprove',
      options: {
        classNames: ['btn-approve'],
        style: 'green',
        icon: {
          fontIcon: 'check',
        },
      },
    });
    this.builderButtonCustom = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: 'custom',
    });
  }
  private builderToolbarComponent() {
    this.builderToolbar = new VnrToolbarNewBuilder({
      isShowConfig: true,
      gridName: this.gridName,
      isSupperAdmin: true,
      gridRef: this.gridControl,
      permission: '',
      screenName: 'appraisals-execution-list-grid',
      storeName: '',
      options: {
        configButtonChangeColumn: {
          isShow: true,
          isConfigMenuDisplay: true,
          isChangeColumnNew: true,
          isDisabled: false,
          configButtonSetting: {
            isShow: true,
            isDisabled: true,
          },
        },
        configButtonExport: {
          isShowBtnExcelAll: true,
          isShowBtnWord: true,
          isShowBtnExcelByTemplate: true,
        },
        configButtonDelete: {
          isShow: true,
          isDisabled: false,
        },
        configQuickSearch: {
          textPlaceHolder: 'Tìm kiếm theo tên, mã...',
          searchKey: 'ProfileName',
        },
        isSetBackground: false,
        buttonMoreFlexDirection: 'inherit',
        isExpanded: false,
      },
    });
  }
  private buildButton() {
    this.builderButtonAskAI = this._btnFactory.builder({
      id: vnrUtilities.generateKey(10),
      action: '',
      text: 'objEval.EvaProposalList.AskAI',
      options: {
        style: 'default',
      },
    });
  }

  private builderGridComponent() {
    this.builderGrid = new VnrGridNewBuilder({
      options: {
        configHeightGrid: {
          isAllowCalcRowHeight: true,
        },
        configIndexColumn: {
          isShow: true,
          width: 40,
        },
        configSelectable: {
          columnKey: 'Id',
        },
        configShowHide: {
          isPageExpand: false,
          isShowDelete: false,
          isShowEdit: false,
        },
        configCommandColumn: {
          isEnabledMenuAction: true,
        },
        configGroupable: {
          isEnabled: false,
        },
      },
    });
  }

  protected getSelectedID($event: any) {
    this.selectedItem = $event;
  }
  protected getDataItem($event: any) {}
  protected onOpenDetail($event: any) {}
  protected onGridEdit($event: any) {}
  protected onGridDelete($event: any) {}
  protected onGridViewDetail($event: any) {}
  protected onGridCellClick($event: any) {}
  public setDataFilter(data: any): void {
    this._dataFormSearch = data;
    this.gridControl.setDataFilter(data);
  }
  public reloadGridData(): void {
    this.gridControl.vnrReadGrid();
  }
  public getSelectedIDs() {
    return this.selectedItem || [];
  }
  protected getColorStatus(value: any): string {
    return this._statusFormat[value];
  }
  protected getColorGrade(value: any): string {
    return this._gradeFormat[value];
  }
  protected getColorGradeText(value: any): string {
    return this._gradeTextFormat[value];
  }
  protected onChangeFitler($event: any) {
    this.reloadTabCount.emit();
  }

  protected onModelChangeOrg($event: any) {
    this.setDataFilter({ OrderNumber: $event?.join() });
    this.reloadGridData();
    this.reloadTabCount.emit();
  }
  protected onModelChangePosition($event: any) {
    this.setDataFilter({ PositionId: $event });
    this.reloadGridData();
    this.reloadTabCount.emit();
  }
  protected onAskAI($event: any): void {
    //this.isShowAIAnalysis = true;
  }
}
