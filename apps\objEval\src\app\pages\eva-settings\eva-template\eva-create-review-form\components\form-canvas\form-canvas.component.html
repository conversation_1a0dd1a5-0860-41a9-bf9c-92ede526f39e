<div class="form-canvas">
  @if (formType === _preview) {
  <div class="form-canvas-preview-select-employee">
    <vnr-select-emp
      class="select-control"
      [builder]="SelectEmployeeBuilder"
      [isShowCheckAll]="false"
      [vnrMode]="'default'"
      [ngModelOptions]="{ standalone: true }"
      [(ngModel)]="staffSelected"
      (ngModelChange)="onChangeProfileID($event)"
      (vnrSelectedItem)="onSelectedItem($event)"
      [usingDataLocalTest]="true"
    ></vnr-select-emp>
  </div>
  } @if (formType === _create && formTemplate?.sections && formTemplate?.sections.length > 0) {
  <div class="form-canvas-header">
    <div class="form-info-alert">
      <div class="alert-content">
        <span class="alert-icon">
          <i nz-icon nzType="info-circle" nzTheme="outline"></i>
        </span>
        <span class="alert-text">
          <PERSON><PERSON> liệu đư<PERSON>c tự động hiển thị theo quy trình đánh giá của cá nhân được đánh giá. Chỉ thực
          hiện thiết lập thông tin cần hiển thị
        </span>
      </div>
    </div>
  </div>
  }
  <div class="form-canvas-content">
    <!-- Container cho các section -->
    <div
      class="sections-container"
      [class.preview-mode]="formType !== _create"
      (dragover)="isDragDropEnabled() && onDragOver($event)"
      (drop)="onDropSection($event)"
      [ngClass]="{
        'sections-container-drag-over': formTemplate?.sections && formTemplate?.sections.length > 0,
        'justify-content-center': formTemplate?.sections && formTemplate?.sections.length === 0
      }"
    >
      @if(formTemplate?.sections && formTemplate?.sections.length === 0){
      <div
        class="form-canvas-content-header d-flex flex-column align-items-center justify-content-center"
      >
        <div class="form-canvas-content-header-title">
          <h5>Bắt đầu tạo mẫu!</h5>
        </div>
        <div class="form-canvas-content-header-description">
          <div
            class="form-canvas-content-header-description-icon d-flex align-items-center justify-content-center"
          >
            <i class="fa-solid fa-hand-pointer"></i>
          </div>
          <div class="form-canvas-content-header-description-text">
            Kéo và thả thành phần từ cột trái vào đây
          </div>
        </div>
      </div>
      }
      <form [formGroup]="formGroup" nz-form *ngIf="formGroup">
        <!-- Render từng section -->
        <div
          *ngFor="let section of formTemplate?.sections"
          class="section-wrapper"
          [id]="'section-' + section.id"
          [class.selected]="isSectionSelected(section.id)"
          [class.preview-mode]="formType !== _create"
          [attr.draggable]="isDragDropEnabled()"
          (dragstart)="onDragStart($event, section)"
          (dragenter)="isDragDropEnabled() && onDragEnter($event, section)"
          (dragleave)="isDragDropEnabled() && onDragLeave($event)"
          (drop)="isDragDropEnabled() && onDragDrop($event, section)"
          (dragend)="onDragEnd($event)"
          (click)="onSectionClick(section)"
          (keydown.enter)="onSectionKeyEvent($event, section)"
          (keydown.space)="onSectionKeyEvent($event, section)"
          [formGroupName]="section.id"
          tabindex="0"
          role="region"
          [attr.aria-label]="section.title"
        >
          <!-- Section header -->
          <div class="section-header">
            <div class="section-title">
              <h3>{{ section.title }}</h3>
              <p *ngIf="section.description">{{ section.description }}</p>
            </div>

            <!-- Action buttons -->
            <div class="section-actions" *ngIf="formType === _create">
              <vnr-button-new
                [builder]="builderButtonEdit"
                (click)="onEditClick(section)"
                (keydown.enter)="onEditKeyEvent($event, section)"
              ></vnr-button-new>
              <vnr-button-new
                [builder]="builderButtonDelete"
                (click)="onRemoveSectionClick(section.id, $event)"
                (keydown.enter)="onRemoveKeyEvent($event, section.id)"
              ></vnr-button-new>
            </div>
          </div>

          <!-- Section content -->
          <div class="section-content" [ngSwitch]="section.type">
            <!-- Thông tin đợt đánh giá -->
            <app-overview-section
              *ngSwitchCase="'evaluation-period'"
              [section]="section"
              [formType]="formType"
            ></app-overview-section>

            <!-- Overview Section -->
            <app-overview-section
              *ngSwitchCase="'overview'"
              [section]="section"
              [formType]="formType"
              [formGroup]="getSectionFormGroup(section.id)"
              [data]="section.data"
            ></app-overview-section>

            <!-- Process Section -->
            <app-process-section
              *ngSwitchCase="'process'"
              [section]="section"
              [formType]="formType"
              [formGroup]="getSectionFormGroup(section.id)"
            ></app-process-section>

            <!-- Personal Goals Section -->
            <app-personal-goals-section
              *ngSwitchCase="'personal-goals'"
              [section]="section"
              [formType]="formType"
              [formGroup]="getSectionFormGroup(section.id)"
            ></app-personal-goals-section>

            <!-- Goal Evaluation Section -->
            <app-goal-evaluation-section
              *ngSwitchCase="'goal-evaluation'"
              [section]="section"
              [formType]="formType"
              [formGroup]="getSectionFormGroup(section.id)"
              [data]="section.data"
              (formValueChange)="onFormValueChange($event)"
            ></app-goal-evaluation-section>

            <!-- Competency Evaluation Section -->
            <app-competency-evaluation-section
              *ngSwitchCase="'competency-evaluation'"
              [section]="section"
              [formType]="formType"
              [formGroup]="getSectionFormGroup(section.id)"
              [data]="section.data"
              (formValueChange)="onFormValueChange($event)"
            ></app-competency-evaluation-section>

            <!-- Evaluation 360 Section -->
            <app-evaluation-360-section
              *ngSwitchCase="'evaluation-360'"
              [section]="section"
              [formType]="formType"
              [formGroup]="getSectionFormGroup(section.id)"
              [data]="section.data"
              (formValueChange)="onFormValueChange($event)"
            ></app-evaluation-360-section>

            <!-- Custom Evaluation Section -->
            <app-custom-evaluation-section
              *ngSwitchCase="'custom-evaluation'"
              [section]="section"
              [formType]="formType"
              [formGroup]="getSectionFormGroup(section.id)"
              [data]="section.data"
              (formValueChange)="onFormValueChange($event)"
            ></app-custom-evaluation-section>

            <!-- Total Score Section -->
            <app-total-score-section
              *ngSwitchCase="'total-score'"
              [section]="section"
              [formType]="formType"
              [formGroup]="getSectionFormGroup(section.id)"
            ></app-total-score-section>

            <!-- General Review Comment Section -->
            <app-general-review-comment-section
              *ngSwitchCase="'general-review-comment'"
              [section]="section"
              [formType]="formType"
              [formGroup]="getSectionFormGroup(section.id)"
            ></app-general-review-comment-section>

            <!-- Signature and Approval Section -->
            <app-signature-and-approval-section
              *ngSwitchCase="'signature-and-approval'"
              [section]="section"
              [formType]="formType"
              [formGroup]="getSectionFormGroup(section.id)"
            ></app-signature-and-approval-section>
            <!-- Default case -->
            <div *ngSwitchDefault class="section-default">
              {{ 'common.unsupportedSectionType' | translate }}
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
