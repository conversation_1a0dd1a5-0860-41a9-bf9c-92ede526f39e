<vnr-grid-new
  class="grid-new"
  #vnrGrid
  [builder]="builderGrid"
  [gridName]="gridName"
  [dataLocal]="dataLocal"
  [columns]="columns"
  [isSupperAdmin]="isSupperAdmin"
  [defaultColumnTemplate]="tplCustomTemplateByColumn"
></vnr-grid-new>
<ng-template #tplCustomTemplateByColumn let-dataItem let-column="columnItem" let-field="field">
  <ng-container [ngSwitch]="column['Name']">
    <span *ngSwitchCase="'TotalTarget'">
      {{ dataItem['TotalTarget'] | targetFormat : dataItem['Unit'] }}
    </span>
    <span *ngSwitchCase="'DoneTarget'">
      {{ dataItem['DoneTarget'] | targetFormat : dataItem['Unit'] }}
    </span>
    <span *ngSwitchDefault>
      {{ dataItem[column['Name']] }}
    </span>
  </ng-container>
</ng-template>
