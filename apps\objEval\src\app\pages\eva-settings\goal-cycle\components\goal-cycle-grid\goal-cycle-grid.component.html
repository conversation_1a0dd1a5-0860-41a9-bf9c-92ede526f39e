<div class="form-header" nz-form [formGroup]="formGroup">
  <vnr-toolbar-new [builder]="builderToolbar">
    <vnr-button-new
      [builder]="builderButtonAddGoalCycle"
      (vnrClick)="onAddGoalCycle($event)"
      rightToolbar
    ></vnr-button-new>
  </vnr-toolbar-new>
</div>
<vnr-grid-new
  #vnrGrid
  [builder]="builderGrid"
  [gridName]="gridName"
  [dataLocal]="dataLocal"
  [columns]="columns"
  [dataFormSearch]="dataFormSearch"
  [columnTemplates]="listColumnTemplates"
  (getSelectedID)="getSelectedID($event)"
  (getDataItem)="getDataItem($event)"
  (vnrDoubleClick)="onOpenDetail($event)"
  (vnrEdit)="onGridEdit($event)"
  (vnrDelete)="onGridDelete($event)"
  (vnrViewDetails)="onGridViewDetail($event)"
  (vnrCellClick)="onGridCellClick($event)"
>
</vnr-grid-new>

<ng-template #templateLastUpdatedBy let-dataItem>
  <span>
    <div class="goal-cycle-grid-profile">
      <app-vnr-letters-avatar
        class="goal-cycle-grid-profile__image"
        [avatarName]="dataItem['LastUpdatedBy']"
        [circular]="true"
        [width]="32"
        [src]="dataItem['AvatarLastUpdatedBy']"
      ></app-vnr-letters-avatar>
      <div class="goal-cycle-grid-profile__name">
        {{ dataItem['LastUpdatedBy'] }}
      </div>
    </div>
  </span>
</ng-template>
