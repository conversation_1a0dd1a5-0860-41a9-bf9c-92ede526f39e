
> @hrm-frontend-workspace/source@0.0.0 start
> node scripts/setup-env.js --devRemotes=dashboard,objEval

Parsed Parameter: devRemotes = "dashboard,objEval"

[36m[1m📄 Using configuration from: C:\SandboxShare\hrm-frontend-workspace\scripts\configs\development.config[0m

[32m[1m✅ ✨ Configuration setup completed for development environment![0m

[36m[1m🚀 Configuring remotes: dashboard, objEval[0m

[36m[1m✨ No changes to remotes, current configuration is already: dashboard, objEval[0m

[36m[1m🚀 Starting application:
[2mcross-env NODE_OPTIONS="--max-old-space-size=16384" npx nx run shell:serve:development --devRemotes=dashboard,objEval --port=4200 --host=0.0.0.0 --open[0m[0m

[36m[1m🔍 Starting integrated build monitor for project: serve[0m
[36m[serve] Build monitor started...[0m
[36m[serve] Watching for changes...[0m
[36m[serve] Source directory: C:\SandboxShare\hrm-frontend-workspace\apps\serve\src[0m
[36m[serve] Output directory: C:\SandboxShare\hrm-frontend-workspace\dist\apps\serve[0m
[36m[serve] Also watching libs directory: C:\SandboxShare\hrm-frontend-workspace\libs[0m
[36m[serve] And compiled libs in: C:\SandboxShare\hrm-frontend-workspace\dist\libs[0m
[31m[serve] Source directory doesn't exist: C:\SandboxShare\hrm-frontend-workspace\apps\serve\src[0m
[36m[serve] Watching libs directory for changes[0m
[36m[serve] Watching dist/libs directory for changes[0m
[31m[serve] Dist directory doesn't exist yet: C:\SandboxShare\hrm-frontend-workspace\dist\apps\serve[0m
[33m[serve] Will start monitoring once the first build is complete[0m

[36m[1m🔄 Starting integrated build monitor for remote: dashboard[0m
[36m[dashboard] Build monitor started...[0m
[36m[dashboard] Watching for changes...[0m
[36m[dashboard] Source directory: C:\SandboxShare\hrm-frontend-workspace\apps\dashboard\src[0m
[36m[dashboard] Output directory: C:\SandboxShare\hrm-frontend-workspace\dist\apps\dashboard[0m
[36m[dashboard] Also watching libs directory: C:\SandboxShare\hrm-frontend-workspace\libs[0m
[36m[dashboard] And compiled libs in: C:\SandboxShare\hrm-frontend-workspace\dist\libs[0m
[36m[dashboard] Watching libs directory for changes[0m
[36m[dashboard] Watching dist/libs directory for changes[0m
[31m[dashboard] Dist directory doesn't exist yet: C:\SandboxShare\hrm-frontend-workspace\dist\apps\dashboard[0m
[33m[dashboard] Will start monitoring once the first build is complete[0m

[36m[1m🔄 Starting integrated build monitor for remote: objEval[0m
[36m[objEval] Build monitor started...[0m
[36m[objEval] Watching for changes...[0m
[36m[objEval] Source directory: C:\SandboxShare\hrm-frontend-workspace\apps\objEval\src[0m
[36m[objEval] Output directory: C:\SandboxShare\hrm-frontend-workspace\dist\apps\objEval[0m
[36m[objEval] Also watching libs directory: C:\SandboxShare\hrm-frontend-workspace\libs[0m
[36m[objEval] And compiled libs in: C:\SandboxShare\hrm-frontend-workspace\dist\libs[0m
[36m[objEval] Watching libs directory for changes[0m
[36m[objEval] Watching dist/libs directory for changes[0m

> nx run shell:serve:development --devRemotes=dashboard,objEval --port=4200 --host=0.0.0.0 --open

[7m[1m[36m NX [39m[22m[27m [1mStarting module federation dev-server for [1mshell[22m[1m with 2 remotes[22m
[7m[1m[36m NX [39m[22m[27m [1mStarting static remotes proxies...[22m
[7m[1m[36m NX [39m[22m[27m [1mStatic remotes proxies started successfully[22m
[1m[33m[ Module Federation Manifest Plugin ] Warn[39m[22m Manifest will use absolute path resolution via its host at runtime, reason: publicPath='auto'
[1m[33m[ Module Federation Manifest Plugin ] Warn[39m[22m Manifest will use absolute path resolution via its host at runtime, reason: publicPath='auto'
[1m[33m[ Module Federation Manifest Plugin ] Warn[39m[22m Manifest will use absolute path resolution via its host at runtime, reason: publicPath='auto'
[7m[1m[36m NX [39m[22m[27m [1mAll remotes started, server ready at http://localhost:4200[22m
[37m[0m[0m[39m
[37m[0m[1mInitial chunk files[22m                                                                                    [2m | [22m[1mNames[22m                               [2m | [22m [1mRaw size[22m[0m[39m
[37m[0m[32mpolyfills.js[39m[37m                                                                                           [2m | [22m[2mpolyfills[22m                           [2m | [22m[36m467.75 kB[39m[37m[2m | [22m[0m[39m
[37m[0m[32mremoteEntry.mjs[39m[37m                                                                                        [2m | [22m[2mdashboard[22m                           [2m | [22m[36m356.73 kB[39m[37m[2m | [22m[0m[39m
[37m[0m[32mstyles.css, styles.js[39m[37m                                                                                  [2m | [22m[2mstyles[22m                              [2m | [22m[36m353.07 kB[39m[37m[2m | [22m[0m[39m
[37m[0m[32mvendor.js[39m[37m                                                                                              [2m | [22m[2mvendor[22m                              [2m | [22m[36m298.74 kB[39m[37m[2m | [22m[0m[39m
[37m[0m[32mmain.js[39m[37m                                                                                                [2m | [22m[2mmain[22m                                [2m | [22m [36m57.05 kB[39m[37m[2m | [22m[0m[39m
[37m[0m[0m[39m
[37m[0m[1m [22m                                                                                                      [2m | [22m[1mInitial total[22m                       [2m | [22m  [1m1.53 MB[22m[0m[39m
[37m[0m[0m[39m
[37m[0m[1mLazy chunk files[22m                                                                                       [2m | [22m[1mNames[22m                               [2m | [22m [1mRaw size[22m[0m[39m
[37m[0m[32mnode_modules_hrm-frontend-workspace_core_fesm2022_hrm-frontend-workspace-core_mjs.js[39m[37m                   [2m | [22m[2m-[22m                                   [2m | [22m [36m28.58 MB[39m[37m[2m | [22m[0m[39m
[37m[0m[32mnode_mod[39m
[37mules_angular_core_fesm2022_core_mjs.js[39m[37m                                                         [2m | [22m[2m-[22m                                   [2m | [22m  [36m1.68 MB[39m[37m[2m | [22m[0m[39m
[37m[0m[32mdefault-node_modules_angular_common_fesm2022_common_mjs.js[39m[37m                                             [2m | [22m[2m-[22m                                   [2m | [22m[36m328.36 kB[39m[37m[2m | [22m[0m[39m
[37m[0m[32mdefault-node_modules_angular_router_fesm2022_router_mjs.js[39m[37m                                             [2m | [22m[2m-[22m                                   [2m | [22m[36m327.66 kB[39m[37m[2m | [22m[0m[39m
[37m[0m[32mdefault-node_modules_angular_animations_fesm2022_browser_mjs.js[39m[37m                                        [2m | [22m[2m-[22m                                   [2m | [22m[36m182.12 kB[39m[37m[2m | [22m[0m[39m
[37m[0m[32mdefault-node_modules_angular_common_fesm2022_http_mjs.js[39m[37m                                               [2m | [22m[2m-[22m                                   [2m | [22m[36m135.00 kB[39m[37m[2m | [22m[0m[39m
[37m[0m[32mdefault-node_modules_angular_platform-browser_fesm2022_platform-browser_mjs.js[39m[37m                         [2m | [22m[2m-[22m                                   [2m | [22m[36m101.25 kB[39m[37m[2m | [22m[0m[39m
[37m[0m[32mnode_modules_angular_core_fesm2022_primitives_event-dispatch_mjs.js[39m[37m                                    [2m | [22m[2m-[22m                                   [2m | [22m [36m75.67 kB[39m[37m[2m | [22m[0m[39m
[37m[0m[32mdefault-node_modules_angular_animations_fesm2022_animations_mjs.js[39m[37m                                     [2m | [22m[2m-[22m                                   [2m | [22m [36m48.56 kB[39m[37m[2m | [22m[0m[39m
[37m[0m[32mdefault-node_modules_rxjs_dist_esm_internal_BehaviorSubject_js-node_modules_rxjs_dist_esm_int-c6b1d5.js[39m[37m[2m | [22m[2m-[22m                                   [2m | [22m [36m39.56 kB[39m[37m[2m | [22m[0m[39m
[37m[0m[32mdefault-no[39m[37mde_modules_rxjs_dist_esm_internal_observable_innerFrom_js-node_modules_rxjs_dist_es-f32a11.js[39m[37m[2m | [22m[2m-[22m                                   [2m | [22m [36m37.84 kB[39m[37m[2m | [22m[0m[39m
[37m[0m[32mdefault-node_modules_rxjs_dist_esm_internal_observable_of_js-node_modules_rxjs_dist_esm_inter-d7ec9d.js[39m[37m[2m | [22m[2m-[22m                                   [2m | [22m [36m32.82 kB[39m[37m[2m | [22m[0m[39m
[37m[0m[32mdefault-node_modules_rxjs_dist_esm_internal_Observable_js.js[39m[37m                                           [2m | [22m[2m-[22m                                   [2m | [22m [36m26.67 kB[39m[37m[2m | [22m[0m[39m
[37m[0m[32mnode_modules_angular_core_fesm2022_primitives_signals_mjs.js[39m[37m                                           [2m | [22m[2m-[22m                                   [2m | [22m [36m25.26 kB[39m[37m[2m | [22m[0m[39m
[37m[0m[32mdefault-node_modules_angular_core_fesm2022_rxjs-interop_mjs.js[39m[37m                                         [2m | [22m[2m-[22m                                   [2m | [22m [36m16.42 kB[39m[37m[2m | [22m[0m[39m
[37m[0m[32mcommon.js[39m[37m                                                                                              [2m | [22m[2mcommon[22m                              [2m | [22m [36m15.81 kB[39m[37m[2m | [22m[0m[39m
[37m[0m[32mnode_modules_rxjs_dist_esm_internal_Subject_js-node_modules_rxjs_dist_esm_internal_operators_-66cf15.js[39m[37m[2m | [22m[2m-[22m                                   [2m | [22m [36m10.46 kB[39m[37m[2m | [22m[0m[39m
[37m[0m[32mapps_dashboard_src_app_pages_portal_portal-dashboard_module_ts.js[39m[37m                                      [2m | [22m[2mpages-portal-portal-dashboard-module[22m[2m | [22m  [36m6.42 kB[39m[37m[2m | [22m[0m[39m
[37m[0m[32mapps_dashboard_src_bootstrap_ts.js[39m[37m                                                                     [2m | [22m[2mbootstrap[22m                           [2m | [22m  [36m4.46 kB[39m[37m[2m | [22m[0m[39m
[37m[0m                 [39m[37m                                                                                      [2m | [22m[2m-[22m                                   [2m | [22m  [36m0 bytes[39m[37m[2m | [22m[0m[39m
[37m[0m                                                                                                       [2m | [22m[2m-[22m                                   [2m | [22m  [36m0 bytes[39m[37m[2m | [22m[0m[39m
[37m[0m                                                                                                       [2m | [22m[2m-[22m                                   [2m | [22m  [36m0 bytes[39m[37m[2m | [22m[0m[39m
[37m[0m                                                                                                       [2m | [22m[2m-[22m                                   [2m | [22m  [36m0 bytes[39m[37m[2m | [22m[0m[39m
[37m[0m                                                                                                       [2m | [22m[2m-[22m                                   [2m | [22m  [36m0 bytes[39m[37m[2m | [22m[0m[39m
[37m[0m                                                                                                       [2m | [22m[2m-[22m                                   [2m | [22m  [36m0 bytes[39m[37m[2m | [22m[0m[39m
[37m[0m                                                                                                       [2m | [22m[2m-[22m                                   [2m | [22m  [36m0 bytes[39m[37m[2m | [22m[0m[39m
[37m[0m                                                                                                       [2m | [22m[2m-[22m                                   [2m | [22m  [36m0 bytes[39m[37m[2m | [22m[0m[39m
[37m[0m[0m[39m
[37m[0mBuild at: [1m[37m2025-07-04T02:10:31.083Z[39m[37m[22m - Hash: [1m[37m7d24228bce3581d0[39m[37m[22m - Time: [1m[37m88025[39m[37m[22mms[0m[39m
[37m[39m
[37m** Angular Live Development Server is listening on localhost:4201, open your browser on http://localhost:4201/ **[39m
[37m[39m
[37m[39m
[37m[92m√[39m[37m Compiled successfully.[39m
[37m[0m[0m[39m
[37m[0m[1mInitial chunk files[22m  [2m | [22m[1mNames[22m    [2m | [22m [1mRaw size[22m[0m[39m
[37m[0m[32mpolyfills.js[39m[37m         [2m | [22m[2mpolyfills[22m[2m | [22m[36m986.30 kB[39m[37m[2m | [22m[0m[39m
[37m[0m[32mremoteEntry.mjs[39m[37m      [2m | [22m[2mobjEval[22m  [2m | [22m[36m881.54 kB[39m[37m[2m | [22m[0m[39m
[37m[0m[32mstyles.css, styles.js[39m[37m[2m | [22m[2mstyles[22m   [2m | [22m[36m871.60 kB[39m[37m[2m | [22m[0m[39m
[37m[0m[32mmain.js[39m[37m              [2m | [22m[2mmain[22m     [2m | [22m[36m582.14 kB[39m[37m[2m | [22m[0m[39m
[37m[0m[0m[39m
[37m[0m252 unchanged chunks[0m[39m
[37m[0m[0m[39m
[37m[0mBuild at: [1m[37m2025-07-04T02:10:42.903Z[39m[37m[22m - Hash: [1m[37mda725a8cf7ae0165[39m[37m[22m - Time: [1m[37m99846[39m[37m[22mms[0m[39m
[37m[39m
[37m** Angular Live Development Server is listening on localhost:4203, open your browser on http://localhost:4203/ **[39m
[37m[39m
[37m[39m
[37m[92m√[39m[37m Compiled successfully.[39m
[37m[0m[0m[39m
[37m[0m[1mInitial chunk files[22m[2m | [22m[1mNames[22m  [2m | [22m[1mRaw size[22m[0m[39m
[37m[0m[32mscripts.js[39m[37m         [2m | [22m[2mscripts[22m[2m | [22m[36m17.79 MB[39m[37m[2m | [22m[0m[39m
[37m[0m[0m[39m
[37m[0m7 unchanged chunks[0m[39m
[37m[0m[0m[39m
[37m[0mBuild at: [1m[37m2025-07-04T02:10:46.752Z[39m[37m[22m - Hash: [1m[37m6885ced0139a38e4[39m[37m[22m - Time: [1m[37m103700[39m[37m[22mms[0m[39m
[37m[39m
[37m** Angular Live Development Server is listening on 0.0.0.0:4200, open your browser on http://localhost:4200/ **[39m
[37m[39m
[37m[39m
[37m[92m√[39m[37m Compiled successfully.[39m
[37m[0m[0m[39m
[37m[0m[0m[39m
[37m[0m[0m[39m
[37m[0m8 unchanged chunks[0m[39m
[37m[0m[0m[39m
[37m[0mBuild at: [1m[37m2025-07-04T02:10:55.229Z[39m[37m[22m - Hash: [1m[37m6885ced0139a38e4[39m[37m[22m - Time: [1m[37m7982[39m[37m[22mms[0m[39m
[37m[39m
[37m[92m√[39m[37m Compiled successfully.[39m
[37m[0m[0m[39m
[37m[0m[0m[39m
[37m[0m[0m[39m
[37m[0m32 unchanged chunks[0m[39m
[37m[0m[0m[39m
[37m[0mBuild at: [1m[37m2025-07-04T02:50:02.971Z[39m[37m[22m - Hash: [1m[37m7d24228bce3581d0[39m[37m[22m - Time: [1m[37m15958[39m[37m[22mms[0m[39m
[37m[39m
[37m[92m√[39m[37m Compiled successfully.[39m
[37m[0m[0m[39m
[37m[0m[1mInitial chunk files[22m                                      [2m | [22m[1mNames[22m                         [2m | [22m [1mRaw size[22m[0m[39m
[37m[0m[32mpolyfills.js[39m[37m                                             [2m | [22m[2mpolyfills[22m                     [2m | [22m[36m986.30 kB[39m[37m[2m | [22m[0m[39m
[37m[0m[32mremoteEntry.mjs[39m[37m                                          [2m | [22m[2mobjEval[22m                       [2m | [22m[36m881.54 kB[39m[37m[2m | [22m[0m[39m
[37m[0m[32mstyles.css, styles.js[39m[37m                                    [2m | [22m[2mstyles[22m                        [2m | [22m[36m871.60 kB[39m[37m[2m | [22m[0m[39m
[37m[0m[32mmain.js[39m[37m                                                  [2m | [22m[2mmain[22m                          [2m | [22m[36m582.14 kB[39m[37m[2m | [22m[0m[39m
[37m[0m[0m[39m
[37m[0m[1mLazy chunk files[22m                                         [2m | [22m[1mNames[22m                         [2m | [22m [1mRaw size[22m[0m[39m
[37m[0m[32mapps_objEval_src_app_pages_eva-goal_eva-goal_module_ts.js[39m[37m[2m | [22m[2mpages-eva-goal-eva-goal-module[22m[2m | [22m  [36m1.72 MB[39m[37m[2m | [22m[0m[39m
[37m[0m[0m[39m
[37m[0m251 unchanged chunks[0m[39m
[37m[0m[0m[39m
[37m[0mBuild at: [1m[37m2025-07-04T02:50:05.071Z[39m[37m[22m - Hash: [1m[37m189d04102ba1388b[39m[37m[22m - Time: [1m[37m36614[39m[37m[22mms[0m[39m
[37m[39m
[37m[92m√[39m[37m Compiled successfully.[39m
