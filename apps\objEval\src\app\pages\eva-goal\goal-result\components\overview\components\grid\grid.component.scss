::ng-deep {
  .ant-segmented {
    height: fit-content;
    margin: auto !important;
    border-radius: 6px !important;
    padding: 6px !important;
  }
  .ant-segmented-group {
    align-items: center !important;
  }
  label.ant-segmented-item {
    margin-bottom: 0 !important;
  }

  .card {
    margin-bottom: 0px;
  }
  .toolbar {
    background-color: #fff;
  }
  .wrapper-vnrTag {
    .ant-tag {
      min-height: 24px;
      border-radius: 2px;
      font-weight: 500;
      font-size: 13px;
      border: 0;
      line-height: 24px;
    }
  }
  .k-header.grid__header--bordered.k-table-th.k-grid-draggable-header {
    border-top: 3px solid #007bff !important;
  }
}