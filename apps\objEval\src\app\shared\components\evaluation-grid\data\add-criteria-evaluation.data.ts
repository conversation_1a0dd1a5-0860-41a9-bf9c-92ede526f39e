export interface ICriteriaEvaluation {
  ID: string;
  CriteriaName: string;
  weight: number;
  [key: string]: any;
}

export const ADD_CRITERIA_EVALUATION_DATA: ICriteriaEvaluation[] = [
  {
    ID: 'd58f1107-5427-45bb-8f33-0b42e2aaa3b3',
    CriteriaName: '<PERSON><PERSON> thủ kỷ luật, quy định, gi<PERSON> làm việ<PERSON>, sự tập trung trong công việc',
    weight: Math.floor(Math.random() * (20 - 5 + 1)) + 5,
  },
  {
    ID: 'd58f1107-5427-45bb-8f33-0b42e2aaa3b4',
    CriteriaName: '<PERSON><PERSON><PERSON> việc tận tâm hết việc thay vì hết giờ. Không làm Job hai',
    weight: Math.floor(Math.random() * (20 - 5 + 1)) + 5,
  },
  {
    ID: 'd58f1107-5427-45bb-8f33-0b42e2aaa3b5',
    CriteriaName: '<PERSON><PERSON> thần trách nhiệm đối với trò và sự mẫn cán',
    weight: Math.floor(Math.random() * (20 - 5 + 1)) + 5,
  },
  {
    ID: 'd58f1107-5427-45bb-8f33-0b42e2aaa3b6',
    CriteriaName: 'Tuân thủ quy định, quy trình, quy định sử dụng công cụ làm việc',
    weight: Math.floor(Math.random() * (20 - 5 + 1)) + 5,
  },
  {
    ID: 'd58f1107-5427-45bb-8f33-0b42e2aaa3b7',
    CriteriaName:
      'Chủ động, nghiêm túc thực hiện theo phân công, yêu cầu của quản lý (với cách tiếp cận)',
    weight: Math.floor(Math.random() * (20 - 5 + 1)) + 5,
  },
  {
    ID: 'd58f1107-5427-45bb-8f33-0b42e2aaa3b8',
    CriteriaName: 'Tinh thần teamwork, cộng tác, hỗ trợ đồng nghiệp công việc',
    weight: Math.floor(Math.random() * (20 - 5 + 1)) + 5,
  },
  {
    ID: 'd58f1107-5427-45bb-8f33-0b42e2aaa3b9',
    CriteriaName:
      'Phong cách làm việc chuyên nghiệp (Cam kết, tuân thể phân hồi của team lead, email)',
    weight: Math.floor(Math.random() * (20 - 5 + 1)) + 5,
  },
  {
    ID: 'd58f1107-5427-45bb-8f33-0b42e2aaa3ba',
    CriteriaName:
      'Tinh thần cầu thị, hoàn thiện cá nhân. Tinh thần xây dựng giải quyết mâu thuẫn, không chỉ trích sâu tương',
    weight: Math.floor(Math.random() * (20 - 5 + 1)) + 5,
  },
  {
    ID: 'd58f1107-5427-45bb-8f33-0b42e2aaa3bb',
    CriteriaName: 'Tự đánh giá thẳng, rõ, đúng mức, không thiên diện chủ nghĩa',
    weight: Math.floor(Math.random() * (20 - 5 + 1)) + 5,
  },
];
