import { CommonModule } from '@angular/common';
import { Component, OnInit, ViewChild } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import {
  VnrToolbarNewBuilder,
  VnrTreelistNewBuilder,
  VnrTreelistNewComponent,
  VnrTreelistNewModule,
} from '@hrm-frontend-workspace/vnr-module';
import { VnrLettersAvatarComponent } from '@hrm-frontend-workspace/ui';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzProgressModule } from 'ng-zorro-antd/progress';
import {
  gridDefineDataFilterQuick,
  listColumns,
  listDataSource,
} from '../../../data/progress-overview-table.data';
import { TargetFormatPipe } from '../../../../shared/pipe/target-format.pipe';

@Component({
  selector: 'app-progress-overview-table',
  imports: [
    CommonModule,
    NzSpinModule,
    NzProgressModule,
    TranslateModule,
    VnrTreelistNewModule,
    VnrLettersAvatarComponent,
    TargetFormatPipe,
  ],
  templateUrl: './progress-overview-table.component.html',
  styleUrls: ['./progress-overview-table.component.scss'],
  standalone: true,
})
export class ProgressOverviewTableComponent implements OnInit {
  @ViewChild('vnrGrid', { static: true }) vnrGrid: VnrTreelistNewComponent;

  public builderGrid!: VnrTreelistNewBuilder;
  public builderToolbar!: VnrToolbarNewBuilder;
  protected gridName = 'gridName';
  protected dataLocal = listDataSource;
  protected columns = listColumns;
  constructor(private translateService: TranslateService) {}

  ngOnInit(): void {
    this.builderTreeListComponent();
    this.builderToolbarComponent();
  }

  private builderToolbarComponent() {
    this.builderToolbar = new VnrToolbarNewBuilder({
      isShowConfig: true,
      gridName: this.gridName,
      screenName: 'progress-overview-table',
      storeName: 'progress-overview-table',
      isSupperAdmin: false,
      gridRef: this.vnrGrid,
      options: {
        configButtonChangeColumn: {
          isShow: false,
          isConfigMenuDisplay: false,
          isChangeColumnNew: false,
          isDisabled: false,
          configButtonSetting: {
            isShow: false,
            isDisabled: false,
          },
        },
        configQuickSearch: {
          isShow: true,
        },
        configFilterAdvanceQuick: {
          isShow: true,
          components: gridDefineDataFilterQuick(),
          keyConfig: 'Grid_FilterAdvanceSeting',
          isShowBtnAdvance: false,
        },
        configButtonExport: {
          isShowBtnExcelAll: false,
          isShowBtnWord: false,
          isShowBtnExcelByTemplate: false,
        },
        configButtonDelete: {
          isShow: false,
          isDisabled: false,
        },
        isSetBackground: true,
        buttonMoreFlexDirection: 'inherit',
        isExpanded: false,
      },
    });
  }

  private builderTreeListComponent() {
    this.builderGrid = new VnrTreelistNewBuilder({
      options: {
        isInitiallyExpanded: false,
        queryOption: {
          take: 10,
        },
        displayField: 'Goal',
        configSelectable: {
          columnKey: 'ID',
          groupKey: 'ParentID',
        },
        configHeightGrid: {
          isAllowCalcRowHeight: true,
          gridBottomMargin: 0,
        },
        configShowHide: {
          isShowEdit: false,
          isShowDelete: false,
          isPageExpand: false,
        },
        configCommandColumn: {
          isEnabledMenuAction: true,
          width: 200,
        },
      },
    });
  }
}
