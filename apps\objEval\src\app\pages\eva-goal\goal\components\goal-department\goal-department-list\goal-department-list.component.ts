import { ChangeDetectionStrategy, Component, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import {
  VnrButtonNewBuilder,
  VnrFilterAdvanceFullBuilder,
  VnrFilterAdvanceFullComponent,
  VnrToolbarNewBuilder,
  VnrTreelistNewBuilder,
  VnrTreelistNewComponent,
} from '@hrm-frontend-workspace/vnr-module';
import { NzDrawerService } from 'ng-zorro-antd/drawer';
import { NzModalService } from 'ng-zorro-antd/modal';

import { ObjSharedModule } from '../../../../../../shared/obj-shared.module';
import { GoalDetailComponent } from '../../../../goal-detail/container/goal-detail.component';
import { GoalRegisterFormComponent } from '../../../../shared/components/goal-register-form/goal-register-form.component';
import { GoalStatusFilterData } from '../../../../shared/enums/menu.data';
import {
  goalTypeColorMap,
  statusColorMap,
  statusTextMap,
} from '../../../../shared/enums/status.enum';
import { TargetFormatPipe } from '../../../../shared/pipe/target-format.pipe';
import { GoalState } from '../../../../shared/states/goal.state';
import { gridDepartmentDefineColumns } from '../../../data/column.data';
import { gridDefineDataFilterQuick } from '../../../data/data-filter-advance';
import { goalDepartmentData } from '../../../data/goal-department.data';
import { TabEnum, TabEnumMap } from '../../../models/goal-period.model';
import { FormAiCreateGoalComponent } from '../form-ai-create-goal/form-ai-create-goal.component';

@Component({
  selector: 'goal-department-list',
  templateUrl: './goal-department-list.component.html',
  styleUrls: ['./goal-department-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ObjSharedModule, VnrTreelistNewComponent, TargetFormatPipe],
})
export class GoalDepartmentListComponent implements OnInit {
  @ViewChild('vnrTreeList', { static: true }) treeListControl: VnrTreelistNewComponent;
  @ViewChild('vnrFilterAdvanceFull') filterAdvanceFullControl: VnrFilterAdvanceFullComponent;

  protected builderTreelist: VnrTreelistNewBuilder;
  protected builderToolbar: VnrToolbarNewBuilder;
  protected builderFilterAdvanceFull: VnrFilterAdvanceFullBuilder;
  protected builderButtonApprove: VnrButtonNewBuilder;
  protected builderButtonReject: VnrButtonNewBuilder;
  protected builderButtonAddNew: VnrButtonNewBuilder;
  protected statusColorMap = statusColorMap;
  protected statusTextMap = statusTextMap;
  protected goalTypeColorMap = goalTypeColorMap;
  protected GoalStatusFilterData = GoalStatusFilterData;
  protected tabDefault = 0;
  protected tabCount: any;
  protected selectedDataItem: any = [];

  private _permission = 'New_PortalV3_Personal_Hre_Dependant';
  private _screenName = 'grid-new-list';
  private _storeName = 'hrm_hr_sp_get_ProfileTest';
  protected gridName = 'ObjEval_ListGoalPeriod';
  protected dataLocal = goalDepartmentData;
  protected columns = gridDepartmentDefineColumns;
  protected isSupperAdmin = true;
  private optionGrid: any = {};

  constructor(
    private drawerService: NzDrawerService,
    private goalState: GoalState,
    private router: Router,
  ) {}
  ngOnInit() {
    this.builderGridComponent();
    this.builderToolbarComponent();
    this.getCountDataTab();
  }

  private builderToolbarComponent() {
    this.builderToolbar = new VnrToolbarNewBuilder({
      isShowConfig: true,
      gridName: this.gridName,
      isSupperAdmin: this.isSupperAdmin,
      gridRef: this.treeListControl,
      permission: this._permission,
      screenName: this._screenName,
      storeName: this._storeName,
      options: {
        configButtonChangeColumn: {
          isShow: true,
          isConfigMenuDisplay: true,
          isChangeColumnNew: true,
          isDisabled: false,
          configButtonSetting: {
            isShow: true,
            isDisabled: true,
          },
        },
        configButtonExport: {
          isShowBtnExcelAll: true,
          isShowBtnWord: true,
          isShowBtnExcelByTemplate: true,
        },
        configButtonDelete: {
          isShow: true,
          isDisabled: false,
        },
        configQuickSearch: {
          textPlaceHolder: 'Tìm kiếm theo mã, tên...',
          searchKey: 'GoalName',
        },
        configFilterAdvanceQuick: {
          isShow: true,
          components: gridDefineDataFilterQuick(),
          keyConfig: 'PortalNew_GridNewExample_Advance',
        },
        isSetBackground: true,
        buttonMoreFlexDirection: 'inherit',
        isExpanded: false,
      },
    });
  }

  private builderGridComponent() {
    this.builderTreelist = new VnrTreelistNewBuilder({
      options: {
        configHeightGrid: {
          gridHeight: 600,
        },
        displayField: 'GoalName',
        configSelectable: {
          columnKey: 'ID',
          groupKey: 'ParentID',
        },
        configCommandColumn: {
          isEnabledMenuAction: false,
        },
        configShowHide: {
          isShowViewDetail: false,
          isPageExpand: false,
          isShowDelete: false,
          isShowEdit: false,
        },
      },
    });
  }

  protected onOpenComponentFilterFull(event: any) {
    this.filterAdvanceFullControl.onOpenFullFilter();
  }

  protected getSelectedDataItem($event) {
    this.selectedDataItem = $event;
  }

  protected onOpenDetail(eventData: any) {
    this.drawerService.create({
      nzTitle: null,
      nzClosable: false,
      nzContent: GoalDetailComponent,
      nzWidth: 1200,
      nzMaskClosable: false,
      nzMask: true,
      nzContentParams: {
        dataItem: eventData.record,
      },
      nzWrapClassName: 'no-header-drawer',
    });
  }

  protected onOpenAddGoalModal() {
    this.drawerService.create({
      nzTitle: 'Thêm mục tiêu',
      nzContent: GoalRegisterFormComponent,
      nzWidth: 700,
      nzClosable: true,
    });
  }

  protected onSelectDepartmentChange(_tabFilter: any) {
    Object.assign(this.optionGrid, {
      Status: TabEnumMap[_tabFilter],
      tabEnum: _tabFilter,
    });
    this.treeListControl.setDataFilter(this.optionGrid);
    this.treeListControl.vnrReadGrid();
  }

  private getCountDataTab() {
    this.tabCount = [
      {
        id: TabEnum.ALL,
        count: goalDepartmentData.filter((item) => item.ParentID === null).length,
      },
      {
        id: TabEnum.E_WAITING_APPROVE,
        count: goalDepartmentData.filter(
          (item) => item.Status === 'E_WAITING_APPROVE' && item.ParentID === null,
        ).length,
      },
      {
        id: TabEnum.E_CONFIRMED,
        count: goalDepartmentData.filter(
          (item) => item.Status === 'E_CONFIRMED' && item.ParentID === null,
        ).length,
      },
      {
        id: TabEnum.E_REJECTED,
        count: goalDepartmentData.filter(
          (item) => item.Status === 'E_REJECTED' && item.ParentID === null,
        ).length,
      },
      {
        id: TabEnum.E_WAITING_CONFIRM,
        count: goalDepartmentData.filter(
          (item) => item.Status === 'E_WAITING_CONFIRM' && item.ParentID === null,
        ).length,
      },
    ];
  }

  protected onAllocationGoal() {
    this.goalState.goalAllocation.set(this.selectedDataItem);
    this.router.navigate(['/objEval/eva-goal/allocation']);
  }

  protected onOpenFormAi() {
    this.drawerService.create({
      nzTitle: 'Tạo mục tiêu với AI',
      nzContent: FormAiCreateGoalComponent,
      nzFooter: null,
      nzMaskClosable: true,
      nzClosable: true,
      nzWidth: 1300,
    });
  }
}
