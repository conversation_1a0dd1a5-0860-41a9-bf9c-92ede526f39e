{"name": "human-resources", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "prefix": "app", "sourceRoot": "apps/human-resources/src", "tags": [], "targets": {"build": {"executor": "@nx/angular:webpack-browser", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/human-resources", "index": "apps/human-resources/src/index.html", "main": "apps/human-resources/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "apps/human-resources/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "apps/human-resources/public"}], "styles": ["apps/human-resources/src/styles.scss"], "scripts": [], "stylePreprocessorOptions": {"includePaths": ["libs", "apps/example/src", "apps/example", "node_modules", "libs/shared/styles", "libs/vnr-module"]}, "customWebpackConfig": {"path": "apps/human-resources/webpack.config.ts"}}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "4kb", "maximumError": "1mb"}], "outputHashing": "all", "customWebpackConfig": {"path": "apps/human-resources/webpack.prod.config.ts"}}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"executor": "@nx/angular:dev-server", "options": {"port": 4204, "publicHost": "http://localhost:4204"}, "configurations": {"production": {"buildTarget": "human-resources:build:production"}, "development": {"buildTarget": "human-resources:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "human-resources:build"}}, "lint": {"executor": "@nx/eslint:lint"}, "serve-static": {"executor": "@nx/web:file-server", "defaultConfiguration": "production", "options": {"buildTarget": "human-resources:build", "port": 4204, "watch": false}, "configurations": {"development": {"buildTarget": "human-resources:build:development"}, "production": {"buildTarget": "human-resources:build:production"}}}}}