# Eva-Goal Migration Guide

## Overview

This guide provides step-by-step instructions for migrating the eva-goal module from its current architecture to the new DDD and Clean Architecture implementation.

## Pre-Migration Checklist

- [ ] Backup current codebase
- [ ] Create feature branch: `feature/eva-goal-ddd-refactoring`
- [ ] Set up testing environment
- [ ] Review current functionality and create test scenarios
- [ ] Establish rollback procedures

## Phase 1: Foundation Setup

### Step 1.1: Create Domain Layer Structure

```bash
# Create domain layer folders
mkdir -p apps/objEval/src/app/pages/eva-goal/domain/{entities,value-objects,aggregates,repositories,services,events}
```

**Create base domain classes:**

```typescript
// domain/base/entity.base.ts
export abstract class Entity<T> {
  protected constructor(protected readonly _id: T) {}
  
  get id(): T {
    return this._id;
  }
  
  equals(other: Entity<T>): boolean {
    return this._id === other._id;
  }
}

// domain/base/value-object.base.ts
export abstract class ValueObject {
  abstract equals(other: ValueObject): boolean;
}

// domain/base/domain-event.base.ts
export interface DomainEvent {
  occurredOn: Date;
}

// domain/base/domain-error.ts
export class DomainError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'DomainError';
  }
}
```

### Step 1.2: Define Core Entities

**Goal Entity Implementation:**

```typescript
// domain/entities/goal.entity.ts
import { Entity } from '../base/entity.base';
import { GoalId } from '../value-objects/goal-id.vo';
import { GoalStatus } from '../value-objects/goal-status.vo';
import { GoalTarget } from '../value-objects/goal-target.vo';

export interface CreateGoalData {
  id?: string;
  name: string;
  group: string;
  target: { value: number; unit: string };
  weight: number;
  period: { start: Date; end: Date };
  implementation: string;
  representative: string;
}

export class Goal extends Entity<GoalId> {
  private constructor(
    id: GoalId,
    private _name: string,
    private _group: string,
    private _target: GoalTarget,
    private _weight: number,
    private _status: GoalStatus,
    private _period: { start: Date; end: Date },
    private _implementation: string,
    private _representative: string
  ) {
    super(id);
  }

  static create(data: CreateGoalData): Goal {
    if (!data.name?.trim()) {
      throw new DomainError('Goal name is required');
    }

    return new Goal(
      GoalId.create(data.id),
      data.name,
      data.group,
      GoalTarget.create(data.target.value, data.target.unit),
      data.weight,
      GoalStatus.DRAFT,
      data.period,
      data.implementation,
      data.representative
    );
  }

  // Getters
  get name(): string { return this._name; }
  get status(): GoalStatus { return this._status; }
  get target(): GoalTarget { return this._target; }

  // Business methods
  confirm(): void {
    if (!this.canBeConfirmed()) {
      throw new DomainError('Goal cannot be confirmed in current state');
    }
    this._status = GoalStatus.CONFIRMED;
  }

  private canBeConfirmed(): boolean {
    return this._status.equals(GoalStatus.WAITING_CONFIRM);
  }
}
```

### Step 1.3: Create Value Objects

**GoalStatus Value Object:**

```typescript
// domain/value-objects/goal-status.vo.ts
import { ValueObject } from '../base/value-object.base';

export class GoalStatus extends ValueObject {
  static readonly DRAFT = new GoalStatus('DRAFT');
  static readonly WAITING_CONFIRM = new GoalStatus('E_WAITING_CONFIRM');
  static readonly CONFIRMED = new GoalStatus('E_CONFIRMED');
  static readonly REJECTED = new GoalStatus('E_REJECTED');
  static readonly WAITING_APPROVE = new GoalStatus('E_WAITING_APPROVE');

  private constructor(private readonly value: string) {
    super();
  }

  equals(other: GoalStatus): boolean {
    return this.value === other.value;
  }

  toString(): string {
    return this.value;
  }
}
```

### Step 1.4: Repository Interfaces

```typescript
// domain/repositories/goal.repository.ts
import { Goal } from '../entities/goal.entity';
import { GoalId } from '../value-objects/goal-id.vo';

export interface GoalRepository {
  findById(id: GoalId): Promise<Goal | null>;
  findAll(): Promise<Goal[]>;
  save(goal: Goal): Promise<void>;
  delete(id: GoalId): Promise<void>;
}

export const GOAL_REPOSITORY_TOKEN = Symbol('GoalRepository');
```

## Phase 2: Application Layer

### Step 2.1: Use Cases Implementation

```typescript
// application/use-cases/create-goal/create-goal.use-case.ts
import { Injectable, Inject } from '@angular/core';
import { Goal } from '../../../domain/entities/goal.entity';
import { GoalRepository, GOAL_REPOSITORY_TOKEN } from '../../../domain/repositories/goal.repository';
import { CreateGoalCommand } from './create-goal.command';
import { CreateGoalResult } from './create-goal.result';

@Injectable()
export class CreateGoalUseCase {
  constructor(
    @Inject(GOAL_REPOSITORY_TOKEN) private goalRepository: GoalRepository
  ) {}

  async execute(command: CreateGoalCommand): Promise<CreateGoalResult> {
    try {
      const goal = Goal.create({
        name: command.name,
        group: command.group,
        target: command.target,
        weight: command.weight,
        period: command.period,
        implementation: command.implementation,
        representative: command.representative
      });

      await this.goalRepository.save(goal);

      return CreateGoalResult.success(goal.id.value);
    } catch (error) {
      return CreateGoalResult.failure(error.message);
    }
  }
}
```

### Step 2.2: Application Services

```typescript
// application/services/goal-management.app-service.ts
import { Injectable } from '@angular/core';
import { Observable, from } from 'rxjs';
import { CreateGoalUseCase } from '../use-cases/create-goal/create-goal.use-case';
import { GetGoalsUseCase } from '../use-cases/get-goals/get-goals.use-case';
import { GoalDto } from '../dtos/goal.dto';
import { CreateGoalCommand } from '../use-cases/create-goal/create-goal.command';

@Injectable()
export class GoalManagementAppService {
  constructor(
    private createGoalUseCase: CreateGoalUseCase,
    private getGoalsUseCase: GetGoalsUseCase
  ) {}

  createGoal(command: CreateGoalCommand): Observable<string> {
    return from(this.createGoalUseCase.execute(command).then(result => {
      if (result.isSuccess) {
        return result.goalId;
      }
      throw new Error(result.error);
    }));
  }

  getGoals(): Observable<GoalDto[]> {
    return from(this.getGoalsUseCase.execute().then(result => result.goals));
  }
}
```

## Phase 3: Infrastructure Migration

### Step 3.1: Repository Implementation

```typescript
// infrastructure/repositories/goal.repository.impl.ts
import { Injectable, Inject } from '@angular/core';
import { GoalRepository } from '../../domain/repositories/goal.repository';
import { Goal } from '../../domain/entities/goal.entity';
import { GoalId } from '../../domain/value-objects/goal-id.vo';
import { GoalApi } from '../api/goal.api';
import { GoalMapper } from '../mappers/goal.mapper';

@Injectable()
export class GoalRepositoryImpl implements GoalRepository {
  constructor(
    private goalApi: GoalApi,
    private goalMapper: GoalMapper
  ) {}

  async findById(id: GoalId): Promise<Goal | null> {
    try {
      const response = await this.goalApi.getById(id.value).toPromise();
      return response ? this.goalMapper.toDomain(response) : null;
    } catch (error) {
      console.error('Error finding goal by id:', error);
      return null;
    }
  }

  async findAll(): Promise<Goal[]> {
    try {
      const response = await this.goalApi.getAll().toPromise();
      return response.map(data => this.goalMapper.toDomain(data));
    } catch (error) {
      console.error('Error finding all goals:', error);
      return [];
    }
  }

  async save(goal: Goal): Promise<void> {
    try {
      const dto = this.goalMapper.toDto(goal);
      await this.goalApi.save(dto).toPromise();
    } catch (error) {
      console.error('Error saving goal:', error);
      throw error;
    }
  }

  async delete(id: GoalId): Promise<void> {
    try {
      await this.goalApi.delete(id.value).toPromise();
    } catch (error) {
      console.error('Error deleting goal:', error);
      throw error;
    }
  }
}
```

### Step 3.2: Mock Data Migration

```typescript
// infrastructure/mock-data/goal.mock-data.ts
import { Goal } from '../../domain/entities/goal.entity';

export const goalMockData: Goal[] = [
  Goal.create({
    id: 'goal-1',
    name: 'Sản lượng Urea sản xuất',
    group: 'OKR',
    target: { value: 1900, unit: 'tấn' },
    weight: 30,
    period: { start: new Date('2024-04-01'), end: new Date('2024-06-30') },
    implementation: 'Phòng sản xuất',
    representative: 'Phòng sản xuất'
  }),
  // ... more mock data
];

export class GoalMockDataFactory {
  static createSampleGoals(): Goal[] {
    return goalMockData;
  }
}
```

## Phase 4: Presentation Layer Migration

### Step 4.1: Container Component Migration

```typescript
// presentation/containers/goal-period.container.ts
import { Component, OnInit, ChangeDetectionStrategy } from '@angular/core';
import { Observable } from 'rxjs';
import { GoalManagementAppService } from '../../application/services/goal-management.app-service';
import { GoalDto } from '../../application/dtos/goal.dto';
import { GoalViewModel } from '../view-models/goal.view-model';

@Component({
  selector: 'app-goal-period-container',
  template: `
    <app-goal-list 
      [goals]="goals$ | async"
      (createGoal)="onCreateGoal($event)"
      (editGoal)="onEditGoal($event)">
    </app-goal-list>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class GoalPeriodContainer implements OnInit {
  goals$: Observable<GoalViewModel[]>;

  constructor(
    private goalManagementService: GoalManagementAppService
  ) {}

  ngOnInit(): void {
    this.loadGoals();
  }

  private loadGoals(): void {
    this.goals$ = this.goalManagementService.getGoals().pipe(
      map(goals => goals.map(goal => GoalViewModel.fromDto(goal)))
    );
  }

  onCreateGoal(goalData: any): void {
    // Handle goal creation
  }

  onEditGoal(goalId: string): void {
    // Handle goal editing
  }
}
```

## Module Configuration

### Updated Module with DI Configuration

```typescript
// eva-goal.module.ts
import { NgModule } from '@angular/core';
import { EvaGoalRoutesModule } from './eva-goal.routes';

// Domain Services
import { GoalAllocationDomainService } from './domain/services/goal-allocation.domain-service';

// Application Services
import { GoalManagementAppService } from './application/services/goal-management.app-service';
import { CreateGoalUseCase } from './application/use-cases/create-goal/create-goal.use-case';

// Infrastructure
import { GoalRepositoryImpl } from './infrastructure/repositories/goal.repository.impl';
import { GoalApi } from './infrastructure/api/goal.api';
import { GOAL_REPOSITORY_TOKEN } from './domain/repositories/goal.repository';

@NgModule({
  imports: [EvaGoalRoutesModule],
  providers: [
    // Domain Services
    GoalAllocationDomainService,
    
    // Application Layer
    GoalManagementAppService,
    CreateGoalUseCase,
    
    // Infrastructure Layer
    GoalApi,
    { provide: GOAL_REPOSITORY_TOKEN, useClass: GoalRepositoryImpl },
  ],
})
export class EvaGoalModule {}
```

## Testing Migration

### Domain Layer Tests

```typescript
// domain/entities/__tests__/goal.entity.spec.ts
import { Goal } from '../goal.entity';
import { DomainError } from '../../base/domain-error';

describe('Goal Entity', () => {
  describe('create', () => {
    it('should create valid goal', () => {
      const goalData = {
        name: 'Test Goal',
        group: 'OKR',
        target: { value: 100, unit: 'units' },
        weight: 50,
        period: { start: new Date(), end: new Date() },
        implementation: 'Test Dept',
        representative: 'Test Rep'
      };

      const goal = Goal.create(goalData);

      expect(goal.name).toBe('Test Goal');
      expect(goal.target.getValue()).toBe(100);
    });

    it('should throw error for invalid name', () => {
      const goalData = {
        name: '',
        group: 'OKR',
        target: { value: 100, unit: 'units' },
        weight: 50,
        period: { start: new Date(), end: new Date() },
        implementation: 'Test Dept',
        representative: 'Test Rep'
      };

      expect(() => Goal.create(goalData)).toThrow(DomainError);
    });
  });
});
```

## Rollback Procedures

### If Migration Fails

1. **Immediate Rollback:**
   ```bash
   git checkout main
   git branch -D feature/eva-goal-ddd-refactoring
   ```

2. **Partial Rollback:**
   - Use feature flags to disable new implementation
   - Revert specific commits using `git revert`
   - Update module configuration to use old services

3. **Database Rollback:**
   - Run rollback scripts if any schema changes were made
   - Restore from backup if necessary

---

*Follow this guide step by step to ensure a smooth migration to the new DDD architecture.*
