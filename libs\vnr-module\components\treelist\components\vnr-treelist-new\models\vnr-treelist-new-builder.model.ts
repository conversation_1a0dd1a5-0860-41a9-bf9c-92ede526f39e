import { AbstractTreelistNewBuilder } from '../../../models/abstract-treelist-new-builder.model';
import { VnrTreeListNewOptionChangeColumnBuilder } from './vnr-treelist-new-option-change-column-builder.model';
import { VnrTreeListNewOptionBuilder } from './vnr-treelist-new-option-builder.model';

export class VnrTreelistNewBuilder extends AbstractTreelistNewBuilder<
  VnrTreelistNewBuilder,
  VnrTreeListNewOptionBuilder,
  VnrTreeListNewOptionChangeColumnBuilder
> {
  constructor(builder?: VnrTreelistNewBuilder) {
    super();
    if (!builder) {
      builder = {};
    }
    Object.assign(this, builder);
    this.options = new VnrTreeListNewOptionBuilder(builder?.options);
    this.optionChangeColumn = new VnrTreeListNewOptionChangeColumnBuilder(
      builder?.optionChangeColumn,
    );
  }

  builder?(builder?: VnrTreelistNewBuilder) {
    Object.assign(this, builder);
    this.options = new VnrTreeListNewOptionBuilder(builder?.options);
    this.optionChangeColumn = new VnrTreeListNewOptionChangeColumnBuilder(
      builder?.optionChangeColumn,
    );
  }
}
