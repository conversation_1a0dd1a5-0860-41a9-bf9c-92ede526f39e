<div class="d-flex justify-content-between align-items-center mb-2">
  <div class="ant-drawer-title"><PERSON><PERSON><PERSON> ti<PERSON><PERSON> chi tiết</div>
  <div class="d-flex align-items-center">
    <vnr-button
      class="mr-2"
      [vnrType]="'primary'"
      (click)="vnrGridDetail.editAllHandler(vnrGridDetail.grid)"
      [vnrText]="'common.edit'"
      *ngIf="!vnrGridDetail?.isEdited"
    >
    </vnr-button>
    <vnr-button
      class="mr-2"
      [vnrType]="'default'"
      [vnrText]="'common.button.cancel'"
      (click)="vnrGridDetail.cancelAllHandler(vnrGridDetail.grid)"
      *ngIf="vnrGridDetail?.isEdited"
    >
    </vnr-button>
    <vnr-button
      class="mr-2"
      [vnrType]="'success'"
      [vnrText]="'common.button.save'"
      (click)="vnrGridDetail.saveAllHandler(vnrGridDetail.grid)"
      *ngIf="vnrGridDetail?.isEdited"
    >
    </vnr-button>
    <vnr-button
      [vnrText]="'objEval.GoalPeriod.Allocation'"
      [vnrType]="'primary'"
      (vnrClick)="onOpenAllocationGoalDetailModal($event)"
    ></vnr-button>
  </div>
</div>
<vnr-grid-new-Edit-Inline
  #vnrGridDetail
  [builder]="builderGrid"
  [gridName]="gridName"
  [dataLocal]="dataLocal"
  [columns]="columns"
  [isSupperAdmin]="isSupperAdmin"
  [defaultColumnTemplate]="tplCustomTemplateByColumn"
>
</vnr-grid-new-Edit-Inline>
<ng-template #tplCustomTemplateByColumn let-dataItem let-column="columnItem" let-field="field">
  <ng-container [ngSwitch]="column['Name']">
    <span *ngSwitchCase="'Representative'">
      <div class="d-flex align-items-center">
        <app-vnr-letters-avatar
          [avatarName]="dataItem[column['Name']]"
          [circular]="true"
          [width]="32"
          [src]="dataItem && dataItem.ImagePathProcess"
          class="mr-2"
        ></app-vnr-letters-avatar>
        <span>{{ dataItem[column['Name']] }}</span>
      </div>
    </span>
    <span *ngSwitchCase="'Status'">
      <vnr-tag
        [vnrColor]="statusColorMap[dataItem['Status']] || 'default'"
        [vnrTitle]="statusTextMap[dataItem['Status']]"
      ></vnr-tag>
    </span>
    <span *ngSwitchDefault>
      {{ dataItem[column['Name']] }}
    </span>
  </ng-container>
</ng-template>
