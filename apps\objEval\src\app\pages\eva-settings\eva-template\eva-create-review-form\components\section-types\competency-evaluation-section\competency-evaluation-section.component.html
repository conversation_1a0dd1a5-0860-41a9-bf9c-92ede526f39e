<div class="section-competency-evaluation">
  <div class="section-competency-evaluation__header mb-3">
    <vnr-radiobutton
      class="radiobutton-competency-evaluation"
      [(ngModel)]="selectedValue"
      name="selectedValue"
      [builder]="builder"
      [ngModelOptions]="{ standalone: true }"
      (ngModelChange)="onModelChange($event)"
      (vnrChange)="onChange($event)"
    ></vnr-radiobutton>
  </div>
  <div class="grid-container">
    <app-evaluation-grid
      #evaluationGrid
      [index]="0"
      [vnrColumGridLocal]="vnrColumGridLocal"
      [vnrDataGridLocal]="vnrDataGridLocal"
      [columnNameTotal]="'CriteriaName'"
      [titleButtonAddRow]="'Thêm nhóm năng lực'"
      [titleButtonAddRowGroup]="'Thêm năng lực'"
      [isShowButton]="true"
      [isEdit]="formType !== _preview"
      [type]="'competency'"
      (changeAddRow)="onChangeAddRow($event)"
      (changeAddRowGroup)="onChangeAddRowGroup($event)"
    ></app-evaluation-grid>
  </div>
</div>
