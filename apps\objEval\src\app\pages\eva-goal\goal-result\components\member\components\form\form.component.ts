import { Component, OnInit, ViewChild, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { VnrGridNewComponent, VnrGridNewBuilder } from '@hrm-frontend-workspace/vnr-module';
import { VnrTagComponent, VnrLettersAvatarComponent } from '@hrm-frontend-workspace/ui';
import { memberFormColumns, memberFormDataSource } from '../../../../data/member.data';
import { STATUS_TAG, STATUS_PROGRESS_TAG } from '../../../../data/general.data';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzTagModule } from 'ng-zorro-antd/tag';

@Component({
  selector: 'app-form',
  imports: [
    CommonModule,
    NzGridModule,
    VnrGridNewComponent,
    VnrTagComponent,
    NzIconModule,
    VnrLettersAvatarComponent,
    NzTagModule,
  ],
  templateUrl: './form.component.html',
  styleUrl: './form.component.scss',
})
export class FormComponent implements OnInit {
  @ViewChild('vnrGrid', { static: false }) vnrGrid: VnrGridNewComponent;

  @Input() dataItem: any;
  builderGrid: VnrGridNewBuilder;

  protected gridName = 'Grid';
  protected _screenName = 'Screen';
  protected isSupperAdmin = false;
  protected _storeName = 'eva_sp_get_GoalResult';
  protected dataLocal = memberFormDataSource;
  protected columns = memberFormColumns;

  // Filter toggle cho ResultStatus
  public resultStatusFilterList: Array<{
    label: string;
    value: string;
    checked: boolean;
    color: string;
    count: number;
  }> = [];

  private _allData = memberFormDataSource;

  ngOnInit(): void {
    this.initResultStatusFilterList();
    this.builderGridComponent();
  }

  private initResultStatusFilterList(): void {
    this.resultStatusFilterList = [
      {
        label: 'Tất cả',
        value: 'all',
        checked: true,
        color: 'default',
        count: this._allData.length,
      },
      ...STATUS_PROGRESS_TAG.map((item) => ({
        label: item.label,
        value: item.label,
        checked: false,
        color: item.backgroundColor,
        count: this._allData.filter((d) => d.ResultStatus === item.label).length,
      })),
    ];
  }

  private updateResultStatusCounts(): void {
    this.resultStatusFilterList.forEach((status) => {
      if (status.value === 'all') {
        status.count = this._allData.length;
      } else {
        status.count = this._allData.filter((d) => d.ResultStatus === status.value).length;
      }
    });
  }

  public toggleResultStatusFilter(status: {
    label: string;
    value: string;
    checked: boolean;
    color: string;
    count: number;
  }): void {
    if (status.value === 'all' && !status.checked) {
      this.resultStatusFilterList.forEach((s) => {
        if (s.value !== 'all') {
          s.checked = false;
        }
      });
    } else if (status.value !== 'all' && !status.checked) {
      const allFilter = this.resultStatusFilterList.find((s) => s.value === 'all');
      if (allFilter) {
        allFilter.checked = false;
      }
    }
    status.checked = !status.checked;
    this.applyResultStatusFilter();
    this.updateResultStatusCounts();
  }

  private applyResultStatusFilter(): void {
    const checkedStatus = this.resultStatusFilterList.filter((s) => s.checked).map((s) => s.value);
    if (checkedStatus.length === 0) {
      const allFilter = this.resultStatusFilterList.find((s) => s.value === 'all');
      if (allFilter) {
        allFilter.checked = true;
      }
      this.dataLocal = this._allData;
      this.updateResultStatusCounts();
      return;
    }
    if (checkedStatus.includes('all')) {
      this.dataLocal = this._allData;
      this.updateResultStatusCounts();
      return;
    }
    const filtered = this._allData.filter((item) => checkedStatus.includes(item.ResultStatus));
    this.dataLocal = filtered;
    this.updateResultStatusCounts();
  }

  private builderGridComponent() {
    this.builderGrid = new VnrGridNewBuilder({
      options: {
        configHeightGrid: {
          isAllowCalcRowHeight: true,
        },
        configSelectable: {
          columnKey: 'Id',
        },
        configShowHide: {
          isPageExpand: false,
          isShowDelete: false,
          isShowEdit: false,
        },
        configCommandColumn: {
          isEnabledMenuAction: true,
        },
        configGroupable: {
          isEnabled: false,
        },
      },
    });
  }

  public getStatusTag(dataItem: any) {
    return STATUS_TAG.find((item) => item.label === dataItem.Status);
  }

  public getStatusProgressTag(dataItem: any) {
    return STATUS_PROGRESS_TAG.find((item) => item.label === dataItem.ResultStatus);
  }

  public getStatusPillClass(value: string): string {
    switch (value) {
      case 'Vượt chỉ tiêu':
        return 'status-pill--done';
      case 'Đạt chỉ tiêu':
        return 'status-pill--doing';
      case 'Không đạt':
        return 'status-pill--notdone';
      case 'Đã huỷ':
        return 'status-pill--cancel';
      case 'Tạm dừng':
        return 'status-pill--pause';
      case 'Chưa bắt đầu':
        return 'status-pill--notstart';
      case 'all':
        return '';
      default:
        return '';
    }
  }
}
