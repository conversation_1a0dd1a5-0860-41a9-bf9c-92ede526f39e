<div class="form-header" nz-form [formGroup]="formGroup">
  <vnr-toolbar-new [builder]="builderToolbar">
    <vnr-filter-advance-quick
      leftToolbar
      [builder]="builderFilterAdvanceQuick"
      (onChange)="onChangesFilterQuick($event)"
    ></vnr-filter-advance-quick>
    <vnr-button-new
      rightToolbar
      class="--custom-bg"
      [builder]="builderButtonAddAppraisal"
      (vnrClick)="onAddAppraisal()"
    ></vnr-button-new>
  </vnr-toolbar-new>
</div>

<vnr-listview-new
  #vnrGrid
  [builder]="builderGrid"
  [gridName]="gridName"
  [dataLocal]="dataLocal"
  [columns]="columns"
  [rowDetailTemplate]="tplCardInfo"
  [rowActionsTemplate]="rowActionsTemplate"
  [columnHeaderTemplate]="tplHeader"
  (vnrLoadData)="onLoadData($event)"
>
</vnr-listview-new>
<ng-template #tplHeader let-dataItem>
  <div class="grid-item-header">
    <span class="grid-item-header__title">
      {{ dataItem.CycleName }}
    </span>
    <div class="grid-item-header__description">
      <div class="grid-item-header__information">
        <nz-icon nzType="clock-circle" nzTheme="outline" />
        <span>
          {{ dataItem.PeriodDate | date : 'dd/MM/yyyy' }} -
          {{ dataItem.DueDate | date : 'dd/MM/yyyy' }}
        </span>
      </div>
      <div>|</div>
      <div class="grid-item-header__information">
        <span><i class="fas fa-table"></i></span>
        <span>
          {{
            'objEval.EvaPerformanceAppraisals.formatNumberTemplate'
              | translate : { n: dataItem.NumberTemplate }
          }}
        </span>
      </div>
    </div>
  </div>
</ng-template>
<ng-template #tplCardInfo let-dataItem>
  <div class="grid-item-box">
    <nz-alert
      *ngIf="dataItem.NumberEmployeesNoTemplate > 0"
      nzShowIcon
      nzType="warning"
      [nzMessage]="
        'objEval.EvaPerformanceAppraisals.warningEmployeeNoTemplate'
          | translate : { n: dataItem.NumberEmployeesNoTemplate }
      "
      [nzAction]="tplBtnAdditional"
    ></nz-alert>
    <ng-template #tplBtnAdditional>
      <a nzType="link" class="text-primary mx-3" (click)="onAdditional($event)">{{
        'objEval.EvaPerformanceAppraisals.additional' | translate
      }}</a>
    </ng-template>
    <div class="grid-item-box__card">
      <div nz-row [nzGutter]="24">
        <!-- Loại kỳ -->
        <div
          nz-col
          class="gutter-row"
          [nzXl]="{ span: 3 }"
          [nzLg]="{ span: 4 }"
          [nzMd]="{ span: 6 }"
          [nzSm]="{ span: 24 }"
          [nzXs]="{ span: 24 }"
          nzOrder="0"
        >
          <div class="grid-item-box__card-info">
            <span class="text-muted">{{
              'objEval.EvaPerformanceAppraisals.employeeParticipating' | translate
            }}</span>
            <strong>{{ dataItem.NumberEmployees }}</strong>
          </div>
        </div>
        <div
          nz-col
          class="gutter-row"
          [nzXl]="{ span: 21 }"
          [nzLg]="{ span: 20 }"
          [nzMd]="{ span: 16 }"
          [nzSm]="{ span: 24 }"
          [nzXs]="{ span: 24 }"
          nzOrder="0"
        >
          <div nz-row [nzGutter]="8">
            <div
              nz-col
              class="gutter-row"
              [nzLg]="{ span: 4 }"
              [nzMd]="{ span: 8 }"
              [nzSm]="{ span: 8 }"
              [nzXs]="{ span: 12 }"
              nzOrder="0"
            >
              <div class="grid-item-box__card-info-item-column">
                <span class="text-muted">{{
                  'objEval.EvaPerformanceAppraisals.implementingDepartment' | translate
                }}</span>
                <span>{{ dataItem.Departments }}</span>
              </div>
            </div>
            <div
              nz-col
              class="gutter-row"
              [nzLg]="{ span: 4 }"
              [nzMd]="{ span: 8 }"
              [nzSm]="{ span: 8 }"
              [nzXs]="{ span: 12 }"
              nzOrder="0"
            >
              <div class="grid-item-box__card-info-item-column">
                <span class="text-secondary">{{
                  'objEval.EvaPerformanceAppraisals.noAppraisals' | translate
                }}</span>
                <strong>{{ dataItem.NumberNoAppraisals || '-' }}</strong>
              </div>
            </div>
            <div
              nz-col
              class="gutter-row"
              [nzLg]="{ span: 4 }"
              [nzMd]="{ span: 8 }"
              [nzSm]="{ span: 8 }"
              [nzXs]="{ span: 12 }"
              nzOrder="0"
            >
              <div class="grid-item-box__card-info-item-column">
                <span class="text-primary">{{
                  'objEval.EvaPerformanceAppraisals.inProgress' | translate
                }}</span>
                <strong>{{ dataItem.NumberInProgress || '-' }}</strong>
              </div>
            </div>
            <div
              nz-col
              class="gutter-row"
              [nzLg]="{ span: 4 }"
              [nzMd]="{ span: 8 }"
              [nzSm]="{ span: 8 }"
              [nzXs]="{ span: 12 }"
              nzOrder="0"
            >
              <div class="grid-item-box__card-info-item-column">
                <span class="text-success">{{
                  'objEval.EvaPerformanceAppraisals.completed' | translate
                }}</span>
                <strong>{{ dataItem.NumberCompleted || '-' }}</strong>
              </div>
            </div>
            <div
              nz-col
              class="gutter-row"
              [nzLg]="{ span: 4 }"
              [nzMd]="{ span: 8 }"
              [nzSm]="{ span: 8 }"
              [nzXs]="{ span: 12 }"
              nzOrder="0"
            >
              <div class="grid-item-box__card-info-item-column">
                <span class="text-warning">{{
                  'objEval.EvaPerformanceAppraisals.reAppraisals' | translate
                }}</span>
                <strong>{{ dataItem.NumberReAppraisals || '-' }}</strong>
              </div>
            </div>
            <div
              nz-col
              class="gutter-row"
              [nzLg]="{ span: 4 }"
              [nzMd]="{ span: 8 }"
              [nzSm]="{ span: 8 }"
              [nzXs]="{ span: 12 }"
              nzOrder="0"
            >
              <div class="grid-item-box__card-info-item-column">
                <span class="text-danger">{{
                  'objEval.EvaPerformanceAppraisals.canceled' | translate
                }}</span>
                <strong>{{ dataItem.NumberCanceled || '-' }}</strong>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</ng-template>
<ng-template #rowActionsTemplate let-dataItem>
  <div class="grid-item-action-more">
    <vnr-button
      [vnrText]="dataItem.StatusPublicView"
      [vnrIconRight]="'down'"
      [vnrType]="getStatusPublic(dataItem.StatusPublic)"
      nz-dropdown
      [nzDropdownMenu]="btnDropdownStatusPublic"
    ></vnr-button>
    <nz-dropdown-menu #btnDropdownStatusPublic="nzDropdownMenu">
      <ul nz-menu>
        <li
          nz-menu-item
          (click)="onChangeStatusPublic($event, item)"
          *ngFor="let item of dropdownStatusPublic"
        >
          {{ item.Name | translate }}
        </li>
      </ul>
    </nz-dropdown-menu>
    <vnr-button nz-dropdown [nzDropdownMenu]="btnDropdownMore" [vnrIcon]="'more'"></vnr-button>
    <nz-dropdown-menu #btnDropdownMore="nzDropdownMenu">
      <ul nz-menu>
        <li nz-menu-item (click)="onEdit($event, dataItem)">
          <nz-icon nzType="edit" nzTheme="outline" />
          {{ 'objEval.EvaPerformanceAppraisals.btnMore.edit' | translate }}
        </li>
        <li nz-menu-item (click)="onViewDetail($event, dataItem)">
          <i class="fal fa-eye"></i>
          {{ 'objEval.EvaPerformanceAppraisals.btnMore.viewDetail' | translate }}
        </li>
        <li nz-menu-item (click)="onClone($event)">
          <i class="far fa-clone"></i>
          {{ 'objEval.EvaPerformanceAppraisals.btnMore.clone' | translate }}
        </li>
        <li nz-menu-item (click)="onDelete($event)">
          <i class="fal fa-trash-alt"></i>
          {{ 'objEval.EvaPerformanceAppraisals.btnMore.delete' | translate }}
        </li>
      </ul>
    </nz-dropdown-menu>
  </div>
</ng-template>
