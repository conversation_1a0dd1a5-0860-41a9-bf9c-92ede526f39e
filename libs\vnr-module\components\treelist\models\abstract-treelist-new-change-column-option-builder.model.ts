import { IVnrApiEndpoint } from '../../../common/models/app-api-config.interface';
import { IApiOptionsChangeColumnModel } from '../../grids/models/grid-new-change-column.model';
import { VnRTreeListNewChangeColumnPosition } from './vnr-treelist.model';

export abstract class AbstractTreeListNewChangeColumnOptionBuilder {
  data?: IApiOptionsChangeColumnModel;
  gridPlacement?: 'leftTop' | 'rightTop' | '' = 'rightTop';
  apiRestoreChangeColumn?: IVnrApiEndpoint;
  apiSaveChangeColumn?: IVnrApiEndpoint;
  apiSaveTranslate?: IVnrApiEndpoint;
  modelName?: string = '';
  assemblyName?: string = '';
  tableDB?: string = '';
  isRestoreApi?: boolean = true;
  isDisabledModeViewRecommended?: boolean = false;
  isDisabledModeViewSimple?: boolean = false;
  position?: VnRTreeListNewChangeColumnPosition = new VnRTreeListNewChangeColumnPosition();
}
