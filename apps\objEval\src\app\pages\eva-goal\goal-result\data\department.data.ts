export function departmentFilter(): any[] {
  const listDepartment = Array.from(
    new Set(departmentDataSource.filter((item) => item.Department).map((item) => item.Department)),
  ).map((department) => ({
    label: department,
    value: department,
  }));
  return [
    {
      numberOrder: 1,
      isFavorite: false,
      isUsing: false,
      field: 'Department',
      type: 'multipleCheckbox',
      value: '',
      title: 'Phòng ban',
      dataSource: {
        staticSource: listDepartment,
      },
    },
  ];
}

export const departmentColumn = [
  {
    Name: 'Department',
    HeaderName: 'Phòng ban',
    HeaderKey: 'Phòng ban',
    DisplayName: 'Phòng ban',
    Width: 185,
  },
  {
    Name: 'Representative',
    HeaderName: 'Người đại diện',
    HeaderKey: 'Người đại diện',
    DisplayName: 'Người đại diện',
    Width: 180,
  },
  {
    Name: 'Position',
    HeaderName: 'Vị trí công việc',
    HeaderKey: 'Vị trí công việc',
    DisplayName: 'Vị trí công việc',
    Width: 152,
  },
  {
    Name: 'CompletionRate',
    HeaderName: 'Mức độ hoàn thành bộ phận',
    HeaderKey: 'Mức độ hoàn thành bộ phận',
    DisplayName: 'Mức độ hoàn thành bộ phận',
    Width: 175,
  },
  {
    Name: 'AchieveRate',
    HeaderName: 'Tỉ lệ đạt',
    HeaderKey: 'Tỉ lệ đạt',
    DisplayName: 'Tỉ lệ đạt',
    Width: 99,
  },
  {
    Name: 'TargetInPeriod',
    HeaderName: 'SL mục tiêu trong kỳ',
    HeaderKey: 'SL mục tiêu trong kỳ',
    DisplayName: 'SL mục tiêu trong kỳ',
    Width: 133,
  },
  {
    Name: 'CurrentTarget',
    HeaderName: 'SL mục tiêu hiện hữu',
    HeaderKey: 'SL mục tiêu hiện hữu',
    DisplayName: 'SL mục tiêu hiện hữu',
    Width: 134,
  },
  {
    Name: 'TotalEmployee',
    HeaderName: 'Tổng số nhân viên',
    HeaderKey: 'Tổng số nhân viên',
    DisplayName: 'Tổng số nhân viên',
    Width: 116,
  },
  {
    Name: 'Over100',
    HeaderName: 'Mức độ hoàn thành NV > 100%',
    HeaderKey: 'Mức độ hoàn thành NV > 100%',
    DisplayName: 'Mức độ hoàn thành NV > 100%',
    Width: 182,
  },
  {
    Name: 'From90To100',
    HeaderName: 'Mức độ hoàn thành NV 90-100%',
    HeaderKey: 'Mức độ hoàn thành NV 90-100%',
    DisplayName: 'Mức độ hoàn thành NV 90-100%',
    Width: 195,
  },
  {
    Name: 'From70To90',
    HeaderName: 'Mức độ hoàn thành NV 70-90%',
    HeaderKey: 'Mức độ hoàn thành NV 70-90%',
    DisplayName: 'Mức độ hoàn thành NV 70-90%',
    Width: 188,
  },
  {
    Name: 'Under70',
    HeaderName: 'Mức độ hoàn thành NV <70%',
    HeaderKey: 'Mức độ hoàn thành NV <70%',
    DisplayName: 'Mức độ hoàn thành NV <70%',
    Width: 172,
  },
  {
    Name: 'EmployeeAchieveRate',
    HeaderName: 'Tỉ lệ NV đạt',
    HeaderKey: 'Tỉ lệ NV đạt',
    DisplayName: 'Tỉ lệ NV đạt',
    Width: 80,
  },
  {
    Name: 'LastUpdate',
    HeaderName: 'Cập nhật lần cuối',
    HeaderKey: 'Cập nhật lần cuối',
    DisplayName: 'Cập nhật lần cuối',
    Width: 110,
  },
  {
    Name: 'Result',
    HeaderName: 'Kết quả',
    HeaderKey: 'Kết quả',
    DisplayName: 'Kết quả',
    Width: 110,
  },
  {
    Name: 'Rank',
    HeaderName: 'Xếp loại',
    HeaderKey: 'Xếp loại',
    DisplayName: 'Xếp loại',
    Width: 110,
  },
];

export const departmentDataSource = [
  {
    ID: '1',
    Department: 'Ban điều hành',
    Representative: 'Phạm Thái Hòa',
    Position: 'Tổng giám đốc',
    CompletionRate: 100,
    AchieveRate: '100% (5/5)',
    ParentID: null,
    HasChildren: true,
    TargetInPeriod: '7',
    CurrentTarget: '5',
    TotalEmployee: '5',
    Over100: '1',
    From90To100: '1',
    From70To90: '1',
    Under70: '2',
    EmployeeAchieveRate: '85%',
    LastUpdate: '25/12/2025',
    Result: 'Chưa đánh giá',
    Rank: 'Chưa đánh giá',
  },
  {
    ID: '2',
    Department: 'CLD AUTO',
    Representative: 'Nguyễn Trung Kiên',
    Position: 'Giám đốc chi nhánh',
    CompletionRate: 95,
    AchieveRate: '60% (2/3)',
    ParentID: '1',
    HasChildren: false,
    TargetInPeriod: '4',
    CurrentTarget: '3',
    TotalEmployee: '50',
    Over100: '5',
    From90To100: '10',
    From70To90: '10',
    Under70: '25',
    EmployeeAchieveRate: '85%',
    LastUpdate: '25/12/2025',
    Result: 'Chưa đánh giá',
    Rank: 'Chưa đánh giá',
  },
  {
    ID: '3',
    Department: 'CLD AGRICO',
    Representative: 'Phạm Hồng Quân',
    Position: 'Giám đốc chi nhánh',
    CompletionRate: 100,
    AchieveRate: '100% (5/5)',
    TargetInPeriod: '7',
    CurrentTarget: '5',
    TotalEmployee: '80',
    Over100: '5',
    From90To100: '10',
    From70To90: '10',
    Under70: '55',
    EmployeeAchieveRate: '85%',
    LastUpdate: '25/12/2025',
    Result: 'Chưa đánh giá',
    Rank: 'Chưa đánh giá',
    ParentID: '1',
    HasChildren: false,
  },
  {
    ID: '4',
    Department: 'CLD LOGI',
    Representative: 'Nguyễn Hải Yến',
    Position: 'Giám đốc chi nhánh',
    CompletionRate: 100,
    AchieveRate: '100% (5/5)',
    TargetInPeriod: '7',
    CurrentTarget: '5',
    TotalEmployee: '45',
    Over100: '5',
    From90To100: '10',
    From70To90: '10',
    Under70: '20',
    EmployeeAchieveRate: '85%',
    LastUpdate: '25/12/2025',
    Result: 'Chưa đánh giá',
    Rank: 'Chưa đánh giá',
    ParentID: '1',
    HasChildren: false,
  },
  {
    ID: '5',
    Department: 'CLD DICO',
    Representative: 'Nguyễn Hải Yến',
    Position: 'Giám đốc chi nhánh',
    CompletionRate: 100,
    AchieveRate: '100% (5/5)',
    TargetInPeriod: '7',
    CurrentTarget: '5',
    TotalEmployee: '38',
    Over100: '5',
    From90To100: '10',
    From70To90: '10',
    Under70: '13',
    EmployeeAchieveRate: '85%',
    LastUpdate: '25/12/2025',
    Result: 'Chưa đánh giá',
    Rank: 'Chưa đánh giá',
    ParentID: '1',
    HasChildren: false,
  },
  {
    ID: '6',
    Department: 'Dịch vụ hậu mãi',
    Representative: 'Bùi Hồng Nhung',
    Position: 'Giám đốc chi nhánh',
    CompletionRate: 80,
    AchieveRate: '60% (3/5)',
    TargetInPeriod: '7',
    CurrentTarget: '5',
    TotalEmployee: '40',
    Over100: '5',
    From90To100: '10',
    From70To90: '10',
    Under70: '15',
    EmployeeAchieveRate: '85%',
    LastUpdate: '25/12/2025',
    Result: 'Chưa đánh giá',
    Rank: 'Chưa đánh giá',
    ParentID: '1',
    HasChildren: false,
  },
  {
    ID: '7',
    Department: 'Ban kỹ thuật',
    Representative: 'Nguyễn Minh Anh',
    Position: 'Trưởng ban',
    CompletionRate: 100,
    AchieveRate: '100% (5/5)',
    TargetInPeriod: '7',
    CurrentTarget: '5',
    TotalEmployee: '58',
    Over100: '5',
    From90To100: '10',
    From70To90: '10',
    Under70: '33',
    EmployeeAchieveRate: '85%',
    LastUpdate: '25/12/2025',
    Result: 'Chưa đánh giá',
    Rank: 'Chưa đánh giá',
    ParentID: '1',
    HasChildren: false,
  },
  {
    ID: '8',
    Department: 'Ban kỹ thuật',
    Representative: 'Lê Phương Linh',
    Position: 'Trưởng ban',
    CompletionRate: 80,
    AchieveRate: '60% (3/5)',
    TargetInPeriod: '7',
    CurrentTarget: '5',
    TotalEmployee: '30',
    Over100: '5',
    From90To100: '10',
    From70To90: '10',
    Under70: '5',
    EmployeeAchieveRate: '85%',
    LastUpdate: '25/12/2025',
    Result: 'Chưa đánh giá',
    Rank: 'Chưa đánh giá',
    ParentID: null,
    HasChildren: true,
  },
  {
    ID: '9',
    Department: 'Ban kỹ thuật',
    Representative: 'Lê Phương Linh',
    Position: 'Trưởng ban',
    CompletionRate: 80,
    AchieveRate: '60% (3/5)',
    TargetInPeriod: '7',
    CurrentTarget: '5',
    TotalEmployee: '30',
    Over100: '5',
    From90To100: '10',
    From70To90: '10',
    Under70: '5',
    EmployeeAchieveRate: '85%',
    LastUpdate: '25/12/2025',
    Result: 'Chưa đánh giá',
    Rank: 'Chưa đánh giá',
    ParentID: '8',
    HasChildren: false,
  },
];

export const departmentFormColumns = [
  {
    Name: 'GoalName',
    HeaderName: 'Tên mục tiêu',
    HeaderKey: 'Tên mục tiêu',
    DisplayName: 'Tên mục tiêu',
    Width: 100,
    Sortable: true,
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: 20,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: true,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    IsLock: false,
    Template: null,
    AggregateFunc: null,
  },
  {
    Name: 'Weight',
    HeaderName: 'Trọng số',
    HeaderKey: 'Trọng số',
    DisplayName: 'Trọng số',
    Width: 100,
    Sortable: true,
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: 20,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: true,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    IsLock: false,
    Template: null,
    AggregateFunc: null,
  },
  {
    Name: 'LastResult',
    HeaderName: 'Cập nhật lần cuối',
    HeaderKey: 'Cập nhật lần cuối',
    DisplayName: 'Cập nhật lần cuối',
    Width: 100,
    Sortable: true,
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: 20,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: true,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    IsLock: false,
    Template: null,
    AggregateFunc: null,
  },
  {
    Name: 'Attachment',
    HeaderName: 'Tệp đính kèm',
    HeaderKey: 'Tệp đính kèm',
    DisplayName: 'Tệp đính kèm',
    Width: 100,
    Sortable: true,
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: 20,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: true,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    IsLock: false,
    Template: null,
    AggregateFunc: null,
  },
  {
    Name: 'TotalTarget',
    HeaderName: 'Tổng mục tiêu',
    HeaderKey: 'Tổng mục tiêu',
    DisplayName: 'Tổng mục tiêu',
    Width: 100,
    Sortable: true,
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: 20,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: true,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    IsLock: false,
    Template: null,
    AggregateFunc: null,
  },
  {
    Name: 'TotalActual',
    HeaderName: 'Tổng thực đạt',
    HeaderKey: 'Tổng thực đạt',
    DisplayName: 'Tổng thực đạt',
    Width: 100,
    Sortable: true,
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: 20,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: true,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    IsLock: false,
    Template: null,
    AggregateFunc: null,
  },
  {
    Name: 'Variance',
    HeaderName: 'Chênh lệch',
    HeaderKey: 'Chênh lệch',
    DisplayName: 'Chênh lệch',
    Width: 100,
    Sortable: true,
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: 20,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: true,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    IsLock: false,
    Template: null,
    AggregateFunc: null,
  },
  {
    Name: 'CompletionRate',
    HeaderName: 'Mức độ hoàn thành',
    HeaderKey: 'Mức độ hoàn thành',
    DisplayName: 'Mức độ hoàn thành',
    Width: 100,
    Sortable: true,
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: 20,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: true,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    IsLock: false,
    Template: null,
    AggregateFunc: null,
  },
  {
    Name: 'ResultStatus',
    HeaderName: 'Kết quả',
    HeaderKey: 'Kết quả',
    DisplayName: 'Kết quả',
    Width: 100,
    Sortable: true,
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: 20,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: true,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    IsLock: false,
    Template: null,
    AggregateFunc: null,
  },
  {
    Name: 'Status',
    HeaderName: 'Trạng thái',
    HeaderKey: 'Trạng thái',
    DisplayName: 'Trạng thái',
    Width: 100,
    Sortable: true,
    Hidden: false,
    Format: null,
    OrderColumn: null,
    RowOnPage: 20,
    Group: false,
    Sum: false,
    isNumber: false,
    Disable: true,
    BackgroundFields: null,
    ColorFields: null,
    GroupIndex: null,
    IsLock: false,
    Template: null,
    AggregateFunc: null,
  },
];

export const departmentFormDataSource = [
  {
    GoalName: 'Doanh thu phân phối bán lẻ',
    Weight: '75%',
    LastResult: '25/12/2025',
    Attachment: 'BC-doanh-thu.xlsx',
    TotalTarget: '100 tỷ VND',
    TotalActual: '120 tỷ VND',
    Variance: '+ 10 tỷ VND',
    CompletionRate: '110%',
    ResultStatus: 'Vượt chỉ tiêu',
    Status: 'Chờ xác nhận',
  },
  {
    GoalName: 'Doanh thu xuất khẩu',
    Weight: '20%',
    LastResult: '25/12/2025',
    Attachment: 'BC-san-luong.xlsx',
    TotalTarget: '200.000 xe',
    TotalActual: '190.000 xe',
    Variance: '-10.000 xe',
    CompletionRate: '95%',
    ResultStatus: 'Không đạt',
    Status: 'Chờ xác nhận',
  },
  {
    GoalName: 'Doanh thu bán xe nội địa',
    Weight: '20%',
    LastResult: '25/12/2025',
    Attachment: 'BC-doanh-thu.xlsx',
    TotalTarget: '50 tỷ VND',
    TotalActual: '290 tỷ VNĐ',
    Variance: '0',
    CompletionRate: '100%',
    ResultStatus: 'Đạt chỉ tiêu',
    Status: 'Chờ xác nhận',
  },
  {
    GoalName: 'Doanh thu sản phẩm mới',
    Weight: '20%',
    LastResult: '25/12/2025',
    Attachment: 'BC-doanh-thu.xlsx',
    TotalTarget: '30 tỷ VND',
    TotalActual: '15 tỷ VND',
    Variance: '-15 tỷ VND',
    CompletionRate: '50%',
    ResultStatus: 'Đã huỷ',
    Status: 'Đã huỷ',
  },
];