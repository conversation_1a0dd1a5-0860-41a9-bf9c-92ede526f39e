{"appInfo": {"modules": {"default": "Dashboard", "example": "Example Page", "attendance": "Attendance", "insurance": "Insurance", "salary": "Salary", "humanResources": "Human Resources", "objEval": "Objective & Evaluation"}}, "loadMore": "Load More ", "E_VACANCY_EXPAND": "Expand", "systemFilter": "Filter", "filterClearAll": "Clear", "filterApply": "Apply", "addFilterAdvance": "Add advanced filtering conditions", "applyFilterAdvance": "Applying filter conditions", "confirmClearAllFilter": "Do you want to delete all currently selected filter conditions?", "saveDefaultValue": "Save the current value as the default value", "uploading": "uploading", "errorUploadFile": "Error during upload", "AdditionalInformation": "Additional Information", "DisplayNameLabelConfig": "Display Name", "FieldNameLabelConfig": "Field Name", "ControlNameLabelConfig": "Control Type", "duplicateFieldName": "already exists, please choose another name.", "ValidatorsFirstIsnotNumber": "The first character cannot be a number or special character.", "messConfirmDeleteInConfig": "Do you want to delete this data?", "general": {"btn": {"cancel": "Cancel", "save": "Save"}}, "system": {"title": "SYSTEM", "versionInvalid": "Version Invalid", "oldVersionInfo": "Feature is using old version 🙁", "suggestUpgrade": "Upgrade feature for a better experience 💡", "backToTop": "Back To Top", "languages": {"VN": "Vietnamese", "EN": "English", "CN": "Chinese", "JP": "Japanese"}}, "topBar": {"issuesHistory": "Issues History", "projectManagement": "Project Management", "typeToSearch": "Search...", "findPages": "Enter keywords to search", "notifyReloadAndMarkedAll": "<PERSON> As Read", "syncLanguage": "<PERSON><PERSON> đồng bộ ngôn ngữ", "actions": {"Title": "Notifications", "MarkIsRead": "Mark all as read", "Notifi": {"noData": "You don't have notifications"}}, "ortherInformation": "Another information", "status": "Status", "profileMenu": {"twoFactorAuth": "Two-Factor Authentication (2FA)", "hello": "Hello", "billingPlan": "Billing Plan", "role": "Role", "email": "Email", "phone": "Phone", "editProfile": "Edit Profile", "logout": "Logout", "signOut": "Sign out", "viewProfile": "View profile", "changePassWord": {"cancel": "Cancel", "title": "Change Password", "oldpass": "Old Password", "newpass": "New Password", "comfirnpass": "Confirm Password", "EnterOldPass": "Enter Old Password", "EnterNewPass": "Enter New Password", "EnterConfirmPass": "Enter Confirm New Password", "oldpassword": {"required": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> nhập mật kh<PERSON>u cũ"}, "newpassword": {"required": "<PERSON><PERSON><PERSON> khẩu mới là bắt buộ<PERSON>, nh<PERSON><PERSON> nó không đư<PERSON><PERSON> điền.", "mustMacthOldPwdvNewPwd": "{{newpassword}} và {{oldpassword}} không được trùng nhau."}, "confirmPassword": {"mustMatchNewPassword": "{{newpassword}} và {{confirmPassword}} ph<PERSON>i gi<PERSON>ng nhau.", "required": "<PERSON><PERSON><PERSON> nh<PERSON>n <PERSON><PERSON><PERSON> kh<PERSON>u là bắt buộc."}, "response": {"errors": "Thay đổi mật khẩu không thành công"}, "newPassword": {"errors": {"minLength": "ph<PERSON><PERSON> chứa ít nhất {{minLength}} kí tự", "pattern": "Sử dụng ít nhất 8 ký tự. <PERSON>o gồm: số, chữ hoa, chữ thường và ký tự đặc biệt"}}}, "changePassWordSalary": {"title": "Change Payslip Password", "oldpass": "Old Password", "newpass": "New Password", "comfirnpass": "Confirn <PERSON>"}, "changePassword": {"oldpassword": {"required": "Required to enter old password"}, "newpassword": {"required": "New password is required, but it is not filled out.", "mustMacthOldPwdvNewPwd": "The new password and the old password cannot be the same."}, "confirmPassword": {"mustMatchNewPassword": "The new password and confirmation password must be the same.", "required": "Confirmation of the Password field is required."}, "response": {"errors": "Password change failed"}, "newPassword": {"errors": {"pattern": "Use at least 8 characters. Include a number, an upper case letter, a lower case letter and a special character"}}}, "saveAvatar": "Change avatar", "checkVersion": "Check Version"}, "notification": {"E_New": "New notification", "E_Seen": "Viewed notifications", "tab": {"personal": "Personal", "all": "All", "manage": "Manage"}, "delete": "Delete notifications", "nodata": "No notifications", "registrationData": "registration data", "hasBeen": "has been", "date": "date", "has": "has", "mySelf": "Mine"}}, "common": {"tooltipTitleUpload": "Including file types", "searchProfileNameOrCodeEmp": "Search for employee code and name", "backHome": "Go back home page", "typingNotes": "Entering notes for the task", "noComment": "There are no additional notes", "feedback": "<PERSON><PERSON><PERSON>", "delete": "Delete", "viewDetail": "View Detail", "deleteComment": "Will the comments in the notes be lost when deleted?", "showMore": "Show More", "edit": "Edit", "create": "Create", "NotTemplateToExport": "Please choose a template to export!", "atDateTime": "At", "scanInfoFromFile": "Scan Information From Files", "expand": "Expand", "collapse": "Collapse", "fileNotFound": "File Not Found!", "processData": "Processing...", "copied": "<PERSON>pied", "copy": "Copy", "note": "Note", "validate": {"PasswordLowercase": "Including at least {{lower}} lowercase (a-z)", "PasswordUppercase": "Including at least {{upper}} uppercase (A-Z)", "PasswordMaxLength": "Can only contain up to {{max}} characters", "PasswordMaxMinLength": "Including at least {{min}} characters and only contains a maximum of {{max}} characters", "PasswordPrivacyPolicy": "Your new password be at least 8 characters long, including digits, uppercase characters, lowercase characters and special characters.", "PasswordMinDigits": "Including at least {{n}} digit (0-9)", "PasswordUpperLowercase": "Including at least {{lower}} lowercase (a-z) and at lease {{upper}} uppercase (A-Z)", "PasswordMinSpecials": "At least {{n}} special characters", "PasswordMinLength": "At least {{min}} characters", "evaMark": "[{{kpiCode}} - {{kpiName}}] score  must be in {{ScoreList}}!", "evaActual": "[{{kpiCode}} - {{kpiName}}] actual must be in {{ActualList}}!", "evaActual2": "[{{kpiCode}} - {{kpiName}}] actual 2 must be in {{ActualList2}}!", "salary": {"PasswordLowercase": "Including at least {{lower}} lowercase (a-z)", "PasswordUppercase": "Including at least {{upper}} uppercase (A-Z)", "PasswordMaxLength": "Can only contain up to {{max}} characters", "PasswordMaxMinLength": "Including at least {{min}} characters and only contains a maximum of {{max}} characters", "PasswordMinDigits": "Including at least {{n}} digit (0-9)", "PasswordUpperLowercase": "Including at least {{lower}} lowercase (a-z) and at lease {{upper}} uppercase (A-Z)", "PasswordMinSpecials": "At least {{n}} special characters", "PasswordMinLength": "At least {{min}} characters"}, "system": {"PasswordMinDigits": "Minimum {{n}} numeric characters (0-9).", "PasswordUpperLowercase": "Including at least {{lower}} lowercase (a-z) and at lease {{upper}} uppercase (A-Z)", "PasswordLowercase": "Including at least {{lower}} lowercase (a-z).", "PasswordUppercase": "Must contain at least one uppercase letter.", "PasswordMinSpecials": "Minimum {{n}} special characters.", "PasswordMinLength": "Minimum {{min}} characters.", "PasswordMaxLength": "Max {{max}} characters.", "PasswordMaxMinLength": "Including at least {{min}} characters and only contains a maximum of {{max}} characters", "PassNearExpirationWarningTime": "New password is duplicate to the one used in the last {{day}} days."}}, "ON": "On", "OFF": "Off", "YES": "Yes", "AGREE": "Agree", "NO": "No", "backward": "Backward", "confirmDeleteSurvey": "Do You Want To Delete This Survey?", "OK": "OK", "loading": "Loading...", "cancel": "Cancel", "errors": {"descErr500Prod": "An error occurred during data processing"}, "from": "From", "to": "To", "search": "Search", "searchHistory": "Search History", "notification": {"titleInfo": "Information", "titleError": "Error", "permissionError": "Permission Denied", "descErr404": "Resource not found. Please contact Admin or IT for support", "descErr403": "You Do Not Have Access To The Resource. Please contact Admin or IT for support", "descErr401": "Unauthenticated account. Please login to the system and try again", "descErr400": "Invalid data entered. Please check the data entered.", "descErr422": "An error occurred during data processing. Please contact Admin or IT for support", "descErr500": "An error occurred during data processing. Please contact Admin or IT for support", "descErr201": "An error occurred during data processing. Please contact Admin or IT for support", "descErr204": "An error occurred during data processing. Please contact Admin or IT for support", "descErr202": "An error occurred during data processing. Please contact Admin or IT for support", "titleErr404": "Resource Not Found", "titleErr403": "Permission Denied", "titleErr401": "Authentication Error", "titleErr400": "Input Error", "titleErr500": "Data Processing Error", "titleErr422": "Data Processing Error", "titleErr201": "Data Processing Error", "titleErr202": "Error Processing Not Completed", "descErr500Prod": "Action Successful", "actionSuccess": "Action Success!"}, "message": {"dataInvalid": "Invalid Grid Data", "actionSuccess": "Action Success!", "actionError": "Action Error!", "noChanges": "No Changes To Save!", "successfully": "Success!", "empty": "No data", "resetTitle": "Are you sure you want to reset to default?", "resetContent": "Everything you set will be returned to default", "actionProgress": "Action progress", "pleaseEnterTheMissingInforBeforeSave": "Please enter the missing information before saving", "columnNameAlreadyExists": "The column with this name already exists. Please give the column a different name.", "updateSuccessful": "Update Successful!", "columnGroupSuccess": "Successfully added column group", "expired": "The news has expired!"}, "disconnectNetwork": "Disconnecting the network...", "connectingWebcamError": "We can't find your camera", "connectingWebcamSupError": "Check to be sure it's connected and installed properly, that it isn't being blocked by antivirus software, and that your camera drivers are up-to-date", "camera": "Camera", "connectingNetwork": "Reconnecting the network...", "titleConnectingNetwork": "Connection information", "messageConnectingNetwork": "Please wait, the system will automatically reload in a moment. If the system does not automatically reload you can press the (F5) key or press refresh in the browser.", "title": {"error": "Error", "success": "Success", "warning": "Warning", "info": "Info"}, "button": {"save": "Save", "cancel": "Cancel", "selectFiles": "Choose file...", "exportExcelWithTemplate": "Export Excel", "exportWordWithTemplate": "Export Word", "exportPdf": "Export PDF", "exportExcel": "Export Excel", "exportWord": "Export Word", "reset": "Reset"}, "grid": {"noData": "No Data Available", "orderNumber": "Order Number", "searchInput": "Search", "searchAdvanced": "Advanced", "actionDelete": "Delete", "actionChangeColumn": "Change Column", "actionChangeColumnV2": "Data Grid", "actionChangeToolbar": "<PERSON><PERSON><PERSON>", "actionCustomizeToolbar": "Toolbar customization", "actionAddNew": "Add New", "actionAddMulti": "Add Multiple", "actionEdit": "Edit", "actionDuplicate": "Duplicate", "actionCancel": "Cancel", "actionCreateTemplate": "Create Template", "actionExportExcel": "Export Excel", "noSelectedId": "Please select data!", "noSelectedIdSync": "Please select data to Sync!", "noSelectedId_Delete": "Please select data row to delete!", "columnName": "Name", "columnWidth": "<PERSON><PERSON><PERSON>", "columnGroup": "Group", "columnTotal": "Total", "columnReadOnly": "Read Only", "columnFormat": "Format", "actionSaveChange": "Save Changes", "actionCancelChange": "Cancel Changes", "pagerItemsPerPage": "item per page", "pagerItems": "items", "pagerOf": "of", "pagerPage": "Pager", "sortAscending": "Sort Ascending", "sortDescending": "Descending", "columnMenu": "<PERSON><PERSON><PERSON>", "columns": "Columns", "columnsReset": "Reset", "columnsApply": "Apply", "setDefaultColumn": "You are using the Default Configuration.", "changeColumn": {"collapse": "Collapse", "expand": "Expand", "changeColumnText": "Change column", "view": "View", "showInfoTitle": "Show data grid information", "pinCal": "Column pin", "unpin": "Unpin", "show": "Show", "hidden": "Hidden", "simple": "Simple", "recommended": "Recommended", "createColumn": "Add Column", "restore": "Format config", "notiRestore": "Do you agree to configure this data column by default?", "notiDeleteCol": "Do you agree to delete this data column?", "edit": "Edit", "delete": "Delete", "translate": "Translate Column", "nameColumn": "Field Column", "dropAdd": "Add", "dropInsertTop": "Insert Top", "dropInsertMiddle": "Insert Middle", "dropInsertBottom": "Insert Bottom", "dropInvalid": "Invalid"}, "buttonAction": {"ReRegister": "Re-Register", "RemoveRequestCanceled": "Cancel Request Canceled", "RequestCanceled": "Request Canceled", "Canceled": "Cancel", "Sendmail": "Send Request", "Edit": "Edit", "Delete": "Delete", "Approved": "Approve", "Rejected": "Reject", "ExpandAll": "Expand All", "CollapseAll": "Collapse All"}, "format": {"Number": "Number", "DateTime": "DateTime", "Others": "Others", "Label": "Label", "UploadFile": "Upload File", "File": "File", "Link": "Link", "Text": "Text", "Dropdown": "Dropdown", "MultiSelect": "MultiSelect", "Category": "Category"}}, "modal": {"createNew": "Create New", "titleAddNew": "Add New", "titleEdit": "Edit", "saveEdit": "Save", "confirmDelete": "Are you sure you want to delete?", "confirmClearList": "Clear all list employee selected?", "employeesSelected": "Employees {{n}} seleccted", "clearSelected": "Clear all", "confirmDeleteTable": "Are you sure you want to delete table?", "cancelSave": "Are you sure you want to cancel save?", "deleteCriteria": "Do you want to delete selected criteria?", "buttonOk": "Yes", "buttonContinue": "Continue", "buttonExit": "Exit page", "buttonNo": "Cancel", "save": "Save", "saveNew": "Save & Add New", "saveClose": "Save & Close", "close": "Close", "invalidForm": "Form Invalid", "confirmExportExcel": "Do You Want To Export All Items?", "saveReload": "Save & Reload", "confirmDeleteV2_sup1": "Do you want to delete", "confirmDeleteV2_sup2": "data row?", "confirmConfirmV2_sup1": "Do you want to confirm", "confirmConfirmV2_sup2": "data row(n)?", "confirmRequest_sup1": "Do you want to send mail request", "confirmSubmitRequest_sup1": "Do you want to send an email to request an edit", "confirmSendMailRequest": "Would you like to email all the data?", "confirmSendMailRequest_sup1": "Would you like to email", "confirmSendMailRequest_sup2": "rows of data?", "confirmApprove_sup1": "Do you want to approve", "confirmSubmitRefuse_sup1": "Do you want to reject", "confirmTransferData_sup1": "Are you sure you want to transfer", "confirmExitPage": "Are you sure you want to exit?", "applyRegisterWeek": "Scheduled for the current week. Do you want to use last week's registration data instead of the current week?", "applyLastWeek": "Can`t apply last week", "titleExitPage": "You have unsaved changes", "reasonContentCancel": "You are cancel", "reasonContentRequestCancel": "You are cancellation requests", "reasonContentApprove": "You are approved", "reasonContentReject": "You are rejecting", "reasonContentRowSelected": "data lines.", "reasonContent2": "If you want to enter a different reason, you need to select again.", "reasonContent_2": "If you want to enter a different note, you need to select again.", "confirmRemoveUploadFile": "Are you sure you want to delete the uploaded data?", "confirmReplaceFile": "Are you sure you want to change the attached file?", "PreviousPage": "Go to the Previous page", "NextPage": "Go to the Next page", "FirstPage": "Go to the first page", "LastPage": "Go to the last page", "PagerOf": "Page"}, "hint": {"dropUploadFile": "Drop files here to upload"}, "butotn": {"selectFiles": "Select files..."}, "validation": {"duplicate": "has been duplicated!", "invalid": " is invalid!", "required": "Can't be empty!", "pattern": "Value does not match required pattern", "minLength": "Value must be {{minLength}} characters", "maxLength": "Value must be a maximum of {{maxLength}} characters", "email": "Required Is Email Format (Ex: <EMAIL>)", "min0": "must be greater than 0", "min": "Value must be greater than or equal to {{n}} ", "max": "Value must be less than or equal to {{n}}", "toMin": "Value must be greater than {{n}} ", "toMax": "Value must be less than {{n}}", "orther": "Value must orther {{n}}", "startNumber": "the starting number must be less than the ending number", "warningDuplicate": "there are duplicate data", "warningInvalid": "there are invalid data", "info": "The warning will not affect the user's experience", "attachmentFile": "Maximum attachment limit {{limit}} files", "IsEqualTo": "{{v}} must be equal to {{n}}.", "IsNotEqualTo": "{{v}} must not be equal to {{n}}.", "IsGreaterThanOrEqualTo": "{{v}} must be greater than or equal to {{n}}.", "IsGreaterThan": "{{v}} must be greater than {{n}}.", "IsLessThanOrEqualTo": "{{v}} must be less than or equal to {{n}}.", "IsLessThan": "{{v}} must be less than {{n}}.", "Contains": "{{v}} must contain the character '{{n}}'.", "DoesNotContain": "{{v}} must not contain the character '{{n}}'.", "StartsWith": "{{v}} must start with {{n}}.", "EndsWith": "{{v}} must end with {{n}}.", "IsAfter": "{{v}} must be after the date {{n}}.", "IsAfterOrEqualTo": "{{v}} must be after or equal to the date {{n}}.", "IsBefore": "{{v}} must be before the date {{n}}.", "IsBeforeOrEqualTo": "{{v}} must be before or equal to the date {{n}}.", "Between": "{{v}} must start from {{n}}.", "NotBetween": "{{v}} must not start from {{n}}.", "UseRegex": "{{v}} must follow the pattern '{{n}}'.", "UseRegex_Customize": "{{v}} must contain a number.", "minLengthCustom": "Value must be {{n}} characters", "maxLengthCustom": "Value must be a maximum of {{n}} characters"}, "nodata": "No Data", "changeColumn": {"title": "Customize Column", "defaultConfig": "Configure by <PERSON><PERSON><PERSON>", "saveConfig": "Save Configuration", "hideColumn": "Hide Column", "showColumn": "Show Column", "sortASC": "Ascending", "sortDESC": "Descending", "rowPage": "Rows/Page", "orderGroup": "Priority Group", "minColumnToOrderGroup": "The Minimum Data Group Must Be 2 Columns Or More", "noColumnToOrderGroup": "No Data Group"}, "action": {"exportExcel": "Export Excel"}, "config": {"restoreConfigSuccess": "Restore config successful!", "restoreConfigDefault": "You are using the default configuration!", "restoreConfigFailed": "<PERSON><PERSON> config failed!", "saveConfigSuccess": "Save config successful!", "dropMoreArea": "Drag and drop functions in the \"more\" area", "dropConfigArea": "Drag and drop the functions you need onto the toolbar", "noDataDrop": "Drag & drop the tool into the configuration area", "restoreDefaultConfig": "Restore configuration", "cancel": "Cancel", "save": "Save"}, "process": {"addSteps": "Add Step", "save": "Save", "add": "Add", "cancel": "Cancel", "implementationSteps": "Step Name", "implementer": "Executor", "required": "Implementation Steps or Implementer Can't be empty!", "exist": "Implementation Steps and Implementer Already Exist!", "selectAssignee": "Please select the primary executor", "saveStep": "Save Workflow"}, "uploadStatus": {"success": "Upload file success", "failed": "Upload file fail"}, "btnCancel": "Cancel", "btnRegister": "Register", "btnSave": "Save", "CodeGenerate": {"systemAutomaticCreate": "<PERSON><PERSON> thống tự động sinh"}}, "menuItem": {"news": {"index": "News V2"}, "survey": {"title": "Survey", "listSurvey": "List Survey", "listSurveyQuiz": "List Quiz", "listSurveyHistory": "History of Survey & Quizzes"}, "report": {"title": "Report", "evalue": "Evaluation Result"}, "evaluation": {"title": "Evaluation V2", "evaluationBoard": "Evaluation Board", "orgChartTarget": "Organizational goals", "achievementReport": "9-Box Grid"}, "overtime": {"title": "Overtime", "titleApprove": "Approve Overtime", "overtimes": "Overtimes", "hour": " Hour", "otMonth": "Exceeded OT Month", "otYear": "Exceeded OT Year", "attWorkingTimeOver": "Overtime", "attOvertimeType": "OT Type", "attSignUpToEat": "Request OT Meal", "attRequestShuttleCar": "Request Shuttle Car", "attWrongShift": "Wrong Shift", "attProfile": "Employee", "attJobTitle": "Job Title", "attPosition": "Position", "otLimit": "Exceeding OT", "inputPlaceholderReason": "Please enter reason"}, "leaveday": {"title": "Leave Day", "leavedays": "Leave Record", "attYear": "Year", "attDay": "Day", "hour": " Hour"}, "tamscanlog": {"title": "Forgot Timekeeping", "title-approve": "Approve Forgot Time Attendance", "tamscanlogs": "Leave Record", "hour": " Hour", "createTamScanlog": "Forgot to register for timekeeping", "updateTamScanlog": "Update forgot timekeeping", "inputPlaceholderComment": "Please enter a description", "reasonMissInOut": "No IN/OUT Reason", "reasonMissInOutDetail": "Add detailed reason or attachment"}, "attendance": {"title": "Attendance V2", "ATT_ALL": "All", "ATT_PENDING": "Pending", "ATT_APPROVED": "Approved", "ATT_DENIED": "Denied", "ATT_CANCEL": "Cancel", "attCreate": "Create", "attEdit": "Edit", "attSendMail": "Send Request", "attCancel": "Cancel", "attCancelAll": "Cancel All", "attRequestCancel": "Request Cancel", "attApprovedAll": "Approved All", "attRejectAll": "Reject All", "attMonth": "Month", "attMonthWork": "ATT Period", "attMonthWorkPlaceholder": "ATT Period", "attYear": "Year", "attOrgAll": "Org All", "attApproved": "Approved", "attReject": "Reject", "attStatus": "Status", "attDelete": "Delete selected row ({{n}})", "attScope": "Search Range", "attFilterByType": "Type"}, "training": {"title": "Training V2", "trainingScheduler": "Training Scheduler", "viewTitle": "Training Schedule V2"}, "fruit": {"title": "Fruit", "compliment": "Compliment"}}, "dashboard": {"exit": "Exit", "save": "Save", "edit": "Edit", "cancel": "Cancel", "menuWidget": {"externalLinkOption": "External Link", "assetManagementTitle": "Asset Management"}, "menuActionDashboard": {"content": "Content", "inputSearch": "Input Search Text...", "searchTitle": "Search", "emptyMenu": "Please set up menu shortcut in Dashboard page", "widgetManagement": "Widget Management"}, "paramConfig": "<PERSON><PERSON><PERSON> hình tham số", "saveConfig": "<PERSON><PERSON><PERSON> c<PERSON>u h<PERSON>nh", "cancelConfig": "<PERSON><PERSON><PERSON>", "menuDashboardItems": {"widgetViewStatistics": "View statistics", "widgetIcon": "Change icon", "widgetSimpleChartist": "Simple chartist", "widgetNews": "News Portal", "widgetClock": "Clock", "widgetCalendar": "Work Calendar", "widgetDynamicChart": "Dynamic chart", "widgetDynamicTable": "Dynamic table", "widgetReportChart": "Report chart", "widgetBirthday": "Birthday", "widgetProgressJob": "Progress job chart", "newCalendar": "Calendar"}, "calendar": {"holiday": "Public Holiday", "searchInput": "Search events, employees...", "seeEventDetail": "View detail", "seeMore": "Show more ({{length}})", "seeLess": "Show less", "emptyEvent": "There is no information or events", "leave": "Leave of Absence", "birthday": "Birthday", "employee": "New Employee", "seniority": "Anniversary", "quit": "Employee Termination", "leaveN": "Leave of Absence ({{n}})", "birthdayN": "Birthday ({{n}})", "employeeN": "New Employee ({{n}})", "seniorityN": "Anniversary ({{n}})", "quitN": "Employee Termination ({{n}})", "today": "Today", "day": "Day", "week": "Week", "month": "Month", "year": "Year", "configDrawer": "General", "content": "Content", "showHide": "Show", "authorized": "Authorization data", "allEmployee": "All employee", "accordingEachUser": "Based On User Permissions", "notStudy": "Not Study", "studying": "Studying", "studied": "Studied", "course": "Course", "courseType": "Course Type", "Location": "Location", "NewsType": "Event type", "Content": "Event description", "MemberName": "Member", "event": "Event", "eventN": "Event ({{n}})", "configEvents": "Show info", "room": "Room", "method": "Method", "certificate": "Certificate", "trainee": "Trainee", "trainer": "Trainer", "note": "Content", "allDay": "Full Day", "showFullDay": "Show Full Day", "showWorkDay": "Show Work Day", "person": "Person", "allCourse": "All Courses", "department": "Department In Charge", "allTeachers": "All Teachers", "filter": "Filter", "classStatus": "Class Status", "trainingFormView": "Training Form", "placeholderSearch": "Search Class Code, Class Name..."}, "training": {"trainerTitle": "Trainers", "departmentTitle": "Rooms", "attendanced": "Attendanced: ", "datePaginate": {"resetDateBtn": "Today", "monthBtn": "Month", "weekBtn": "Week"}, "filter": {"title": "Filter", "classroom": "Class", "trainingForm": "Training form", "startDay": "From", "endDay": "To", "trainingDepartment": "Room", "saveBtn": "Save", "resetBtn": "Reset", "loading": "Loading...", "staffPlancehoder": "Trainers", "departmentPlancehoder": "Rooms", "provincePlancehoder": "Province/City", "trainingFormEnum": {"outside": "Training outside", "inside": "Training inside", "other": "Other"}}, "dayOfWeek": {"sunday": "Sunday", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday"}, "qrPopup": {"ClassName": "Class name", "ScheduleTimeStart": "Start time", "ScheduleTimeEnd": "End time", "CourseName": "Course", "ClassForm": "Class form", "TrainingForm": "Training place", "TrainingPlace": "Training place", "LocationName": "Training location", "RoomName": "Conference room", "TrainingMethodName": "Training method", "TrainingTime": "Training period", "AttachedFile": "Attachment", "OrgStructureName": "Implementing Unit", "CertificateName": "Certificate", "WorkplaceName": "Workplace", "PartnerName": "Partner", "MassNumProfileAttendanced": "Expected Trainees", "ActualTraineeNumber": "Actual Trainees", "Note": "Note", "QRCode": "QR Code", "DownloadQRCode": "Download QR Code"}}}, "menuTop": {"Pages": "Pages"}, "message": {"noSupportFeatureMenuTopOnScreen": "No support menu top on this screen device"}, "config": {"Title": "Title", "Value": "Value", "Placeholder": "Select chossen", "ViewColumn": "Column number", "colors": {"title": "Configs colors", "text": "Text  color", "Background": "Background color"}, "Icon": {"Change": "Select Icon", "configTitle": "Change icon"}, "simpleChart": {"title": "Simple Chartist"}, "Link": {"Image": "Image link"}, "ViewStatistics": "View Statistics", "chartist": {"function": "Function"}, "dataType": "Data type", "getTheStructure": "Get the structure", "getparam": "Get param", "addField": "Add field", "moreConfiguration": "More configuration", "parameterValue": {"enterString": "Enter String", "selectDateWithTime": "Select date with time", "selectDate": "Select a date", "enterNumber": "Enter Number", "enterNumberFormat": "Enter number in format", "selectStartDateeEndDate": "Select a start date and an end date", "selectMonthYear": "Select month and year", "enterStartEndNumber": "Enter a start number and an end number", "selectDepartment": "Select department", "chooseTheStartEndMonth": "Choose the start month and the end month"}}, "permission": {"dined": "Permission Deined"}, "template": {"default": "<PERSON><PERSON><PERSON>", "widgetTest": {"template1": "Template 1", "template2": "Template 2", "template3": "Template 3"}}, "vnrcontrol": {"common": {"dateFromPlaceholder": "Date Start", "dateToPlaceholder": "Date End", "dateFromPlaceholderV2": "Start", "dateToPlaceholderV2": "End", "selectPlaceholder": "Please select...", "inputPlaceholder": "Please enter...", "monthFromPlaceholder": "Month start", "monthToPlaceholder": "Month end", "dropGroupGrid": "Drag a column header and drop it here to group by that column", "warningReload": "The translation has changed. Please reload the page to update the new translation."}, "demo": {"labelOrg": "Org Structure", "labelTreeView": "TreeView", "labelTreeViewPanel": "TreeView Panel", "labelComboBox": "ComboBox", "labelAutoComplete": "AutoComplete", "labelInput": "Input", "labelInputNumber": "Input Number", "labelInputNumberFormat": "Input Number Format", "labelMulti": "Multi Select", "labelCheckBox": "CheckBox", "labelSwitch": "Switch", "labelColorPicker": "Color Picker", "labelDateTimePicker": "DateTime Picker", "labelTimePicker": "Time Picker", "labelDatePicker": "Date Picker", "labelDateRangePicker": "Date Range Picker", "labelRadioButton": "Radio Button", "labelFileAttachment": "File Attachment", "labelTextArea": "Description", "labelEditor": "Content", "jobVacancyName": "Job Vacancy Name", "dateRequest": "Date Request", "code": "Code"}, "uploadFile": {"allowFileExtensions": "Only Upload File {{extensions}}", "maxLengthFileName": "File name length must not exceed {{maxLength}} characters."}}, "portal-general": {"tabs": {"general": "General", "configs": "Configs", "advanced": "Advanced"}, "panels": {"settingApi": "API", "settingCommon": "Title", "configApi": "Config Condition", "settingProgress": "Process configuration"}}, "common-config": {"chooseLanguage": "Select language", "header": "Header", "sub-title": "Subtitle", "footer": "Footer", "template": "Template", "show": "Show", "hide": "<PERSON>de", "on": "On", "off": "Off", "Dashboard": "Dashboard", "MenuShortcut": "Shortcut", "totalWork": "Total work", "complete": "Complete", "permissions": "Key Permissions"}, "auth": {"titleSuccess": "Action Success!", "titleError": "Action Error!", "title403": "Access Denied", "message403": "Sorry, you are not authorized to access this page.", "title403Ids": "Access Denied", "message403Ids": "Sorry, you are not authorized to access this system.", "title500": "Server Error", "message500": "Sorry, there is an error on server.", "title404": "Page Not Found", "message404": "Sorry, the page you visited does not exist.", "tokenExpired": "QR code has expired.", "common": {"accessDined": "Access is not available, authorization is not yet supported, or functionality is not supported!"}, "button": {"backHome": "Back Home", "backHomeIds": "Sign out"}}, "layout": {"positon": {"leftToRight": "Left to Right", "rightToLeft": "Right to Left", "topToBottom": "Top to Bottom", "bottomToTop": "Bottom to Top"}}, "grid": {"toolbar": {"more": "More", "enterKeywordToSearch": "Enter keywords to search..."}, "filter": {"contains": "Contains", "isEqualTo": "Is Equal To", "isNotEqualTo": "Is Not Equal To", "doesNotContain": "Does Not Contain", "startsWith": "Starts With", "endsWith": "Ends With", "isNull": "<PERSON>", "isNotNull": "Is Not Null", "isEmpty": "Is Empty", "isNotEmpty": "Is Not Empty", "isAfter": "Is After", "isAfterOrEqualTo": "Is Afer Or EqualTo", "isBefore": "Is Before", "isBeforeOrEqualTo": "Is Before Or Equal To", "andLogic": "And", "orLogic": "Or", "filterButton": "Filters"}}, "table-column": {"config": {"data": {"type": "Config data type"}}}, "themeSettings": {"isShowUndoDelete": "Hide confirm message", "ConfigDrawerFilter": "Config Drawer <PERSON>", "title": "Theme Settings", "menuLayout": "<PERSON><PERSON>out", "noMenu": "Group", "topMenu": "Top", "leftMenu": "Left", "routerAnimation": "Router Animation", "breadcrumbs": "Breadcrumbs", "show": "Show", "header": "Header", "leftMenuWidth": "<PERSON><PERSON><PERSON>", "leftMenuCollapsed": "Collapsed", "leftMenuUnFixed": "Unfixed", "leftMenuShadow": "Shadow", "menuColor": "Menu Color", "auth": "<PERSON><PERSON>", "authBackground": "Background", "topbarFixed": "Fixed", "topbarGrayBackground": "Gray <PERSON>", "app": "App", "appContentMaxWidth": "Content Max-W<PERSON>th", "appMaxWidth": "<PERSON>", "appGrayBackground": "Gray <PERSON>", "cards": "Cards", "cardsSquaredBorders": "Square Borders", "cardsShadow": "Shadow", "cardsBorderless": "Borderless", "leftMenuTitle": "<PERSON><PERSON>", "tenantDashboard": "Hide Tenant Dashboard", "tenantOpenNewTab": "Open in new tab", "config": "Configuration", "format": "Format", "configSystem": "Configuration System", "startDayOfTheWeek": "Start day of the week", "timeFormat": "Time format", "gridDensity": "Grid Density", "dayOfWeek": {"sunday": "Sunday", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday"}}, "emptyDataFavs": "No Bookmarks", "errorHandler": {"alert": "An error occurred from the system; please try again later.", "throwError": "Something bad happened; please try again later.", "error": "An error occurred:"}, "modalConfig": {"arrangementPosition": "Order", "title": "Config", "contentEmpty": "The device's screen is not supported", "currentKeyTranslate": "Current Translation", "key": "Key", "translate": "Re-Translate", "order": "Order", "hidden": "Hidden", "required": "Required", "min": "Min", "max": "Max", "placeholderMin": "Enter The Mnimum Number", "placeholderMax": "Enter the maximum number", "minLength": "Minimum Characters", "maxLength": "Maximum Characters", "placeholderMinLength": "Enter Minimum Characters", "placeholderMaxLength": "Enter Maximum Characters", "pattern": "Pattern", "copyTransKey": "Click the box to copy the key", "copied": "<PERSON>pied", "copyAll": "Copy All"}, "extendedInformation": {"isUnitAssistant": "Unit assistant switch", "isRequestForBenefit": "Request Emergency Allowance Payment", "isTripOTInDay": "Biz Trip OT Hrs/Day", "isRequestEntryExitGate": "Request Entry/Exit Gate", "isSignUpToEat": "Request OT Meal", "isRequestShuttleCar": "Request Shuttle Car"}, "dayInWeek": {"Monday": "Monday", "Tuesday": "Tuesday", "Wednesday": "Wednesday", "Thursday": "Thursday", "Friday": "Friday", "Saturday": "Saturday", "Sunday": "Sunday", "Daily": "Daily"}, "filter": {"comfirmClosePopup": "Do you want to save the unapplied filter conditions?", "yes": "Yes", "no": "No", "from": "From", "to": "To", "config": "Config", "currentFilter": "current filter", "filterLibrary": "filter library", "pleaseconfigureFilter": "please configure filter", "success": "Success", "dataSourceType": "Data source type", "method": "method", "dataSource": "Data source", "theSelectedFilterWasNotFoundPleaseReselectTheConfig": "The selected filter was not found. Please re-select the config", "doYouWantToRestoreThisFilter": "Do you want to restore this filter?", "doYouWantToCancelChangeThisFilter": "Do you want to cancel change this filter?", "doYouWantToSaveChangeThisFilter": "Do you want to save change this filter?", "theFilterWillDisappear": "The filter will disappear?", "warningChangeStore": "You must re-add the filter because when you select [Store], the current filter conditions will change.", "warningChangeQuickFilter": "You must re-add [Quick Filter] because re-selecting it will change the current filter conditions.", "warningChangeFullFilter": "You must re-add [Filter] because re-selecting it will change the current filter conditions.", "customizeFilters": "Customize filters", "apiUrl": "Api url", "textField": "Text field", "valueField": "Value field", "mapperFieldDateTo": "Mapper field to [DateTo]", "mapperFieldToDateRangeFrom": "Mapper field to [DateRangeFrom]", "mapperFieldToDateRangeTo": "Mapper field to [DateRangeTo]", "mapperFieldToDateExpried": "Mapper field to [DateExpried]", "disabledKey": "Disabled key", "minValue": "Min value", "maxValue": "Max value", "maxCount": "Max count", "maxShow": "Max show", "placeholderLeft": "Please holder [left]", "placeholderRight": "Please holder [right]", "expandOrCollapse": "Expand/Collapse", "pleaseSelect": "Please Select", "doYouWantToDeleteThisFilter": "Do you want to delete this filter?", "defaultValue": "Default value", "pleaseEnterDefaultValue": "Please enter default value", "fullFilter": "Full filter", "quickFilter": "Quick filter", "availableFilters": "Available filters", "filter": "Filter", "showFullFilter": "Show full filter", "orPressTheKeyCombination": "or press the key combination", "saveConfig": "Save config", "cancelConfig": "<PERSON>cel config", "restoreDefault": "Restore default", "DataSource_Api": "Data Source API", "DataSource_Store": "Data Source Store", "DataSource_Entity": "Data Source Entity", "DataSource_Enum": "Data Source Enum", "DataSource_Table": "Data Source Table", "unfiltered": "Unfiltered", "addRow": "Add row", "labelName": "Label name", "value": "Value"}, "systems": {"DataPosition": "DataPosition", "index": "Systems V3.0", "No": "No", "ConfirmPopup": "Are you sure you want to save the data?", "Confirm": "Confirm", "Cancel": "Cancel", "Please": "Please", "btnDownload": "download the sample file", "supPlease": "if you don't have a configuration template.", "ConfigurationName": "Configuration Name", "Function": "Function", "ConfigurationList": "Configuration List", "SelectDataSourcePage": "Select Data Source Page", "SpecifyHeaderRow": "Specify Header Row", "SpecifyDataStartRow": "Specify Data Start Row", "InitializeData": "Initialize Data", "initData": "Initializing data...", "initDataErr": "Initializing data failed!", "initDataSuccess": "Initializing data successfully!", "Check": "Check", "Save": "Save", "Edit": "Edit", "ConfirmImport": "Confirm", "ProcessingProposal": "Processing Proposal", "DeleteConfiguration": "Delete Configuration", "SaveTemplateConfiguration": "Save Template Configuration", "DataConfiguration": "Data Configuration", "CheckData": "Check Data", "popupSaveTemplateConfiguration": "Are you sure you want to save the configuration?", "contentPopupConfirmImport": "There are {{n}} error cells. Are you sure you want to save and ignore the errors?", "FileDataRows": "{{n}} File Data Rows", "DeleteInvalidData": "Delete invalid data", "FileName": "File Name", "SystemName": "System Name", "Format": "Format", "error": "Error", "errorNoFile": "No Configuration Files", "errorNoImport": "No data to save", "DefaultValue": "Default Value", "DateUpdated": "Date Updated", "SelectProcessing": "Select data processing method", "AddNew": "Add New", "Replace": "Replace", "search": "Search", "DuplicateDataRows": "The system detected {{n}} duplicate data rows.", "OnlyExcelFiles": "Only Excel files (xls, xlsx, xlsm) are supported.", "supportType": "Supported file formats (doc or pdf).", "popupEditImport": "Are you sure you want to discard the entered data and go to the configuration page?", "popupDeleteImport": "Are you sure you want to delete configuration?", "popupDeleteInvalidImport": "Are you sure you want to delete the invalid data lines in the file?", "noChanges": "No changes have been made to the configuration.", "noSelectedConfig": "Please select a configuration list first to enter data.", "noFileImportConfig": "Please upload the file to the system, and it will suggest the initialization configuration for you.", "sysImport": {"index": "Import Data", "onlyDisplayErrData": "Only display error data", "noData": "No data", "alertGridDataErr": "Total of {{n}} errors in the uploaded file. Please fix the errors and upload the file again.", "checkData": "Check Data", "importData": "Import Data", "pleaseEnterData": "Please Enter Data", "Executor": "Executor", "DateExecution": "Date Execution", "Expertise": "Expertise", "ConfigurationTemplate": "Configuration Template", "ChangeHistory": "Change History", "searchText": "Search", "createCofigImport": "Create Configuration"}}, "configv3": {"information": "Information register", "anotherInfo": "Another information", "undefined": "Information general", "Undefined": "Information general", "visible": "Visible", "required": "Required", "checkDuplicate": "Duplicate", "disable": "Disable", "default": "<PERSON><PERSON><PERSON>", "condition": "Condition", "addCondition": "Add condition", "clearCondition": "Clear condition", "clear": "Clear", "value": "Value", "add": "Add", "actionVisible": "Custom hide and show", "actionRequired": "Is additional data required?", "actionCheckvalidator": "Does the data match or not?", "actionDisable": "Only viewing permission, no editing allowed", "actionCondition": "Default display on the interface", "blockEditEntity": "Major data, cannot be edit", "blockEditRequired": "Required data, cannot be hidden", "blockEditHidden": "Data is hidden, cannot be forced to enter", "nonePhone": "Don`t have on version mobile", "keyUndefined": "Don`t have key translate"}, "objEval": {"Appraisals": {"execution": "Goal Execution", "statusTab": {"all": "All", "todo": "Need to do", "notsendtome": "Not sent to me", "approved": "Approved", "summaryResult": "Summary Result", "incomplete": "Incomplete", "complete": "Complete", "selfAssessment": "Self Assessment", "managerAssessment": "Manager Assessment", "approval": "Approval"}, "selectAllOrg": "Select all departments", "selectAllPosition": "Select all positions", "ProfileName": "Employee", "Department": "Department", "Position": "Position", "Grade": "Grade", "TargetScore": "Target Score", "CompetencyScore": "Competency Score", "TotalScore": "Total Score", "Status": "Status", "SelfAssessmentScore": "Self Assessment Score", "ManageDirectScore": "Manage Direct Score", "ManageSeniorScore": "Manage Senior Score", "AverageScore": "Average Score", "SuggestedScore": "Suggested Score", "Comment": "Comment", "btnAprove": "Approve", "btnViewEmployeeList": "View Employee List"}, "GoalTemplate": {"titleViewDetailTemplateGoal": "Template goal: {{name}}", "tagGoalItemsPerPage": "{{n}} goal", "template": "Template goal", "btnAddGoalGroup": "Add goal group", "btnAddGoalDetail": "Add goal", "GoalName": "Goal name", "GoalType": "Goal type", "GoalGroup": "Goal group", "GroupCode": "Goal group code", "GroupName": "Goal group name", "Description": "Description", "MeasurementalValue": "Measurement value", "Unit": "Unit", "addGoalGroup": "Add goal group", "addGoalDetail": "Add goal", "btnCancel": "Cancel", "btnSave": "Save", "pleaseEnterGroupCode": "Please enter goal group code", "pleaseEnterGroupName": "Please enter goal group name", "pleaseEnterDescription": "Please enter description", "Information": "Basic information", "SetupCalculation": "Setup calculation", "ConnectDataSource": "Connect data source", "AllocationBy": "Allocation by", "AllocationCorpFormula": "Allocation formula for company", "AllocationEmployeeFormula": "Allocation formula for employee", "AllocationTimeFormula": "Allocation formula for time", "CalculatorResultBy": "Calculate result by", "CalculatorResultOther": "Calculate result other", "ConnectionToSystem": "Connect to system", "ConfigIntegration": "Config integration", "pleaseEnterAllocationCorpFormula": "Please enter allocation formula for company", "pleaseEnterAllocationEmployeeFormula": "Please enter allocation formula for employee", "pleaseEnterAllocationTimeFormula": "Please enter allocation formula for time", "pleaseEnterCalculatorResultBy": "Please enter calculation result", "pleaseEnterCalculatorResultOther": "Please enter calculation result other", "pleaseEnterConnectionToSystem": "Please enter connection system", "pleaseEnterConfigIntegration": "Please enter config integration", "pleaseEnterTargetScale": "Please enter target scale", "pleaseEnterWeight": "Please enter weight", "pleaseEnterMinimumValue": "Please enter minimum value", "pleaseEnterMaximumValue": "Please enter maximum value", "pleaseEnterUnit": "Please enter unit", "pleaseEnterGoalCode": "Please enter goal code", "pleaseEnterGoalName": "Please enter goal name", "pleaseEnterGoalGroup": "Please enter goal group", "Weight": "Weight", "GoalCode": "Goal code", "MinimumValue": "Minimum value", "MaximumValue": "Maximum value", "MeasurementRange": "Measurement range", "NoMeasurement": "No measurement", "TargetScale": "Target scale", "AIFormulaSuggestions": "AI formula suggestions", "refresh": "Refresh", "warningConfigConnectSystem": "Cannot configure data source when selecting 'Manual input' calculation method", "AddNewSource": "+ Add new source", "NoConnect": "No connect", "AddGoal": "Add goal", "cancelSelect": "Cancel select", "selectGoalGroup": "Select goal group", "ConfigureAllocationFormula": "Configure allocation formula", "ConfigFormulaAllocationByCorp": "Configure allocation formula by corp", "ConfigFormulaAllocationByEmployee": "Configure allocation formula by employee", "ConfigFormulaAllocationByTime": "Configure allocation formula by time"}, "GoalPeriod": {"Title": "Goal period", "GoalName": "Goal name", "GoalGroup": "Goal group", "GoalDetail": "Goal detail", "Implementation": "Implementation", "Representative": "Representative", "Quarter1": "Quarter 1", "Quarter2": "Quarter 2", "Quarter3": "Quarter 3", "Quarter4": "Quarter 4", "Month1": "January", "Month2": "February", "Month3": "March", "Month4": "April", "Month5": "May", "Month6": "June", "Month7": "July", "Month8": "August", "Month9": "September", "Month10": "October", "Month11": "November", "Month12": "December", "TotalTarget": "Total target", "ProfileName": "Employee", "Type": "Type", "Department": "Department", "Trend": "Trend", "Target": "Target", "Unit": "Unit", "Status": "Status", "Process": "Process", "Allocation": "Allocation", "PersonalObjective": "Personal Objective", "DepartmentObjective": "Department Objective", "Position": "Position", "ExecutionObject": "Execution Object", "ConfirmApprove": "Confirm Approve", "Reject": "Reject", "Confirm": "Confirm", "Cancel": "Cancel", "Period": "Period", "InputPeriod": "Input Period", "TargetValue": "Target Value", "InputTargetValue": "Input Target Value", "AllocationType": "Allocation Type", "Employee": "Employee", "InputEmployee": "Select employee", "Reminder": "Reminder", "RequestAdjust": "Request Adjust", "Approve": "Approve", "Adjust": "Adjust", "InformationDetail": "Information detail", "CalculationMethod": "Calculation method", "Description": "Description", "Discussion": "Discussion", "History": "History", "GoalProgress": "Goal progress", "TotalRevenue": "Total revenue", "Tree": "Tree", "List": "List", "Table": "Table", "GoalPersonal": "Goal Personal", "GoalDepartment": "Goal Department", "All": "All", "WaitingConfirm": "Waiting Confirm", "Confirmed": "Confirmed", "Rejected": "Rejected", "Registered": "Registered", "Allocated": "Allocated", "GoalRegister": "Goal Register", "GoalConfirm": "Goal Confirm", "GoalAllocated": "Goal Allocated", "AllocationDetail": "Allocation goal detail", "AllocationChild": "Allocation goal child", "WaitingApprove": "Waiting Approve", "Approved": "Approved", "ApprovalTarget": "Approval target", "ApprovalComment": "Approval comment", "ApproveGoal": "Approve goal", "AllocationCycle": "Allocation cycle", "AllocationCycleDescription": "Select the way to allocate goals by time", "AllocationPersonDepartment": "Allocation by department/person", "AllocationPersonDepartmentDescription": "Select the object to receive allocation and set details", "AllocationMethod": "Allocation method", "DoneTarget": "Done target", "JobTitle": "Job title", "Guideline": "Guideline"}, "GoalResult": {"Title": "Result", "Extend": "Extend", "Passed": "Passed", "Failed": "Not passed", "Exceeded": "Exceeded", "UpdateDate": "Update date", "DueDate": "Due date"}, "GoalCycle": {"GoalCycle": "Goal Cycle", "Code": "Code", "Name": "Name", "TypeName": "Type", "Type": "Type", "btnAddGoalCycle": "Add cycle", "CreatedDate": "Created date", "LastUpdatedDate": "Last updated date", "LastUpdatedBy": "Last updated by", "TimeOfCycle": "Time of cycle", "btnSave": "Save", "btnCancel": "Cancel", "addGoalCycle": "Add new", "pleaseEnterCode": "Please enter code", "pleaseEnterName": "Please enter name", "pleaseEnterTypeName": "Please enter type", "TimeFrom": "From", "TimeTo": "To", "Quarter": "Quarter", "Month": "Month", "Year": "Year", "HalfYear": "Half year", "Custom": "Custom"}, "EvaPerformanceAppraisals": {"formatNumberTemplate": "{{n}} appraisal forms", "warningEmployeeNoTemplate": "Warning: {{n}} employees have no appraisal form.", "additional": "Additional", "statusTab": {"All": "All", "InProgress": "In Progress", "Plan": "Plan", "Completed": "Completed"}, "addEmployee": {"title": "Add Employee", "employeeInformation": "Employee Information", "listOfEmployeesExpectedToParticipate": "List of Employees Expected to Participate", "btnCreateList": "Create List", "btnRefresh": "Refresh"}, "statusPublic": {"Plan": "Plan", "InProgress": "In Progress", "Public": "Public", "Pending": "Pending", "Canceled": "Canceled", "Completed": "Completed", "Published": "Published"}, "btnMore": {"edit": "Edit", "viewDetail": "View Detail", "clone": "<PERSON><PERSON>", "delete": "Delete", "transferResult": "Transfer Result"}, "quickFilter": {"TypeAppraisals": "Type Appraisals", "Status": {"Label": "Status", "Draft": "Draft", "Submitted": "Submitted", "InReview": "In Review", "Completed": "Completed", "Cancel": "Cancel"}}, "MonthlySalaryReview": "Monthly Salary Review", "PeriodicSalaryReview": "Periodic Salary Review", "PerformanceReview": "Performance Review", "RewardReview": "Reward Review", "ProbationaryReview": "Probationary Review", "SuccessionPlanning": "Succession Planning", "AppointmentReview": "Appointment Review", "employeeParticipating": "Employees", "implementingDepartment": "Implementing Department", "noAppraisals": "No Appraisals", "inProgress": "In Progress", "canceled": "Canceled", "reAppraisals": "Re Appraisals", "btnAddAppraisal": "Add Appraisal", "title": "Performance Appraisals", "cycleName": "Appraisal Cycle Name", "totalEmployees": "Total Employees", "completed": "Completed", "incomplete": "Incomplete", "departments": "Applied Departments", "positions": "Job Positions", "dueDate": "Due Date", "status": "Status", "addAppraisal": "Add Appraisal", "editAppraisal": "Edit Appraisal", "closed": "Close", "addNew": "Add New Appraisal", "edit": "Edit Appraisal", "continueRegister": "Continue Registration", "configFormValidatorRegister": "Register Performance Appraisal", "configFormValidatorEdit": "Edit Performance Appraisal", "EvaluationType": {"label": "Evaluation type", "placeholder": "Please select"}, "Description": {"label": "Description", "placeholder": "Please enter"}, "AnonymousAppraisals": {"label": "Anonymous appraisals"}, "IsRepeatAppraisals": {"label": "Repeat appraisals"}, "Status": {"label": "Status", "placeholder": "Please select"}, "PeriodType": {"label": "Period goal", "placeholder": "Please select"}, "PeriodDate": {"label": "Evaluation period", "placeholder": {"from": "From", "to": "To"}}, "ApplyObject": {"label": "Apply Object", "placeholder": "Select apply object"}, "ApplyDepartment": {"label": "Apply Department", "placeholder": "Select apply department"}, "ApplyPosition": {"label": "Apply Position", "placeholder": "Select apply position"}, "ApplyEmployee": {"label": "Apply Employee", "placeholder": "Select apply employee"}, "AppraisalName": {"label": "Appraisal Name", "placeholder": "Enter appraisal name"}, "AppraisalTemplate": {"label": "Appraisal Template", "placeholder": "Select appraisal template"}, "AppraisalProcess": {"label": "Appraisal Process", "placeholder": "Select appraisal process"}, "EvaluationForm": {"label": "Evaluation Form", "placeholder": "Select evaluation form"}, "grid": {"ProfileName": "Employee", "JobPosition": "Job Position", "Department": "Department", "Status": "Status", "EvaluationForm": "Evaluation Form", "AppraisalsProcess": "Appraisals Process", "AppraisalsTime": "Appraisals Time", "SelfEvaluation": "Self Evaluation", "EvaluatorName": "Manager Evaluation", "CriteriaName": "Criteria Name", "Direction": "Direction", "Target": "Target", "MinimumTarget": "Minimum Target", "Unit": "Unit", "Weight": "Weight", "Actual": "Actual", "CompletionRate": "Completion Rate"}, "btnSendEvaluation": "Send Evaluation", "btnAddEmployee": "Add Employee", "evaluationFormDetails": "Evaluation Form Details"}, "EvaTemplate": {"template": "Evaluation Template", "name": "Template Name", "enterName": "Enter template name", "description": "Description", "enterDescription": "Enter template description", "evaName": "Template Name", "evaType": "Template Type", "btnAddEvaGroup": "Add Evaluation Group", "btnAddEvaTemplate": "Create New", "selectTemplate": "Select Template", "createNewTemplate": "Create New Evaluation Template", "createSuccess": "Create evaluation template successfully", "updateSuccess": "Update evaluation template successfully", "deleteSuccess": "Delete evaluation template successfully", "createTemplate": "Create Evaluation Template", "editTemplate": "Edit Evaluation Template", "viewDetail": "View Evaluation Template Details", "deleteConfirm": "Are you sure you want to delete this evaluation template?", "startCreate": "Start Creating", "applyFor": "Apply For", "method": "Evaluation Method", "status": "Status", "selectApplyFor": "Select apply for", "selectMethod": "Select evaluation method", "department": "Department", "position": "Position", "selectDepartment": "Select department", "selectPosition": "Select position", "employee": "Employee", "selectEmployee": "Select employee", "templateType": "Template Type"}, "EvaCriteria": {"criteriaRepository": "Criteria repository", "criteriaType": "Criteria type", "btnAddCriteriaGroup": "Add criteria group", "btnAddCriteriaDetail": "Add criteria", "pleaseEnter": "Please enter", "Description": "Description", "InUse": "In use", "StopUse": "Stop use", "Code": "Criteria code", "Name": "Criteria name", "TypeRank": "Type", "CriteriaGroup": "Criteria group", "DescriptionCalculatorScore": "Description calculator score", "Scale": "Scale", "UseRateinsteadOfScore": "Use rate instead of score", "LinkToGoal": "Link to goal", "Note": "Note", "FileAttach": "File attach", "selectAttachment": "Select attachment", "selectCriteriaGroup": "Select criteria group", "cancelSelect": "Cancel select", "AddNewCriteriaRate": "Add new rating", "AddCriteria": "Add criteria", "EditCriteria": "Edit criteria", "btnAddCriteriaRate": "Add new rating", "CriteriaDetail": "Criteria detail"}, "EvaCriteriaGroup": {"CriteriaGroup": "Criteria group", "gridCriteriaGroupHeader": "Group: {{name}} ({{n}} criteria)", "Code": "Criteria group code", "Name": "Criteria group name", "CriteriaType": "Criteria type"}, "EvaCriteriaType": {"Code": "Criteria type code", "Name": "Criteria type name", "CriteriaTypeCode": "Criteria type code", "CriteriaTypeName": "Criteria type name", "NumberOrder": "Number order", "EditCriteriaType": "Edit criteria type", "AddCriteriaType": "Add criteria type", "btnAddNew": "Add new criteria type", "Appraisals_Goal": "Appraisals Goal", "Appraisals_Competency": "Appraisals Competency", "Appraisals_360": "Appraisals 360", "Appraisals_Custom": "Custom", "CriteriaType": "Type"}, "EvaCriteriaRate": {"Scale": "Scale", "Name": "Name", "Description": "Description", "AddNewCriteriaRate": "Add new rating", "EditCriteriaRate": "Edit rating", "AddCriteriaRate": "Add rating"}, "EvaConductAssessmentList": {"title": "Conduct Assessment List"}, "EvaProposalList": {"title": "Proposal List", "list": "List", "nineBox": "9-Box Grid", "StatusView": "Status", "Status": "Status", "TotalAssessmentScore": "Total Assessment Score", "Proposal": "Proposal", "ConvertToTraining": "Convert to Training", "PotentialList": "Potential List", "Promotion": "Promotion", "Grade": "Grade", "ProfileName": "Employee", "EmpCode": "Employee Code", "PerformanceResult": "Performance Result", "CompetencyResult": "Competency Result", "Position": "Job Position", "Department": "Department", "AskAI": "Ask Reca", "ViewEmployeeList": "View Employee List", "titleViewEmployeeNineBoxDetail": "Employee list: {{boxname}}", "nineBoxGrid": {"warningDeleteLabel": "Labeled data will be lost", "warningDeleteLabelSup": "This label is in use. Deleting it will remove associated labels", "warningCancel": "Cancel", "warningAgree": "Agree", "Add": "Add", "AddIDP": "Add IDP", "MarkLabel": "Mark Label", "CancelIDPNomination": "Cancel IDP nomination", "AddNewLabel": "Add New Label", "RemoveLabel": "Remove Label", "achievementExcellent": "Achievement Excellent", "achievementGood": "Achievement Good", "achievementBad": "Achievement Bad", "highCapacity": "High Capacity", "mediumCapacity": "Medium Capacity", "lowCapacity": "Low Capacity", "capacity": "COMPETENCY", "achievementResult": "PERFORMANCE", "high": "High", "medium": "Medium", "low": "Low", "excellent": "High", "good": "Medium", "bad": "Low", "employeeList": "Employees", "employeeListTitle": "Employees ( {{value1}} - {{value2}}% )", "unitStructureAll": "Division", "orgAll": "Department", "performanceTypeNew": "Evaluation Type", "NominateforIDP": "Nominate for IDP", "performancePlanNew": "Evaluation Period", "employeeList1": "Employee List"}}}, "formulaConfig": {"Config": "Config", "copied": "<PERSON>pied", "yourBrowserDoesNotSupportVoiceRecognition": "Your browser does not support voice recognition", "voiceNotRecognizedPleaseTryAgain": "Voice not recognized. Please try again.", "logicAndConditional": "Logic & Conditional", "functionIFRequires3ParametersSeparatedBy2Commas": "The IF function requires 3 parameters, separated by 2 commas", "functionANDRequiresAtLeast2ParametersSeparatedByCommas": "The AND function requires at least 2 parameters, separated by commas", "functionORRequiresAtLeast2ParametersSeparatedByCommas": "The OR function requires at least 2 parameters, separated by commas", "functionNOTRequires1Parameter": "The NOT function requires 1 parameter", "functionISNULLRequires1Parameter": "The ISNULL function requires 1 parameter", "functionIFERRORRequires2ParametersSeparatedByCommas": "The IFERROR function requires 2 parameters, separated by commas", "functionSWITCHRequiresAtLeast2ParametersSeparatedByCommas": "The SWITCH function requires at least 2 parameters, separated by commas", "mathAndArithmetic": "Math & Arithmetic", "functionSUMRequiresAtLeast1ParametersSeparatedByCommas": "The SUM function requires at least 1 parameter, separated by commas", "functionSUMIFSRequiresAtLeast1ParametersSeparatedByCommas": "The SUMIF function requires at least 1 parameter, separated by commas", "functionCOUNTRequires1Parameter": "The COUNT function requires 1 parameter", "functionCOUNTIFSRequiresAtLeast1ParameterSeparatedByCommas": "The COUNTIFS function requires at least 1 parameter, separated by commas", "functionAVERAGERequiresAtLeast1ParameterSeparatedByCommas": "The AVERAGE function requires at least 1 parameter, separated by commas", "functionROUNDRequires2ParametersSeparatedByCommas": "The ROUND function requires 2 parameters, separated by commas", "functionFLOORRequires1parameter": "The FLOOR function requires 1 parameter", "functionCEILINGRequires1parameter": "The CEILING function requires 1 parameter", "functionMODRequires2ParametersSeparatedByCommas": "The MOD function requires 2 parameters, separated by commas", "functionABSRequires1parameter": "The ABS function requires 1 parameter", "functionPOWERRequires2ParametersSeparatedByCommas": "The POWER function requires 2 parameters, separated by commas", "functionSQRTRequires1parameter": "The SQRT function requires 1 parameter", "dateAndTime": "Date & Time", "functionNOWDoesNotRequireParameters": "The NOW function does not require parameters", "functionTODAYDoesNotRequireParameters": "The TODAY function does not require parameters", "functionDATEDIFFRequires3ParametersSeparatedByCommas": "The DATEDIFF function requires 3 parameters, separated by commas", "functionADDYEARRequires2ParametersSeparatedByCommas": "The ADDYEAR function requires 2 parameters, separated by commas", "functionADDMONTHRequires2ParametersSeparatedByCommas": "The ADDMONTH function requires 2 parameters, separated by commas", "functionADDDAYRequires2ParametersSeparatedByCommas": "The ADDDAY function requires 2 parameters, separated by commas", "functionADDHOURRequires2ParametersSeparatedByCommas": "The ADDHOUR function requires 2 parameters, separated by commas comma", "functionADDMINUTERequires2ParametersSeparatedByCommas": "The ADDMINUTE function requires 2 parameters, separated by commas", "functionWEEKDAYRequires1Parameter": "The WEEKDAY function requires 1 parameter", "functionDAYRequires1Parameter": "The DAY function requires 1 parameter", "functionMONTHRequires1Parameter": "The MONTH function requires 1 parameter", "functionYEARRequires1Parameter": "The YEAR function requires 1 parameter", "stringProcessing": "String processing", "functionCONCATRequiresAtLeast1ParameterSeparatedByCommas": "The CONCAT function requires at least 1 parameter, separated by commas", "functionLEFTRequires2ParametersSeparatedByCommas": "The LEFT function requires 2 parameters, separated by commas", "functionRIGHTRequires2ParametersSeparatedByCommas": "The RIGHT function requires 2 parameters, separated by commas", "functionLENRequires1Parameter": "The LEN function requires 1 parameter", "functionTRIMRequires1Parameter": "The TRIM function requires 1 parameter", "functionLOWERRequires1Parameter": "The LOWER function requires 1 parameter", "functionUPPERRequires1Parameter": "The UPPER function requires 1 parameter", "functionSUBSTRINGRequires3ParametersSeparatedByCommas": "The SUBSTRING function requires 3 parameters, separated by commas", "functionFINDRequires2ParametersSeparatedByCommas": "The FIND function requires 2 parameters, separated by commas", "advancedCheck": "Advanced Check", "functionINRequires2ParametersSeparatedByCommas": "The IN requires 2 parameters, separated by commas", "functionBETWEENRequires3ParametersSeparatedByCommas": "The BETWEEN function requires 2 parameters, separated by commas", "functionEXISTSRequires1Parameter": "The EXISTS function requires 1 parameter", "functionTYPEOFRequires1Parameter": "The TYPEOF function requires 1 parameter", "functionFORMATRequires2ParametersSeparatedByCommas": "The FORMAT function requires 2 parameters, separated by commas", "operators": "Operators", "Equal": "Equal", "NotEqual": "Not equal", "GreaterThan": "Greater than", "LessThan": "Less than", "GreaterThanEqual": "Greater than equal", "LessThanEqual": "Less than equal", "Plus": "Plus", "Minus": "Minus", "Multiplication": "Multiplication", "Division": "Division", "Percent": "Percent", "missingOpeningParenthesis": "Missing opening parenthesis '('", "missingClosingParenthesis": "Missing closing parenthesis '('", "thisSymbolIsNotSupported": "Character {name} is not supported", "invalidCharacterOutsideString": "Character {name} is not valid", "missingStringTerminator": "String does not have a closing terminator", "correctSyntax": "Correct syntax {}", "invalidFunctionSyntax": "Syntax error", "functionDoesNotExist": "Function does not exist", "suggestion": "Suggestion", "enumDoesNotExist": "enum does not exist", "doNotUseTheFunctionInTheLogicalConditionOfIF": "Do not use the function {name} in the logical condition of IF", "errorDividingByZero": "Error dividing by zero", "addEnum": "Add enum", "search": "Search", "formula": "Formula", "aiSuggestionFormula": "AI suggests formula", "descriptionToAiSuggestFormula": "Description for AI to suggest formula", "functionAndOperator": "Function and Operator", "cancelConfig": "Cancel", "saveConfig": "Save"}}