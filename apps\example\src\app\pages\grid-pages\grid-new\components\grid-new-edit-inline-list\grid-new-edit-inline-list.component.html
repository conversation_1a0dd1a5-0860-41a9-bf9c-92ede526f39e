<button nz-button nzType="warning" class="mr-2 ant-btn ant-btn-warning" (click)="reload()">
  IsSupperAdmin = {{ isSupperAdmin }}
</button>
<button nz-button nzType="warning" class="mr-2 ant-btn ant-btn-warning" (click)="reload1()">
  IsSupperAdmin = {{ isSupperAdmin }}
</button>

<div id="grid" class="mt-2">
  <div class="grid-2">
    <button
      nz-button
      class="mr-2 ant-btn ant-btn-primary"
      #anchor
      vnr-Popover-ChangeColumn
      (click)="toggleChangeColumn(false)"
    >
      Đ<PERSON><PERSON> cột v.1
    </button>
    <button
      nz-button
      class="mr-2 ant-btn ant-btn-success"
      (click)="gridEditInLine.addRowHandler(gridEditInLine.grid)"
      *ngIf="gridEditInLine?.isEdited"
    >
      Add New
    </button>
    <button
      nz-button
      class="mr-2 ant-btn ant-btn-primary"
      (click)="gridEditInLine.editAllHandler(gridEditInLine.grid)"
      *ngIf="!gridEditInLine?.isEdited"
    >
      Edit All
    </button>
    <button
      nz-button
      class="mr-2 ant-btn ant-btn-success"
      (click)="gridEditInLine.cancelAllHandler(gridEditInLine.grid)"
      *ngIf="gridEditInLine?.isEdited"
    >
      Cancel All
    </button>
    <button
      nz-button
      class="mr-2 ant-btn ant-btn-success"
      (click)="gridEditInLine.saveAllHandler(gridEditInLine.grid)"
      *ngIf="gridEditInLine?.isEdited"
    >
      Save
    </button>
    <vnr-grid-new-Edit-Inline
      #gridEditInLine
      [builder]="builderGrid"
      [dataLocal]="dataLocal"
      [columns]="columns"
      [gridName]="gridName"
      [isSupperAdmin]="isSupperAdmin"
      [isChangeColumnNew]="true"
      (getSelectedID)="getSelectedID($event)"
      (getDataItem)="getDataItem($event)"
      (vnrDoubleClick)="onOpenDetail($event)"
      (vnrViewModeGrid)="onVnRViewModeGrid($event)"
      (vnrEdit)="onGridEdit($event)"
      (vnrDelete)="onGridDelete($event)"
      (vnrViewDetails)="onGridViewDetail($event)"
      (vnrCellClick)="onGridCellClick($event)"
    ></vnr-grid-new-Edit-Inline>

    <nz-divider></nz-divider>
  </div>
</div>
<ng-template #vnrTemplateCustomButtonAction let-dataItem>
  <!-- {{ dataItem | json }} -->
  <!-- <div class="mr-1">
    <button
      nz-button
      nzType="primary"
      (click)="onEdit(dataItem)"
      class="btn-kendoGrid-customize btn-kendoGrid-customize__primary ant-btn ant-btn-primary"
    >
      <i class="fas fa-pen"></i>
    </button>
  </div>
  <div>
    <button
      nz-button
      nzType="primary"
      (click)="onViewDetails(dataItem)"
      class="btn-kendoGrid-customize btn-kendoGrid-customize__primary ant-btn ant-btn-primary"
    >
      <i nz-icon nzType="eye" nzTheme="outline"></i>
    </button>
  </div> -->
</ng-template>
<ng-template #tplCustomTemplateByColumn let-dataItem let-column="columnItem" let-field="field">
  <ng-container [ngSwitch]="column['Name']">
    <span class="--has-custom-template" *ngSwitchCase="'KPIID'">
      <span *ngIf="dataItem?.KPIID">
        <div class="vnrGrid-manage-personnel-records__profiles">
          {{ dataItem?.KPIName }}
          <!-- <app-vnr-letters-avatar
              class="vnrGrid-manage-personnel-records__image"
              [avatarName]="dataItem?.ProfileName"
              [circular]="true"
              [width]="32"
              [src]="dataItem?.ImagePath"
            ></app-vnr-letters-avatar> -->
          <!-- <div class="d-flex flex-column justify-content-center">
            <div class="vnrGrid-manage-personnel-records__name">
              {{ dataItem?.ProfileName }}
            </div>
          </div> -->
        </div>
      </span>
    </span>
    <span class="--has-custom-template" *ngSwitchCase="'StatusView'">
      <!-- <vnr-tag
          class="vnrGrid-manage-personnel-records__tag mb-0"
          [vnrColor]="dataItem?.Status | formatStatus"
          [vnrTitle]="dataItem?.StatusView"
        >
        </vnr-tag> -->
      {{ dataItem?.StatusView }} - {{ dataItem?.Status }}
    </span>
    <span class="--has-custom-template" *ngSwitchDefault>
      {{ dataItem[column['Name']] }}
    </span>
  </ng-container>
</ng-template>
<ng-template #vnrTemplateCustomButton let-dataItem>
  <div class="mr-1">
    <button
      nz-button
      nzType="primary"
      (click)="gridEditInLine.cancelAllHandler(gridEditInLine.grid)"
      class="btn-kendoGrid-customize btn-kendoGrid-customize__primary ant-btn ant-btn-primary"
    >
      <i class="fas fa-pen"></i>
    </button>
  </div>
  <div>
    <button
      nz-button
      nzType="primary"
      (click)="gridEditInLine.cancelAllHandler(gridEditInLine.grid)"
      class="btn-kendoGrid-customize btn-kendoGrid-customize__primary ant-btn ant-btn-primary"
    >
      <i nz-icon nzType="eye" nzTheme="outline"></i>
    </button>
  </div>
</ng-template>

<ng-template
  #templateHeaderGroup
  let-dataItem
  let-aggregates="aggregates"
  let-aggregateItem="aggregateItem"
  let-columnItem="columnItem"
  let-group="group"
>
  {{ dataItem | json }}
  <div [ngClass]="{ 'vnr-grid-no-border': columnItem.Name === 'KPIName' }"></div>
  <div
    class="header-custom"
    *ngIf="
      aggregates &&
      aggregates.aggregates &&
      aggregates.aggregates[columnItem.Name] &&
      columnItem.AggregateFunc
    "
  >
    <ng-container
      *ngIf="
        columnItem &&
          columnItem.Format &&
          columnItem?.Format?.split('|')[0]?.toLowerCase() === 'number';
        else tplDefaultFormat
      "
    >
      <ng-container
        *ngIf="columnItem?.Format?.split('|')[1] === 'cp'; else tplColumnNoCustomHeader"
      >
        {{
          aggregates?.aggregates[columnItem.Name][columnItem.AggregateFunc] !== null
            ? (aggregates?.aggregates[columnItem.Name][columnItem.AggregateFunc]
              | kendoNumber : '#,##0.00 \\%')
            : '%'
        }}
      </ng-container>
      <ng-template #tplColumnNoCustomHeader>
        {{
          aggregates?.aggregates[columnItem.Name][columnItem.AggregateFunc]
            | kendoNumber : columnItem?.Format?.split('|')[1]
        }}
      </ng-template>
    </ng-container>
    <ng-template #tplDefaultFormat>
      {{ aggregates?.aggregates[columnItem.Name][columnItem.AggregateFunc] }}
    </ng-template>
  </div>
</ng-template>
<ng-template
  #templateFooter
  let-dataItem="dataItem"
  let-aggregates="aggregates"
  let-aggregateItem="aggregateItem"
  let-columnItem="columnItem"
>
  <div *ngIf="columnItem.Name === 'KPIName'">
    {{ 'Tổng cộng :' }}
  </div>
  <ng-container *ngIf="aggregateItem && aggregateItem[columnItem.Name]">
    <div class="header-custom" [ngSwitch]="columnItem.Name">
      <div *ngSwitchCase="'aaaaa'"></div>
      <div *ngSwitchDefault>
        <ng-container
          *ngIf="
            columnItem &&
              columnItem.Format &&
              columnItem?.Format?.split('|')[0]?.toLowerCase() === 'number';
            else tplDefaultFormat
          "
        >
          <ng-container
            *ngIf="columnItem?.Format?.split('|')[1] === 'cp'; else tplColumnNoCustomHeader"
          >
            {{
              aggregateItem[columnItem.Name][columnItem.AggregateFunc] !== null
                ? (aggregateItem[columnItem.Name][columnItem.AggregateFunc]
                  | kendoNumber : '#,##0.00 \\%')
                : '%'
            }}
          </ng-container>
          <ng-template #tplColumnNoCustomHeader>
            {{
              aggregateItem[columnItem.Name][columnItem.AggregateFunc]
                | kendoNumber : columnItem?.Format?.split('|')[1]
            }}
          </ng-template>
        </ng-container>
        <ng-template #tplDefaultFormat>
          {{ aggregateItem[columnItem.Name][columnItem.AggregateFunc] }}
        </ng-template>
      </div>
    </div>
  </ng-container>
</ng-template>
<ng-template #vnrTemplateCustomButtonAddRowGroup let-group let-index="index">
  <button
    (click)="gridEditInLine.addRowHandler(gridEditInLine.grid, index)"
    class="btn-kendoGrid-customize btn-kendoGrid-customize__add btn-kendoGrid-customize__rounded btn-kendoGrid-customize__small ant-btn ant-btn-primary"
  >
    <span>
      <i class="far fa-plus"></i>
    </span>
  </button>
</ng-template>
<ng-template
  #templateFooterGroup
  let-dataItem
  let-aggregates="aggregates"
  let-aggregateItem="aggregateItem"
  let-columnItem="columnItem"
  let-group="group"
>
  <!-- {{ aggregates | json }}
  aaaa -->
  <div [ngClass]="{ 'vnr-grid-no-border': columnItem.Name === 'KPIName' }"></div>
  <div
    class="header-custom"
    *ngIf="aggregates && aggregates[columnItem.Name] && columnItem.AggregateFunc"
  >
    <ng-container
      *ngIf="
        columnItem &&
          columnItem.Format &&
          columnItem?.Format?.split('|')[0]?.toLowerCase() === 'number';
        else tplDefaultFormat
      "
    >
      <ng-container
        *ngIf="columnItem?.Format?.split('|')[1] === 'cp'; else tplColumnNoCustomHeader"
      >
        {{
          aggregates[columnItem.Name][columnItem.AggregateFunc] !== null
            ? (aggregates[columnItem.Name][columnItem.AggregateFunc] | kendoNumber : '#,##0.00 \\%')
            : '%'
        }}
      </ng-container>
      <ng-template #tplColumnNoCustomHeader>
        {{
          aggregates[columnItem.Name][columnItem.AggregateFunc]
            | kendoNumber : columnItem?.Format?.split('|')[1]
        }}
      </ng-template>
    </ng-container>
    <ng-template #tplDefaultFormat>
      {{ aggregates[columnItem.Name][columnItem.AggregateFunc] }}
    </ng-template>
  </div>
</ng-template>
