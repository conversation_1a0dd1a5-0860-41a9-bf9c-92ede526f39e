<vnr-toolbar-new
  class="border-0 --custom-bg width-100p manage-personnel-records-grids__toolbar"
  [builder]="builderToolbar"
  (vnrChangeColumn)="toggleChangeColumn($event)"
>
</vnr-toolbar-new>
<vnr-treelist-new
  #vnrTreeList
  [builder]="builderTreeList"
  [gridName]="gridName"
  [dataLocal]="dataLocal"
  [columns]="columns"
  [isSupperAdmin]="isSupperAdmin"
  [isChangeColumnNew]="false"
  [vnrRowClass]="rowClass"
  (getSelectedID)="getSelectedID($event)"
  (getDataItem)="getDataItem($event)"
  (getSelectedDataItem)="getSelectedDataItem($event)"
  (vnrEdit)="onGridEdit($event)"
  (vnrDelete)="onGridDelete($event)"
  (vnrViewDetails)="onGridViewDetail($event)"
  (vnrCellClick)="onGridCellClick($event)"
  (vnrCellGroupClick)="onGridCellGroupClick($event)"
  (vnrAfterCollapse)="onAfterCollapse($event)"
  (vnrAfterExpand)="onAfterExpand($event)"
  (vnrColumnClick)="onColumnClick($event)"
  (vnrDoubleClick)="onDoubleClick($event)"
></vnr-treelist-new>
<ng-template #templateCurrentNumber let-dataItem>
  <span class="bg-success p-2">{{ dataItem?.CurrentNumber }}</span>
</ng-template>
<ng-template #tplCustomTemplateByColumn let-dataItem let-column="columnItem" let-field="field">
  <ng-container [ngSwitch]="column['Name']">
    <span class="--has-custom-template" *ngSwitchCase="'CurrentNumber'">
      <span class="bg-success p-2">{{ dataItem?.CurrentNumber }}</span>
    </span>
    <span class="--has-custom-template" *ngSwitchCase="'LastYearEstablishmentNumber'">
      {{ dataItem?.LastYearEstablishmentNumber }}
    </span>
    <span class="--has-custom-template" *ngSwitchDefault>
      <span class="p-2"> {{ dataItem[column['Name']] }} </span>
    </span>
  </ng-container>
</ng-template>
