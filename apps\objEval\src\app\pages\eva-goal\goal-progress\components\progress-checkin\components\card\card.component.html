<div class="card mb-3 shadow-sm">
  <div class="card-header d-flex justify-content-between align-items-center bg-white border-bottom-0 py-3">
    <div class="d-flex align-items-center" style="gap: 8px;">
      <strong>Báo cáo tuần 4</strong>
      <span class="">(03/06/2025 - 09/06/2025)</span>
      <vnr-tag [vnrColor]="'default'" [vnrTitle]="'Lưu nháp'" [isBordered]="true" class="bg-white"></vnr-tag>
    </div>
    <div class=" d-flex align-items-center justify-content-end">
      <button class="btn p-1 mr-1 btn-view-detail">Xem chi tiết</button>
      <button class="btn text-secondaryp-0" type="button" (click)="isCollapsed = !isCollapsed"
        [attr.aria-expanded]="!isCollapsed" aria-controls="cardBodyCollapse">
        <i class="fa" [ngClass]="{'fa-chevron-down': isCollapsed, 'fa-chevron-up': !isCollapsed}"></i>
      </button>
    </div>
  </div>
  <div [ngClass]="{'collapse': isCollapsed, 'show': !isCollapsed}" id="cardBodyCollapse">
    <div class="card-body pt-0 pb-3">
      <div class=" d-flex align-items-center">
        <div class="d-flex align-items-center">
          <app-vnr-letters-avatar [avatarName]="data.name" [circular]="true" [width]="32"
            class="mr-2"></app-vnr-letters-avatar>
          <div>
            <div>{{ data.name }} - {{ data.code }}</div>
            <div class="d-flex">
              <vnr-tag [vnrColor]="'default'" [vnrTitle]="data.factory" class="mr-1" [isBordered]="true"></vnr-tag>
              <vnr-tag [vnrColor]="'default'" [vnrTitle]="data.position" [isBordered]="true"></vnr-tag>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="card-footer bg-white py-2" style="border-top: 1px solid #E3E3E3;">
      <div class="d-flex align-items-center" style="color: #616161;gap: 16px;">
        <div class="d-flex align-items-center" style="gap: 4px;"><i class="fa-light fa-bullseye"></i>3 mục tiêu</div>
        <span>|</span>
        <div class="d-flex align-items-center" style="gap: 4px;"><i class="fa-light fa-comment"></i>0 phản hồi</div>
        <span>|</span>
        <div class="d-flex align-items-center" style="gap: 4px;"><i class="fa-light fa-clock"></i>12/06/2025</div>
      </div>
    </div>
  </div>
</div>