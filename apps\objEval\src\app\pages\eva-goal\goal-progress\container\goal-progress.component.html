<div class="page-header-container">
    <div class="d-flex justify-content-between align-items-center p-1 bg-white">
        <div class="page-title">
            Tiến độ
        </div>
        <form [formGroup]="form" class="d-flex">
            <vnr-combobox formControlName="quarter" [builder]="quarterBuilder" class="ml-2"></vnr-combobox>
            <vnr-datepicker formControlName="time" [builder]="datePickerBuilder" class="ml-2"></vnr-datepicker>
        </form>
    </div>
    <div class="tab-container">
        <div class="tab-text">
            <nz-tabset (nzSelectChange)="onTabChange($event)">
                <nz-tab *ngFor="let tab of tabs" [nzTitle]="tab.title"></nz-tab>
            </nz-tabset>
        </div>
    </div>
</div>

<!-- Body -->
<div [ngSwitch]="tabs[selectedTab]?.key">
    <ng-container *ngSwitchCase="'overview'">
        <div class="p-3 bg-white">
            <app-progress-overview></app-progress-overview>
        </div>
    </ng-container>

    <app-progress-checkin *ngSwitchCase="'checkin'"></app-progress-checkin>
    <app-progress-schedule *ngSwitchCase="'schedule'"></app-progress-schedule>
</div>