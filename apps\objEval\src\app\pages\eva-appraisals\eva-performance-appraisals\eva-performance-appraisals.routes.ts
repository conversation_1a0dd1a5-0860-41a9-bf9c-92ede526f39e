import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { EvaPerformanceAppraisalsDetailComponent } from './eva-performance-appraisals-detail/container/eva-performance-appraisals-detail.component';
import { EvaPerformanceAppraisalsComponent } from './eva-performance-appraisals-list/container/eva-performance-appraisals.component';
import { EvaAppraisalsFormComponent } from '../shared/components/eva-appraisals-form/container/eva-appraisals-form.component';

export const routes: Routes = [
  {
    path: 'list',
    component: EvaPerformanceAppraisalsComponent,
    data: {
      title: 'Danh sách đánh giá hiệu suất',
    },
  },
  {
    path: 'detail',
    component: EvaPerformanceAppraisalsDetailComponent,
    data: {
      title: 'Chi tiết đánh giá hiệu suất',
    },
  },
  {
    path: 'form-appraisal',
    component: EvaAppraisalsFormComponent,
  },
  {
    path: 'form-appraisal/:id',
    component: EvaAppraisalsFormComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class EvaPerformanceAppraisalsRoutesModule {}
