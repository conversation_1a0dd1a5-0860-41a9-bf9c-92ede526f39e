.evaluation-grid {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  &__header {
    color: #0971dc;
    font-weight: 600;
    font-size: 14px;
    line-height: 20px;
    margin-bottom: 4px;
  }
  ::ng-deep {
    .--has-custom-template {
      display: block;
      width: 100%;
      text-align: left;
      padding: 0 8px;
    }
  }
}
.eva-grid-content {
  // display: block; /* hoặc flex tùy context */
  // white-space: pre-wrap; /* cho phép xuống dòng tự động và giữ nguyên các dấu xuống dòng */
  // word-wrap: break-word; /* cho phép break word khi cần */
  // overflow-wrap: break-word; /* hỗ trợ break word tốt hơn trên các trình duyệt mới */
  // line-height: 1.5em; /* giữ lại line-height để dễ đọc */
}
