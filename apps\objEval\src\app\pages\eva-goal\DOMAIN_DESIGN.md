# Eva-Goal Domain Design

## Domain Model Overview

The eva-goal domain focuses on goal management within an organizational context, supporting both departmental and personal goal setting, allocation, tracking, and evaluation.

## Core Domain Concepts

### Entities

#### Goal Entity
```typescript
export class Goal {
  private constructor(
    private readonly _id: GoalId,
    private _name: string,
    private _group: GoalGroup,
    private _target: GoalTarget,
    private _weight: GoalWeight,
    private _status: GoalStatus,
    private _period: PeriodRange,
    private _implementation: Department,
    private _representative: Department
  ) {}

  static create(data: CreateGoalData): Goal {
    // Validation and business rules
    if (!data.name || data.name.trim().length === 0) {
      throw new DomainError('Goal name is required');
    }
    
    return new Goal(
      GoalId.create(data.id),
      data.name,
      data.group,
      data.target,
      data.weight,
      GoalStatus.DRAFT,
      data.period,
      data.implementation,
      data.representative
    );
  }

  // Business methods
  confirm(): void {
    if (!this.canBeConfirmed()) {
      throw new DomainError('Goal cannot be confirmed in current state');
    }
    this._status = GoalStatus.CONFIRMED;
  }

  reject(reason: string): void {
    if (!this.canBeRejected()) {
      throw new DomainError('Goal cannot be rejected in current state');
    }
    this._status = GoalStatus.REJECTED;
    // Emit domain event
  }

  allocate(allocation: GoalAllocation): void {
    // Business rules for allocation
  }

  private canBeConfirmed(): boolean {
    return this._status === GoalStatus.WAITING_CONFIRM;
  }

  private canBeRejected(): boolean {
    return [GoalStatus.WAITING_CONFIRM, GoalStatus.WAITING_APPROVE].includes(this._status);
  }
}
```

#### GoalAllocation Entity
```typescript
export class GoalAllocation {
  private constructor(
    private readonly _id: GoalAllocationId,
    private readonly _goalId: GoalId,
    private readonly _departmentId: DepartmentId,
    private _allocatedTarget: GoalTarget,
    private _formula: AllocationFormula,
    private _status: AllocationStatus
  ) {}

  static create(data: CreateGoalAllocationData): GoalAllocation {
    // Validation and business rules
    return new GoalAllocation(
      GoalAllocationId.create(data.id),
      data.goalId,
      data.departmentId,
      data.allocatedTarget,
      data.formula,
      AllocationStatus.PENDING
    );
  }

  calculateTarget(context: AllocationContext): GoalTarget {
    return this._formula.calculate(context);
  }

  approve(): void {
    if (this._status !== AllocationStatus.PENDING) {
      throw new DomainError('Allocation can only be approved when pending');
    }
    this._status = AllocationStatus.APPROVED;
  }
}
```

### Value Objects

#### GoalStatus Value Object
```typescript
export class GoalStatus {
  static readonly DRAFT = new GoalStatus('DRAFT');
  static readonly WAITING_CONFIRM = new GoalStatus('E_WAITING_CONFIRM');
  static readonly CONFIRMED = new GoalStatus('E_CONFIRMED');
  static readonly REJECTED = new GoalStatus('E_REJECTED');
  static readonly WAITING_APPROVE = new GoalStatus('E_WAITING_APPROVE');

  private constructor(private readonly value: string) {}

  equals(other: GoalStatus): boolean {
    return this.value === other.value;
  }

  toString(): string {
    return this.value;
  }

  canTransitionTo(newStatus: GoalStatus): boolean {
    const transitions = {
      'DRAFT': ['E_WAITING_CONFIRM'],
      'E_WAITING_CONFIRM': ['E_CONFIRMED', 'E_REJECTED'],
      'E_CONFIRMED': ['E_WAITING_APPROVE'],
      'E_WAITING_APPROVE': ['E_CONFIRMED', 'E_REJECTED'],
      'E_REJECTED': ['E_WAITING_CONFIRM']
    };

    return transitions[this.value]?.includes(newStatus.value) || false;
  }
}
```

#### GoalTarget Value Object
```typescript
export class GoalTarget {
  private constructor(
    private readonly value: number,
    private readonly unit: string
  ) {
    if (value < 0) {
      throw new DomainError('Goal target value cannot be negative');
    }
    if (!unit || unit.trim().length === 0) {
      throw new DomainError('Goal target unit is required');
    }
  }

  static create(value: number, unit: string): GoalTarget {
    return new GoalTarget(value, unit);
  }

  getValue(): number {
    return this.value;
  }

  getUnit(): string {
    return this.unit;
  }

  add(other: GoalTarget): GoalTarget {
    if (this.unit !== other.unit) {
      throw new DomainError('Cannot add targets with different units');
    }
    return new GoalTarget(this.value + other.value, this.unit);
  }

  multiply(factor: number): GoalTarget {
    return new GoalTarget(this.value * factor, this.unit);
  }

  equals(other: GoalTarget): boolean {
    return this.value === other.value && this.unit === other.unit;
  }

  toString(): string {
    return `${this.value} ${this.unit}`;
  }
}
```

#### GoalWeight Value Object
```typescript
export class GoalWeight {
  private constructor(private readonly value: number) {
    if (value < 0 || value > 100) {
      throw new DomainError('Goal weight must be between 0 and 100');
    }
  }

  static create(value: number): GoalWeight {
    return new GoalWeight(value);
  }

  getValue(): number {
    return this.value;
  }

  equals(other: GoalWeight): boolean {
    return this.value === other.value;
  }

  toString(): string {
    return `${this.value}%`;
  }
}
```

### Aggregates

#### GoalManagement Aggregate
```typescript
export class GoalManagementAggregate {
  private constructor(
    private readonly goal: Goal,
    private readonly allocations: GoalAllocation[] = [],
    private readonly progress: GoalProgress[] = []
  ) {}

  static create(goal: Goal): GoalManagementAggregate {
    return new GoalManagementAggregate(goal);
  }

  addAllocation(allocation: GoalAllocation): void {
    // Business rules for adding allocation
    if (!this.canAddAllocation(allocation)) {
      throw new DomainError('Cannot add allocation to this goal');
    }
    this.allocations.push(allocation);
  }

  updateProgress(progress: GoalProgress): void {
    // Business rules for updating progress
    const existingProgress = this.progress.find(p => 
      p.getPeriod().equals(progress.getPeriod())
    );
    
    if (existingProgress) {
      existingProgress.update(progress.getValue());
    } else {
      this.progress.push(progress);
    }
  }

  calculateTotalAllocatedTarget(): GoalTarget {
    return this.allocations.reduce(
      (total, allocation) => total.add(allocation.getAllocatedTarget()),
      GoalTarget.create(0, this.goal.getTarget().getUnit())
    );
  }

  private canAddAllocation(allocation: GoalAllocation): boolean {
    return this.goal.getStatus().equals(GoalStatus.CONFIRMED) &&
           !this.allocations.some(a => a.getDepartmentId().equals(allocation.getDepartmentId()));
  }
}
```

### Domain Services

#### GoalAllocationDomainService
```typescript
@Injectable()
export class GoalAllocationDomainService {
  allocateGoalToDepartments(
    goal: Goal,
    departments: Department[],
    allocationStrategy: AllocationStrategy
  ): GoalAllocation[] {
    if (!goal.getStatus().equals(GoalStatus.CONFIRMED)) {
      throw new DomainError('Can only allocate confirmed goals');
    }

    return allocationStrategy.allocate(goal, departments);
  }

  validateAllocationFormula(formula: AllocationFormula): boolean {
    // Complex business logic for formula validation
    return formula.isValid();
  }

  calculateDepartmentTarget(
    goal: Goal,
    department: Department,
    context: AllocationContext
  ): GoalTarget {
    // Business logic for target calculation
    const baseTarget = goal.getTarget();
    const departmentWeight = context.getDepartmentWeight(department);
    return baseTarget.multiply(departmentWeight);
  }
}
```

### Repository Interfaces

#### GoalRepository
```typescript
export interface GoalRepository {
  findById(id: GoalId): Promise<Goal | null>;
  findByPeriod(period: PeriodRange): Promise<Goal[]>;
  findByStatus(status: GoalStatus): Promise<Goal[]>;
  findByDepartment(departmentId: DepartmentId): Promise<Goal[]>;
  save(goal: Goal): Promise<void>;
  delete(id: GoalId): Promise<void>;
}
```

#### GoalAllocationRepository
```typescript
export interface GoalAllocationRepository {
  findByGoalId(goalId: GoalId): Promise<GoalAllocation[]>;
  findByDepartmentId(departmentId: DepartmentId): Promise<GoalAllocation[]>;
  save(allocation: GoalAllocation): Promise<void>;
  delete(id: GoalAllocationId): Promise<void>;
}
```

### Domain Events

#### GoalCreatedEvent
```typescript
export class GoalCreatedEvent implements DomainEvent {
  constructor(
    public readonly goalId: GoalId,
    public readonly goalName: string,
    public readonly createdBy: UserId,
    public readonly occurredOn: Date = new Date()
  ) {}
}
```

#### GoalAllocatedEvent
```typescript
export class GoalAllocatedEvent implements DomainEvent {
  constructor(
    public readonly goalId: GoalId,
    public readonly allocationId: GoalAllocationId,
    public readonly departmentId: DepartmentId,
    public readonly allocatedTarget: GoalTarget,
    public readonly occurredOn: Date = new Date()
  ) {}
}
```

## Business Rules

### Goal Management Rules
1. Goals must have a valid name, target, and period
2. Goals can only be confirmed if they are in WAITING_CONFIRM status
3. Goals can only be allocated if they are confirmed
4. Goal weight must be between 0 and 100
5. Goal target value cannot be negative

### Allocation Rules
1. Each department can only have one allocation per goal
2. Total allocated targets should not exceed the original goal target
3. Allocation formulas must be valid and calculable
4. Allocations can only be created for confirmed goals

### Progress Tracking Rules
1. Progress can only be updated for allocated goals
2. Progress values must be within valid ranges
3. Progress updates must be for valid time periods
4. Historical progress cannot be modified beyond a certain timeframe

---

*This domain design serves as the foundation for implementing the DDD architecture in the eva-goal module.*
